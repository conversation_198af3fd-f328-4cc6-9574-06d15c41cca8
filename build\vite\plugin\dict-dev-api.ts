import type { Plugin } from 'vite'
import fs from 'fs'
import path from 'path'
import { resolve } from 'path'

/**
 * 开发环境字典类型自动更新插件
 */
export function createDictDevApiPlugin(): Plugin {
  return {
    name: 'dict-dev-api',
    configureServer(server) {
      server.middlewares.use('/__dev_api/update-dict-enum', async (req, res) => {
        if (req.method !== 'POST') {
          res.statusCode = 405
          res.end('Method Not Allowed')
          return
        }

        try {
          let body = ''
          req.on('data', chunk => {
            body += chunk.toString('utf8')
          })

          req.on('end', async () => {
            try {
              console.log('收到请求体长度:', body.length)
              console.log('请求体前100字符:', body.substring(0, 100))
              
              // 确保body是完整的JSON
              if (!body.trim()) {
                res.statusCode = 400
                res.end('Empty request body')
                return
              }
              
              const parsedBody = JSON.parse(body)
              const { newEnumItems } = parsedBody
              
              if (!Array.isArray(newEnumItems) || newEnumItems.length === 0) {
                res.statusCode = 400
                res.end('Invalid newEnumItems')
                return
              }

              // 读取dict.ts文件
              const dictFilePath = path.resolve(process.cwd(), 'src/utils/dict.ts')
              const fileContent = fs.readFileSync(dictFilePath, 'utf-8')

              // 查找DICT_TYPE枚举的位置
              const enumStartRegex = /export\s+enum\s+DICT_TYPE\s*\{/
              const enumStartMatch = fileContent.match(enumStartRegex)
              
              if (!enumStartMatch) {
                res.statusCode = 500
                res.end('Could not find DICT_TYPE enum in dict.ts')
                return
              }

              const enumStartIndex = enumStartMatch.index! + enumStartMatch[0].length
              
              // 找到枚举结束位置
              let braceCount = 1
              let enumEndIndex = enumStartIndex
              
              for (let i = enumStartIndex; i < fileContent.length && braceCount > 0; i++) {
                const char = fileContent[i]
                if (char === '{') {
                  braceCount++
                } else if (char === '}') {
                  braceCount--
                  if (braceCount === 0) {
                    enumEndIndex = i
                    break
                  }
                }
              }

              // 提取现有枚举内容
              const enumContent = fileContent.substring(enumStartIndex, enumEndIndex)
              
              // 解析现有的枚举项
              const existingEnumValues = new Set<string>()
              const existingEnumKeys = new Set<string>()
              const enumItemRegex = /([A-Z_]+)\s*=\s*'([^']+)'/g
              let match
              
              while ((match = enumItemRegex.exec(enumContent)) !== null) {
                existingEnumKeys.add(match[1]) // 添加枚举键名
                existingEnumValues.add(match[2]) // 添加枚举值
              }
              
              // 生成唯一键名的函数
              const generateUniqueKey = (baseKey: string, value: string): string => {
                let uniqueKey = baseKey
                
                // 如果键名已存在，使用字母前缀
                if (existingEnumKeys.has(uniqueKey)) {
                  // 使用字母前缀 A、B、C 等
                  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
                  let letterIndex = 0
                  
                  while (existingEnumKeys.has(uniqueKey) && letterIndex < letters.length) {
                    uniqueKey = `${letters[letterIndex]}_${baseKey}`
                    letterIndex++
                  }
                  
                  // 如果26个字母都用完了，回退到数字后缀
                  if (existingEnumKeys.has(uniqueKey)) {
                    let counter = 1
                    uniqueKey = baseKey
                    while (existingEnumKeys.has(uniqueKey)) {
                      uniqueKey = `${baseKey}_${counter}`
                      counter++
                    }
                  }
                }
                
                existingEnumKeys.add(uniqueKey) // 添加到已存在的键名集合中
                return uniqueKey
              }
              
              // 过滤出新的枚举项
              const newItems: string[] = []
              newEnumItems.forEach(item => {
                // 解析枚举项格式：KEY = 'value', // comment
                const itemMatch = item.match(/([A-Z_]+)\s*=\s*'([^']+)'(?:\s*,)?(?:\s*\/\/\s*(.*))?/)
                if (itemMatch) {
                  const [, originalKey, value, comment] = itemMatch
                  if (!existingEnumValues.has(value)) {
                    // 生成唯一的键名
                    const uniqueKey = generateUniqueKey(originalKey, value)
                    
                    // 格式化新的枚举项
                    const formattedItem = comment 
                      ? `  ${uniqueKey} = '${value}', // ${comment}`
                      : `  ${uniqueKey} = '${value}',`
                    newItems.push(formattedItem)
                    
                    // 如果键名被修改了，记录日志
                    if (uniqueKey !== originalKey) {
                      console.log(`键名冲突已解决: ${originalKey} -> ${uniqueKey}`)
                    }
                  }
                }
              })

              if (newItems.length === 0) {
                res.statusCode = 200
                res.setHeader('Content-Type', 'application/json')
                res.end(JSON.stringify({ message: '所有枚举项已存在，无需更新' }))
                return
              }

              // 在枚举结束前插入新项目
              const beforeClosingBrace = fileContent.substring(0, enumEndIndex)
              const afterClosingBrace = fileContent.substring(enumEndIndex)
              
              // 更精确地处理最后一个枚举项的逗号
              let insertPosition = beforeClosingBrace
              
              // 查找最后一个枚举项并检查是否需要添加逗号
              const lines = insertPosition.split('\n')
              let lastEnumLineIndex = -1
              
              // 从后往前找到最后一个包含枚举项的行
              for (let i = lines.length - 1; i >= 0; i--) {
                const line = lines[i].trim()
                if (line && /[A-Z_]+\s*=\s*'[^']+'/g.test(line)) {
                  lastEnumLineIndex = i
                  break
                }
              }
              
              if (lastEnumLineIndex >= 0) {
                const lastEnumLine = lines[lastEnumLineIndex]
                
                // 检查该行是否已经有逗号
                const commentIndex = lastEnumLine.indexOf('//')
                let valueEndIndex = lastEnumLine.lastIndexOf("'")
                
                if (valueEndIndex > 0) {
                  // 检查引号后到注释前（或行尾）是否有逗号
                  const afterQuote = commentIndex >= 0 
                    ? lastEnumLine.substring(valueEndIndex + 1, commentIndex)
                    : lastEnumLine.substring(valueEndIndex + 1)
                  
                  if (!afterQuote.includes(',')) {
                    // 需要添加逗号
                    if (commentIndex >= 0) {
                      // 有注释，在注释前添加逗号
                      lines[lastEnumLineIndex] = lastEnumLine.substring(0, valueEndIndex + 1) + ',' + lastEnumLine.substring(valueEndIndex + 1)
                    } else {
                      // 没有注释，在行尾添加逗号
                      lines[lastEnumLineIndex] = lastEnumLine + ','
                    }
                  }
                }
              }
              
              insertPosition = lines.join('\n')
              
              // 确保有换行符
              if (!insertPosition.endsWith('\n')) {
                insertPosition += '\n'
              }
              
              // 添加新的枚举项
              const newEnumContent = newItems.join('\n') + '\n'
              
              const newFileContent = insertPosition + newEnumContent + afterClosingBrace

              // 写入文件
              fs.writeFileSync(dictFilePath, newFileContent, 'utf-8')

              res.statusCode = 200
              res.setHeader('Content-Type', 'application/json')
              res.end(JSON.stringify({ 
                message: `成功添加 ${newItems.length} 个新的字典类型`,
                addedItems: newItems
              }))
              
              console.log(`✅ 字典类型已更新: 添加了 ${newItems.length} 个新项目`)
              
            } catch (parseError) {
              console.error('解析请求失败:', parseError)
              res.statusCode = 400
              res.end('Invalid JSON')
            }
          })
        } catch (error) {
          console.error('处理字典更新请求失败:', error)
          res.statusCode = 500
          res.end('Internal Server Error')
        }
      })
    }
  }
}