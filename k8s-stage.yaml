apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: stage
spec:
  revisionHistoryLimit: 3
  replicas: 1
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      imagePullSecrets:
        - name: frontend
      containers:
        - name: pod-frontend
          image: ${image}
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80
              protocol: TCP
              name: frontend-port

