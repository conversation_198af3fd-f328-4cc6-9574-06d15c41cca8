user  nginx;
worker_processes  auto;
# hrq
error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    client_max_body_size 2048m;
    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;
	  map $http_upgrade $connection_upgrade { 
        default          keep-alive;  #默认为keep-alive 可以支持 一般http请求
        'websocket'      upgrade;     #如果为websocket 则为 upgrade 可升级的。
    }
    #gzip  on;

    server {
		listen       80;
		listen  [::]:80;
		server_name  localhost;

		#access_log  /var/log/nginx/host.access.log  main;
		
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		# proxy_set_header Connection "upgrade";
		proxy_set_header Connection $connection_upgrade;
		proxy_set_header X-real-ip $remote_addr;
		proxy_set_header X-Forwarded-For $remote_addr;

		location / {
					# 允许跨域
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

			if ($request_method = 'OPTIONS') {
				return 204;
			}
			try_files $uri $uri/ /index.html;
			root /usr/share/nginx/html;
			expires -1;
			add_header Cache-Control 'no-cache, no-store, must-revalidate';

		}

        # 前端路由
		# location ^~ /h5 {
		# 	# 允许跨域
		# 	add_header Access-Control-Allow-Origin *;
		# 	add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
		# 	add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

		# 	if ($request_method = 'OPTIONS') {
		# 		return 204;
		# 	}
		# 	# root   /usr/share/nginx/html;
		# 	alias /usr/share/nginx/html;
		# 	index  index.html index.htm;
		# 	try_files $uri $uri/ /h5/index.html;
		# }
		# 后端路由

		# location ^~ /front-api/ {
    #         proxy_pass http://192.168.10.166:9908;
		# 	rewrite ^/front-api/(.*)$ /$1 break;
		# }
		
		# 后端路由-实时推送消息
		# location ^~ /front-ws/ {
		# 	proxy_set_header Upgrade $http_upgrade; #此处配置 上面定义的变量
		# 	proxy_set_header Connection $connection_upgrade;
    #         proxy_pass http://***:***;
		# 	rewrite ^/front-ws/(.*)$ /$1 break;
		# }
		
	

		#error_page  404              /404.html;

		# redirect server error pages to the static page /50x.html
		#
		error_page   500 502 503 504  /50x.html;
		location = /50x.html {
			root   /usr/share/nginx/html;
		}

	}
}