<script lang="ts" setup>
import { ThirdPartyAccountApi } from '@/api/system/third'
import routerSearch from '@/components/RouterSearch/index.vue'
import VersionUpdate from '@/components/VersionUpdate/index.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { isDark } from '@/utils/is'

defineOptions({ name: 'APP' })

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
  if (isDarkTheme === null) {
    isDarkTheme = isDark()
  }
  appStore.setIsDark(isDarkTheme)
}
setDefaultTheme()
onMounted(async () => {
  if (!localStorage.getItem('shopList')) {
    const shopRes = await ThirdPartyAccountApi.getShopList()
    if (shopRes) {
      localStorage.setItem('shopList', JSON.stringify(shopRes))
    }
  }
})
</script>
<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
    <routerSearch />
    <!-- 公告组件 -->
    <!-- <Announcement ref="announcementRef" /> -->
    <!-- 版本更新提示组件 -->
    <VersionUpdate />
  </ConfigGlobal>
</template>
<style lang="scss">
$prefix-cls: #{$namespace}-app;

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  @extend .size;

  padding: 0 !important;
  margin: 0;
  overflow: hidden;

  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}

.el-form-item {
  padding-bottom: 10px !important;
  margin-right: 10px !important;
  margin-bottom: 0;
  margin-left: 0 !important;
}

.vxe-modal--wrapper {
  z-index: 99999 !important;
}
</style>
