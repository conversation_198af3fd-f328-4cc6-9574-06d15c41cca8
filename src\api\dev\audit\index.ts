import request from '@/config/axios'

// 审核列表的分页查询参数
export interface AuditPageReqVO extends PageParam {
  category?: string
  status?: number
  createTime?: string[]
}

// 审核对象
export interface AuditVO {
  id?: number
  category?: string
  brand?: string
  productName?: string
  sku?: string
  status?: number
  createTime?: Date
}

// 获取审核分页
export const getAuditPage = async (params: AuditPageReqVO) => {
  return await request.get({ url: '/dev/audit/page', params })
}

// 获取审核详情
export const getAudit = async (id: number) => {
  return await request.get({ url: '/dev/audit/get?id=' + id })
}

// 新增审核
export const createAudit = async (data: AuditVO) => {
  return await request.post({ url: '/dev/audit/create', data })
}

// 修改审核
export const updateAudit = async (data: AuditVO) => {
  return await request.put({ url: '/dev/audit/update', data })
}

// 删除审核
export const deleteAudit = async (id: number) => {
  return await request.delete({ url: '/dev/audit/delete?id=' + id })
}

// 导出审核
export const exportAudit = async (params: AuditPageReqVO) => {
  return await request.download({ url: '/dev/audit/export', params })
}
