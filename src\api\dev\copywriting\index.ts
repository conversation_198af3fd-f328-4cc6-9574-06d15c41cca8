import request from '@/config/axios'

export interface ProductVO {
  id: number // 主键
  sku: string // 产品唯一标识
  titleFlag: string // 是否生成标题，0否1是
  titleWordCount: number // 标题字数
  bulletPointsFlag: string // 是否生成五点，0否1是
  bulletPointsWordCount: number // 五点字数
  productDescriptionFlag: string // 是否生成产品描述，0否1是
  descriptionWordCount: number // 产品描述字数
  language: string // 语言
  brandName: string // 品牌名称
  productName: string // 产品名称
  targetAudience: string // 产品受众
  usageScenarios: string // 使用场景
  productSpecifications: string // 产品参数
  coreKeywords: string // 核心词
  keywords: string // 关键词
  avoidWords: string // 规避词
  languageStyle: string // 语言风格
  specialInstructions: string // 特殊说明
  status: string // 状态，0待生成 1生成中 2成功 -1失败
  userId: number // 操作人
  userName: string // 操作人姓名
  deptId: number // 部门id
  model: string // 模型选择
  url: string // 商品链接
}

// 查询列表
export const getProductList = (params) => {
  return request.get({ url: '/dev/automation-product-info/page', params })
}

// 获取详情
export const getProductInfo = (id: number) => {
  return request.get({ url: '/dev/automation-product-info/get?id=' + id })
}

// 新增
export const createProduct = (data: ProductVO) => {
  return request.post({ url: '/dev/automation-product-info/create', data })
}

// 修改
export const updateProduct = (data: ProductVO) => {
  return request.put({ url: '/dev/automation-product-info/update', data })
}

// 删除
export const deleteProduct = (id: number) => {
  return request.delete({ url: '/dev/automation-product-info/delete?id=' + id })
}

// 批量删除
export const deleteBatch = (ids: number[]) => {
  return request.put({
    url: '/dev/automation-product-info/delete-batch',
    params: {
      ids: ids.join(',')
    }
  })
}

// 批量生成文案
export const generateBatch = (ids: number[]) => {
  return request.put({
    url: '/dev/automation-product-info/generate-batch',
    params: {
      ids: ids.join(',')
    }
  })
}

// 生成文案
export const generate = (id: number) => {
  return request.put({
    url: '/dev/automation-product-info/generate',
    params: {
      id: id
    }
  })
}

// 查询文案生成记录列表
export const getAutomationLogList = (params) => {
  return request.get({ url: '/dev/automation-product-copy-gen-log/list', params })
}

// 查询产品文案生成记录详情
export const getAutomationProductCopyGenLog = async (id: number) => {
  return await request.get({ url: `/dev/automation-product-copy-gen-log/get?id=` + id })
}

// 文案分析
export const chatAnalysis = (data: ProductVO) => {
  return request.post({ url: '/ai/chat/analysis', data })
}

// 下载用户导入模板
export const importUserTemplate = () => {
  return request.download({ url: '/dev/automation-product-info/get-import-template' })
}

// 根据 ASIN 获取竞品信息
export interface GetCompetitorByAsinsRequest {
  asins: string[],
  asinCountry: string
}

export interface AppAutomationProductInfoCompetitorRespVO {
  asin?: string
  competitorBullets?: string
  competitorTitle?: string
  createTime?: string
  id?: number
  productInfoId?: number
}

export const getCompetitorByAsins = (params: GetCompetitorByAsinsRequest) => {
  return request.get<AppAutomationProductInfoCompetitorRespVO[]>({ url: '/dev/automation-product-info/get-competitor-by-asins?asins=' + params.asins.join(',') + '&asinCountry=' + params.asinCountry })
}
