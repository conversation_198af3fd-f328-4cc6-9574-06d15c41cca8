import request from '@/config/axios'

// 管理费保存请求VO
export interface ManagementFeeSaveReqVO {
  id?: number // 管理费项id
  name: string // 管理费名称
  amount: number // 管理费金额
  type: number // 管理费类型(字典)
  rule: number // 管理费分摊方式(规则)
  isMonthly: boolean // 是否应用到下一个月
  date: string // 月份
}

// 管理费响应VO
export interface ManagementFeeRespVO {
  id: number // 管理费项id
  name: string // 管理费名称
  amount: number // 管理费金额
  type: number // 管理费类型(字典)
  rule: number // 管理费分摊方式(规则)
  isMonthly: boolean // 是否应用到下一个月
  createTime: string // 创建时间
  date: string // 月份
}

// 管理费分页查询参数
export interface ManagementFeePageReqVO extends PageParam {
  name?: string // 管理费名称
  amount?: string // 管理费金额
  type?: number // 管理费类型(字典)
  rule?: number // 管理费分摊方式(规则)
  isMonthly?: boolean // 是否应用到下一个月
  createTime?: string[] // 创建时间
  date?: string[] // 月份
}

// 创建费用项
export const createManagementFee = (data: ManagementFeeSaveReqVO) => {
  return request.post({ url: '/infra/management-fee/create', data })
}

// 更新费用项
export const updateManagementFee = (data: ManagementFeeSaveReqVO) => {
  return request.put({ url: '/infra/management-fee/update', data })
}

// 删除费用项
export const deleteManagementFee = (id: number) => {
  return request.delete({ url: '/infra/management-fee/delete?id=' + id })
}

// 获得费用项详情
export const getManagementFee = (id: number) => {
  return request.get({ url: '/infra/management-fee/get?id=' + id })
}

// 获得费用项分页
export const getManagementFeePage = (params: ManagementFeePageReqVO) => {
  return request.get({ url: '/infra/management-fee/page', params })
}

// 下载费用项Excel模板
export const exportManagementFee = (params: ManagementFeePageReqVO) => {
  return request.download({ url: '/infra/management-fee/export-excel', params })
}
