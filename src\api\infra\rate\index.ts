import request from '@/config/axios'

// 汇率响应VO
export interface RateRespVO {
  id: number
  date: string // 生效月份
  code: string // 币种
  icon: string // 币种符号
  name: string // 币种名
  rateSelf: string // 我的汇率
  rateOrg: string // 官方汇率
  createTime: string // 创建时间
}

// 汇率保存请求VO
export interface RateSaveReqVO {
  id?: number
  date: string // 生效月份
  code: string // 币种
  icon: string // 币种符号
  name: string // 币种名
  rateSelf: string // 我的汇率
  rateOrg: string // 官方汇率
}

// 币种代码VO
export interface RateCodeVO {
  code: string
  name: string
}

// 汇率查询参数
export interface RatePageReqVO extends PageParam {
  date?: string[] // 生效月份
  codes?: any[] // 币种
  icon?: string // 币种符号
  name?: string // 币种名
  rateSelf?: string // 我的汇率
  rateOrg?: string // 官方汇率
  createTime?: string[] // 创建时间
}

// 获得汇率设置分页
export const getRatePage = (params: RatePageReqVO) => {
  return request.get({ url: '/infra/rate/page', params })
}

// 获得汇率设置列表
export const getRateList = (params: RatePageReqVO) => {
  return request.get({ url: '/infra/rate/list', params })
}

// 更新汇率设置
export const updateRate = (data: RateSaveReqVO) => {
  return request.put({ url: '/infra/rate/update', data })
}

// 刷新当月汇率数据
export const refreshRate = () => {
  return request.put({ url: '/infra/rate/refresh' })
}

// 获取币种
export const getRateCode = () => {
  return request.get({ url: '/infra/rate/get-code' })
}
