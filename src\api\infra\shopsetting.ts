import request from '@/config/axios'

export interface ShopSettingVO {
  id: number
  sid: number
  shopType: string
  sellerItem: string
  accountName: string
  region: string
  country: string
  status: string
  createTime: string
}

export const getShopSettingPage = async (params) => {
  return await request.get({ url: '/infra/shop-setting/page', params })
}

export const saveShopSetting = async (data) => {
  return await request.post({ url: '/infra/shop-setting/save', data }) //7.19文星检查save和update接口入参出参一致叫我改回save接口
}

export const getShopSimpleListProfit = async () => {
  return await request.get({ url: '/infra/shop-setting/simple-list-profit' })
}
