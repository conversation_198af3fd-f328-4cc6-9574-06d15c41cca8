import request from '@/config/axios'

export interface ToolsTableConditionTemplateVO {
  id?: number
  userId?: number
  type?: string
  template?: string
  content?: string
  creator?: string
  createTime?: Date
  updater?: string
  updateTime?: Date
}

// 查询列表查询条件模板
export const getToolsTableConditionTemplateList = (params) => {
  return request.get({
    url: '/infra/tools-table-condition/list',
    params
  })
}

// 查询详情查询条件模板
export const getToolsTableConditionTemplate = (id: number) => {
  return request.get({
    url: '/infra/tools-table-condition/get?id=' + id
  })
}

// 新增查询条件模板
export const createToolsTableConditionTemplate = (data: ToolsTableConditionTemplateVO) => {
  return request.post({
    url: '/infra/tools-table-condition/create',
    data
  })
}

// 修改查询条件模板
export const updateToolsTableConditionTemplate = (data: ToolsTableConditionTemplateVO) => {
  return request.put({
    url: '/infra/tools-table-condition/update',
    data
  })
}

// 删除查询条件模板
export const deleteToolsTableConditionTemplate = (id: number) => {
  return request.delete({
    url: '/infra/tools-table-condition/delete?id=' + id
  })
}