import request from '@/config/axios'

export interface ToolsTableHeadTemplateVO {
  id?: number
  userId?: number
  type?: string
  template?: string
  content?: string
  creator?: string
  createTime?: Date
  updater?: string
  updateTime?: Date
}

// 查询列表表头模板
export const getToolsTableHeadTemplateList = (params) => {
  return request.get({
    url: '/infra/tools-table-head-template/list',
    params
  })
}

// 查询详情表头模板
export const getToolsTableHeadTemplate = (id: number) => {
  return request.get({
    url: '/infra/tools-table-head-template/get?id=' + id
  })
}

// 新增表头模板
export const createToolsTableHeadTemplate = (data: ToolsTableHeadTemplateVO) => {
  return request.post({
    url: '/infra/tools-table-head-template/create',
    data
  })
}

// 修改表头模板
export const updateToolsTableHeadTemplate = (data: ToolsTableHeadTemplateVO) => {
  return request.put({
    url: '/infra/tools-table-head-template/update',
    data
  })
}

// 删除表头模板
export const deleteToolsTableHeadTemplate = (id: number) => {
  return request.delete({
    url: '/infra/tools-table-head-template/delete?id=' + id
  })
}