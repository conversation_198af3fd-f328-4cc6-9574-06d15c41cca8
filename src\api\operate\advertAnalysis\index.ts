import request from '@/config/axios'

// 广告效果分析日志 VO
export interface AutomationAdvertisementAnalysisLogVO {
  id: number // 主键ID，自增整数
  keyword: string // 关键词
  satisfactionRules: string // 满足规则
  optimizationSuggestion: string // 优化建议
  shopName: string // 店铺名称
  adType: string // 类型
  adPortfolio: string // 广告组合
  adCampaign: string // 广告活动
  adGroup: string // 广告组
  analysisTimeRange: string // 分析时间跨度
  matchingMethod: string // 匹配方式
  impressions: number // 曝光量
  clicks: number // 点击
  ctr: number // CTR(点击通过率)
  cpcLocalCurrency: number // CPC-本币
  spendLocalCurrency: number // 花费-本币
  acos: number // ACoS
  cvr: number // CVR(转化率)
  budget: number // 预算
  budgetUtilizationRate: number // 预算使用率
  analysisTime: Date // 分析时间
  lingxingAccount: string // 领星账号
  operator: string // 操作人
  userId: number // 操作人
  deptId: number // 部门id
}

// 广告效果分析 VO
export interface AutomationAdvertisementAnalysisVO {
  id: number // 主键ID，自增整数
  keyword: string // 关键词
  satisfactionRules: string // 满足规则
  optimizationSuggestion: string // 优化建议
  shopName: string // 店铺名称
  adType: string // 类型
  adPortfolio: string // 广告组合
  adCampaign: string // 广告活动
  adGroup: string // 广告组
  analysisTimeRange: string // 分析时间跨度
  matchingMethod: string // 匹配方式
  impressions: number // 曝光量
  clicks: number // 点击
  ctr: number // CTR(点击通过率)
  cpcLocalCurrency: number // CPC-本币
  spendLocalCurrency: number // 花费-本币
  acos: number // ACoS
  cvr: number // CVR(转化率)
  budget: number // 预算
  budgetUtilizationRate: number // 预算使用率
  analysisTime: Date // 分析时间
  lingxingAccount: string // 领星账号
  operator: string // 操作人
  userId: number // 操作人
  deptId: number // 部门id
}

// 广告策略
export const AutomationAdvertisementAnalysisApi = {
  // 查询广告效果分析分页
  getAutomationAdvertisementAnalysisPage: async (params: any) => {
    return await request.get({ url: `/operation/automation-advertisement-analysis/page`, params })
  },
  // 查询广告分析进度
  getProgress: async () => {
    return await request.get({ url: `/operation/automation-advertisement-analysis/get-progress` })
  },

  // 查询广告效果分析详情
  getAutomationAdvertisementAnalysis: async (id: number) => {
    return await request.get({ url: `/operation/automation-advertisement-analysis/get?id=` + id })
  },

  // 新增广告效果分析
  createAutomationAdvertisementAnalysis: async (data: AutomationAdvertisementAnalysisVO) => {
    return await request.post({ url: `/operation/automation-advertisement-analysis/create`, data })
  },

  // 修改广告效果分析
  updateAutomationAdvertisementAnalysis: async (data: AutomationAdvertisementAnalysisVO) => {
    return await request.put({ url: `/operation/automation-advertisement-analysis/update`, data })
  },

  // 删除广告效果分析
  deleteAutomationAdvertisementAnalysis: async (id: number) => {
    return await request.delete({
      url: `/operation/automation-advertisement-analysis/delete?id=` + id
    })
  },

  // 导出广告效果分析 Excel
  exportAutomationAdvertisementAnalysis: async (params) => {
    return await request.download({
      url: `/operation/automation-advertisement-analysis/export-excel`,
      params
    })
  },
  // 查询广告效果分析日志分页
  getAutomationAdvertisementAnalysisLogPage: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis-log/page`,
      params
    })
  },

  // 查询广告效果分析日志详情
  getAutomationAdvertisementAnalysisLog: async (id: number) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis-log/get?id=` + id
    })
  },

  // 新增广告效果分析日志
  createAutomationAdvertisementAnalysisLog: async (data: AutomationAdvertisementAnalysisLogVO) => {
    return await request.post({
      url: `/operation/automation-advertisement-analysis-log/create`,
      data
    })
  },
  // 智能广告分析
  intelligentAnalysis: async (data: any) => {
    return await request.post({
      url: `/operation/automation-advertisement-analysis/intelligent-Analysis`,
      data
    })
  },

  // 修改广告效果分析日志
  updateAutomationAdvertisementAnalysisLog: async (data: AutomationAdvertisementAnalysisLogVO) => {
    return await request.put({
      url: `/operation/automation-advertisement-analysis-log/update`,
      data
    })
  },

  // 删除广告效果分析日志
  deleteAutomationAdvertisementAnalysisLog: async (id: number) => {
    return await request.delete({
      url: `/operation/automation-advertisement-analysis-log/delete?id=` + id
    })
  },

  // 导出广告效果分析日志 Excel
  exportAutomationAdvertisementAnalysisLog: async (params) => {
    return await request.download({
      url: `/operation/automation-advertisement-analysis-log/export-excel`,
      params
    })
  },

  // 获取广告策略list
  getAutomationAdvertisementStrategy: async () => {
    return await request.get({ url: `/operation/automation-advertising-strategy/get` })
  },

  // 获得导入广告策略模板
  getStrategyExportTemplate: async () => {
    return await request.get({
      url: `/operation/automation-advertising-strategy/get-import-template`
    })
  },

  // 获取统计值
  getStatistical: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/getStatistical`,
      params
    })
  },

  // 获取诊断历史
  getPageHistory: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/pageHistory`,
      params
    })
  },

  // 获取广告组合列表
  getAdPortfolio: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/getAdPortfolio`,
      params
    })
  },

  // 忽略建议
  ignore: async (id: number) => {
    return await request.delete({
      url: `/operation//automation-advertisement-analysis/ignore?id=` + id
    })
  },

  // 批量忽略建议
  ignoreBatch: async (ids: AutomationAdvertisementAnalysisVO) => {
    return await request.put({
      url: `/operation/automation-advertisement-analysis/ignore-batch`,
      params: {
        ids: ids.join(',')
      }
    })
  },
  // 导出选中
  exportSelected: (ids: number[]) => {
    return request.download({
      url: '/operation/automation-advertisement-analysis/export-selected',
      method: 'GET',
      params: { ids: ids.join(',') }
    })
  },

  // 导出全部
  exportAll: (exData: any) => {
    return request.download({
      url: '/operation/automation-advertisement-analysis/export-excel',
      method: 'GET',
      params: exData
    })
  },

  // 获取应用优化建议
  getApplication: async (ids: number[]) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/get-application`,
      params: { ids: ids.join(',') }
    })
  },

  // 执行优化建议
  updateApplication: async (data: any[]) => {
    return await request.post({
      url: `/operation/automation-advertisement-analysis/update-application`,
      data
    })
  },

  // 获取预算竞价设置列表
  getBudgetBiddingList: async () => {
    return await request.get({ url: `/operation/advertisement-setting/list` })
  },

  // 保存预算竞价设置
  saveBudgetBidding: async (data: any) => {
    return await request.put({ url: `/operation/advertisement-setting/save`, data })
  },

  // 更新广告活动预算
  updateCampaign: async (data: any) => {
    return await request.put({
      url: `/operation/automation-advertisement-analysis/update-campaign`,
      data
    })
  },

  // 更新关键词竞价
  updateBudget: async (data: any) => {
    return await request.put({
      url: `/operation/automation-advertisement-analysis/update-keyword`,
      data
    })
  },

  // 获取单个种类的最新的日志记录
  getSingleLog: async (params: { id: number; bizType: string }) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/get-single-log`,
      params
    })
  },

  // 获取单个种类的最新的日志记录
  getSingleLogSelf: async (params: { appId: number; bizType: string; selfId: number }) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/get-single-log-self`,
      params
    })
  },

  // 获得广告分析日志分页
  getPageLog: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertisement-analysis/page-log`,
      params
    })
  }
}
