import request from '@/config/axios'

// 广告效果分析日志 VO
export interface AutomationAdvertisementAnalysisLogVO {
  id: number // 主键ID，自增整数
  keyword: string // 关键词
  satisfactionRules: string // 满足规则
  optimizationSuggestion: string // 优化建议
  shopName: string // 店铺名称
  adType: string // 类型
  adPortfolio: string // 广告组合
  adCampaign: string // 广告活动
  adGroup: string // 广告组
  analysisTimeRange: string // 分析时间跨度
  matchingMethod: string // 匹配方式
  impressions: number // 曝光量
  clicks: number // 点击
  ctr: number // CTR(点击通过率)
  cpcLocalCurrency: number // CPC-本币
  spendLocalCurrency: number // 花费-本币
  acos: number // ACoS
  cvr: number // CVR(转化率)
  budget: number // 预算
  budgetUtilizationRate: number // 预算使用率
  analysisTime: Date // 分析时间
  lingxingAccount: string // 领星账号
  operator: string // 操作人
  userId: number // 操作人
  deptId: number // 部门id
}

// 广告列表模块 API 地址前缀
const advertisingCreationBaseURL = '/operation/advertising-creation'

// 广告列表模块 API
export const AdvertisingCreationApi = {
  // 创建广告
  createAdvertising: (data: any) => {
    return request.post({ url: advertisingCreationBaseURL + '/create', data })
  },

  // 重试
  retryApi: (data: any) => {
    return request.post({ url: advertisingCreationBaseURL + '/retry', data })
  },

  // 获得广告详情
  getAdvertisingDetail: (id: number) => {
    return request.get({ url: advertisingCreationBaseURL + '/get?id=' + id })
  },

  // 获得广告详情分页
  getAdvertisingPage: (params: any) => {
    return request.get({ url: advertisingCreationBaseURL + '/page', params })
  },

  // 获得广告sku分页
  getAdvertisingSkuPage: (params: any) => {
    return request.get({ url: advertisingCreationBaseURL + '/sku-page', params })
  },

  // 获得广告keywords分页
  getAdvertisingKeywordsPage: (params: any) => {
    return request.get({ url: advertisingCreationBaseURL + '/keywords-page', params })
  },
}

// 广告模板管理模块 API 地址前缀
const advertisingTemplateBaseURL = '/operation/advertising-template'

// 广告模板管理模块 API
export const AdvertisingTemplateApi = {
  // 创建广告模板
  createAdvertisingTemplate: (data: any) => {
    return request.post({ url: advertisingTemplateBaseURL + '/create', data })
  },

  // 更新广告模板
  updateAdvertisingTemplate: (data: any) => {
    return request.put({ url: advertisingTemplateBaseURL + '/update', data })
  },

  // 删除广告模板
  deleteAdvertisingTemplate: (id: number) => {
    return request.delete({ url: advertisingTemplateBaseURL + '/delete?id=' + id })
  },

  // 获得广告模板详情
  getAdvertisingTemplateDetail: (id: number) => {
    return request.get({ url: advertisingTemplateBaseURL + '/get?id=' + id })
  },

  // 获得广告模板分页
  getAdvertisingTemplatePage: (params: any) => {
    return request.get({ url: advertisingTemplateBaseURL + '/page', params })
  }
}

// 创建广告-广告详情分页
export const AutomationAdvertisementAnalysisApi = {
  // 获得广告详情分页
  getAdvertisingCreationPage: async (params: any) => {
    return await request.get({ url: `/operation/advertising-creation/page`, params })
  },
}
