import request from '@/config/axios'

// 获取策略分页 Request VO
export interface AutomationAdvertisementStrategyPageReqVO {
  /**
   * 创建时间
   */
  createTime?: string[];
  /**
   * 部门id
   */
  deptId?: number;
  /**
   * 页码，从 1 开始
   */
  pageNo: number;
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number;
  /**
   * 策略名
   */
  strategyName?: string;
  /**
   * 所属人
   */
  userId?: number;
  [property: string]: any;
}

// 复制策略 Request VO
export interface AutomationAdvertisementStrategySaveReqVO {
  /**
   * 部门id
   */
  deptId?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 策略名
   */
  strategyName?: string;
  /**
   * 所属人
   */
  userId?: number;
  [property: string]: any;
}

// 广告策略 API
export const AutomationAdvertisementStrategyApi = {
  // 获取策略分页
  getAutomationAdvertisementStrategyPage: async (params: AutomationAdvertisementStrategyPageReqVO) => {
    return await request.get({
      url: `/operation/automation-advertising-strategy/page`,
      params
    })
  },

  // 复制策略
  createAutomationAdvertisementStrategy: async (data: AutomationAdvertisementStrategySaveReqVO) => {
    return await request.post({
      url: `/operation/automation-advertising-strategy/copy-strategy`,
      data
    })
  },

  // 获得广告策略区间规则
  getStrategyDetail: async (strategyId: any) => {
    return await request.get({
      url: `/operation/automation-advertising-strategy/get-detail?strategyId=` + strategyId
    })
  },

  // 保存区间划分规则
  updateStrategyBatch: async (data: any) => {
    return await request.post({
      url: `/operation/automation-advertising-strategy/update-detail-batch`,
      data
    })
  },

  // 获得广告策略组合
  getStrategyAll: async (params: any) => {
    return await request.get({
      url: `/operation/automation-advertising-strategy/get-all`,
      params
    })
  },

  // 保存广告策略组合规则
  updateAllBatch: async (data: any) => {
    return await request.post({
      url: `/operation/automation-advertising-strategy/update-all-batch`,
      data
    })
  },
  // 批量删除策略
  deleteStrategyBatch: async (ids: number[]) => {
    return await request.put({
      url: '/operation/automation-advertising-strategy/delete-batch',
      params: {
        ids: ids.join(',')
      }
    })
  },
  // 分享 shareScope 1 :表示分享至团队，2 :表示分享至所有团队
  shareStrategy: async (data: any) => {
    return await request.post({
      url: '/operation/automation-advertising-strategy/share',
      data
    })
  }
}
