import request from '@/config/axios'

// 违禁词库 API 地址前缀
const baseURL = '/operation/forbidden-words'
// 获取违禁词详情
export const getForbiddenWordDetailApi = (id: number) => {
  return request.get({ url: baseURL + '/get?id=' + id })
}
// 违禁词分页查询
export const getForbiddenWordPageApi = (params: any) => {
  return request.get({ url: baseURL + '/page', params })
}

// 获得所有团队
export const getAllTenant = () => {
  return request.get({ url: `/system/tenant/get-all-tenant` })
}

// 新增违禁词
export const createForbiddenWordApi = (data: any) => {
  return request.post({ url: baseURL + '/create', data })
}

// 根据语言查国家
export const getCountryByLanguage = (language: number) => {
  return request.post({ url: baseURL + '/get-country-by-language?language=' + language })
}



// 修改违禁词
export const updateForbiddenWordApi = (data: any) => {
  return request.put({ url: baseURL + '/update', data })
}

// 删除违禁词
export const deleteForbiddenWordApi = (id: number) => {
  return request.delete({ url: baseURL + '/delete?id=' + id })
}

// 批量添加违禁词
export const batchCreateForbiddenWordApi = (data: any) => {
  return request.post({ url: baseURL + '/batch-create', data })
}

// 批量审批违禁词
export const batchApproveForbiddenWordApi = (ids: number[]) => {
  return request.post({ url: baseURL + '/approval-batch', data: { ids } })
}

// 导出违禁词
export const exportForbiddenWordApi = (params: any) => {
  return request.download({ url: baseURL + '/export', params })
}

// 获取导入模板
export const getImportTemplateApi = () => {
  return request.download({ url: baseURL + '/get-import-template' })
}

// 导入违禁词

export const importForbiddenWordsApi = (data: any) => {
  return request.upload({ url: baseURL + '/import', data })
}
