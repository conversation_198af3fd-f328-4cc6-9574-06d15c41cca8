import request from '@/config/axios'

// 补货建议API
export type RestockSuggestRespVO = {
  id: number
  [key: string]: any
}

// 权重选项类型定义
export interface RestockWeightOptionVO {
  /** 权重配置ID */
  id?: number
  /** 库存周期 */
  inventoryCycle?: number
  /** 是否默认配置 */
  isDefault?: boolean
  /** 权重显示名称，如：3天+7天 → 60% - 40% */
  name?: string
  [property: string]: any
}

export const ReplenishmentProposalApi = {
  // 获取补货建议分页列表
  getTableList: (params: any) => {
    return request.get({ url: '/operation/restock-suggest/page', params })
  },

  // 获取已标记补货
  getIsMarked: (params: any) => {
    return request.get({ url: '/operation/restock-suggest/apply-restock', params })
  },

  // 标记需要补货
  markNeedRestock: (ids: number[], markedRestock?: any) => {
    return request.post({
      url: `/operation/restock-suggest/need-restock`,
      data: { ids: ids, markedRestock: markedRestock }
    })
  },

  // 导出
  exportFc: (params, ids?: number[]) => {
    return request.download({
      url: '/operation/restock-suggest/export-excel',
      method: 'GET',
      params: { ...params, ids: ids.join(',') }
    })
  },

  // 获取历史诊断记录
  getLogList: (params: any) => {
    return request.get({
      url: `/operation/restock-suggest/get-historical-diagnosis`,
      params
    })
  },
  // 智能补货分析
  analyzeRestock: async (data: any) => {
    return await request.post({ url: `/operation/restock-suggest/ai-restock-analyze`, data })
  },

  // 获取补货建议详情
  getRestockSuggest: (id: number) => {
    return request.get({ url: '/operation/restock-suggest/get?id=' + id })
  },

  // 获取补货建议利润数据
  getProfitData: (id: number) => {
    return request.get({ url: '/operation/restock-suggest/get-profit?id=' + id })
  },

  // 获取近七天的销量
  getSaleData: (id: number) => {
    return request.get({ url: '/operation/restock-suggest/get-sale?id=' + id })
  },
  // 获取国家列表
  getCountryList: async () => {
    return await request.get({ url: `/thirdparty/account/get-country-listr` })
  },

  // 负责人列表
  getPrincipalList: async () => {
    return await request.get({ url: `/operation/restock-suggest/principal_info_list` })
  },

  // 批量编辑备注
  batchEditRemark: async (data: any) => {
    return await request.post({ url: `/operation/restock-suggest/batch-edit-remark`, data })
  },

  // 获取权重列表
  listWeight: async () => {
    return await request.get({ url: `/operation/restock-suggest/list-weight` })
  },

  // 新增权重
  createWeight: async (data: any) => {
    return await request.post({ url: `/operation/restock-suggest/create-weight`, data })
  },

  // 更新权重
  updateWeight: async (data: any) => {
    return await request.put({ url: `/operation/restock-suggest/update-weight`, data })
  },

  // 删除权重
  deleteWeight: async (id: number) => {
    return await request.delete({ url: `/operation/restock-suggest/delete-weight`, params: { id } })
  },

  // 获取标签列表
  getGlobalTagList: async () => {
    return await request.get({ url: '/thirdparty/lx-amz-listing-global-tag/global-tag-list' })
  },

  // 获取默认规则设置
  getDefaultRuleSettings: async () => {
    return await request.get({ url: '/operation/restock-suggest-custom-weight/get' })
  },

  // 创建默认规则设置
  createDefaultRuleSettings: async (data: any) => {
    data.defaultFlag = 1
    return await request.post({ url: '/operation/restock-suggest-custom-weight/create', data })
  },

  // 更新默认规则设置
  updateDefaultRuleSettings: async (data: any) => {
    data.defaultFlag = 1
    return await request.put({ url: '/operation/restock-suggest-custom-weight/update', data })
  },

  // 批量设置自定义规则
  batchSetCustomRuleSettings: async (data: any) => {
    return await request.post({ url: '/operation/restock-suggest/batch-setting-weight', data })
  },

  // 批量申请补货
  batchRequestRestock: async (data: any) => {
    return await request.post({ url: '/operation/restock-suggest/batch-request-restock', data })
  },

  // 获取权重选项列表
  getWeightOptions: async (): Promise<RestockWeightOptionVO[]> => {
    return await request.get({ url: '/operation/restock-suggest/restock-suggest/weight-options' })
  }
}
