import request from '@/config/axios'

// 分页查询-日报数据
export const getDailyReportPage = (data: any) => {
  return request.post({ url: '/infra/profit_report/daily-report', data })
}
// 分页查询-月报数据
export const getMonthlyReportPage = (data: any) => {
  return request.post({ url: '/infra/profit_report/monthly-report', data })
}

// 日报-统计数据
export const getDailyReportStatistic = (data: any) => {
  return request.post({ url: '/infra/profit_report/daily-repor-statistic', data })
}

// 月报-统计数据
export const getMonthlyReportStatistic = (data: any) => {
  return request.post({ url: '/infra/profit_report/monthly-report-statistic', data })
}
