import request from '@/config/axios'

export interface RestockApplyRespVO {
  amazonQuantityShipping?: number
  amazonQuantityShippingPlan?: number
  analysisTime?: string
  appId?: number
  applicantId?: number
  applicantName?: string
  applyTime?: string
  approvalId?: number
  approvalName?: string
  approvalRemark?: string
  approvalStatusCode?: number
  approvalStatusName?: string
  approvalTime?: string
  asin?: string
  country?: string
  createPurchasePlan?: number // 详情接口特有
  createTime?: string
  deptId?: number
  expectedArrivalTime?: string
  fbaInventory?: number
  fnsku?: string
  id?: number
  imgUrl?: string
  inventoryCycle?: number
  isMarkedRestock?: number // 列表接口特有，详情接口为 markedRestock
  markedRestock?: number // 详情接口特有
  localName?: string
  localSku?: string
  msku?: string
  plannedPurchaseQty?: number
  predictedDailySales?: number
  predictedInventoryTarget?: number
  principalNames?: string
  principalUids?: string
  purchaseId?: number
  purchaseName?: string
  purchasePlanRespVO?: PurchasePlanRespVO // 详情接口特有
  region?: string
  remark?: string
  restockId?: number
  restockSuggestGlobalTagRespVoList?: RestockSuggestGlobalTagRespVo[]
  salesAvg14?: number
  salesAvg3?: number
  salesAvg30?: number
  salesAvg60?: number
  salesAvg7?: number
  salesAvg90?: number
  scQuantityLocalQc?: number
  scQuantityLocalShipping?: number
  scQuantityLocalValid?: number
  scQuantityOverseaShipping?: number
  scQuantityOverseaValid?: number
  scQuantityPurchaseShipping?: number
  sellerItem?: string
  sid?: number
  suggestedRestockQuantity?: number
  suggestPurchaseQty?: number
  totalInventory?: number
  userId?: number
  warehouseId?: number
  warehouseName?: string
  weightFactor?: string
  [property: string]: any
}

export interface RestockSuggestGlobalTagRespVo {
  color?: string
  globalTagId?: number
  id?: number
  operationRestockId?: number
  remark?: string
  tagName?: string
  tagType?: string
  [property: string]: any
}

export interface PurchasePlanRespVO {
  appId?: number
  asin?: string
  createTime?: string
  deptId?: number
  expectedArrivalTime?: string
  fnsku?: string
  id?: number
  imgUrl?: string
  localName?: string
  localSku?: string
  msku?: string
  plannedPurchaseQty?: number
  planSn?: string
  ppgSn?: string
  purchaseId?: number
  purchaseName?: string
  remark?: string
  restockApplyId?: number
  sellerItem?: string
  sid?: number
  suggestPurchaseQty?: number
  userId?: number
  warehouseId?: number
  warehouseName?: string
  [property: string]: any
}

export interface PageResultRestockApplyRespVO {
  list?: RestockApplyRespVO[]
  total?: number
  [property: string]: any
}

export interface RepApplyListRequest {
  applicationIds?: string
  applyTime?: string[]
  approvalIdList?: string
  approvalStatusList?: string
  approvalTime?: string[]
  asinList?: string
  countrys?: string
  fnskuList?: string
  ids?: string
  localNameList?: string
  localSkuList?: string
  mskuList?: string
  pageNo: number
  pageSize: number
  sids?: string
  [property: string]: any
}

// 查询补货申请列表
export const getRepApplyList = (params: RepApplyListRequest): Promise<IResponse<PageResultRestockApplyRespVO>> => {
  return request.get({ url: '/operation/restock-apply/page', params })
}
// 获取补货申请详情
export const getRepApplyDetail = (id: number) => {
  return request.get<RestockApplyRespVO>({ url: `/operation/restock-apply/get?id=${id}` })
}

// 获取采购员--当前账号下所有领星账号
export const getAllAccount = (id: number) => {
  return request.get({ url: `/thirdparty/account/get-all-lx-account` })
}

// 获取仓库列表
export const getWarehouseList = () => {
  return request.get({ url: `/thirdparty/lx-warehouse/warehouse-list` })
}

// 审核补货申请
export interface RepApplyApprovalReqVO {
  approvalRemark?: string // 审核备注
  approvalStatusCode?: string // 审核状态编码 (例如：'approve', 'reject')
  ids?: number[] // 补货申请数组ID编号
  operationChannelName?: string // 渠道名称
  operationChannelCode?: string // 渠道编码
  plannedPurchaseQty?: number // 计划采购量
}
export const approveRepApply = (data: RepApplyApprovalReqVO): Promise<IResponse<boolean>> => {
  return request.post({ url: '/operation/restock-apply/approval', data })
}

// 根据补货申请ID获取采购计划列表
export interface PurchasePlanListByApplyIdReqVO {
  ids: number[]
}

export interface PurchasePlanItemVO {
  appId?: number
  asin?: string
  createTime?: string
  deptId?: number
  expectedArrivalTime?: string
  fnsku?: string
  id?: number
  imgUrl?: string
  localName?: string
  localSku?: string
  msku?: string
  plannedPurchaseQty?: number
  planSn?: string
  ppgSn?: string
  purchaseId?: number
  purchaseName?: string
  remark?: string
  restockApplyId?: number
  sellerItem?: string
  sid?: number
  suggestPurchaseQty?: number
  userId?: number
  warehouseId?: number
  warehouseName?: string
  [property: string]: any
}

// 更新计划采购量
export const updatePlannedPurchaseQty = (data: { id: number; plannedPurchaseQty: number }) => {
  return request.post({ url: '/operation/restock-apply/update-planned-purchase-qty', data })
}

export const getPurchasePlanListByApplyId = (
  params: PurchasePlanListByApplyIdReqVO
): Promise<IResponse<PurchasePlanItemVO[]>> => {
  return request.post({ url: '/operation/restock-apply/get-purchase-plan-list-by-apply-id', data: params })
}

// 创建采购计划
export interface CreatePurchasePlanReqVO {
  planRemark?: string // 计划备注
  items: PurchasePlanItemVO[] // 采购计划项列表
}

export const createPurchasePlan = (data: CreatePurchasePlanReqVO): Promise<IResponse<boolean>> => {
  return request.post({ url: '/operation/restock-apply/create-purchase-plan', data })
}

// 导出
export const exportFc = (params, ids?: number[]) => {
  return request.download({
    url: '/operation/restock-apply/export-excel',
    method: 'GET',
    params: { ...params, ids: ids.join(',') }
  })
}





