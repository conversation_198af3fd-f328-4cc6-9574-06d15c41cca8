import request from '@/config/axios'

// 定义API返回的TypeScript接口，基于用户提供的信息
export interface RpaRobotClientListRespVO {
  clientIp?: string // 客户端系统ip
  createTime?: string // 创建时间
  description?: string // 机器人备注描述
  id?: number // 主键ID
  machineName?: string // 客户端系统host名称
  robotClientName?: string // 机器人名称，同accountName
  robotClientUuid?: string // 机器人uuid
  status?: string // 机器人状态
  windowsAccount?: string // 客户端系统账号
  [property: string]: any
}

export interface PageResultRpaRobotClientListRespVO {
  list?: RpaRobotClientListRespVO[] // 数据
  total?: number // 总量
  [property: string]: any
}

// 定义查询参数的接口 (虽然Vue组件中已有queryParams，但为API函数定义明确类型是个好习惯)
export interface RpaRobotClientListReqVO {
  pageNo?: number
  pageSize?: number
  uuid?: string
  name?: string
  status?: string
  // 根据实际API需求，可能还有其他过滤参数
}

import request from '@/config/axios'

export interface RpaTaskListRespVO {
  addTime?: string
  createTime?: string
  dayOfWeeks?: number
  editTime?: string
  hour?: number
  id?: number
  minimumIntervalSeconds?: number
  minute?: number
  month?: number
  nextTime?: string
  scheduleName?: string
  scheduleType?: string
  scheduleUuid?: string
  time?: string
  type?: string
}

export interface PageResultRpaTaskListRespVO {
  list?: RpaTaskListRespVO[]
  total?: number
}

export interface RpaTaskListReqVO extends PageParam {
  taskName?: string
}

// 获得RPA-任务市场列表分页
export const getRpaTaskList = (params: RpaTaskListReqVO) => {
  return request.get<PageResultRpaTaskListRespVO>({
    url: '/thirdparty/rpa-task-list/page',
    params
  })
}

// 获取RPA任务详情
export const getRpaMyTaskDetail = (id: number) => {
  return request.get<RpaMyTaskRespVO>({
    url: '/thirdparty/rpa-my-task/get',
    params: { id }
  })
}

// 立即执行
export const executeImmediately = (data: any) => {
  return request.post({
    url: '/thirdparty/rpa-task-list/executeImmediately',
    data
  })
}

// 同步最新任务
export const syncTaskApi = () => {
  return request.post({
    url: '/thirdparty/rpa-task-list/sync'
  })
}

// 定义RPA我的任务列表的请求参数VO
export interface RpaMyTaskListReqVO extends PageParam {
  taskName?: string // 任务名称，对应 scheduleName
  status?: string // 任务状态
  // 根据实际API需求，可能还有其他过滤参数
}

// 定义RPA我的任务列表的响应VO
export interface RpaMyTaskStatusCountResp {
  运行中?: number
  已完成?: number
  等待中?: number
  异常?: number
}

export interface RpaMyTaskRespVO {
  addTime?: string // 任务创建时间，格式为yyyy-mm-dd hh:MM:ss
  complateTime?: string // 完成时间
  createTime?: string // 创建时间
  cronExpress?: string // cron
  dayOfWeeks?: number // 周天
  deptId?: number // 部门ID
  editTime?: string // 任务更新时间，格式为yyyy-mm-dd hh:MM:ss
  hour?: number // 小时
  id?: number // 主键ID
  minimumIntervalSeconds?: number // 最小秒
  minute?: number // 分钟
  month?: number // 月
  nextTime?: string // 下一次触发时间 yyyy-mm-dd hh:MM:ss
  scheduleName?: string // 任务名称
  scheduleType?: string // 任务类型，参考任务类型枚举值说明
  scheduleUuid?: string // 任务UUID
  status?: string // 任务状态
  statusName?: string // 任务状态名称
  submitTime?: string // 提交时间
  time?: string // 时分秒
  type?: string // 定时器类型
  userId?: number // 用户ID
  [property: string]: any
}

export interface PageResultRpaMyTaskRespVO {
  list?: RpaMyTaskRespVO[] // 数据
  total?: number // 总量
  [property: string]: any
}

// 获得RPA-我的任务分页
export const getRpaMyTaskStatusCount = () => {
  return request.get<RpaMyTaskStatusCountResp>({
    url: '/thirdparty/rpa-my-task/status-count'
  })
}

export const getRpaMyTaskListPage = (params: RpaMyTaskListReqVO) => {
  return request.get<PageResultRpaMyTaskRespVO>({
    url: '/thirdparty/rpa-my-task/page',
    params
  })
}

// 查询机器人客户端列表分页
export const getRpaRobotClientListPage = (params: RpaRobotClientListReqVO) => {
  return request.get<PageResultRpaRobotClientListRespVO>({
    url: '/thirdparty/rpa-robot-client-list/page',
    params
  })
}

// 获得RPA-停止
export const taskStop = (id: any) => {
  return request.post({
    url: '/thirdparty/rpa-my-task/stop?id=' + id
  })
}
// /rpa-my-task/stop
