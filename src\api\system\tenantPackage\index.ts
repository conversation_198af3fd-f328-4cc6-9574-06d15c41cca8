import request from '@/config/axios'

export interface TenantPackageVO {
  id: number
  name: string
  status: number
  remark: string
  creator: string
  updater: string
  updateTime: string
  menuIds: number[]
  createTime: Date
}

// 查询团队套餐列表
export const getTenantPackagePage = (params: PageParam) => {
  return request.get({ url: '/system/tenant-package/page', params })
}

// 获得团队
export const getTenantPackage = (id: number) => {
  return request.get({ url: '/system/tenant-package/get?id=' + id })
}

// 新增团队套餐
export const createTenantPackage = (data: TenantPackageVO) => {
  return request.post({ url: '/system/tenant-package/create', data })
}

// 修改团队套餐
export const updateTenantPackage = (data: TenantPackageVO) => {
  return request.put({ url: '/system/tenant-package/update', data })
}

// 删除团队套餐
export const deleteTenantPackage = (id: number) => {
  return request.delete({ url: '/system/tenant-package/delete?id=' + id })
}
// 获取团队套餐精简信息列表
export const getTenantPackageList = () => {
  return request.get({ url: '/system/tenant-package/simple-list' })
}
