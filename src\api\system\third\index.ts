import request from '@/config/axios'

// 第三方用户信息 VO
export interface AccountVO {
  id: number // 主键ID
  uid: number // 用户ID
  realname: string // 真实姓名
  username: string // 用户名
  mobile: string // 手机号
  email: string // 邮箱
  loginNum: number // 登录次数
  lastLoginTime: string // 最后登录时间
  lastLoginIp: string // 最后登录IP
  status: number // 状态(1:正常,0:禁用)
  zid: number // 组织ID
  role: string // 角色列表
  seller: string // 销售列表
  isMaster: number // 是否主账号(1:是,0:否)
}

// 第三方平台账号 VO
export interface ThirdPartyAccountVO {
  id: number // 主键ID
  platform: string // 平台
  account: string // 账号
  authInfo: string // 授权信息
}

// 第三方平台账号 API
export const ThirdPartyAccountApi = {
  // 获取第三方平台账号列表
  getAccountList: async (params: any) => {
    return await request.get({ url: `/thirdparty/account/page`, params })
  },
  // 获取领星账号列表
  getLxUser: async () => {
    return await request.get({ url: `/thirdparty/account/get-lx-account-by-user` })
  },
  // 获取登录网址列表
  getHistoryLoginUrls: async () => {
    return await request.get({ url: `/thirdparty/app/getAllApp` })
  },
  // 获取店铺列表
  getShopList: async (uids: number[], countrys: any[]) => {
    return await request.post({
      url: `/thirdparty/account/get-seller-by-lx-account`,
      data: { uids, countrys }
    })
  },
  // 获取自身的店铺数据列表
  getSellerBySelf: async () => {
    return await request.get({ url: '/thirdparty/account/get-seller-by-self' })
  },
  // 获取平台下的账号选项
  getAccountOptions: async (params: any) => {
    return await request.get({ url: `/thirdparty/account/get-account-by-platform-code`, params })
  },

  // 绑定第三方平台账号
  bindAccount: async (data: ThirdPartyAccountVO) => {
    return await request.post({ url: `/thirdparty/account/bind`, data })
  },

  // 解绑第三方平台账号
  unbindAccount: async (ids: number[]) => {
    return await request.post({ url: `/thirdparty/account/batch-unbind`, data: { ids } })
  },

  // 批量解绑第三方平台账号
  batchUnbindAccount: async (ids: number[]) => {
    return await request.post({ url: `/thirdparty/account/batch-unbind`, data: { ids } })
  },

  // 同步第三方平台数据
  syncPlatformData: async (platformCode: string) => {
    return await request.post({
      url: `/thirdparty/account/sync-third-data`,
      params: { platformCode }
    })
  },
  // 绑定密码
  bindPassword: async (params: any) => {
    return await request.post({
      url: `/thirdparty/account/bind-password`,
      data: params
    })
  },
  // 修改登录网址
  editLoginUrl: async (params: any) => {
    return await request.post({
      url: `/thirdparty/account/edit-login-url`,
      data: params
    })
  },
  // 查询cpc竞价
  cpcQuery: async (params: any) => {
    return await request.post({
      url: `/thirdparty/cpc/query`,
      data: params
    })
  },
  // 查询cpcAd竞价
  cpcQueryAd: async (params: any) => {
    return await request.post({
      url: `/thirdparty/cpc/query-ad`,
      data: params
    })
  }
}
