import request from '@/config/axios'
import { VERSION_API_URL } from '@/config/version'

/**
 * 版本更新相关接口
 * 目前仅预留接口，暂不与后端交互
 */

// 版本信息接口返回类型
export interface VersionInfo {
  version: string
  title: string
  content: string
  buttonText: string
  forceUpdate: boolean
}

/**
 * 检查版本更新
 * @returns 版本信息
 */
export const checkVersion = () => {
  // 预留接口，暂不调用后端
  // return request.get<VersionInfo>({ url: VERSION_API_URL })

  // 模拟返回数据
  return Promise.resolve({
    version: '2.4.1',
    title: '系统更新提示',
    content: '系统已更新到最新版本，请刷新页面以获取最新功能。',
    buttonText: '立即刷新',
    forceUpdate: false
  })
}

/**
 * 记录版本更新操作
 * @param version 版本号
 * @returns 操作结果
 */
export const recordVersionUpdate = (version: string) => {
  // 预留接口，暂不调用后端
  // return request.post({ url: VERSION_API_URL + '/record', data: { version } })

  // 模拟返回数据
  return Promise.resolve({ success: true })
}
