import type { PageParam } from '@/api/types'
import request from '@/config/axios'

// 创建利润表模板
export const createProfitTableTemplate = (data: ProfitTableTemplateSaveReqVO) => {
  return request.post({ url: '/infra/profit-table-template/create', data })
}

// 更新利润表模板
export const updateProfitTableTemplate = (data: ProfitTableTemplateSaveReqVO) => {
  return request.put({ url: '/infra/profit-table-template/update', data })
}

// 删除利润表模板
export const deleteProfitTableTemplate = (id: number) => {
  return request.delete({ url: `/infra/profit-table-template/delete?id=${id}` })
}

// 获取利润表模板详情
export const getProfitTableTemplate = (id: number) => {
  return request.get({ url: `/infra/profit-table-template/get?id=${id}` })
}

// 获取利润表模板分页列表
export const getProfitTableTemplatePage = (params: ProfitTableTemplatePageReqVO) => {
  return request.get({ url: '/infra/profit-table-template/page', params })
}

// 导出利润表模板Excel
export const exportProfitTableTemplateExcel = (params: ProfitTableTemplatePageReqVO) => {
  return request.get({ url: '/infra/tools-table-template/export-excel', params })
}

// 测试公式
export const testFormula = (rule: string) => {
  return request.post({ url: '/infra/tools-table-template/test-formula', data: { rule: rule } })
}

// 测试公式结果
export const testFormulaWithValues = (data: any) => {
  return request.post({ url: '/infra/tools-table-template/test-formula-result', data })
}

/**
 * 利润表模板分页查询请求VO
 */
export interface ProfitTableTemplatePageReqVO extends PageParam {
  /** 模板名称 */
  name?: string
  /** 国家 */
  nation?: string
  /** 模板类型字典编码 */
  templateTypeCode?: string
  /** 模板类型字典名称 */
  templateTypeName?: string
  /** 关联开发表模板 */
  refId?: number
  /** 是否草稿 */
  draft?: number
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime?: string[]
  /** 用户编号 */
  userId?: number
  /** 部门编号 */
  deptId?: number
}

/**
 * 利润表模板保存请求VO
 */
export interface ProfitTableTemplateSaveReqVO {
  /** 主键ID */
  id?: number
  /** 模板名称 */
  name: string
  /** 国家 */
  nation: string
  /** 模板类型字典编码 */
  templateTypeCode: string
  /** 模板类型字典名称 */
  templateTypeName: string
  /** 关联开发表模板 */
  refId?: number
  /** 备注 */
  remark?: string
  /** 模板字段列表 */
  fields: ProfitFieldDto[]
}

/**
 * 利润字段DTO
 */
export interface ProfitFieldDto {
  /** 主键ID */
  id?: number
  /** 字段编码 */
  code?: string
  /** 名称 */
  name: string
  /** 分类字典编码 */
  categoryCode: string
  /** 分类字典名称 */
  categoryName: string
  /** 是否必填 0:否,1:是 */
  required?: number
  /** 是否编辑 0否1是 */
  edit?: number
  /** 是否有单位 0否1是 */
  needUnit?: number
  /** 单位 */
  unit?: string
  /** 参数类型字典编码 */
  paramTypeCode?: string
  /** 参数类型字典名称 */
  paramTypeName?: string
  /** 默认值 */
  defaultValue?: string
  /** 模板ID */
  templateId?: number
  /** 备注 */
  remark?: string
  /** 公式 */
  rule?: string
}

/**
 * 利润表分页查询请求VO
 */
export interface ProfitTableDataPageReqVO extends PageParam {
  tableHeadId?: number
  fieldId?: number
  auditStatus?: string
  productName?: string
  production?: string
  creatorNameList?: string
  createTime?: string[]
  approvalTime?: string[]
  templateType?: number
  listingCopyStatus?: string
  shelfStatus?: string
  personsInCharge?: string
  participants?: string
  countryList?: string
  shopNameList?: string
  approvalNameList?: string
  sortingFields?: { field: string; order: string }[]
}

/**
 * 利润表数据响应VO
 */
export interface ProfitTableDataRespVO {
  rowId: number
  tableHeadId: number
  resultMap: Record<string, any>
  resultNameMap: Record<string, string>
}

// 根据模板ID获取表头ID
export const getTableHeadByTemplateId = (templateId: number) => {
  return request.get({
    url: `/infra/profit-table-template/get-table-head-by-templateId?templateId=${templateId}`
  })
}

// 获取利润表分页列表
export const getProfitPage = (params: ProfitTableDataPageReqVO) => {
  return request.get({ url: '/infra/profit-table-data/page', params })
}

/**
 * 利润表模板响应VO
 */
export interface ProfitTableTemplateRespVO {
  /** 主键ID */
  id: number
  /** 模板名称 */
  name: string
  /** 国家 */
  nation: string
  /** 模板类型字典编码 */
  templateTypeCode: string
  /** 模板类型字典名称 */
  templateTypeName: string
  /** 关联开发表模板 */
  refId?: number
  /** 是否草稿 */
  draft?: number
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime: string
  /** 用户编号 */
  userId: number
  /** 部门编号 */
  deptId: number
}

/**
 * 通用结果响应
 */
export interface CommonResult<T = any> {
  /** 错误码 */
  code: number
  /** 返回数据 */
  data: T
  /** 错误提示，用户可阅读 */
  msg: string
}

/**
 * 分页结果响应
 */
export interface PageResult<T = any> {
  /** 数据列表 */
  list: T[]
  /** 总数量 */
  total: number
}

// 导出API对象
export const ProfitTableTemplateApi = {
  create: createProfitTableTemplate,
  update: updateProfitTableTemplate,
  delete: deleteProfitTableTemplate,
  get: getProfitTableTemplate,
  getPage: getProfitTableTemplatePage,
  getProfitPage,
  exportExcel: exportProfitTableTemplateExcel,
  testFormula: testFormula,
  testFormulaWithValues: testFormulaWithValues
}

// 默认导出
export default ProfitTableTemplateApi
