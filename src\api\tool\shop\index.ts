import request from '@/config/axios'

export interface ShopVO {
  id: number
  shopName: string
  accountName: string
  siteCode: string
  countryName: string
  status: string
  shopType: string
  createTime: Date
}

export interface ShopPageReqVO extends PageParam {
  shopName?: string
  shopType?: string
}

export interface ShopSaveReqVO {
  id?: number
  shopName?: string
  accountName?: string
  siteCode?: string
  countryName?: string
  status?: string
  shopType?: string
}

// 查询店铺分页
export const getShopPage = (params: ShopPageReqVO) => {
  return request.get({ url: '/tool/shop/page', params })
}

// 查询店铺详情
export const getShop = (id: number) => {
  return request.get({ url: '/tool/shop/get?id=' + id })
}

// 新增店铺
export const createShop = (data: ShopSaveReqVO) => {
  return request.post({ url: '/tool/shop/create', data })
}

// 修改店铺
export const updateShop = (data: ShopSaveReqVO) => {
  return request.put({ url: '/tool/shop/update', data })
}

// 删除店铺
export const deleteShop = (id: number) => {
  return request.delete({ url: '/tool/shop/delete?id=' + id })
}

// 导出店铺 Excel
export const exportShop = (params: ShopPageReqVO) => {
  return request.download({ url: '/tool/shop/export-excel', params })
}