import request from '@/config/axios'

// 更新汇率计算
export const refreshRate = (id: number) => {
  return request.post({ url: '/infra/tools-table-data/refreshRate', data: { id } })
}
import { AnyColumns } from 'element-plus/es/components/table-v2/src/types'

// 根据表头ID获取列表数据
export const getPage = (params: any) => {
  return request.get({ url: '/infra/tools-table-data/page', params })
}

// 修改数据
export const update = (data: any) => {
  return request.post({ url: '/infra/tools-table-data/update-data', data })
}

// 计算数据
export const calculate = (data: any) => {
  return request.post({ url: '/infra/tools-table-data/calculate', data })
}

// 新增数据
export const addData = (data: any) => {
  return request.post({ url: '/infra/tools-table-data/add-data', data })
}

/**
 * 删除数据
 * @param data 删除请求参数 ToolsTableDataRowReqVo
 */
export const deleteData = (data: {
  /** 行ID数组 */
  rowIds?: number[]
  /** 模板ID */
  templateId?: number
  [property: string]: any
}) => {
  return request.delete({ url: '/infra/tools-table-data/delete', data })
}

/**
 * 保存数据
 * @param data 保存请求参数
 */
export const save = (data: any) => {
  return request.post({ url: '/infra/tools-table-data/save', data })
}

/**
 * 提交审核
 * @param data 提交审核请求参数 ToolsTableDataRowReqVo
 */
export const submitApproval = (data: {
  /** 行ID数组 */
  rowIds?: number[]
  /** 模板ID */
  templateId?: number
  [property: string]: any
}) => {
  return request.post({ url: '/infra/tools-table-data/submit-approval', data })
}

/**
 * 投产
 * @param data 投产请求参数 ToolsTableDataRowReqVo
 */
export const submitProd = (data: AnyColumns) => {
  return request.post({ url: '/infra/tools-table-data/prod', data })
}

/**
 * 分配参与人
 * @param data 分配参与人请求参
 */
export const assignParticipants = (data: any) => {
  return request.post({ url: '/infra/tools-table-data/assign-participants', data })
}

// 导出 Excel
export interface ExportReqVO {
  pageNo?: string
  pageSize?: string
  tableHeadId?: string
  templateId?: string
  templateType?: string
}
export const exportExcel = async (params: ExportReqVO) => {
  // return request.download({
  //   url: '/infra/tools-table-data/export-excel',
  //   params
  // })
  return await request.download({ url: `/infra/tools-table-data/export-excel`, params })

}

// 获取导入模板
export const getImportTemplate = async (templateId: string) => {
  return await request.download({ 
    url: `/infra/tools-table-data/get-import-template`, 
    params: { templateId } 
  })
}
