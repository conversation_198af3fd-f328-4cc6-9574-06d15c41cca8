import request from '@/config/axios'

// 创建工具表格模板
export const create = (data: ToolsTableTemplateSaveReqVO) => {
  return request.post({ url: '/infra/tools-table-template/create', data })
}

// 更新工具表格模板
export const update = (data: ToolsTableTemplateSaveReqVO) => {
  return request.put({ url: '/infra/tools-table-template/update', data })
}

// 获取工具表格模板详情
export const getDetail = (id: string | number) => {
  return request.get({ url: `/infra/tools-table-template/get?id=${id}` })
}

// 获取工具表格模板分页
export const getPage = (params: PageParam) => {
  return request.get({ url: '/infra/tools-table-template/page', params })
}

// 根据模板类型获取模板列表
export const getTemplateList = (params: PageParam) => {
  return request.get({ url: '/infra/tools-table-template/get-template-list', params })
}

// 根据开发表模板ID获取关联的投产表模板
export const getProdByDevList = (params: PageParam) => {
  return request.get({ url: '/infra/tools-table-template/get-prod-by-dev', params })
}

// 置顶
export const top = (id: number) => {
  return request.post({ url: '/infra/tools-table-template/top?id=' + id })
}

// 取消置顶
export const cancelTop = (id: number) => {
  return request.post({ url: '/infra/tools-table-template/cancel-top?id=' + id })
}

// 批量置顶
export const batchTop = (ids: number[]) => {
  return request.post({ url: '/infra/tools-table-template/batch-top', data: ids })
}

// 批量取消置顶
export const batchCancelTop = (ids: number[]) => {
  return request.post({ url: '/infra/tools-table-template/batch-cancel-top', data: ids })
}

// 测试公式
export const testFormulaApi = (rule: string) => {
  return request.post({ url: '/infra/tools-table-template/test-formula', data: { rule: rule } })
}

// 根据模板ID获取表头
export const getTableHeadByTemplateId = (templateId: string | number, headTemplateId?: string | number) => {
  return request.get({ url: `/infra/tools-table-template/get-table-head-by-templateId?templateId=${templateId}` + `&headTemplateId=${headTemplateId || ''}` })
}

// 删除工具模板配置
export const deleteTemplate = (id: any) => {
  return request.delete({ url: `/infra/tools-table-template/delete?id=` + id })
}

// 保存表头配置
export const saveHeadSetting = (data: ToolsTableHeadSettingSaveReqVO[]) => {
  return request.post({ url: '/infra/tools-table-template/save-head-setting', data })
}

// 测试公式结果
export const testFormulaWithValues = (data: ToolsTableHeadSettingSaveReqVO[]) => {
  return request.post({ url: '/infra/tools-table-template/test-formula-result', data })
}

// 分享
export const shareTemplate = (data: any) => {
  return request.post({ url: '/infra/tools-table-template/share', data })
}

/** 
 * 工具表格模板保存请求VO 
 */
export interface ToolsTableTemplateSaveReqVO {
  /** 
   * 模板字段列表 
   */
  fields: ToolsFieldDto[];
  /** 
   * 主键ID 
   */
  id?: number;
  /** 
   * 模板名称 
   */
  name: string;
  /** 
   * 国家 
   */
  nation: string;
  /** 
   * 关联开发表模板 
   */
  refId?: number;
  /** 
   * 备注 
   */
  remark?: string;
  /** 
   * 模板类型字典编码 
   */
  templateTypeCode: string;
  /** 
   * 模板类型字典名称 
   */
  templateTypeName: string;
}

/** 
 * 模板字段dto 
 */
export interface ToolsFieldDto {
  /** 
   * 分类 
   */
  category: string;
  /** 
   * 字段编码 
   */
  code?: string;
  /** 
   * 是否必填 
   */
  edit?: number;
  /** 
   * 模板字段公式列表 
   */
  formulas?: ToolsFormulaDto[];
  /** 
   * 主键ID 
   */
  id?: number;
  /** 
   * 名称 
   */
  name: string;
  /** 
   * 是否有单位 
   */
  needUnit?: number;
  /** 
   * 参数类型 
   */
  paramType?: string;
  /** 
   * 备注 
   */
  remark?: string;
  /** 
   * 是否必填 
   */
  required?: number;
  /** 
   * 模板ID 
   */
  templateId?: number;
  /** 
   * 单位 
   */
  unit?: string;
}

/** 
 * 模板工具公式DTO 
 */
export interface ToolsFormulaDto {
  /** 
   * 字段ID 
   */
  fieldId?: number;
  /** 
   * 主键ID 
   */
  id?: number;
  /** 
   * 备注 
   */
  remark?: string;
  /** 
   * 公式 
   */
  rule?: string;
  /** 
   * 模板ID 
   */
  templateId?: number;
}

/**
 * 表头配置保存请求VO
 */
export interface ToolsTableHeadSettingSaveReqVO {
  /**
   * 表头id
   */
  fieldId: number;
  /**
   * 固定：left/right
   */
  fixed?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 排序
   */
  sort?: number;
  /**
   * 表模板id
   */
  templateId: number;
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 是否显示
   */
  visible?: boolean;
  [property: string]: any;
}

export const ToolsTableTemplateApi = {
  create,
  update,
  getDetail,
  testFormulaApi,
  deleteTemplate,
  saveHeadSetting,
  testFormulaWithValues
}
