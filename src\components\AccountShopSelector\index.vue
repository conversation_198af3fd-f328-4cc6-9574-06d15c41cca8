<template>
  <div class="account-shop-selector" :class="{ 'flex-col': isCol }">
    <!-- 领新账号 -->
    <el-form-item v-if="showAccount" :label="accountLabel" prop="lingxingAccount">
      <RcInputSelect
        style="min-width: 170px"
        v-model="selectedAccount"
        :options="accounts"
        @change="handleAccountChange"
      />
    </el-form-item>
    <!-- 国家 -->
    <el-form-item :label="countryLabel" prop="country">
      <RcInputSelect
        style="min-width: 170px"
        v-model="selectedCountry"
        :options="countries"
        isTree
        @change="handleCountryChange"
      />
      <!-- checkStrictly -->
    </el-form-item>
    <!-- 店铺 -->
    <el-form-item :label="shopLabel" prop="shopName">
      <RcInputSelect
        v-model="selectedShop"
        style="min-width: 240px"
        :options="shops"
        @change="handleShopChange"
      />
    </el-form-item>
  </div>
</template>

<script lang="ts" setup>
import { ThirdPartyAccountApi } from '@/api/system/third'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { handleTree } from '@/utils/tree'
import { computed, defineEmits, defineOptions, defineProps, onMounted, ref } from 'vue'

defineOptions({ name: 'AccountShopSelector' })

const props = defineProps({
  instanceId: {
    type: String,
    default: () => `account-shop-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },
  // 是否显示领新账号
  showAccount: {
    type: Boolean,
    default: true
  },
  // 是否纵向排列
  isCol: {
    type: Boolean,
    default: false
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  // 账号相关
  accountValues: {
    type: Array,
    default: () => []
  },
  accountNames: {
    type: Array,
    default: () => []
  },
  accountLabel: {
    type: String,
    default: '领星账号'
  },
  accountPlaceholder: {
    type: String,
    default: '请选择账号'
  },
  // 店铺相关
  shopValues: {
    type: Array,
    default: () => []
  },
  shopNames: {
    type: Array,
    default: () => []
  },
  shopLabel: {
    type: String,
    default: '店铺名称'
  },
  shopPlaceholder: {
    type: String,
    default: '请选择店铺'
  },
  // 国家相关
  countryLabel: {
    type: String,
    default: '国家'
  },
  countryPlaceholder: {
    type: String,
    default: '请选择国家'
  },
  countryValues: {
    type: Array,
    default: () => []
  },
  countryNames: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  // 领星账号相关
  'update:accountValues',
  'update:accountNames',
  'account-change',
  // 店铺相关
  'update:shopValues',
  'update:shopNames',
  'shop-change',
  // 国家相关
  'update:countryValues',
  'update:countryNames',
  'country-change'
])

// 账号数据
const accounts = ref([])
const selectedAccount = computed({
  get: () => props.accountValues,
  set: (val) => {
    // 确保val是数组
    const validVal = Array.isArray(val) ? val : []
    const { values, names } = getSelectedData(validVal, accounts.value)
    emit('update:accountValues', values)
    emit('update:accountNames', names)
    // 不在这里触发account-change，而是在handleAccountChange中触发
  }
})

// 店铺数据
const shops = ref([])
const selectedShop = computed({
  get: () => props.shopValues,
  set: (val) => {
    // 确保val是数组
    const validVal = Array.isArray(val) ? val : []
    const { values, names } = getSelectedData(validVal, shops.value)
    emit('update:shopValues', values)
    emit('update:shopNames', names)
    // 不在这里触发shop-change，而是在handleShopChange中触发
  }
})

// 国家数据
const countries = ref(
  handleTree(getIntDictOptions(DICT_TYPE.INFRA_TREE_COUNTRY), 'value', 'parentId')
)
const selectedCountry = computed({
  get: () => props.countryValues,
  set: (val) => {
    const validVal = Array.isArray(val) ? val : []
    const { values, names } = getSelectedData(validVal, countries.value)
    emit('update:countryValues', values)
    emit('update:countryNames', names)
  }
})

// 获取选中数据的通用方法
const getSelectedData = (selectedValues, sourceList) => {
  // 处理空值
  if (!Array.isArray(selectedValues)) {
    selectedValues = []
  }

  // 如果包含"全部"则只保留"全部"
  if (selectedValues.includes('all')) {
    return { values: [], names: [] }
  }

  // 过滤无效值
  const filteredValues = selectedValues.filter((v) => v !== '')
  const names = filteredValues.map(
    (value) => sourceList.find((item) => item.value === value)?.label || ''
  )

  return {
    values: filteredValues,
    names
  }
}

// 获取领星账号列表
const getAccounts = async () => {
  try {
    const res = await ThirdPartyAccountApi.getLxUser()
    accounts.value =
      res.map((item) => ({
        value: item.uid,
        label: item.username + '(' + item.realname + ')'
      })) || []

    // The watcher will handle fetching shops based on props.
    // This function only needs to handle the "default selection" logic if isDefault is true.
    if (props.isDefault && res.length === 1) {
      const defaultUid = [res[0].uid]
      selectedAccount.value = defaultUid // This triggers the watcher
      emit('account-change', defaultUid, [res[0]?.username || ''])
    }
  } catch (error) {
    console.error('获取领星账号失败:', error)
  }
}

// 根据账号获取店铺列表
const getShopsByAccount = async (uids, countrys?) => {
  shops.value = [] // Clear options before fetching
  try {
    const res = await ThirdPartyAccountApi.getShopList(uids, countrys)
    const newShops =
      res.map((item) => ({
        value: item.sid,
        label: item.sellerItem
      })) || []
    shops.value = newShops

    // After fetching new shops, validate the current selection
    const currentShopValues = props.shopValues || []
    const validShopValues = currentShopValues.filter((sid) =>
      newShops.some((shop) => shop.value === sid)
    )

    // If the selection has changed, emit an update
    if (validShopValues.length !== currentShopValues.length) {
      const { values, names } = getSelectedData(validShopValues, newShops)
      emit('update:shopValues', values)
      emit('update:shopNames', names)
      emit('shop-change', values, names)
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
    shops.value = []
    // Also clear selection if fetch fails
    emit('update:shopValues', [])
    emit('update:shopNames', [])
  }
}

// 账号变更处理
const handleAccountChange = (uids) => {
  // The watcher handles all the logic. This just emits the event for the parent.
  const names = uids.map((uid) => accounts.value.find((item) => item.value === uid)?.label || '')
  emit('account-change', uids, names)
}

// 店铺变更处理
const handleShopChange = (sids) => {
  // 获取店铺名称
  const names = sids.map((sid) => shops.value.find((item) => item.value === sid)?.label || '')
  emit('update:shopValues', sids)
  emit('update:shopNames', names)
  emit('shop-change', sids, names)
}

// 处理国家变更
const handleCountryChange = (codes) => {
  // v-model 会更新值，watch 会获取店铺列表。
  // 此处理器仅用于为父组件触发 'country-change' 事件。
  if (codes.includes('all')) {
    emit('country-change', ['all'], [])
    return
  }

  const filteredCodes = codes.filter((v) => v !== '')
  const names = filteredCodes.map(
    (code) =>
      getIntDictOptions(DICT_TYPE.INFRA_TREE_COUNTRY).find((item) => item.value === code)?.label ||
      ''
  )
  emit('country-change', names, names)
}

// 重置选择
const resetSelection = (type) => {
  switch (type) {
    case 'account':
      emit('update:accountValues', [])
      emit('update:accountNames', [])
      break
    case 'shop':
      emit('update:shopValues', [])
      emit('update:shopNames', [])
      break
    case 'country':
      emit('update:countryValues', [])
      emit('update:countryNames', [])
      break
  }
}

// 监听账号和国家变化，更新店铺列表
watch(
  [() => props.accountValues, () => props.countryValues],
  ([newAccountValues, newCountryValues]) => {
    const uids = newAccountValues || []
    const countryIds = newCountryValues || []

    const hasAccount = uids.length > 0
    const hasCountry = countryIds.length > 0

    // When either account or country is selected, fetch shops
    // if (hasAccount || hasCountry) {
    const countryNames = countryIds
      .map((code) => {
        const country = getIntDictOptions(DICT_TYPE.INFRA_TREE_COUNTRY).find(
          (item) => item.value === code
        )
        return country ? country.label : null
      })
      .filter((name): name is string => name !== null)

    getShopsByAccount(uids, countryNames)
    // } else {
    //   // Clear shops if both account and country are empty
    //   shops.value = []
    //   resetSelection('shop')
    // }
  },
  {
    immediate: true, // 立即执行，以便在组件创建时处理初始值
    deep: true // 深度监听，确保数组内部变化也能被捕获
  }
)

// 初始化时获取所有账号
onMounted(() => {
  getAccounts()
})

// 暴露方法
defineExpose({
  getAccounts,
  getShopsByAccount,
  resetSelection,
  shops
})
</script>

<style scoped>
.account-shop-selector {
  display: flex;
  gap: 16px;
  padding-bottom: 10px;
}

.el-form-item {
  padding-bottom: 0 !important;
}
</style>
