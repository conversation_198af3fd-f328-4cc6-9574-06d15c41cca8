<template>
  <el-dialog
    v-model="dialogVisible"
    :title="noticeInfo.title || '系统公告'"
    width="530px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    append-to-body
  >
    <div class="announcement-content">
      <el-alert
        v-if="noticeInfo.loginAgain"
        title="重要通知：此消息处理后需要您重新登录系统"
        type="warning"
        show-icon
        :closable="false"
        class="mb-15px"
      />
      <LoginCarousel
        v-if="noticeInfo.imgUrlList && noticeInfo.imgUrlList.length > 0"
        :key="carouselKey"
        :height="500"
        :width="500"
        :interval="6000"
        :customImages="noticeInfo.imgUrlList"
        image-class="carousel-image"
        @image-click="handleImageClick"
      />
      <div class="mt-20px" v-if="noticeInfo.content" v-html="noticeInfo.content"></div>
    </div>

    <div class="announcement-footer" v-if="noticeInfo.isShowUpdateBtn">
      <el-button
        type="primary"
        @click="openFeedbackForm"
        class="feedback-button"
        size="large"
        :style="{ fontWeight: 'bold' }"
      >
        问题与需求收集表
      </el-button>
      <p class="reward-text">优质建议将获丰厚奖励！！</p>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import * as NoticeApi from '@/api/system/notice'
import LoginCarousel from '@/components/LoginCarousel/index.vue'
import { getRefreshToken } from '@/utils/auth'
import { useWebSocket } from '@vueuse/core'
import { defineExpose, onMounted, ref, watch } from 'vue'

import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()
const { push, replace, currentRoute } = useRouter()

const dialogVisible = ref(false)
const noticeInfo = ref<any>({})
const carouselKey = ref(0)

// 暴露方法给外部组件调用
const openNoticeDetail = async (noticeId: string) => {
  try {
    const res = await NoticeApi.getNotice(noticeId)
    if (res) {
      noticeInfo.value = {
        ...res,
        title: res.title,
        content: res.content,
        imgUrlList: res.imgUrl ? JSON.parse(res.imgUrl) : [],
        loginAgain: res.loginAgain,
        id: res.id,
        isShowUpdateBtn: res.isShowUpdateBtn
      }
      // 如果有轮播图，则强制刷新LoginCarousel组件
      if (noticeInfo.value.imgUrlList && noticeInfo.value.imgUrlList.length > 0) {
        carouselKey.value++
      }
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
  }
}

defineExpose({
  openNoticeDetail
})

// WebSocket 相关
const server = ref(
  (import.meta.env.VITE_BASE_URL + '/infra/ws').replace('http', 'ws') +
    '?token=' +
    getRefreshToken()
)

const { status, data, send, close, open } = useWebSocket(server.value, {
  autoReconnect: true,
  heartbeat: true
})

const handleClose = async () => {
  let requestData = {
    ids: [noticeInfo.value.id],
    logout: noticeInfo.value.loginAgain
  }
  await NoticeApi.batchConfirm(requestData)
  // 增加判断条件：如果通知状态存在且不为0 (未读)，则即使 loginAgain 为 true 也不强制登出
  if (
    noticeInfo.value.loginAgain &&
    (noticeInfo.value.status === undefined || noticeInfo.value.status === 0)
  ) {
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    replace('/login?redirect=/index')
  } else {
    dialogVisible.value = false
  }
}

const handleImageClick = (imageData: any) => {
  // 点击图片时打开对应的公告详情
  if (imageData && imageData.linkUrl) {
    window.open(imageData.linkUrl, '_blank')
  }
}

const openFeedbackForm = () => {
  window.open('https://hxpjzk2jv4k.feishu.cn/share/base/form/shrcn4bfp0QxNrHiwySauBMXmid', '_blank')
}

// 监听WebSocket消息
watch(data, (newData) => {
  if (!newData) {
    return
  }
  try {
    if (newData === 'pong') {
      return
    }
    const jsonMessage = JSON.parse(newData as string)
    const type = jsonMessage.type
    const content = JSON.parse(jsonMessage.content)
    console.log('Announcement WebSocket消息:', jsonMessage)
    console.log('Announcement 内容:', content)

    // 只处理type为2的公告消息
    if (type === 'notice-push' && content.type == 2) {
      // 检查当前路由，如果在登录页面则不显示弹窗
      if (currentRoute.value.path === '/login') {
        return
      }
      noticeInfo.value = {
        ...content,
        title: content.title,
        content: content.content,
        imgUrlList: content.imgUrl ? JSON.parse(content.imgUrl) : [],
        loginAgain: content.loginAgain,
        id: content.id,
        isShowUpdateBtn: content.isShowUpdateBtn
      }
      // 如果有轮播图，则强制刷新LoginCarousel组件
      if (noticeInfo.value.imgUrlList && noticeInfo.value.imgUrlList.length > 0) {
        carouselKey.value++
      }
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('处理Announcement WebSocket消息失败:', error, newData)
  }
})

onMounted(async () => {
  const { query } = currentRoute.value
  const hasShown = sessionStorage.getItem('announcementShown')
  // 登录弹窗公告 - 只在非登录页面且未显示过时弹出
  if (!hasShown && currentRoute.value.path !== '/login') {
    try {
      const res = await NoticeApi.getNoticePage({ pageNo: 1, pageSize: 1, type: 2 })
      if (res && res.list && res.list.length > 0) {
        openNoticeDetail(res.list[0].id)
        sessionStorage.setItem('announcementShown', 'true')
      }
    } catch (error) {
      console.error('获取最新公告失败:', error)
    }
  }
  // WebSocket会自动连接
})
</script>

<style scoped>
.el-carousel {
  width: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.announcement-footer {
  display: flex;
  padding: 0 15px;
  padding-bottom: 37px;
  margin-top: 30px;
  text-align: center;
  flex-direction: column;
  align-items: center;
}

.announcement-title {
  margin-bottom: 15px;
  font-size: 24px;
  color: #333;
}

.announcement-text {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6;
  color: #333;
  text-align: left;
  text-indent: 2em; /* 1em ≈ 当前字体大小，所以 2em = 两个字符宽度 */
}

.feedback-button {
  padding: 0 25px;
  margin-bottom: 15px;
}

.reward-text {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}
</style>
