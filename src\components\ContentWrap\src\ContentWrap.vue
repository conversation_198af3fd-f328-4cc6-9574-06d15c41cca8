<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

defineOptions({ name: 'ContentWrap' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('content-wrap')

defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  bodyStyle: propTypes.object.def({ padding: '10px' })
})
</script>

<template>
  <ElCard
    :body-style="bodyStyle"
    :class="[prefixCls, 'mb-15px']"
    shadow="never"
  >
    <template
      v-if="title"
      #header
    >
      <div class="flex items-center">
        <span
          class="text-16px font-700"
          style="font-size: 16px !important;"
        >{{ title }}</span>
        <ElTooltip
          v-if="message"
          effect="dark"
          placement="right"
        >
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <Icon
            :size="14"
            class="ml-5px"
            icon="ep:question-filled"
          />
        </ElTooltip>
        <div class="flex flex-grow pl-20px">
          <slot name="header"></slot>
        </div>
      </div>
    </template>
    <slot></slot>
  </ElCard>
</template>
