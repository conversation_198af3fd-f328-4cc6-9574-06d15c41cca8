<template>
  <div>
    <!-- 头像显示部分 -->
    <div
      class="user-info-head"
      @click="openModal"
    >
      <el-avatar
        v-if="sourceValue"
        :src="sourceValue"
        alt="avatar"
        class="img-circle img-lg"
      />
      <el-avatar
        v-if="!sourceValue"
        :src="avatar"
        alt="avatar"
        class="img-circle img-lg"
      />
      <el-button
        v-if="showBtn"
        :class="`${avatarPrefixCls}-upload-btn`"
        @click.stop="openModal"
      >
        {{ btnText ? btnText : t('cropper.selectImage') }}
      </el-button>
    </div>

    <!-- 裁剪弹窗部分 -->
    <Dialog
      v-model="dialogVisible"
      :canFullscreen="false"
      :title="t('cropper.modalTitle')"
      maxHeight="380px"
      width="800px"
      :close-on-click-modal="false"
    >
      <div :class="modalPrefixCls">
        <div :class="`${modalPrefixCls}-left`">
          <div :class="`${modalPrefixCls}-cropper`">
            <CropperImage
              v-if="src"
              :circled="circled"
              :src="src"
              height="300px"
              @cropend="handleCropend"
              @ready="handleReady"
            />
          </div>

          <div :class="`${modalPrefixCls}-toolbar`">
            <el-upload
              :beforeUpload="handleBeforeUpload"
              :fileList="[]"
              accept="image/*"
            >
              <el-tooltip
                :content="t('cropper.selectImage')"
                placement="bottom"
              >
                <XButton
                  preIcon="ant-design:upload-outlined"
                  type="primary"
                />
              </el-tooltip>
            </el-upload>
            <el-space>
              <el-tooltip
                :content="t('cropper.btn_reset')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="ant-design:reload-outlined"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('reset')"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_rotate_left')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="ant-design:rotate-left-outlined"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('rotate', -45)"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_rotate_right')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="ant-design:rotate-right-outlined"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('rotate', 45)"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_scale_x')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="vaadin:arrows-long-h"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('scaleX')"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_scale_y')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="vaadin:arrows-long-v"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('scaleY')"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_zoom_in')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="ant-design:zoom-in-outlined"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('zoom', 0.1)"
                />
              </el-tooltip>
              <el-tooltip
                :content="t('cropper.btn_zoom_out')"
                placement="bottom"
              >
                <XButton
                  :disabled="!src"
                  preIcon="ant-design:zoom-out-outlined"
                  size="small"
                  type="primary"
                  @click="handlerToolbar('zoom', -0.1)"
                />
              </el-tooltip>
            </el-space>
          </div>
        </div>
        <div :class="`${modalPrefixCls}-right`">
          <div :class="`${modalPrefixCls}-preview`">
            <img
              v-if="previewSource"
              :alt="t('cropper.preview')"
              :src="previewSource"
            />
          </div>
          <template v-if="previewSource">
            <div :class="`${modalPrefixCls}-group`">
              <el-avatar
                :src="previewSource"
                size="large"
              />
              <el-avatar
                :size="48"
                :src="previewSource"
              />
              <el-avatar
                :size="64"
                :src="previewSource"
              />
              <el-avatar
                :size="80"
                :src="previewSource"
              />
            </div>
          </template>
        </div>
      </div>
      <template #footer>
        <el-button
          type="primary"
          @click="handleOk"
        >{{ t('cropper.okText') }}</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { useDesign } from '@/hooks/web/useDesign'
import { dataURLtoBlob } from '@/utils/filt'
import { useI18n } from 'vue-i18n'
import type { CropendResult, Cropper } from './types'
import { propTypes } from '@/utils/propTypes'
import { CropperImage } from '@/components/Cropper'
import avatar from '@/assets/imgs/avatar.gif'

defineOptions({ name: 'CropperUser' })

const props = defineProps({
  width: propTypes.string.def('200px'),
  value: propTypes.string.def(''),
  showBtn: propTypes.bool.def(true),
  btnText: propTypes.string.def(''),
  circled: propTypes.bool.def(true)
})

const emit = defineEmits(['update:value', 'change'])
const { t } = useI18n()
const { getPrefixCls } = useDesign()
const message = useMessage()

// 前缀类名
const avatarPrefixCls = getPrefixCls('cropper-avatar')
const modalPrefixCls = getPrefixCls('cropper-am')

// 状态变量
const sourceValue = ref(props.value)
const dialogVisible = ref(false)
const src = ref('')
const previewSource = ref('')
const cropper = ref<Cropper>()
let filename = ''
let scaleX = 1
let scaleY = 1

// 监听值变化
watchEffect(() => {
  sourceValue.value = props.value
})

watch(
  () => sourceValue.value,
  (v: string) => {
    emit('update:value', v)
  }
)

// 打开模态框
function openModal() {
  // 4.17需求，不给改头像给看
  dialogVisible.value = true
  // 如果已有头像，则设置为当前头像
  if (sourceValue.value) {
    src.value = sourceValue.value
  }
}

// 关闭模态框
function closeModal() {
  dialogVisible.value = false
}

// 文件上传前处理
function handleBeforeUpload(file: File) {
  const reader = new FileReader()
  reader.readAsDataURL(file)
  src.value = ''
  previewSource.value = ''
  reader.onload = function (e) {
    src.value = (e.target?.result as string) ?? ''
    filename = file.name
  }
  return false
}

// 裁剪结束处理
function handleCropend({ imgBase64 }: CropendResult) {
  previewSource.value = imgBase64
}

// 裁剪器就绪
function handleReady(cropperInstance: Cropper) {
  cropper.value = cropperInstance
}

// 工具栏操作
function handlerToolbar(event: string, arg?: number) {
  if (event === 'scaleX') {
    scaleX = arg = scaleX === -1 ? 1 : -1
  }
  if (event === 'scaleY') {
    scaleY = arg = scaleY === -1 ? 1 : -1
  }
  cropper?.value?.[event]?.(arg)
}

// 确认裁剪
async function handleOk() {
  if (!previewSource.value) {
    message.warning('请先选择并裁剪图片')
    return
  }

  const blob = dataURLtoBlob(previewSource.value)
  sourceValue.value = previewSource.value
  emit('change', { source: previewSource.value, data: blob, filename: filename })
  message.success(t('cropper.uploadSuccess'))
  dialogVisible.value = false
}

// 暴露方法
defineExpose({
  openModal,
  closeModal
})
</script>

<style lang="scss">
$avatar-prefix-cls: #{$namespace}--cropper-avatar;
$modal-prefix-cls: #{$namespace}-cropper-am;

// 头像样式
.#{$avatar-prefix-cls} {
  display: inline-block;
  text-align: center;

  &-image-wrapper {
    overflow: hidden;
    cursor: pointer;
    border: 1px solid;
    border-radius: 50%;

    img {
      width: 100%;
    }
  }

  &-image-mask {
    position: absolute;
    width: inherit;
    height: inherit;
    cursor: pointer;
    background: rgb(0 0 0 / 40%);
    border: inherit;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.4s;

    ::v-deep(svg) {
      margin: auto;
    }
  }

  &-image-mask:hover {
    opacity: 40;
  }

  &-upload-btn {
    margin: 10px auto;
  }
}

.user-info-head {
  position: relative;
  display: inline-block;
}

.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}
// 4.17需求，不给改头像给看
.user-info-head:hover::after {
  position: absolute;
  inset: 0;
  font-size: 24px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  line-height: 110px;
  color: #eee;
  cursor: pointer;
  background: rgb(0 0 0 / 50%);
  border-radius: 50%;
  content: '+';
}

// 模态框样式
.#{$modal-prefix-cls} {
  display: flex;

  &-left,
  &-right {
    height: 340px;
  }

  &-left {
    width: 55%;
  }

  &-right {
    width: 45%;
  }

  &-cropper {
    height: 300px;
    background: #eee;
    background-image: linear-gradient(
        45deg,
        rgb(0 0 0 / 25%) 25%,
        transparent 0,
        transparent 75%,
        rgb(0 0 0 / 25%) 0
      ),
      linear-gradient(
        45deg,
        rgb(0 0 0 / 25%) 25%,
        transparent 0,
        transparent 75%,
        rgb(0 0 0 / 25%) 0
      );
    background-position: 0 0, 12px 12px;
    background-size: 24px 24px;
  }

  &-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

  &-preview {
    width: 220px;
    height: 220px;
    margin: 0 auto;
    overflow: hidden;
    border: 1px solid;
    border-radius: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &-group {
    display: flex;
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid;
    justify-content: space-around;
    align-items: center;
  }
}
</style>
