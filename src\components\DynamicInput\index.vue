<template>
  <div class="dynamic-input-container">
    <!-- 根据状态和焦点显示input或textarea -->
    <el-input
      :disabled="disabled"
      v-if="!isTextarea || !isFocused"
      v-model="inputValue"
      :placeholder="placeholder"
      @focus="handleFocus"
      @blur="handleBlur"
      ref="inputRef"
      style="z-index: 10;"
    />
    <el-input
      v-else
      type="textarea"
      v-model="inputValue"
      :placeholder="placeholder"
      :rows="rows"
      @keydown.enter="handleEnterKey"
      @input="handleInput"
      @blur="handleBlur"
      ref="textareaRef"
      autofocus
      style="z-index: 100;"
    />
    <div
      class="input-info"
      v-if="isTextarea && isFocused"
    >
      <span
        class="count-info"
        :class="{ 'max-reached': isMaxLinesReached }"
      >
        {{ currentCount }}/{{ maxLines }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  // 默认提示文案
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 文本域行数
  rows: {
    type: Number,
    default: 6
  },
  // 初始值
  modelValue: {
    type: String,
    default: ''
  },
  // 是否启用textarea模式（聚焦后变为textarea）
  enableTextarea: {
    type: Boolean,
    default: true
  },
  // 是否允许回车换行
  allowEnter: {
    type: Boolean,
    default: true
  },
  // 最大行数限制
  maxLines: {
    type: Number,
    default: 50
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const inputValue = ref(props.modelValue)
const inputRef = ref(null)
const textareaRef = ref(null)
const isFocused = ref(false)
const isTextarea = ref(false)
const isMaxLinesReached = ref(false)

// 计算当前输入的行数
const currentCount = computed(() => {
  if (!inputValue.value) return 0
  return processInputLines(inputValue.value).length
})

// 判断是否达到最大行数
const isReachedMaxLines = computed(() => {
  return currentCount.value >= props.maxLines
})

// 处理输入行，去除空行和前后空格
const processInputLines = (input) => {
  if (!input) return []
  // 分割行，去除空行和前后空格
  return input
    .split('\n')
    .map((item) => item.trim()) // 去除每行前后空格
    .filter((item) => item !== '') // 过滤掉空行
}

// 获取所有输入的数组
const lineArray = computed(() => {
  return processInputLines(inputValue.value)
})

// 监听输入值变化
watch(inputValue, (newVal) => {
  emit('update:modelValue', newVal)
  emit('change', {
    value: newVal,
    arr: lineArray.value
  })
})

// 监听 modelValue 变化并更新 inputValue
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal
  }
)

// 处理聚焦
const handleFocus = () => {
  isFocused.value = true
  if (props.enableTextarea) {
    isTextarea.value = true
    // 确保DOM更新后再聚焦
    nextTick(() => {
      if (textareaRef.value) {
        textareaRef.value.focus()
      }
    })
  }
}

// 处理失焦
const handleBlur = () => {
  // 延迟执行，避免在切换元素时出现问题
  setTimeout(() => {
    isFocused.value = false
    isTextarea.value = false
  }, 100)
}

// 处理回车键
const handleEnterKey = (e) => {
  if (!props.allowEnter) {
    // 如果不允许回车换行，则阻止默认行为
    e.preventDefault()
    return
  }

  // 检查是否已达到最大行数
  if (isReachedMaxLines.value) {
    // 如果已达到最大行数，阻止添加新行
    e.preventDefault()
    isMaxLinesReached.value = true
    return
  }

  // 阻止默认行为，避免表单提交
  e.preventDefault()

  // 获取当前光标位置
  const textarea = textareaRef.value.$el.querySelector('textarea')
  const cursorPosition = textarea.selectionStart

  // 在光标位置插入换行符
  const textBeforeCursor = inputValue.value.substring(0, cursorPosition)
  const textAfterCursor = inputValue.value.substring(cursorPosition)

  inputValue.value = textBeforeCursor + '\n' + textAfterCursor

  // 更新光标位置
  nextTick(() => {
    textarea.selectionStart = cursorPosition + 1
    textarea.selectionEnd = cursorPosition + 1
  })
}
const message = useMessage() // 消息弹窗

// 处理输入
const handleInput = () => {
  // 检查是否超出最大行数限制
  isMaxLinesReached.value = isReachedMaxLines.value

  // 如果超出最大行数，可以在这里添加处理逻辑
  if (isMaxLinesReached.value) {
    // 可以选择截断输入或显示警告
    const lines = inputValue.value.split('\n')
    if (lines.length > props.maxLines) {
      message.error(`最多只能输入${props.maxLines}行`)
      return
    }
  }
}

// 暴露方法给父组件
defineExpose({
  clear: () => {
    inputValue.value = ''
  },
  getLineArray: () => lineArray.value,
  getCount: () => currentCount.value,
  focus: () => {
    if (inputRef.value) {
      inputRef.value.focus()
    }
  }
})
</script>

<style lang="scss" scoped>
.dynamic-input-container {
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  // z-index: 10;
  width: 100%;
  vertical-align: middle;

  .input-info {
    position: absolute;
    right: 15px;
    bottom: 1px;
    z-index: 10;
    display: flex;
    padding: 2px 6px;
    background-color: rgb(245 245 245 / 90%);
    border-radius: 4px;
    justify-content: flex-end;

    .count-info {
      font-size: 12px;
      color: #909399;

      &.max-reached {
        color: #f56c6c;
      }
    }
  }

  :deep(.el-textarea__inner) {
    font-family: monospace;
    line-height: 1.5;
  }
}
</style>
