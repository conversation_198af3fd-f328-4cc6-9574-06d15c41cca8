<template>
  <div class="dynamic-table-container">
    <!-- 工具栏 -->
    <div
      v-if="customToolbarButtons.length > 0"
      class="table-toolbar"
      style="margin-bottom: 16px;"
    >
      <slot name="toolbarButtons">
        <component
          v-for="(btn, index) in customToolbarButtons"
          :key="index"
          :is="btn.component"
          v-bind="btn.props"
          @click="btn.handler?.()"
        />
      </slot>
    </div>

    <!-- 表格 -->
    <el-auto-resizer>
      <template #default="{ height, width }">
        <el-table-v2
          ref="tableRef"
          :columns="mergedColumns"
          :data="tableData"
          :width="width"
          :height="height"
          :loading="tableLoading"
          :row-height="50"
          :header-height="50"
          :estimated-row-height="50"
          :sort-by="sortBy"
          :sort-state="sortState"
          @column-sort="handleSort"
          @scroll="handleScroll"
          v-loading="tableLoading"
          border
          scrollbar-always-on
          cache="10"
        />
      </template>
    </el-auto-resizer>

    <!-- 分页 -->
    <div
      v-if="!enableLoadMore && paginationConfig.enabled"
      class="table-pagination"
      style="margin-top: 16px; text-align: right;"
    >
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="paginationConfig.pageSizes"
        :total="paginationConfig.total"
        :layout="paginationConfig.layout"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch, nextTick } from 'vue'
import type { Column } from 'element-plus'
import { 
  ElSelect, 
  ElOption, 
  ElInputNumber, 
  ElDatePicker, 
  ElSwitch, 
  ElImage, 
  ElUpload, 
  ElButton, 
  ElIcon, 
  ElMessage,
  ElCheckbox,
  ElTableV2,
  ElTableV2Column,
  ElAutoResizer,
  ElPagination
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import * as FileApi from '@/api/infra/file'
import { useUpload } from '@/components/UploadFile/src/useUpload'
const { uploadUrl, httpRequest } = useUpload()
import { DICT_TYPE, getIntDictOptions, getDictLabel, getDictOptions } from '@/utils/dict'

interface ColumnConfig {
  field: string
  title: string
  fieldName?: string
  width?: number | string
  minWidth?: number | string
  type?: string
  edit?: number
  required?: number
  paramTypeCode?: string
  unit?: string
  needUnit?: number
  copyText?: boolean
  [key: string]: any
}

interface Props {
  columns: ColumnConfig[]
  list?: any[]
  editRules?: Record<string, any>
  pageSize?: number
  currentPage?: number
  total?: number
  toolbarButtons?: Array<{
    component: string
    props: Record<string, any>
    handler?: () => void
  }>
  enableVirtualScroll?: boolean
  enableLoadMore?: boolean
  enableSelection?: boolean
  rowKey?: string | ((row: any, index: number) => string)
  sexOptions?: Array<{ label: string; value: string }>
  cityOptions?: Array<{ label: string; value: string }>
  uploadImageMethod?: (formData: FormData, updateProgress: (percent: number) => void) => Promise<any>
  uploadFileMethod?: (formData: FormData, updateProgress: (percent: number) => void) => Promise<any>
}

const props = withDefaults(defineProps<Props>(), {
  enableSelection: true,
  rowKey: 'id'
})

const emit = defineEmits([
  'update:list',
  'save',
  'add',
  'delete',
  'update:page',
  'select-all',
  'select-change',
  'load-more',
  'edit-change'
])

// 默认配置
const defaultSexOptions = [
  { label: '女', value: '0' },
  { label: '男', value: '1' }
]
const defaultCityOptions = [
  { label: '广东省深圳市', value: 'sz' },
  { label: '北京市', value: 'bj' },
  { label: '上海市', value: 'sh' },
  { label: '浙江省杭州市', value: 'hz' }
]

const tableRef = ref()
const tableData = ref<any[]>(props.list || [])
const tableLoading = ref(false)
const selectedRowKeys = ref<(string | number)[]>([])
const editingCells = ref<Set<string>>(new Set())

// 分页和虚拟滚动相关
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 200)
const isLoadingMore = ref(false)
const hasMore = ref(true)

// 排序相关
const sortBy = ref({ key: '', order: '' })
const sortState = ref({})

// 获取行键
const getRowKey = (row: any, index: number): string | number => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  }
  return row[props.rowKey] || index
}

// 多选相关计算属性
const isAllSelected = computed(() => {
  return tableData.value.length > 0 && selectedRowKeys.value.length === tableData.value.length
})

const isIndeterminate = computed(() => {
  return selectedRowKeys.value.length > 0 && selectedRowKeys.value.length < tableData.value.length
})

// 分页配置
const paginationConfig = computed(() => {
  if (props.enableLoadMore) {
    return { enabled: false }
  }
  return {
    enabled: true,
    total: props.total || tableData.value.length,
    pageSizes: [50, 100, 200, 500],
    layout: 'total, sizes, prev, pager, next, jumper'
  }
})

// 创建编辑单元格组件
const createEditCell = (column: ColumnConfig, cellData: any, rowData: any, rowIndex: number) => {
  const cellKey = `${rowIndex}-${column.field}`
  const isEditing = editingCells.value.has(cellKey)
  
  const startEdit = () => {
    editingCells.value.add(cellKey)
  }
  
  const stopEdit = () => {
    editingCells.value.delete(cellKey)
    emit('edit-change', { row: rowData, column, value: cellData })
    emit('update:list', tableData.value, rowData)
  }
  
  const handleChange = (value: any) => {
    rowData[column.field] = value
  }
  
  if (!isEditing) {
    return (
      <div 
        class="cell-content" 
        style="cursor: pointer; padding: 8px;" 
        onDblclick={startEdit}
      >
        {cellData || ''}
      </div>
    )
  }
  
  // 根据paramTypeCode渲染不同的编辑组件
  switch (column.paramTypeCode) {
    case 'inputNumber':
      return (
        <el-input-number
          modelValue={cellData}
          placeholder={column.fieldName || column.title}
          style="width: 100%;"
          onUpdate:modelValue={handleChange}
          onBlur={stopEdit}
          onKeyup={(e: KeyboardEvent) => e.key === 'Enter' && stopEdit()}
          {...{ autofocus: true }}
        />
      )
    
    case 'time':
      return (
        <el-date-picker
          modelValue={cellData}
          type="datetime"
          placeholder={column.fieldName || column.title}
          style="width: 100%;"
          onUpdate:modelValue={handleChange}
          onBlur={stopEdit}
          {...{ autofocus: true }}
        />
      )
    
    case 'select':
      let options: Array<{ label: string; value: string }> = []
      if (column.fieldName === '是否投产') {
        options = [
          { label: '是', value: '是' },
          { label: '否', value: '否' }
        ]
      } else if (column.title?.includes('listing文案状态')) {
        options = getIntDictOptions(DICT_TYPE.INFRA_LISTING_STATUS).map(item => ({
          label: item.label,
          value: item.label
        }))
      } else if (column.title?.includes('上架状态')) {
        options = [
          { label: '是', value: '是' },
          { label: '否', value: '否' }
        ]
      }
      
      return (
        <el-select
          modelValue={cellData}
          placeholder={`请选择${column.fieldName || column.title}`}
          style="width: 100%;"
          onUpdate:modelValue={handleChange}
          onBlur={stopEdit}
          {...{ autofocus: true }}
        >
          {options.map(option => (
            <el-option
              key={option.value}
              label={option.label}
              value={option.value}
            />
          ))}
        </el-select>
      )
    
    case 'input':
    default:
      return (
        <el-input
          modelValue={cellData}
          placeholder={column.fieldName || column.title}
          style="width: 100%;"
          onUpdate:modelValue={handleChange}
          onBlur={stopEdit}
          onKeyup={(e: KeyboardEvent) => e.key === 'Enter' && stopEdit()}
          {...{ autofocus: true }}
        />
      )
  }
}

// 创建特殊类型单元格
const createSpecialCell = (column: ColumnConfig, cellData: any, rowData: any) => {
  switch (column.type) {
    case 'sex':
      const sexOptions = props.sexOptions || defaultSexOptions
      const sexOption = sexOptions.find(opt => opt.value === cellData)
      return sexOption?.label || cellData
    
    case 'city':
      const cityOptions = props.cityOptions || defaultCityOptions
      const cityOption = cityOptions.find(opt => opt.value === cellData)
      return cityOption?.label || cellData
    
    case 'flag':
      return (
        <el-switch
          modelValue={cellData}
          onUpdate:modelValue={(value) => {
            rowData[column.field] = value
            emit('update:list', tableData.value, rowData)
          }}
        />
      )
    
    case 'image':
      return cellData ? (
        <el-image
          src={cellData}
          style="width: 36px; height: 36px;"
          fit="cover"
          preview-src-list={[cellData]}
        />
      ) : null
    
    case 'updateImage':
      return (
        <el-upload
          class="avatar-uploader"
          action={uploadUrl}
          http-request={httpRequest}
          show-file-list={false}
          onSuccess={(response: any) => {
            rowData[column.field] = response.data
            emit('update:list', tableData.value, rowData)
          }}
          beforeUpload={(file: File) => {
            const isImage = file.type.startsWith('image/')
            if (!isImage) {
              ElMessage.error('只能上传图片文件!')
            }
            return isImage
          }}
        >
          {cellData ? (
            <el-image
              src={cellData}
              style="width: 40px; height: 40px; border-radius: 50%;"
              fit="cover"
            />
          ) : (
            <el-icon style="font-size: 28px; color: #8c939d; width: 40px; height: 40px; border: 1px dashed #d9d9d9; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
              <Plus />
            </el-icon>
          )}
        </el-upload>
      )
    
    case 'updateFile':
      return (
        <el-upload
          action={uploadUrl}
          http-request={httpRequest}
          file-list={cellData ? [{ name: '文件', url: cellData }] : []}
          onSuccess={(response: any) => {
            rowData[column.field] = response.data
            emit('update:list', tableData.value, rowData)
          }}
        >
          <el-button size="small" type="primary">上传文件</el-button>
        </el-upload>
      )
    
    default:
      return cellData
  }
}

// 合并列配置
const mergedColumns = computed(() => {
  const columns = [];
  
  // 添加复选框列
  if (props.enableSelection) {
    columns.push({
      key: 'selection',
      dataKey: 'selection',
      title: '',
      width: 60,
      minWidth: 60,
      resizable: false,
      sortable: false,
      headerRenderer: () => {
        return (
          <el-checkbox
            modelValue={isAllSelected.value}
            indeterminate={isIndeterminate.value}
            onUpdate:modelValue={handleSelectAll}
          />
        )
      },
      cellRenderer: ({ rowData, rowIndex }) => {
        return (
          <el-checkbox
            modelValue={selectedRowKeys.value.includes(getRowKey(rowData, rowIndex))}
            onUpdate:modelValue={(val) => handleSelectRow(val, rowData, rowIndex)}
          />
        )
      }
    });
  }
  
  // 添加其他列
  const mappedColumns = props.columns.map((col) => {
    const column: Column<any> = {
      key: col.field,
      dataKey: col.field,
      title: col.title,
      width: col.width || col.minWidth || 120,
      minWidth: typeof col.minWidth === 'string' ? parseInt(col.minWidth) : (col.minWidth || 120),
      resizable: true,
      sortable: true
    }
    
    // 处理标题单位显示
    if (col.unit && col.needUnit === 1) {
      const unitLabel = getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, col.unit)
      if (unitLabel) {
        const originalTitle = col.fieldName || col.title
        if (!originalTitle.includes(`(${unitLabel})`)) {
          column.title = `${originalTitle}(${unitLabel})`
        }
      }
    }
    
    // 设置列宽
    if (col.title?.includes('时间')) {
      column.width = 140
    } else if (col.title?.includes('店铺')) {
      column.width = 200
    } else if (col.title?.includes('sku') || col.title?.includes('SKU')) {
      column.width = 200
    } else if (col.title?.includes('产品名称')) {
      column.width = 140
    } else if (col.title?.includes('操作')) {
      column.width = 180
    } else if (col.title?.includes('listing文案状态')) {
      column.width = 140
    }
    
    // 自定义单元格渲染
    column.cellRenderer = ({ cellData, rowData, rowIndex }) => {
      // 特殊字段处理 - 审核状态
      if (col.title === '审核状态') {
        const status = getIntDictOptions(DICT_TYPE.INFRA_TABLE_APPROVAL_STATUS).filter(
          (item) => item.label == cellData
        )[0]?.value
        return <el-tag type={getAuditStatusType(status)}>{cellData}</el-tag>
      }
      
      // 是否投产状态
      if (col.title === '是否投产') {
        const status = cellData == '是' ? '1' : '3'
        return <el-tag type={getAuditStatusType(status)}>{cellData}</el-tag>
      }
      
      // 复制文本
      if (col.copyText) {
        return (
          <div 
            style="cursor: pointer; color: #409eff;"
            onClick={() => {
              navigator.clipboard.writeText(cellData || '')
              ElMessage.success('复制成功')
            }}
          >
            {cellData}
          </div>
        )
      }
      
      // 可编辑单元格
      if (col.edit === 1 && col.paramTypeCode) {
        return createEditCell(col, cellData, rowData, rowIndex)
      }
      
      // 特殊类型处理
      if (col.type) {
        return createSpecialCell(col, cellData, rowData)
      }
      
      return cellData
    }
    
    return column
  })
  
  return [...columns, ...mappedColumns];
})

// 获取审核状态类型
const getAuditStatusType = (status: any) => {
  switch (status) {
    case 3:
    case '3':
      return 'danger' // 待审核
    case 1:
    case '1':
      return 'success' // 已通过
    case 2:
    case '2':
      return 'info' // 已拒绝
    case 4:
    case '4':
      return 'info' // 草稿
    default:
      return 'warning' // 未知状态
  }
}

// 同步数据
watch(
  () => props.list,
  (newVal) => {
    if (newVal) {
      tableData.value = newVal
    }
  },
  { immediate: true }
)

// 多选处理
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedRowKeys.value = tableData.value.map((row, index) => getRowKey(row, index))
  } else {
    selectedRowKeys.value = []
  }
  emit('select-all', selectedRowKeys.value.map(key => 
    tableData.value.find((row, index) => getRowKey(row, index) === key)
  ).filter(Boolean))
}

const handleSelectRow = (checked: boolean, rowData: any, rowIndex: number) => {
  const key = getRowKey(rowData, rowIndex)
  if (checked) {
    if (!selectedRowKeys.value.includes(key)) {
      selectedRowKeys.value.push(key)
    }
  } else {
    const index = selectedRowKeys.value.indexOf(key)
    if (index > -1) {
      selectedRowKeys.value.splice(index, 1)
    }
  }
  emit('select-change', selectedRowKeys.value.map(key => 
    tableData.value.find((row, index) => getRowKey(row, index) === key)
  ).filter(Boolean))
}

// 排序处理
const handleSort = ({ key, order }: { key: string; order: string }) => {
  sortBy.value = { key, order }
  // 这里可以添加排序逻辑或触发外部排序事件
}

// 滚动处理 - 触底加载更多
const handleScroll = ({ scrollTop }: { scrollTop: number }) => {
  if (!props.enableLoadMore || isLoadingMore.value || !hasMore.value) {
    return
  }
  
  const tableElement = tableRef.value?.$el
  if (tableElement) {
    const { scrollHeight, clientHeight } = tableElement
    if (scrollTop + clientHeight >= scrollHeight - 50) {
      loadMore()
    }
  }
}

// 分页处理
const handlePageSizeChange = (newPageSize: number) => {
  pageSize.value = newPageSize
  emit('update:page', { currentPage: currentPage.value, pageSize: newPageSize })
}

const handleCurrentPageChange = (newCurrentPage: number) => {
  currentPage.value = newCurrentPage
  emit('update:page', { currentPage: newCurrentPage, pageSize: pageSize.value })
}

// 加载更多数据
const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) {
    return
  }
  
  isLoadingMore.value = true
  try {
    emit('load-more', {
      currentPage: currentPage.value + 1,
      pageSize: pageSize.value
    })
  } catch (error) {
    console.error('加载更多数据失败:', error)
  } finally {
    isLoadingMore.value = false
  }
}

// 设置是否还有更多数据
const setHasMore = (value: boolean) => {
  hasMore.value = value
}

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1
  hasMore.value = true
  isLoadingMore.value = false
}

// 新增行
const addEvent = async () => {
  const newRow = {}
  tableData.value.unshift(newRow)
  emit('add', newRow)
  emit('update:list', tableData.value)
}

// 删除行
const removeRow = async (row: any) => {
  const index = tableData.value.indexOf(row)
  if (index > -1) {
    tableData.value.splice(index, 1)
    emit('delete', row)
    emit('update:list', tableData.value)
  }
}

// 保存
const saveEvent = async () => {
  // 这里可以添加验证逻辑
  emit('save', {
    insertRecords: [], // 新增的记录
    updateRecords: tableData.value, // 更新的记录
    removeRecords: [] // 删除的记录
  })
}

// 内部默认配置
const defaultToolbarButtons = [
  { component: 'el-button', props: { type: 'primary', icon: 'Plus' }, handler: addEvent },
  { component: 'el-button', props: { type: 'success', icon: 'Check' }, handler: saveEvent }
]

// 使用外部传入的 toolbar 按钮或使用默认按钮
const customToolbarButtons = computed(() => {
  if (props.toolbarButtons && props.toolbarButtons.length) {
    // 自定义按钮处理
    props.toolbarButtons.forEach((item) => {
      if (typeof item.handler === 'string') {
        if (item.handler === 'addEvent') {
          item.handler = addEvent
        }
      }
    })
    return props.toolbarButtons
  }
  return defaultToolbarButtons
})

defineExpose({
  tableRef,
  setHasMore,
  resetPagination,
  loadMore,
  addEvent,
  removeRow,
  saveEvent,
  selectedRowKeys
})
</script>

<style scoped>
.dynamic-table-container {
  width: 100%;
  height: 100%;
}

.table-toolbar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
}

.cell-content {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
}

.avatar-uploader {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-upload) {
  border: none !important;
}

:deep(.el-upload:hover) {
  border: none !important;
}
</style>
