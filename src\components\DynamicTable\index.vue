<template>
  <div
    class="dynamic-table-container"
    style="position: relative"
  >
    <!-- 自动保存状态提示 -->
    <div
      v-if="saveStatus"
      class="save-status-container"
      style="position: absolute; top: 10px; right: 60px; z-index: 1000; pointer-events: none"
    >
      <Transition
        name="save-status"
        mode="out-in"
      >
        <el-tag
          v-if="saveStatus.lastSaved || saveStatus.saving"
          :type="saveStatus.error ? 'danger' : saveStatus.saving ? 'warning' : 'success'"
          size="small"
          class="save-status-tag flex"
          style="pointer-events: auto"
        >
          <template v-if="saveStatus.saving">
            <Icon
              icon="ep:loading"
              class="animate-spin mr-1"
            />
            正在保存...
          </template>
          <template v-else-if="saveStatus.error">
            <Icon
              icon="ep:warning"
              class="mr-1"
            />
            {{ saveStatus.error }}
          </template>
          <template v-else>
            <Icon
              icon="ep:check"
              class="mr-1"
            />
            已自动保存
            {{ saveStatus.lastSaved ? new Date(saveStatus.lastSaved).toLocaleTimeString() : '' }}
          </template>
        </el-tag>
      </Transition>
    </div>

    <vxe-grid
      id="dynamic-table-grid"
      ref="gridRef"
      height="auto"
      v-model:loading="tableLoading"
      :columns="mergedColumns"
      :data="tableData"
      show-overflow
      sync-resize
      :key="tableKey"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :row-config="rowConfig"
      :toolbar-config="toolbarConfig"
      :checkbox-config="checkboxConfig"
      :column-config="columnConfig"
      :custom-config="customConfig"
      :scroll-y="scrollYConfig"
      :pager-config="pagerConfig"
      @edit-closed="handleEditClosed"
      @page-change="handlePageChange"
      @checkbox-all="handleSelectAll"
      @checkbox-change="handleSelectRow"
      @scroll="handleScroll"
      @custom="handleCustomChange"
    >
      <!-- 自定义top工具栏 -->
      <template #toolbar_buttons>
        <slot name="toolbarButtons">
          <component
            v-for="(btn, index) in customToolbarButtons"
            :key="index"
            :is="btn.component"
            v-bind="btn.props"
            @click="btn.handler?.()"
          />
        </slot>
      </template>

      <!-- 自定义复制 -->
      <template #copyText="{ row, column }">
        <vxe-text
          v-if="row[column.field]"
          :content="row[column.field]"
          v-clipboard="row[column.field]"
        />
      </template>

      <!-- 动态生成自定义表头插槽 -->
      <template
        v-for="col in columnsWithCustomHeader"
        :key="col.field"
        #[`${col.field}_header`]
      >
        <div class="custom-header">
          <div class="header-title-row">
            <el-tooltip
              :content="col.title!='图片'?
                (col.tooltipContent || col.title || col.fieldName) +
                (col.unitLabel ? col.unitLabel : ''):'可粘贴图片上传'
              "
              placement="top"
              :disabled="!col.tooltipContent && !col.title && !col.fieldName"
            >
              <span class="header-title">{{ col.title || col.fieldName }}</span>
            </el-tooltip>
            <!-- <i
              v-if="col.edit == 1"
              class="vxe-table-icon-edit header-edit-icon"
            ></i> -->
          </div>
          <div
            v-if="col.unitLabel"
            class="header-unit"
          >{{ col.unitLabel }}</div>

          <div
            v-if="col.title == '图片'"
            class="header-unit"
          >可粘贴上传</div>
        </div>
      </template>

      <!-- 动态生成自定义行内容插槽 -->
      <template
        v-for="col in columnsWithCustomSlots"
        :key="col.field"
        #[col.field]="{ row, column, $index }"
      >
        <slot
          :name="col.field"
          :row="row"
          :column="column"
          :index="$index"
          :value="row[col.field]"
        >
          <!-- 默认显示内容 -->
          <span>{{ row[col.field] }}</span>
        </slot>
      </template>

      <!-- 动态生成操作列的原始插槽内容 -->
      <template
        v-for="col in columnsWithActionSlots"
        :key="col.field + '_original'"
        #[col.field]="{ row, column, $index }"
      >
        <!-- 渲染原始的操作列内容 -->
        <div class="action-buttons">
          <!-- 编辑/保存按钮 -->
          <el-button
            v-if="!isRowEditing(row)"
            type="text"
            size="small"
            @click="startEdit(row)"
            :disabled="props.disableEdit && isRowApproved(row)"
          >
            编辑
          </el-button>
          <el-button
            v-else
            type="text"
            size="small"
            @click="saveEdit(row)"
            :disabled="props.disableEdit && isRowApproved(row)"
          > 保存 </el-button>

          <!-- 渲染原始操作列的内容 -->
          <template v-if="col.slots && col.slots.default && typeof col.slots.default == 'function'">
            <RenderSlot
              :slot-fn="col.slots.default"
              :params="{ row, column, $index }"
            />
          </template>
        </div>
      </template>
    </vxe-grid>
  </div>
</template>

<script setup lang="tsx">
import {
  ref,
  computed,
  watch,
  useSlots,
  nextTick,
  onMounted,
  onUnmounted,
  defineComponent
} from 'vue'
import { ElMessage } from 'element-plus'
import type { VxeGridProps, VxeGridInstance, VxeColumnPropTypes } from 'vxe-table'
import * as FileApi from '@/api/infra/file'
import { useUpload } from '@/components/UploadFile/src/useUpload'
const { uploadUrl, httpRequest } = useUpload()
import { DICT_TYPE, getIntDictOptions, getDictLabel, getDictOptions } from '@/utils/dict'
import { saveHeadSetting } from '@/api/tool/tableTemplateCenter'
import type { ToolsTableHeadSettingSaveReqVO } from '@/api/tool/tableTemplateCenter'
import {
  getPage as getTableDataPage,
  update as updateTableData,
  addData,
  deleteData,
  submitApproval,
  submitProd
} from '@/api/tool/tableData'
interface Props {
  columns: VxeColumnPropTypes<any>[]
  list?: any[]
  editRules?: Record<string, any>
  pageSize?: number
  currentPage?: number
  total?: number
  disableEdit?: boolean,
  templateId?: number
  toolbarButtons?: Array<{
    component: string
    props: Record<string, any>
    handler?: () => void
  }>
  enableVirtualScroll?: boolean
  enableLoadMore?: boolean
  saveStatus?: {
    saving: boolean
    lastSaved: Date | null
    error: string | null
  }
}

import { defineProps } from 'vue'

// 组件属性定义
const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  list: {
    type: Array,
    default: () => []
  },
  toolbarButtons: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  saveStatus: {
    type: Object,
    default: null
  },
  enableVirtualScroll: {
    type: Boolean,
    default: false
  },
  enableLoadMore: {
    type: Boolean,
    default: false
  },
  pageSize: {
    type: Number,
    default: 50
  },
  currentPage: {
    type: Number,
    default: 1
  },
  total: {
    type: Number,
    default: 0
  },
  templateId: {
    type: String,
    default: ''
  },
  editRules: {
    type: Object,
    default: () => ({})
  },
  disableEdit: {
    type: Boolean,
    default: false
  },
  hideSaveButtons: {
    type: Boolean,
    default: false
  }
})
const tableKey = ref(0)

const emit = defineEmits([
  'update:list',
  'save',
  'add',
  'delete',
  'update:page',
  'select-all',
  'select-change',
  'load-more',
  'add-data',
  'custom-change' // 新增自定义列配置变化事件
])

// 默认配置
const defaultSexOptions = [
  { label: '女', value: '0' },
  { label: '男', value: '1' }
]
const defaultCityOptions = [
  { label: '广东省深圳市', value: 'sz' },
  { label: '北京市', value: 'bj' },
  { label: '上海市', value: 'sh' },
  { label: '浙江省杭州市', value: 'hz' }
]

const slots = useSlots()

const gridRef = ref<VxeGridInstance>()
const tableData = ref<any[]>(props.list || [])
const tableLoading = ref(false)
const selectedRows = ref<any[]>([])

// 滚动位置保存
const savedScrollPosition = ref({ scrollLeft: 0, scrollTop: 0 })

// 分页和虚拟滚动相关
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 200)
const isLoadingMore = ref(false)
const hasMore = ref(true)
// 记录当前处于编辑状态的行
const editingRowId = ref<string | number | null>(null)

// 动态计算列宽度
const calculateColumnWidth = (title: string) => {
  // 基础宽度
  const baseWidth = 80
  // 每个字符的平均宽度（中文字符按2个字符计算）
  const charWidth = 6
  // 计算标题长度（中文字符按2个字符计算）
  const titleLength = title.replace(/[\u4e00-\u9fa5]/g, 'aa').length
  // 计算宽度，最小120px，最大300px
  const calculatedWidth = Math.max(80, Math.min(300, baseWidth + titleLength * charWidth))
  return `${calculatedWidth}px`
}

// 获取需要自定义表头的列
const columnsWithCustomHeader = computed(() => {
  return props.columns
    .filter((col) => {
      // 如果有单位需要换行显示，或者有自定义tooltip内容，或者是可编辑列
      return (col.unit && col.needUnit == 1) || col.tooltipContent || col.edit == 1
    })
    .map((col) => {
      const result = { ...col }
      // 处理单位标签
      if (col.unit && col.needUnit == 1) {
        const unitLabel = getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, col.unit)
        if (unitLabel) {
          result.unitLabel = unitLabel
        }
      }
      return result
    })
})

// 获取需要自定义行内容插槽的列
const columnsWithCustomSlots = computed(() => {
  return props.columns.filter((col) => {
    // 检查是否有对应的插槽
    return slots[col.field]
  })
})

// 获取有操作列插槽的列
const columnsWithActionSlots = computed(() => {
  return props.columns.filter((col) => {
    // 检查是否是操作列且有原始插槽配置
    return (col.field == 'action' || col.title == '操作') && col.slots && col.slots.default
  })
})

// 定义一个渲染组件来处理JSX内容
const RenderSlot = defineComponent({
  props: {
    slotFn: {
      type: Function,
      required: true
    },
    params: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    return () => {
      try {
        return props.slotFn(props.params)
      } catch (error) {
        console.error('渲染插槽内容失败:', error)
        return null
      }
    }
  }
})

// 合并列配置
const mergedColumns = computed(() => {
  return (
    props.columns.map((col) => {
      // 动态计算列宽度
      const title = col.fieldName || col.title || ''
      col.minWidth = calculateColumnWidth(title)
      col.align = 'center'
      col.titleAlign = 'center'

      // 检查是否需要自定义表头
      const needCustomHeader =
        (col.unit && col.needUnit == 1) || col.tooltipContent || col.edit == 1
      // 检查是否有自定义行内容插槽
      const hasCustomSlot = slots[col.field]

      if (needCustomHeader) {
        // 使用自定义表头插槽
        col.slots = { header: `${col.field}_header` }
        // 不在title中添加单位，因为会在自定义表头中换行显示
      } else if (col.unit && col.needUnit == 1) {
        // 处理标题单位显示（原有逻辑，用于不需要自定义表头的列）
        const unitLabel = getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, col.unit)

        if (unitLabel && !title.includes(`(${unitLabel})`)) {
          col.title = `${title}(${unitLabel})`
          // 重新计算包含单位的标题宽度
          col.width = calculateColumnWidth(col.title)
        }
      }

      // 设置列对齐方式和复制功能
      const fieldName = (col.fieldName || col.title || '').toLowerCase()
      const field = (col.field || '').toLowerCase()

      // 预定义特殊字段列表
      const leftAlignFields = [
        'sku',
        'SKU',
        'asin',
        '关键词',
        '店铺名称',
        '产品',
        '商品',
        '操作',
        '人',
        '状态'
      ]
      const copyFields = ['sku', 'SKU', 'asin', '关键词', '店铺名称', '产品', '商品']

      // 检查是否为需要左对齐的字段
      const isLeftAlignField = leftAlignFields.some(
        (alignField) =>
          fieldName.includes(alignField.toLowerCase()) ||
          field.includes(alignField.toLowerCase()) ||
          col.title?.includes(alignField)
      )

      // 检查是否为需要复制的内容
      const isCopyFields = copyFields.some(
        (alignField) =>
          fieldName.includes(alignField.toLowerCase()) ||
          field.includes(alignField.toLowerCase()) ||
          col.title?.includes(alignField)
      )

      // 设置复制功能和左对齐
      if (isCopyFields) {
        col.copyText = true
        col.align = 'left'
      } else if (isLeftAlignField) {
        col.align = 'left'
      }

      // 根据标题内容设置列宽度
      if (col.title) {
        // 特殊字段处理
        if (col.title == '审核状态') {
          return {
            ...col,
            slots: {
              default: ({ row }) => {
                const status = getIntDictOptions(DICT_TYPE.INFRA_TABLE_APPROVAL_STATUS).filter(
                  (item) => item.label == row[col.field]
                )[0]?.value
                return <el-tag type={getAuditStatusType(status)}>{row[col.field]}</el-tag>
              }
            }
          }
        } else if (col.title == '是否投产') {
          return {
            ...col,
            slots: {
              default: ({ row }) => {
                const status = row[col.field] == '是' ? '1' : '3'
                return <el-tag type={getAuditStatusType(status)}>{row[col.field]}</el-tag>
              }
            }
          }
        } else if (col.title.includes('时间')) {
          col.minWidth = '140px'
        } else if (col.title.includes('店铺')) {
          col.minWidth = '200px'
        } else if (col.title.includes('sku') || col.title.includes('SKU')) {
          col.minWidth = '200px'
        } else if (col.title.includes('产品名称')) {
          col.minWidth = '140px'
        } else if (col.title.includes('操作')) {
          // 操作列使用自适应宽度
          col.minWidth = '230px'
          delete col.width // 删除固定宽度，使用自适应
        } else if (col.title == 'listing文案状态') {
          col.minWidth = '140px'
          return {
            ...col,
            editRender: {
              name: 'VxeSelect',
              props: {
                placeholder: '请选择listing文案状态'
              },
              options: getIntDictOptions(DICT_TYPE.INFRA_LISTING_STATUS).map((item) => ({
                label: item.label,
                value: item.label
              }))
            }
          }
        } else if (col.title.includes('上架')) {
          return {
            ...col,
            editRender: {
              name: 'VxeSelect',
              props: {
                placeholder: '请选择上架状态'
              },
              options: [
                { label: '是', value: '是' },
                { label: '否', value: '否' }
              ]
            }
          }
        }
      }

      // 处理可编辑列
      // if (col.edit && col.edit == 1) {
      // 创建基础列配置
      const createColumnWithRules = (editRender) => {
        const column = { ...col, editRender }

        // 添加必填校验
        if (col.required == 1) {
          const fieldLabel = col.fieldName || col.title
          const isSelectType = col.paramTypeCode && ['select', 'time'].includes(col.paramTypeCode)
          const message = `请${isSelectType ? '选择' : '输入'}${fieldLabel}`

          column.editRules = [{ required: true, message }]
        }

        return column
      }

      // 如果有 paramTypeCode，根据类型处理
      if (col.edit == 1 && col.paramTypeCode) {
        switch (col.paramTypeCode) {
          case 'href':
            return {
              ...col,
              editRender: {
                name: 'VxeInput',
                props: {
                  type: 'text',
                  placeholder: col.fieldName || col.title
                }
              },
              slots: {
                default: ({ row }) => {
                  console.log(row[col.field])
                  return (
                    <el-link href={row[col.field]} target="_blank">
                      {row[col.field]}
                    </el-link>
                  )
                }
              }
            }
          case 'updateImage':
          case 'file':
          case 'image':
            // 图片上传保持使用VxeUpload
            col.type = 'updateImage'
            return {
              ...col,
              cellRender: getImagePreviewCellRender(),
              editRender: getAvatarCellRender()
            }
          case 'inputNumber':
            return createColumnWithRules({
              name: 'VxeInput',
              props: {
                type: 'number',
                placeholder: col.fieldName || col.title
              },
              events: {
                input: ({ row, column }) => {
                  const value = row[column.field]
                  if (value && isNaN(Number(value))) {
                    row[column.field] = ''
                  }
                }
              }
            })
          case 'time':
            return createColumnWithRules({
              name: 'VxeDatePicker',
              props: {
                placeholder: col.fieldName || col.title,
                type: 'datetime'
              }
            })
          case 'select':
            const options =
              col.fieldName == '是否投产'
                ? [
                    { label: '是', value: '是' },
                    { label: '否', value: '否' }
                  ]
                : []

            return createColumnWithRules({
              name: 'VxeSelect',
              props: {
                placeholder: `请选择${col.fieldName || col.title}`
              },
              options
            })
          case 'sex':
            return {
              ...col,
              editRender: {
                name: 'VxeSelect',
                props: {
                  placeholder: '请选择性别'
                },
                options: props.sexOptions || defaultSexOptions
              }
            }
          case 'city':
            return {
              ...col,
              editRender: {
                name: 'VxeSelect',
                props: {
                  placeholder: '请选择城市'
                },
                options: props.cityOptions || defaultCityOptions
              }
            }
          case 'flag':
            return {
              ...col,
              editRender: {
                name: 'VxeSwitch'
              }
            }
          case 'input':
          case 'text':
          default:
            return createColumnWithRules({
              name: 'VxeInput',
              props: {
                placeholder: col.fieldName || col.title
              }
            })
        }
      } else {
        switch (col.paramTypeCode) {
          case 'file':
          case 'image':
            // 图片上传保持使用VxeUpload
            col.type = 'updateImage'
            return {
              ...col,
              cellRender: getImagePreviewCellRender()
            }
        }
      }

      // 处理复制文本功能
      if (col.copyText) {
        return {
          ...col,
          slots: { default: 'copyText' }
        }
      }

      // 处理操作列
      if (col.field == 'action' || col.title == '操作') {
        // 如果原来有slots配置，保留原有配置并使用特殊的插槽名
        if (col.slots && col.slots.default) {
          return {
            ...col,
            slots: {
              ...col.slots,
              default: col.field // 使用原字段名作为插槽名
            }
          }
        } else {
          // 如果没有原有插槽，使用默认的action插槽
          return {
            ...col,
            slots: {
              ...col.slots,
              default: 'action'
            }
          }
        }
      }

      // 处理自定义插槽
      if (hasCustomSlot) {
        return {
          ...col,
          slots: {
            ...col.slots,
            default: col.field
          }
        }
      }

      return col
    }) || []
  )
})

// 上传图片 Cell Render
const getAvatarCellRender = () => ({
  name: 'VxeUpload',
  props: {
    mode: 'image',
    singleMode: true,
    urlMode: true,
    showButtonText: false,
    pasteToUpload: true,
    dragToUpload: true,
    showPreview: true,
    autoHiddenButton: true,
    progressText: '{percent}%',
    imageConfig: {
      circle: false,
      width: 40,
      height: 40
    },
    uploadMethod: async ({ file, updateProgress }) => {
      const formData = new FormData()
      formData.append('file', file)
      const method = props.uploadImageMethod || defaultUploadImage
      return method(formData, updateProgress)
    }
  }
})

// 上传图片默认方法
const defaultUploadImage = async (
  formData: FormData,
  updateProgress: (percent: number) => void
) => {
  const res = await FileApi.updateFile(formData)
  updateProgress(100)
  return { ...res, url: res.data }
}

// 上传文件 Cell Render
const getFileListCellRender = () => ({
  name: 'VxeUpload',
  props: {
    multiple: true,
    urlMode: true,
    showButtonText: false,
    pasteToUpload: true,
    dragSort: true,
    progressText: '{percent}%',
    moreConfig: {
      maxCount: 1,
      layout: 'horizontal'
    },
    uploadMethod: async ({ file, updateProgress }) => {
      const formData = new FormData()
      formData.append('file', file)
      const method = props.uploadFileMethod || defaultUploadFile
      return method(formData, updateProgress)
    }
  }
})
// 上传文件默认方法
const defaultUploadFile = (formData: FormData, updateProgress: (percent: number) => void) => {
  return axios
    .post('/infra/file/upload', formData, {
      onUploadProgress: (event) => {
        const percent = Math.round((event.loaded * 100) / event.total!)
        updateProgress(percent)
      }
    })
    .then((res) => res.data)
}

// 性别 Edit Render (已迁移到mergedColumns中)
// const getSexEditRender = () => ({
//   name: 'VxeSelect',
//   options: props.sexOptions || defaultSexOptions
// })

// 城市 Edit Render (已迁移到mergedColumns中)
// const getCityEditRender = () => ({
//   name: 'VxeSelect',
//   options: props.cityOptions || defaultCityOptions
// })

// 开关 Cell Render (已迁移到mergedColumns中)
// const getFlagCellRender = () => ({
//   name: 'VxeSwitch'
// })

// 图片预览模式
const getImagePreviewCellRender = () => ({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36,
    fit: 'cover', // 保持宽高比，裁剪多余部分
    preview: true // 启用预览功能
  }
})

// 图片（保留原函数以兼容）
const getImage = () => ({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36,
    fit: 'cover' // 保持宽高比，裁剪多余部分
  }
})

// 获取审核状态类型 (红色:待审核, 绿色:已通过, 灰色:已拒绝)
const getAuditStatusType = (status) => {
  switch (status) {
    case 3:
    case '3':
      return 'danger' // 待审核 (原型图显示红色)
    case 1:
    case '1':
      return 'success' // 已通过 (原型图显示绿色)
    case 2:
    case '2':
      return 'info' // 已拒绝 (原型图显示灰色)
    case 4:
    case '4':
      return 'info' // 草稿
    default:
      return 'warning' // 未知或其他状态
  }
}

// 默认配置
const editConfig = {
  mode: 'row', // 修改为行编辑模式
  trigger: 'manual', // 修改为手动触发
  showStatus: true,
  showIcon: false, // 显示编辑图标
  autoClear: false, // 保持编辑状态
  showUpdateStatus: false, // 不显示更新状态，只在保存时更新
  showInsertStatus: true // 显示新增状态
}

const checkboxConfig = {
  range: true
}
const columnConfig = {
  resizable: true //是否可拖动宽度
  // isCurrent : true, //是否高亮当前行
  // drag: true //拖拽排序
}

const rowConfig = {
  useKey: true,
  isHover: true
}

const toolbarConfig = {
  custom: true,
  immediate: true,
  slots: {
    buttons: 'toolbar_buttons'
  }
}

// 自定义列配置
const customConfig = ref({
  storage: {
    visible: true,
    resizable: true,
    sort: true,
    fixed: true
  },
  resetButtonText: '撤销', // 恢复默认按钮文本
  checkMethod: ({ column }) => {
    // 可以控制哪些列允许自定义显示/隐藏
    return !['checkbox', 'seq'].includes(column.type)
  }
})

// 虚拟滚动配置
const scrollYConfig = computed(() => {
  if (props.enableVirtualScroll && tableData.value.length > 100) {
    return {
      enabled: true,
      gt: 100, // 当数据超过100条时启用虚拟滚动
      oSize: 5 // 预渲染行数
    }
  }
  return {}
})

// 分页配置
const pagerConfig = computed(() => {
  if (props.enableLoadMore) {
    return false // 禁用分页，使用触底加载
  }
  return {
    enabled: true,
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    total: props.total || tableData.value.length,
    pageSizes: [50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'Total']
  }
})

// 编辑关闭后更新数据
const handleEditClosed = ({ row }) => {
  // 设置loading状态
  tableLoading.value = true

  try {
    // 保存当前滚动位置
    saveScrollPosition()
    emit('update:list', tableData.value, row)
  } finally {
    // 延迟关闭loading
    setTimeout(() => {
      tableLoading.value = false
    }, 300)
  }
}

// 判断行是否已审核通过
const isRowApproved = (row: any) => {
  // 检查行中是否有任何值为"已通过"，表示该行已审核通过
  return Object.values(row).some(value => value === "已通过")
}

// 判断行是否处于编辑状态
const isRowEditing = (row: any) => {
  const rowId = row.id || row._X_ROW_KEY
  return editingRowId.value == rowId
}

// 开始编辑行
const startEdit = async (row: any) => {
  const $grid = gridRef.value
  if ($grid) {
    try {
      // 先清除其他行的编辑状态
      await $grid.clearEdit()
      // 使用setEditRow激活编辑状态
      await $grid.setEditRow(row)

      // 记录当前编辑的行ID
      const rowId = row.id || row._X_ROW_KEY
      editingRowId.value = rowId
    } catch (error) {
      console.error('开始编辑失败:', error)
    }
  }
}

// 保存编辑
const saveEdit = async (row: any) => {
  const $grid = gridRef.value
  if ($grid) {
    try {
      // 验证当前行数据
      const errMap = await $grid.validate(row)
      if (errMap) {
        console.error('数据验证失败:', errMap)
        return
      }

      // 设置loading状态
      // tableLoading.value = true

      // 清除编辑状态
      await $grid.clearEdit()
      editingRowId.value = null

      // 调用update接口
      emit('update:list', tableData.value, row)
    } catch (error) {
      console.error('保存编辑失败:', error)
    } finally {
      // 延迟关闭loading
      setTimeout(() => {
        tableLoading.value = false
      }, 300)
    }
  }
}

// 取消编辑
const cancelEdit = async (row: any) => {
  const $grid = gridRef.value
  if ($grid) {
    try {
      // 恢复原始数据
      await $grid.revertData(row)

      // 清除编辑状态
      await $grid.clearEdit()
      editingRowId.value = null
    } catch (error) {
      console.error('取消编辑失败:', error)
    }
  }
}

// 新增
const addEvent = async () => {
  const $grid = gridRef.value
  if ($grid) {
    // 设置loading状态
    tableLoading.value = true

    try {
      // 先清除其他行的编辑状态
      await $grid.clearEdit()
      editingRowId.value = null

      try {
        let res = await addData({ templateId: props.templateId })
        let rowData = {
          id: res.rowId,
          tableHeadId: res.tableHeadId,
          ...res.resultMap
        }

        if ($grid) {
          const newRow = await $grid.createRow(rowData)
          tableData.value.unshift(rowData)

          // 等待DOM更新后再设置编辑状态
          await nextTick()
          await $grid.setEditRow(newRow)
          // 记录当前编辑的行ID
          const rowId = newRow.id || newRow._X_ROW_KEY
          editingRowId.value = rowId
          emit('add-data')
        }
      } catch (error) {
        console.error('新增数据失败:', error)
        ElMessage.error('新增数据失败')
      } finally {
        tableLoading.value = false
      }
    } catch (error) {
      console.error('新增数据失败:', error)
      tableLoading.value = false
    }
  }
}

// 删除
const removeRow = async (row: any) => {
  // 设置loading状态
  tableLoading.value = true

  try {
    // 如果删除的是正在编辑的行，清除编辑状态
    const rowId = row.id || row._X_ROW_KEY
    if (editingRowId.value == rowId) {
      editingRowId.value = null
    }

    await gridRef.value!.remove(row)
    emit('delete', row)
    emit('update:list', tableData.value)
  } finally {
    // 延迟关闭loading
    setTimeout(() => {
      tableLoading.value = false
    }, 300)
  }
}

// 保存
const saveEvent = async () => {
  const errMap = await gridRef.value!.validate(true)
  if (errMap) return

  // 设置loading状态
  tableLoading.value = true

  try {
    // 保存当前滚动位置
    saveScrollPosition()
    const { insertRecords, updateRecords, removeRecords } = gridRef.value!.getRecordset()
    emit('save', { insertRecords, updateRecords, removeRecords })
  } finally {
    // 确保loading状态被重置
    setTimeout(() => {
      tableLoading.value = false
    }, 500) // 延迟500ms关闭loading，确保用户能看到loading效果
  }
}

// 分页切换
const handlePageChange = ({ currentPage: newCurrentPage, pageSize: newPageSize }) => {
  currentPage.value = newCurrentPage
  pageSize.value = newPageSize
  emit('update:page', { currentPage: newCurrentPage, pageSize: newPageSize })
}

// 滚动事件处理 - 触底加载更多
const handleScroll = ({ scrollTop, scrollLeft, isX, isY }) => {
  // 保存当前滚动位置
  savedScrollPosition.value = { scrollLeft, scrollTop }
  // 处理横向滚动时的表头同步问题
  if (isX) {
    const grid = gridRef.value
    if (grid) {
      // 强制刷新表头同步，解决shift+滚轮导致的表头数据分离问题
      nextTick(() => {
        try {
          grid.recalculate()
        } catch (error) {
          console.warn('表头同步刷新失败:', error)
        }
      })
    }
  }

  if (!props.enableLoadMore || isLoadingMore.value || !hasMore.value) {
    return
  }
  const grid = gridRef.value
  if (grid && isY) {
    const { scrollYLoad } = grid.getScroll()
    // 当滚动到底部附近时触发加载更多
    if (
      scrollYLoad &&
      scrollYLoad.visibleSize + scrollYLoad.startIndex >= tableData.value.length - 10
    ) {
      loadMore()
    }
  }
}

// 保存滚动位置
const saveScrollPosition = () => {
  const grid = gridRef.value
  if (grid) {
    const scrollInfo = grid.getScroll()
    if (scrollInfo) {
      savedScrollPosition.value = {
        scrollLeft: scrollInfo.scrollLeft || 0,
        scrollTop: scrollInfo.scrollTop || 0
      }
    }
  }
}

// 恢复滚动位置
const restoreScrollPosition = () => {
  const grid = gridRef.value
  if (
    grid &&
    (savedScrollPosition.value.scrollLeft > 0 || savedScrollPosition.value.scrollTop > 0)
  ) {
    try {
      // 延迟恢复滚动位置，确保表格布局已完成
      setTimeout(() => {
        grid.scrollTo(savedScrollPosition.value.scrollLeft, savedScrollPosition.value.scrollTop)

        // 手动同步表头滚动位置
        const headerWrapper = grid.$el.querySelector('.vxe-table--header-wrapper')
        if (headerWrapper) {
          headerWrapper.scrollLeft = savedScrollPosition.value.scrollLeft
        }
      }, 100) // 增加延迟时间确保DOM更新完成
    } catch (error) {
      console.warn('恢复滚动位置失败:', error)
    }
  }
}

// 加载更多数据
const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) {
    return
  }

  isLoadingMore.value = true
  tableLoading.value = true // 设置表格loading状态

  try {
    emit('load-more', {
      currentPage: currentPage.value + 1,
      pageSize: pageSize.value
    })
  } catch (error) {
    console.error('加载更多数据失败:', error)
  } finally {
    isLoadingMore.value = false
    setTimeout(() => {
      tableLoading.value = false
    }, 300)
  }
}

// 设置是否还有更多数据
const setHasMore = (value: boolean) => {
  hasMore.value = value
}

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1
  hasMore.value = true
  isLoadingMore.value = false
}

// 多选处理
const handleSelectAll = () => {
  selectedRows.value = gridRef.value!.getCheckboxRecords()
  emit('select-all', selectedRows.value)
}
const handleSelectRow = () => {
  selectedRows.value = gridRef.value!.getCheckboxRecords()
  emit('select-change', selectedRows.value)
}

// 内部默认配置
const defaultToolbarButtons = [
  { component: 'vxe-button', props: { icon: 'vxe-icon-add', content: '新增' }, handler: addEvent },
  { component: 'vxe-button', props: { icon: 'vxe-icon-save', content: '保存' }, handler: saveEvent }
]

// 使用外部传入的 toolbar 按钮或使用默认按钮
const customToolbarButtons = computed(() => {
  if (props.toolbarButtons && props.toolbarButtons.length) {
    // 自定义按钮处理，这里处理了默认的add方法
    props.toolbarButtons.forEach((item) => {
      if (typeof item.handler == 'string') {
        if (item.handler == 'addEvent') {
          item.handler = addEvent
        }
      }
    })
  }
  return props.toolbarButtons || defaultToolbarButtons
})

// renderVisible 控制是否显示表头
// fixed浮动
// 处理自定义列配置变化
const handleCustomChange = async (params) => {
  if (params && params.type == 'confirm') {
    // 保存表头配置到后端
    if (props.templateId) {
      try {
        // 立即设置loading状态
        tableLoading.value = true

        const columns = params.$grid.getTableColumn()
        const headSettings: ToolsTableHeadSettingSaveReqVO[] = []

        // 遍历所有列，构建保存数据
        columns.collectColumn.forEach((column, index) => {
          // 跳过操作列和复选框列
          if (column.field == 'action' || column.field == 'checkbox') {
            return
          }
          const setting: ToolsTableHeadSettingSaveReqVO = {
            fieldId: column.field, // 字段ID
            templateId: props.templateId!,
            visible: column.visible !== false, // 是否显示
            sort: index // 排序
          }

          // 如果有固定列设置
          if (column.fixed) {
            setting.fixed = column.fixed
          }

          // 如果原始列数据中有id，说明是已存在的配置
          const originalColumn = props.columns.find((col) => col.field == column.field)
          if (originalColumn && originalColumn.id) {
            setting.id = originalColumn.id
          }

          headSettings.push(setting)
        })

        // 调用保存接口
        await saveHeadSetting(headSettings)
        console.log('表头配置保存成功')

        // 发送事件给父组件，通知重新获取数据
        emit('custom-change', 'confirm', params)
      } catch (error) {
        console.error('保存表头配置失败:', error)
        ElMessage.error('保存表头配置失败')
        // 保存失败时也要关闭loading
        tableLoading.value = false
      }
    }

    // 列配置变化后，需要重新同步表头滚动
    nextTick(() => {
      tableKey.value++ // 强制重新渲染表格
      syncHeaderScroll()
    })
  }
}

// 获取当前列配置
const getCustomConfig = () => {
  const $grid = gridRef.value
  if ($grid) {
    return $grid.getCustomConfig()
  }
  return null
}

// 设置列配置
const setCustomConfig = (config) => {
  const $grid = gridRef.value
  if ($grid) {
    return $grid.setCustomConfig(config)
  }
}

// 添加表头滚动同步引用
const headerRef = ref(null)
const bodyRef = ref(null)

// 表头滚动处理
const handleHeaderScroll = (event) => {
  const $grid = gridRef.value
  if (!$grid) return

  const bodyWrapper = $grid.$el.querySelector('.vxe-table--body-wrapper')
  if (bodyWrapper) {
    bodyWrapper.scrollLeft = event.target.scrollLeft
  }
}

// 表体滚动处理
const handleBodyScroll = (event) => {
  const $grid = gridRef.value
  if (!$grid) return

  const headerWrapper = $grid.$el.querySelector('.vxe-table--header-wrapper')
  if (headerWrapper) {
    headerWrapper.scrollLeft = event.target.scrollLeft
  }
}

// 添加表头滚动同步方法
const syncHeaderScroll = () => {
  nextTick(() => {
    const $grid = gridRef.value
    if (!$grid) return

    // 获取表头和表体的DOM元素
    const headerWrapper = $grid.$el.querySelector('.vxe-table--header-wrapper')
    const bodyWrapper = $grid.$el.querySelector('.vxe-table--body-wrapper')

    if (headerWrapper && bodyWrapper) {
      // 解除之前的监听器
      headerWrapper.removeEventListener('scroll', handleHeaderScroll)
      bodyWrapper.removeEventListener('scroll', handleBodyScroll)

      // 重新绑定监听器
      headerWrapper.addEventListener('scroll', handleHeaderScroll)
      bodyWrapper.addEventListener('scroll', handleBodyScroll)
    }
  })
}

// 组件挂载时同步滚动
onMounted(() => {
  nextTick(() => {
    syncHeaderScroll()
  })
})

// 组件卸载时移除监听器
onUnmounted(() => {
  const $grid = gridRef.value
  if (!$grid) return

  const headerWrapper = $grid.$el.querySelector('.vxe-table--header-wrapper')
  const bodyWrapper = $grid.$el.querySelector('.vxe-table--body-wrapper')

  if (headerWrapper) {
    headerWrapper.removeEventListener('scroll', handleHeaderScroll)
  }

  if (bodyWrapper) {
    bodyWrapper.removeEventListener('scroll', handleBodyScroll)
  }
})

// 同步数据
watch(
  () => props.list,
  (newVal) => {
    if (newVal) {
      // 设置loading状态
      tableLoading.value = true

      tableData.value = newVal
      // 数据刷新时清除编辑状态
      editingRowId.value = null
      // 数据刷新时
      tableKey.value++ // 强制重新渲染表格

      // 数据更新后恢复滚动位置
      nextTick(() => {
        restoreScrollPosition()
        // 添加表头滚动同步
        syncHeaderScroll()

        // 延迟关闭loading，确保用户能看到数据更新效果
        setTimeout(() => {
          tableLoading.value = false
        }, 400)
      })
    }
  }
)

defineExpose({
  gridRef,
  setHasMore,
  resetPagination,
  loadMore,
  getCustomConfig,
  setCustomConfig,
  handleCustomChange,
  startEdit,
  saveEdit,
  cancelEdit,
  isRowEditing,
  addEvent,
  tableLoading
})
</script>

<style scoped>
.save-status-tag {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  backdrop-filter: blur(4px);
}

/* 自定义表头样式 */
.custom-header {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0 8px;
  overflow: hidden;
  line-height: 1.2;
  white-space: nowrap;
  box-sizing: border-box;
  flex-direction: column;
  align-items: center;
}

.header-title-row {
  display: flex;
  width: 100%;
  white-space: nowrap;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.header-title {
  overflow: hidden;
  font-weight: bold;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
}

.header-edit-icon {
  font-size: 12px;
  color: #409eff;
  opacity: 0.8;
  flex-shrink: 0;
}

.header-unit {
  width: 100%;
  margin-top: 2px;
  overflow: hidden;
  font-size: 12px;
  line-height: 1;
  color: #999;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.save-status-enter-active,
.save-status-leave-active {
  transition: all 0.3s ease;
}

.save-status-enter-from,
.save-status-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
