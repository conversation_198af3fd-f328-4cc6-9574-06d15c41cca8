<template>
  <el-dialog
    v-model="visible"
    title="执行结果"
    width="30%"
    append-to-body
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div class="result-container">
      <!-- 成功部分 -->
      <div
        v-if="
          result.successCampaigns?.length > 0 ||
          result.successKeywords?.length > 0
        "
        class="success-section"
      >
        <div class="section-title success-title">
          <el-icon class="success-icon"><SuccessFilled /></el-icon>
          <span>执行成功</span>
        </div>

        <div v-if="result.successCampaigns?.length > 0" class="success-item">
          <div class="item-label"
            >成功调整的广告活动 ({{ result.successCampaigns.length }}个)：</div
          >
          <div class="item-content">
            <el-tag
              v-for="campaign in result.successCampaigns"
              :key="campaign"
              type="success"
              class="tag-item"
            >
              {{ campaign }}
            </el-tag>
          </div>
        </div>

        <div v-if="result.successKeywords?.length > 0" class="success-item">
          <div class="item-label"
            >成功调整的关键词 ({{ result.successKeywords.length }}个)：</div
          >
          <div class="item-content">
            <el-tag
              v-for="keyword in result.successKeywords"
              :key="keyword"
              type="success"
              class="tag-item"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 失败部分 -->
      <div
        v-if="
          result.failCampaigns?.length > 0 || result.failKeywords?.length > 0
        "
        class="fail-section"
      >
        <div class="section-title fail-title">
          <el-icon class="fail-icon"><CircleCloseFilled /></el-icon>
          <span>执行失败</span>
        </div>

        <div v-if="result.failCampaigns?.length > 0" class="fail-item">
          <div class="item-label"
            >失败的广告活动 ({{ result.failCampaigns.length }}个)：</div
          >
          <div class="fail-list">
            <div
              v-for="campaign in result.failCampaigns"
              :key="campaign.name"
              class="fail-item-detail"
            >
              <div class="fail-name">
                <el-tag type="danger" size="small">{{ campaign.name }}</el-tag>
              </div>
              <div class="fail-reason">失败原因：{{ campaign.description }}</div>
            </div>
          </div>
        </div>

        <div v-if="result.failKeywords?.length > 0" class="fail-item">
          <div class="item-label"
            >失败的关键词 ({{ result.failKeywords.length }}个)：</div
          >
          <div class="fail-list">
            <div
              v-for="keyword in result.failKeywords"
              :key="keyword.name"
              class="fail-item-detail"
            >
              <div class="fail-name">
                <el-tag type="danger" size="small">{{ keyword.name }}</el-tag>
              </div>
              <div class="fail-reason">失败原因：{{ keyword.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无结果时的提示 -->
      <div
        v-if="
          !result.successCampaigns?.length &&
          !result.successKeywords?.length &&
          !result.failCampaigns?.length &&
          !result.failKeywords?.length
        "
        class="no-result"
      >
        <el-icon class="no-result-icon"><InfoFilled /></el-icon>
        <span>执行完成，无具体结果返回</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { CircleCloseFilled, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'
import { ref, watch } from 'vue'

interface ExecutionResult {
  successCampaigns?: string[]
  successKeywords?: string[]
  failCampaigns?: Array<{ name: string; description: string }>
  failKeywords?: Array<{ name: string; description: string }>
}

interface Props {
  modelValue: boolean
  result?: ExecutionResult
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'closed'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  result: () => ({})
})

const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)

watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

const handleClosed = () => {
  emit('closed')
}
</script>

<style lang="scss" scoped>
.result-container {
  max-height: 500px;
  overflow-y: auto;
}

.success-section,
.fail-section {
  padding: 16px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.success-section {
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
}

.fail-section {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
}

.section-title {
  display: flex;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  align-items: center;
}

.success-title {
  color: #059669;
}

.fail-title {
  color: #dc2626;
}

.success-icon,
.fail-icon {
  margin-right: 8px;
  font-size: 18px;
}

.success-item,
.fail-item {
  margin-bottom: 12px;
}

.item-label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.item-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 2px;
}

.fail-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.fail-item-detail {
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.fail-name {
  margin-bottom: 6px;
}

.fail-reason {
  font-size: 14px;
  line-height: 1.4;
  color: #6b7280;
  word-break: break-all;
}

.no-result {
  display: flex;
  padding: 40px;
  font-size: 16px;
  color: #6b7280;
  align-items: center;
  justify-content: center;
}

.no-result-icon {
  margin-right: 8px;
  font-size: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>