<template>
  <div class="login-carousel-container" :style="containerStyle">
    <TransitionGroup
      v-if="showTransition"
      appear
      :enter-active-class="transitionClass"
      tag="div"
      :style="{ width: width + 'px' }"
    >
      <el-carousel :height="height + 'px'" :width="width + 'px'" :interval="interval">
        <el-carousel-item v-for="(item, index) in images" :key="item">
          <img alt="" :class="imageClass" :src="item" @click="handleImgClick(index)" />
        </el-carousel-item>
      </el-carousel>
    </TransitionGroup>
    <el-carousel v-else :height="height + 'px'" :width="width + 'px'" :interval="interval">
      <el-carousel-item v-for="(item, index) in images" :key="item">
        <img alt="" :class="imageClass" :src="item" @click="handleImgClick(index)" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
import act2img from '@/assets/imgs/20250604-110723.jpg'
import act1img from '@/assets/imgs/20250625-144630.jpg'
import { defineEmits, defineProps } from 'vue'

const props = defineProps({
  // 容器样式
  containerStyle: {
    type: Object,
    default: () => ({})
  },
  // 是否显示过渡动画
  showTransition: {
    type: Boolean,
    default: false
  },
  // 过渡动画类名
  transitionClass: {
    type: String,
    default: 'animate__animated animate__bounceInLeft'
  },
  // 轮播图宽度
  width: {
    type: Number,
    default: 500
  },
  // 轮播图高度
  height: {
    type: Number,
    default: 500
  },
  // 轮播间隔时间（毫秒）
  interval: {
    type: Number,
    default: 6000
  },
  // 图片类名
  imageClass: {
    type: String,
    default: 'w-500px'
  },
  // 自定义图片数组，如果不传则使用默认图片
  customImages: {
    type: Array,
    default: null
  },
  // 点击图片时的回调函数
  onImageClick: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['image-click'])

// 默认图片数组
const defaultImages = [
  {
    imgUrl: act1img,
    linkUrl: ''
  },
  {
    imgUrl: act2img,
    linkUrl: ''
  }
]

// 使用自定义图片或默认图片
const images = computed(() => {
  if (props.customImages && props.customImages.length > 0) {
    return props.customImages.map((item) => item.imgUrl || item)
  }
  return defaultImages.map((item) => item.imgUrl || item)
})
// 处理图片点击事件
const handleImgClick = (index: number) => {
  // 发出点击事件
  emit('image-click', index)

  // 如果提供了回调函数，则调用它
  if (props.onImageClick) {
    props.onImageClick(index)
  }

  // 处理点击跳转逻辑
  if (props.customImages && props.customImages.length > 0 && props.customImages[index]) {
    // 使用自定义图片时，跳转到对应的linkUrl
    const linkUrl = props.customImages[index].linkUrl
    if (linkUrl) {
      window.open(linkUrl)
    }
  } else {
    // 使用默认图片时，跳转到对应的linkUrl
    const linkUrl = defaultImages[index].linkUrl
    if (linkUrl) {
      window.open(linkUrl)
    }
  }
}

// 导出组件内部图片，方便外部使用
defineExpose({
  act1img,
  act2img,
  images
})
</script>

<style scoped>
.login-carousel-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-carousel {
  width: 100%;
}

:deep(.el-carousel__item) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-carousel__item img) {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: contain;
}
</style>
