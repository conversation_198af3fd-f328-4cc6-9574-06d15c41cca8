<template>
  <div class="ml-10px mr-10px">
    <el-button @click="openMyFilter">我的筛选条件</el-button>
    <el-button @click="saveCurrentFilter">保存当前筛选</el-button>

    <el-dialog title="我的筛选条件" v-model="dialogVisible" width="50%">
      <el-table :data="filterList" style="width: 100%">
        <el-table-column prop="template" label="名称" />
        <el-table-column prop="createTime" label="保存时间" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="applyFilter(scope.row)">快速访问</el-button>
            <el-button size="small" @click="editFilter(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteFilter(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createToolsTableConditionTemplate,
  deleteToolsTableConditionTemplate,
  getToolsTableConditionTemplateList,
  updateToolsTableConditionTemplate
} from '@/api/infra/toolsTableConditionTemplate'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'

const props = defineProps({
  type: String,
  formData: Object
})

const emit = defineEmits(['update:formData'])

const dialogVisible = ref(false)
const filterList = ref([])

const openMyFilter = async () => {
  dialogVisible.value = true
  const res = await getToolsTableConditionTemplateList({ type: props.type })
  filterList.value = res
}

const saveCurrentFilter = () => {
  ElMessageBox.prompt('请输入筛选条件名称', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  })
    .then(async ({ value }) => {
      await createToolsTableConditionTemplate({
        template: value,
        type: props.type,
        content: JSON.stringify(props.formData)
      })
      ElMessage.success('保存成功')
    })
    .catch(() => {})
}

const applyFilter = (row) => {
  emit('update:formData', JSON.parse(row.content))
  dialogVisible.value = false
}

const editFilter = (row) => {
  ElMessageBox.prompt('请输入新的筛选条件名称', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: row.template
  })
    .then(async ({ value }) => {
      await updateToolsTableConditionTemplate({
        id: row.id,
        template: value,
        type: props.type,
        content: row.content
      })
      ElMessage.success('更新成功')
      openMyFilter() // Refresh list
    })
    .catch(() => {})
}

const deleteFilter = async (row) => {
  await deleteToolsTableConditionTemplate(row.id)
  ElMessage.success('删除成功')
  openMyFilter() // Refresh list
}
</script>
