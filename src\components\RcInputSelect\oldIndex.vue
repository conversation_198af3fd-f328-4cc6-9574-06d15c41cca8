<template>
  <el-cascader
    v-if="props.isTree"
    ref="cascaderRef"
    v-model="selectedValues"
    :options="treeOptions"
    :props="cascaderProps"
    clearable
    collapse-tags
    collapse-tags-tooltip
    :filterable="true"
    :filter-method="filterNode"
    placeholder="请选择"
    class="rc-input-select"
    @change="handleChange"
    @clear="handleClear"
    @remove-tag="handleRemoveTag"
    @visible-change="handleVisibleChange"
  />
  <el-select
    v-else
    :model-value="props.modelValue"
    multiple
    clearable
    collapse-tags
    collapse-tags-tooltip
    :filterable="false"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    ref="selectRef"
    popper-class="rc-input-select"
    class="rc-input-select"
  >
    <!-- 筛选输入框 -->
    <div class="filter-section" v-if="props.options.length > 0">
      <el-input
        v-model="searchQuery"
        placeholder="输入关键词筛选"
        clearable
        @input="handleFilter"
      />
    </div>

    <!-- 全选选项 -->
    <el-option class="select-all-option" v-if="props.options.length > 0">
      <div class="option-container">
        <el-checkbox
          :model-value="allSelected"
          @change="handleSelectAll"
          label="全选"
          value="all"
        />
      </div>
    </el-option>

    <!-- 常规选项 -->
    <el-option
      v-for="item in filteredOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      @mouseenter="handleOptionMouseEnter(item.value)"
      @mouseleave="handleOptionMouseLeave"
    >
      <div class="option-container">
        <el-checkbox
          :model-value="tempSelected.includes(item.value)"
          :label="item.label"
          @click.stop="toggleOption(item.value)"
        />
        <div
          v-if="showFilterTag === item.value"
          class="filter-only-tag"
          @click.stop="filterOnly(item)"
        >
          仅筛选此项
        </div>
      </div>
    </el-option>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button size="small" @click="handleCancel">取消</el-button>
      <el-button type="primary" size="small" @click="handleConfirm">确定</el-button>
    </div>
  </el-select>
</template>

<script setup lang="ts">
import type { ElSelect } from 'element-plus'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

interface Option {
  value: string | number
  label: string
  country?: string
  children?: Option[]
  parentId?: string | number
}

const props = defineProps<{
  options: Option[]
  modelValue: any[]
  countryFilter?: string
  isTree?: boolean
  checkStrictly?: boolean // 是否严格模式（不联动父子）
}>()

const emit = defineEmits(['update:modelValue', 'change'])

const selectRef = ref<InstanceType<typeof ElSelect>>()
const cascaderRef = ref()
const searchQuery = ref('')
const tempSelected = ref<(string | number)[]>([])
const showFilterTag = ref<string | number | null>(null)

// 级联选择器配置
const cascaderProps = computed(() => ({
  multiple: true,
  checkStrictly: props.checkStrictly,
  emitPath: false,
  expandTrigger: 'click',
  lazy: false,
  lazyLoad: undefined
}))

// 树形结构数据
const treeOptions = computed(() => {
  if (!props.isTree) return props.options
  return props.countryFilter
    ? props.options.filter((opt) => opt.country === props.countryFilter)
    : props.options
})

// 级联选择器选中值
const selectedValues = computed({
  get: () => props.modelValue,
  set: (val) => {
    // 不在这里直接emit，而是通过handleChange统一处理
    // 这样避免重复触发接口调用
  }
})

// 级联选择器过滤方法
const filterNode = (node: Option, keyword: string) => {
  if (!keyword) return true
  // 检查当前节点的标签
  if (node.label.toLowerCase().includes(keyword.toLowerCase())) {
    return true
  }

  // 如果有子节点，递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some((child) => {
      return filterNode(child, keyword)
    })
  }
  return false
}

// 处理级联选择器的父子级联逻辑
const handleCascaderSelection = (
  newValue: (string | number)[],
  oldValue: (string | number)[] = props.modelValue
) => {
  const added = newValue.filter((v) => !oldValue.includes(v))
  const removed = oldValue.filter((v) => !newValue.includes(v))

  let result = [...newValue]

  // 处理新增的选项
  added.forEach((value) => {
    // 选中父节点时，自动选中所有子节点
    const childrenValues = getAllChildrenValues(props.options, value)
    childrenValues.forEach((childValue) => {
      if (!result.includes(childValue)) {
        result.push(childValue)
      }
    })
  })

  // 处理移除的选项
  removed.forEach((value) => {
    // 取消选中父节点时，自动取消选中所有子节点
    const childrenValues = getAllChildrenValues(props.options, value)
    result = result.filter((v) => v !== value && !childrenValues.includes(v))
  })

  // 获取所有节点，用于全面检查父子关系
  const getAllNodes = (options: Option[]): Option[] => {
    const nodes: Option[] = []
    const traverse = (opts: Option[]) => {
      opts.forEach((opt) => {
        nodes.push(opt)
        if (opt.children && opt.children.length > 0) {
          traverse(opt.children)
        }
      })
    }
    traverse(options)
    return nodes
  }

  // 简化的父子关系检查，只在严格模式下进行基本的联动
  if (props.checkStrictly) {
    const allNodes = getAllNodes(props.options)

    // 检查父节点状态
    allNodes.forEach((node) => {
      const nodeValue = node.value
      const childrenValues = getAllChildrenValues(props.options, nodeValue)

      if (childrenValues.length > 0) {
        const allChildrenSelected = childrenValues.every((child) => result.includes(child))
        const isParentSelected = result.includes(nodeValue)

        // 如果所有子节点都选中，确保父节点也选中
        if (allChildrenSelected && !isParentSelected) {
          result.push(nodeValue)
        }
        // 如果没有子节点选中，确保父节点也不选中
        else if (!childrenValues.some((child) => result.includes(child)) && isParentSelected) {
          result = result.filter((v) => v !== nodeValue)
        }
      }
    })
  }

  return result
}

// 级联选择器变更事件
const handleChange = (value: (string | number)[]) => {
  if (props.checkStrictly) {
    // 严格模式下，需要自定义父子级联逻辑
    const newSelected = handleCascaderSelection(value)
    emit('update:modelValue', newSelected)
    emit('change', newSelected)
  } else {
    // 非严格模式，直接使用原生级联逻辑
    emit('update:modelValue', value)
    emit('change', value)
  }
  // 修复：当父节点选中后，子菜单未及时更新
  if (props.isTree && cascaderRef.value) {
    nextTick(() => {
      cascaderRef.value.panel?.syncMenuState()
    })
  }
}

// 全局变量用于清理
let globalObserver: MutationObserver | null = null
let globalAddedListeners = new Set<HTMLElement>()

// 添加点击事件监听器，实现点击标题区域选中功能
onMounted(() => {
  if (props.isTree && props.checkStrictly) {
    nextTick(() => {
      const handleLabelClick = (event: Event) => {
        const target = event.target as HTMLElement
        const node = target.closest('.el-cascader-node')
        if (node) {
          const checkbox = node.querySelector('.el-checkbox__original') as HTMLInputElement
          if (checkbox && !target.closest('.el-checkbox')) {
            checkbox.click()
          }
        }
      }

      const addClickListeners = () => {
        const cascaderPanel = document.querySelector('.el-cascader-panel')
        if (cascaderPanel) {
          const labels = cascaderPanel.querySelectorAll('.el-cascader-node__label')
          labels.forEach((label: Element) => {
            const labelElement = label as HTMLElement
            if (!globalAddedListeners.has(labelElement)) {
              labelElement.addEventListener('click', handleLabelClick)
              globalAddedListeners.add(labelElement)
            }
          })
        }
      }

      // 初始添加监听器
      addClickListeners()

      // 监听DOM变化，为动态加载的节点添加监听器
      globalObserver = new MutationObserver(() => {
        addClickListeners()
      })

      const cascaderPanel = document.querySelector('.el-cascader-panel')
      if (cascaderPanel) {
        globalObserver.observe(cascaderPanel, {
          childList: true,
          subtree: true
        })
      }
    })
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (globalObserver) {
    globalObserver.disconnect()
    globalObserver = null
  }
  globalAddedListeners.clear()
})

// 初始化临时选择
watch(
  () => props.modelValue,
  (newVal) => {
    tempSelected.value = [...newVal]
  },
  { immediate: true }
)

// 计算属性
const allSelected = computed(() => {
  return (
    filteredOptions.value.length > 0 &&
    filteredOptions.value.every((opt) => tempSelected.value.includes(opt.value))
  )
})

// 修复显示true/false的bug，使用计算属性处理显示值
const displayValue = computed({
  get: () => {
    // 始终返回选中的值数组，而不是布尔值
    return tempSelected.value
  },
  set: (val) => {
    // 确保val是数组
    if (Array.isArray(val)) {
      tempSelected.value = [...val]
    }
    // 如果不是数组，则保持原值不变
  }
})

// 处理树形结构数据，将树形结构扁平化
const flattenTree = (tree: Option[]): Option[] => {
  const result: Option[] = []
  const traverse = (nodes: Option[]) => {
    nodes.forEach((node) => {
      result.push(node)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(tree)
  return result
}

// 获取所有子节点的值
const getAllChildrenValues = (
  options: Option[],
  parentValue: string | number
): (string | number)[] => {
  const values: (string | number)[] = []
  const findChildren = (opts: Option[], pValue: string | number) => {
    opts.forEach((opt) => {
      if (props.isTree && opt.children) {
        // 树形结构，通过children查找
        if (opt.value === pValue) {
          opt.children.forEach((child) => {
            values.push(child.value)
            if (child.children && child.children.length > 0) {
              findChildren(child.children, child.value)
            }
          })
        } else if (opt.children.length > 0) {
          findChildren(opt.children, pValue)
        }
      } else if (!props.isTree) {
        // 扁平结构，通过parentId查找
        if (opt.parentId === pValue) {
          values.push(opt.value)
          // 递归查找子节点的子节点
          findChildren(props.options, opt.value)
        }
      }
    })
  }
  findChildren(options, parentValue)
  return values
}

// 获取所有父节点的值
const getAllParentValues = (
  options: Option[],
  childValue: string | number
): (string | number)[] => {
  const values: (string | number)[] = []
  const findParent = (opts: Option[], cValue: string | number) => {
    for (const opt of opts) {
      if (props.isTree && opt.children) {
        // 树形结构，通过children查找
        for (const child of opt.children) {
          if (child.value === cValue) {
            values.push(opt.value)
            // 递归查找父节点的父节点
            findParent(props.options, opt.value)
            return
          }
        }
        // 如果当前层级没找到，继续在子节点中查找
        if (opt.children.length > 0) {
          findParent(opt.children, cValue)
        }
      } else if (!props.isTree) {
        // 扁平结构，通过parentId查找
        if (opt.value === cValue && opt.parentId) {
          values.push(opt.parentId)
          // 递归查找父节点的父节点
          findParent(props.options, opt.parentId)
        }
      }
    }
  }
  findParent(options, childValue)
  return values
}

const filteredOptions = computed(() => {
  let options = props.isTree ? flattenTree(props.options) : props.options
  return options.filter((option) => {
    const matchesSearch = option.label.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesCountry = !props.countryFilter || option.country === props.countryFilter
    return matchesSearch && matchesCountry
  })
})

// 方法
const handleFilter = () => {
  // 自动触发computed更新
}

const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // 全选：添加所有过滤后的选项
    const newValues = [...tempSelected.value]
    filteredOptions.value.forEach((opt) => {
      if (!newValues.includes(opt.value)) {
        newValues.push(opt.value)

        // 如果启用了级联选择，同时选择所有子节点
        if (props.isTree || props.options.some((o) => o.parentId)) {
          const childrenValues = getAllChildrenValues(props.options, opt.value)
          childrenValues.forEach((v) => {
            if (!newValues.includes(v)) {
              newValues.push(v)
            }
          })
        }
      }
    })
    tempSelected.value = newValues
  } else {
    // 取消全选：移除所有过滤后的选项及其子项
    let newValues = [...tempSelected.value]
    filteredOptions.value.forEach((opt) => {
      // 移除当前项
      newValues = newValues.filter((v) => v !== opt.value)

      // 如果启用了级联选择，同时取消选择所有子节点
      if (props.isTree || props.options.some((o) => o.parentId)) {
        const childrenValues = getAllChildrenValues(props.options, opt.value)
        newValues = newValues.filter((v) => !childrenValues.includes(v))
      }
    })
    tempSelected.value = newValues
  }
}

const toggleOption = (value: string | number) => {
  const isSelected = tempSelected.value.includes(value)
  let newSelected = [...tempSelected.value]

  if (isSelected) {
    // 取消选择当前项
    newSelected = newSelected.filter((v) => v !== value)

    // 如果启用了级联选择，同时取消选择所有子节点
    if (props.isTree || props.options.some((o) => o.parentId)) {
      const childrenValues = getAllChildrenValues(props.options, value)
      newSelected = newSelected.filter((v) => !childrenValues.includes(v))
    }
  } else {
    // 选择当前项
    newSelected.push(value)

    // 如果启用了级联选择，同时选择所有子节点
    if (props.isTree || props.options.some((o) => o.parentId)) {
      const childrenValues = getAllChildrenValues(props.options, value)
      childrenValues.forEach((v) => {
        if (!newSelected.includes(v)) {
          newSelected.push(v)
        }
      })

      // 同时选择所有父节点
      const parentValues = getAllParentValues(props.options, value)
      parentValues.forEach((v) => {
        if (!newSelected.includes(v)) {
          newSelected.push(v)
        }
      })
    }
  }

  tempSelected.value = newSelected
}

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    searchQuery.value = ''
    showFilterTag.value = null

    // 当级联选择器关闭时，重置其内部状态以确保下次打开时正确显示
    if (props.isTree && cascaderRef.value) {
      nextTick(() => {
        // 通过重新设置值来刷新级联选择器的内部状态
        const currentValue = cascaderRef.value.getCheckedNodes()
        if (currentValue) {
          cascaderRef.value.setCheckedNodes([])
          nextTick(() => {
            cascaderRef.value.setCheckedNodes(currentValue)
          })
        }
      })
    }
  }
}

const handleRemoveTag = (value: string | number) => {
  if (props.isTree) {
    // 级联选择器已经处理了移除标签的逻辑
    return
  }

  // 从临时选择中移除该值
  let newSelected = tempSelected.value.filter((v) => v !== value)

  // 如果启用了级联选择，同时移除所有子节点
  if (props.isTree || props.options.some((o) => o.parentId)) {
    const childrenValues = getAllChildrenValues(props.options, value)
    newSelected = newSelected.filter((v) => !childrenValues.includes(v))
  }

  // 更新临时选择
  tempSelected.value = newSelected

  // 更新modelValue并触发change事件
  emit('update:modelValue', tempSelected.value)
  emit('change', tempSelected.value)
}

const handleClear = () => {
  tempSelected.value = []
  emit('update:modelValue', tempSelected.value)
  emit('change', tempSelected.value)
}

const handleOptionMouseEnter = (value: string | number) => {
  showFilterTag.value = value
}

const handleOptionMouseLeave = () => {
  showFilterTag.value = null
}

const filterOnly = (item: Option) => {
  // 选中当前项及其所有子级，清空其他选择
  const value = item.value
  let newSelected = [] // 清空之前的选择

  // 添加当前项
  newSelected.push(value)

  // 添加所有子项
  const childrenValues = getAllChildrenValues(props.options, value)
  childrenValues.forEach((v) => {
    if (!newSelected.includes(v)) {
      newSelected.push(v)
    }
  })

  tempSelected.value = newSelected

  // 设置筛选条件并关闭标签
  searchQuery.value = item.label
  showFilterTag.value = null

  // 确认选择并关闭下拉框
  handleConfirm()
}

const handleConfirm = () => {
  // 更新modelValue并触发change事件
  emit('update:modelValue', tempSelected.value)
  emit('change', tempSelected.value)
  selectRef.value?.blur()
}

const handleCancel = () => {
  tempSelected.value = [...props.modelValue]
  selectRef.value?.blur()
}
</script>

<style lang="scss" scoped>
.filter-section {
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 8px 12px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.select-all-option {
  /* position: sticky;
  top: 40px;
  z-index: 4;
  width: 100%;
  height: 40px;
  background: white; */
}

.action-buttons {
  position: sticky;
  bottom: 0;
  z-index: 2;
  display: flex;
  padding: 8px 12px;
  background: white;
  border-top: 1px solid #ebeef5;
  justify-content: flex-end;
  gap: 8px;
}

.el-checkbox {
  flex: 1;
  padding: 8px 12px;
}

.el-select-dropdown__item {
  padding: 0 !important;
}

.option-container {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  padding: 0 8px;
}

.filter-only-tag {
  position: absolute;
  top: 50%;
  right: 2px;
  z-index: 1;
  padding: 2px 6px;
  font-size: 12px;
  color: #409eff;
  white-space: nowrap;
  cursor: pointer;
  background-color: #f0f9ff;
  border: 1px solid #d9ecff;
  border-radius: 4px;
  transform: translateY(-50%);
}

.filter-only-tag:hover {
  background-color: #ecf5ff;
}

/* 修复选中和取消右侧都会有个勾选的标志 */
:deep(.el-select-dropdown__item.selected) {
  font-weight: normal;
}

:deep(.el-select-dropdown__item.selected::after) {
  content: none !important;
}

/* 级联选择器样式 */
.rc-input-select {
  width: 100%;
}

/* 级联选择器样式优化 - 让整个标题区域可点击 */
:deep(.el-cascader-panel .el-cascader-node) {
  cursor: pointer;
}

:deep(.el-cascader-panel .el-cascader-node__label) {
  position: relative;
  padding: 8px 12px;
  margin: 0;
  cursor: pointer;
  user-select: none;
  flex: 1;
}

:deep(.el-cascader-panel .el-cascader-node__label:hover) {
  background-color: #f5f7fa;
}

/* 让标题文字点击时也能触发选择 */
:deep(.el-cascader-panel .el-cascader-node__content) {
  display: flex;
  width: 100%;
  align-items: center;
}

:deep(.el-cascader-panel .el-checkbox) {
  margin-right: 8px;
}

/* 确保复选框区域可以正常点击 */
:deep(.el-cascader-panel .el-checkbox__input) {
  position: relative;
  z-index: 2;
}
</style>
