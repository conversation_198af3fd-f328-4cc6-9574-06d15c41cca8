# RcSelectInput 组件

一个集成了下拉选择器和多功能输入框的组合组件，将 `el-select` 和 `TipInput` 的功能整合在一起。

## 特性

- 🔄 **双向绑定**: 支持选择器和输入框的独立双向绑定
- 🎨 **样式统一**: 采用领星 search-last-item 样式，视觉上无缝连接
- ⚙️ **高度可配置**: 支持自定义选项、样式类、占位文本等
- 📝 **多行输入**: 内置多行输入弹窗，支持批量数据处理
- 🔍 **智能去重**: 自动去除重复项，提升数据质量
- 🎯 **事件丰富**: 提供完整的事件回调机制

## 基础用法

```vue
<template>
  <RcSelectInput
    v-model:select-value="queryParams.skuType"
    v-model:input-value="queryParams.skuValue"
    @on-btn="handleQuery"
  />
</template>

<script setup>
const queryParams = ref({
  skuType: '',
  skuValue: ''
})

const handleQuery = (values) => {
  console.log('查询值:', values)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| selectValue | 选择器的值 | String | '' |
| inputValue | 输入框的值 | String | '' |
| selectOptions | 选择器选项 | Array<{label: string, value: string}> | 默认SKU选项 |
| selectClass | 选择器类名 | String | '!w-90px' |
| inputClass | 输入框类名 | String | '!w-150px' |
| selectPlaceholder | 选择器占位文本 | String | '请选择' |
| inputPlaceholder | 输入框占位文本 | String | '' |
| clearable | 是否可清空 | Boolean | true |
| tooltipIcon | 提示图标 | String | 'fa-solid:bars' |
| tooltipContent | 提示内容 | String | '多项精确搜索' |
| tooltipPlacement | 提示位置 | String | 'bottom' |
| rawContent | 是否使用原始HTML内容 | Boolean | false |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:selectValue | 选择器值更新 | (value: string) |
| update:inputValue | 输入框值更新 | (value: string) |
| selectChange | 选择器值改变 | (value: string) |
| inputChange | 输入框值改变 | (value: string) |
| enter | 回车或输入完成 | (values: string[]) |
| onBtn | 点击多行输入按钮 | (values: string[]) |

## 默认选项

组件默认提供以下选择器选项：

```javascript
[
  { label: 'ASIN', value: 'ASIN' },
  { label: 'SKU', value: 'SKU' },
  { label: 'MSKU', value: 'MSKU' },
  { label: 'FNSKU', value: 'FNSKU' },
  { label: '品名', value: '品名' }
]
```

## 自定义选项示例

```vue
<template>
  <RcSelectInput
    v-model:select-value="customType"
    v-model:input-value="customValue"
    :select-options="customOptions"
    select-class="!w-120px"
    input-class="!w-200px"
  />
</template>

<script setup>
const customOptions = [
  { label: '产品名称', value: 'product_name' },
  { label: '产品编码', value: 'product_code' },
  { label: '品牌', value: 'brand' }
]
</script>
```

## 样式特性

- 组件采用 `inline-flex` 布局，选择器和输入框无缝连接
- 选择器右侧圆角被移除，输入框左侧圆角被移除
- 输入框左边框被隐藏，形成视觉上的一体化效果
- 支持领星设计规范的 search-last-item 样式

## 多行输入功能

- 点击输入框右侧的图标按钮打开多行输入弹窗
- 支持一行一项的批量输入
- 自动去重处理
- 最多支持1000行输入
- 提供清空、关闭、确定操作

## 迁移指南

### 从 el-select + TipInput 迁移

**原有代码：**
```vue
<el-select v-model="queryParams.skuType" class="!w-90px" clearable placeholder="请选择">
  <el-option label="ASIN" value="ASIN" />
  <el-option label="SKU" value="SKU" />
  <el-option label="MSKU" value="MSKU" />
  <el-option label="FNSKU" value="FNSKU" />
  <el-option label="品名" value="品名" />
</el-select>
<TipInput
  v-model="queryParams.skuValue"
  inputClass="!w-150px"
  placeholder=""
  tooltipIcon="fa-solid:bars"
  @on-btn="handleSkuValueQuery"
/>
```

**迁移后：**
```vue
<RcSelectInput
  v-model:select-value="queryParams.skuType"
  v-model:input-value="queryParams.skuValue"
  @on-btn="handleSkuValueQuery"
/>
```

### 注意事项

1. **双向绑定语法变化**: 使用 `v-model:select-value` 和 `v-model:input-value`
2. **事件名保持一致**: `@on-btn` 事件保持不变
3. **默认样式**: 组件已内置默认的选项和样式，无需重复配置
4. **全局注册**: 组件已全局注册，可直接使用

## 开发说明

- 组件位置: `src/components/RcSelectInput/index.vue`
- 示例文件: `src/components/RcSelectInput/example.vue`
- 全局注册: `src/components/index.ts`
- 遵循项目开发规范和代码风格