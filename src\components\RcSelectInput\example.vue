<template>
  <div class="example-container">
    <h3>RcSelectInput 组件使用示例</h3>
    
    <div class="example-section">
      <h4>基础用法</h4>
      <el-form :model="queryParams" inline>
        <el-form-item label="" prop="skuType">
          <!-- 新的组合组件 -->
          <RcSelectInput
            v-model:select-value="queryParams.skuType"
            v-model:input-value="queryParams.skuValue"
            @on-btn="handleSkuValueQuery"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="example-section">
      <h4>自定义选项</h4>
      <el-form :model="customParams" inline>
        <el-form-item label="" prop="customType">
          <RcSelectInput
            v-model:select-value="customParams.customType"
            v-model:input-value="customParams.customValue"
            :select-options="customOptions"
            select-class="!w-120px"
            input-class="!w-200px"
            select-placeholder="选择类型"
            input-placeholder="输入内容"
            @on-btn="handleCustomQuery"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="example-section">
      <h4>原有代码对比</h4>
      <div class="code-comparison">
        <div class="old-code">
          <h5>原有代码：</h5>
          <pre><code>&lt;el-select v-model="queryParams.skuType" class="!w-90px" clearable placeholder="请选择"&gt;
  &lt;el-option label="ASIN" value="ASIN" /&gt;
  &lt;el-option label="SKU" value="SKU" /&gt;
  &lt;el-option label="MSKU" value="MSKU" /&gt;
  &lt;el-option label="FNSKU" value="FNSKU" /&gt;
  &lt;el-option label="品名" value="品名" /&gt;
&lt;/el-select&gt;
&lt;TipInput
  v-model="queryParams.skuValue"
  inputClass="!w-150px"
  placeholder=""
  tooltipIcon="fa-solid:bars"
  @on-btn="handleSkuValueQuery"
/&gt;</code></pre>
        </div>
        
        <div class="new-code">
          <h5>新组件代码：</h5>
          <pre><code>&lt;RcSelectInput
  v-model:select-value="queryParams.skuType"
  v-model:input-value="queryParams.skuValue"
  @on-btn="handleSkuValueQuery"
/&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h4>当前值</h4>
      <p>选择器值: {{ queryParams.skuType }}</p>
      <p>输入框值: {{ queryParams.skuValue }}</p>
      <p>自定义选择器值: {{ customParams.customType }}</p>
      <p>自定义输入框值: {{ customParams.customValue }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 基础用法的数据
const queryParams = ref({
  skuType: '',
  skuValue: ''
})

// 自定义选项的数据
const customParams = ref({
  customType: '',
  customValue: ''
})

// 自定义选项
const customOptions = [
  { label: '产品名称', value: 'product_name' },
  { label: '产品编码', value: 'product_code' },
  { label: '品牌', value: 'brand' },
  { label: '分类', value: 'category' }
]

// 事件处理
const handleSkuValueQuery = (values: string[]) => {
  console.log('SKU查询值:', values)
}

const handleCustomQuery = (values: string[]) => {
  console.log('自定义查询值:', values)
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.code-comparison {
  display: flex;
  gap: 20px;
}

.old-code,
.new-code {
  flex: 1;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.old-code h5,
.new-code h5 {
  margin-top: 0;
  color: #606266;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

code {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>