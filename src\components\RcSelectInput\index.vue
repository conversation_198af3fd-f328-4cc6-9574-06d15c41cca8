<template>
  <div class="rc-select-input-container">
    <el-select
      v-model="selectValue"
      :class="selectClass"
      clearable
      :placeholder="selectPlaceholder"
      @change="handleSelectChange"
    >
      <el-option
        v-for="option in selectOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
    <div class="tip-input-wrapper">
      <el-input
        v-model="inputValue"
        :class="inputClass"
        :clearable="clearable"
        :placeholder="inputPlaceholder"
        @keyup.enter="handleEnterKey"
        @clear="handleClear"
        @change="handleInputChange"
      >
        <template #append>
          <el-tooltip
            teleported
            class="box-item"
            effect="dark"
            :content="tooltipContent"
            :placement="tooltipPlacement"
            :raw-content="rawContent"
          >
            <div class="ak-advanced-input">
              <el-popover
                :visible="popoverVisible"
                :width="220"
                trigger="manual"
                placement="bottom"
                popper-class="tip-input-popover"
              >
                <template #reference>
                  <div class="ak-advanced-input" @click="openPopover">
                    <Icon :icon="tooltipIcon" />
                  </div>
                </template>
                <template #default>
                  <div class="popover-content">
                    <el-input
                      v-model="multilineInput"
                      type="textarea"
                      :rows="9"
                      placeholder="精确搜索，一行一项，最多支持1000行"
                    />
                    <div class="popover-footer">
                      <div class="popover-actions">
                        <el-button size="small" @click="clearInput">清空</el-button>
                        <div class="right-actions">
                          <el-button size="small" @click="popoverVisible = false">关闭</el-button>
                          <el-button size="small" type="primary" @click="confirmInput"
                            >确定</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>
          </el-tooltip>
        </template>
      </el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, ref, watch } from 'vue'

interface SelectOption {
  label: string
  value: string
}

const props = defineProps({
  // 选择器的值
  selectValue: {
    type: String,
    default: ''
  },
  // 输入框的值
  inputValue: {
    type: String,
    default: ''
  },
  // 选择器选项
  selectOptions: {
    type: Array as () => SelectOption[],
    default: () => [
      { label: 'ASIN', value: 'ASIN' },
      { label: 'SKU', value: 'SKU' },
      { label: 'MSKU', value: 'MSKU' },
      { label: 'FNSKU', value: 'FNSKU' },
      { label: '品名', value: '品名' }
    ]
  },
  // 选择器类名
  selectClass: {
    type: String,
    default: '!w-90px'
  },
  // 输入框类名
  inputClass: {
    type: String,
    default: '!w-150px'
  },
  // 选择器占位文本
  selectPlaceholder: {
    type: String,
    default: '请选择'
  },
  // 输入框占位文本
  inputPlaceholder: {
    type: String,
    default: ''
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true
  },
  // 提示图标
  tooltipIcon: {
    type: String,
    default: 'fa-solid:bars'
  },
  // 提示内容
  tooltipContent: {
    type: String,
    default: '多项精确搜索'
  },
  // 提示位置
  tooltipPlacement: {
    type: String,
    default: 'bottom'
  },
  // 是否使用原始HTML内容
  rawContent: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:selectValue',
  'update:inputValue',
  'selectChange',
  'inputChange',
  'enter',
  'onBtn'
])

// 响应式数据
const selectValue = ref(props.selectValue)
const inputValue = ref(props.inputValue)
const popoverVisible = ref(false)
const multilineInput = ref('')

// 监听props变化
watch(
  () => props.selectValue,
  (newVal) => {
    selectValue.value = newVal
  },
  { immediate: true }
)

watch(
  () => props.inputValue,
  (newVal) => {
    nextTick(() => {
      if (newVal !== inputValue.value) {
        inputValue.value = newVal || ''
        multilineInput.value = newVal ? newVal.split(',').join('\n') : ''
      }
    })
  },
  { immediate: true, deep: true }
)

// 监听内部值变化
watch(selectValue, (newVal) => {
  emit('update:selectValue', newVal)
  emit('selectChange', newVal)
})

watch(
  inputValue,
  (newVal) => {
    emit('update:inputValue', newVal)
    emit('inputChange', newVal)
  },
  { deep: true }
)

// 事件处理
const handleSelectChange = (val: string) => {
  selectValue.value = val
}

const handleInputChange = (val: string) => {
  inputValue.value = val
  if (val) {
    emit(
      'enter',
      val.split(',').filter((item) => item.trim() !== '')
    )
  } else {
    emit('enter', [])
  }
}

const handleClear = () => {
  inputValue.value = ''
  const values: string[] = []
  emit('enter', values)
  nextTick(() => {
    emit('onBtn', values)
  })
}

const handleEnterKey = () => {
  const values = inputValue.value
    ? inputValue.value.split(',').filter((item) => item.trim() !== '')
    : []
  emit('enter', values)
  nextTick(() => {
    emit('onBtn', values)
  })
}

const openPopover = () => {
  if (inputValue.value) {
    const uniqueValues = [
      ...new Set(inputValue.value.split(',').filter((item) => item.trim() !== ''))
    ]
    inputValue.value = uniqueValues.join(',')
    multilineInput.value = uniqueValues.join('\n')
  } else {
    multilineInput.value = ''
  }
  popoverVisible.value = true
}

const clearInput = () => {
  multilineInput.value = ''
}

const confirmInput = () => {
  const lines = multilineInput.value.split('\n').filter((line) => line.trim() !== '')
  const uniqueLines = [...new Set(lines)]
  inputValue.value = uniqueLines.join(',')
  emit('enter', uniqueLines)
  popoverVisible.value = false
  nextTick(() => {
    emit('onBtn', uniqueLines)
  })
}
</script>

<style lang="scss" scoped>
.rc-select-input-container {
  display: inline-flex;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  align-items: center;
  gap: 0;

  .tip-input-wrapper {
    display: inline-block;
  }

  .ak-advanced-input {
    position: absolute;
    top: 50%;
    z-index: 3;
    display: flex;
    width: 94%;
    height: 94%;
    cursor: pointer;
    background-color: #f0f2f5;
    border-radius: 4px;
    transform: translateY(-50%);
    align-items: center;
    justify-content: center;
  }

  // 移除内部select和input的边框
  .el-select {
    :deep(.el-select__wrapper) {
      border-right: 1px solid #dcdfe6;
      border-radius: 0;
      box-shadow: unset !important;

      &:hover {
        border: 1px solid #dcdfe6;
      }
    }

    :deep(.el-input .el-input__wrapper) {
      border: none;
      border-radius: 0;
      box-shadow: none;
    }
  }

  .tip-input-wrapper {
    :deep(.el-input .el-input__wrapper) {
      border: none;
      border-radius: 0;
      box-shadow: none;

      &:focus,
      &.is-focus,
      &:hover {
        border: none;
        box-shadow: none;
      }
    }
  }
}

:deep(.tip-input-popover) {
  padding: 0;
}

.popover-content {
  display: flex;
  flex-direction: column;

  .popover-footer {
    padding-top: 10px;
    margin-top: 10px;
    border-top: 1px solid #eee;

    .popover-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right-actions {
        display: flex;
      }
    }
  }
}
</style>
