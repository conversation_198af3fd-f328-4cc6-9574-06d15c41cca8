<template>
  <div class="sku-input-container">
    <el-input
      type="textarea"
      v-model="inputValue"
      :placeholder="placeholder"
      :rows="rows"
      @keydown.enter="handleEnterKey"
      @input="handleInput"
      ref="textareaRef"
    />
    <div class="input-info">
      <span class="count-info">{{ currentCount }}/{{ maxCount }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  // 默认提示文案
  placeholder: {
    type: String,
    default: '请输入SKU，一行一个回车换行，最多50个'
  },
  // 最大输入数量限制
  maxCount: {
    type: Number,
    default: 50
  },
  // 文本域行数
  rows: {
    type: Number,
    default: 5
  },
  // 初始值
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const inputValue = ref(props.modelValue)
const textareaRef = ref(null)

// 计算当前输入的SKU数量
const currentCount = computed(() => {
  if (!inputValue.value) return 0
  return processInputLines(inputValue.value).length
})

// 处理输入行，去除空行和前后空格
const processInputLines = (input) => {
  if (!input) return []
  // 分割行，去除空行和前后空格
  return input
    .split('\n')
    .map((item) => item.trim()) // 去除每行前后空格
    .filter((item) => item !== '') // 过滤掉空行
}

// 获取所有输入的SKU数组
const skuList = computed(() => {
  return processInputLines(inputValue.value)
})

// 监听输入值变化
watch(inputValue, (newVal) => {
  emit('update:modelValue', newVal)
  emit('change', {
    value: newVal,
    list: skuList.value,
    count: currentCount.value
  })
})

watch(props, (newVal) => {
  inputValue.value = newVal.modelValue
})

// 处理回车键
const handleEnterKey = (e) => {
  // 阻止默认行为，避免表单提交
  e.preventDefault()

  // 如果已经达到最大限制，不再添加新行
  if (currentCount.value >= props.maxCount) {
    return
  }

  // 获取当前光标位置
  const textarea = textareaRef.value.$el.querySelector('textarea')
  const cursorPosition = textarea.selectionStart

  // 在光标位置插入换行符
  const textBeforeCursor = inputValue.value.substring(0, cursorPosition)
  const textAfterCursor = inputValue.value.substring(cursorPosition)

  inputValue.value = textBeforeCursor + '\n' + textAfterCursor

  // 更新光标位置
  nextTick(() => {
    textarea.selectionStart = cursorPosition + 1
    textarea.selectionEnd = cursorPosition + 1
  })
}

// 处理输入，限制最大数量
const handleInput = () => {
  if (currentCount.value > props.maxCount) {
    // 如果超过最大限制，只保留前maxCount个有效行
    const lines = processInputLines(inputValue.value)
    inputValue.value = lines.slice(0, props.maxCount).join('\n')
  }
}

// 暴露方法给父组件
defineExpose({
  clear: () => {
    inputValue.value = ''
  },
  getSkuList: () => skuList.value,
  getCount: () => currentCount.value
})
</script>

<style lang="scss" scoped>
.sku-input-container {
  position: relative;
  width: 100%;

  .input-info {
    display: flex;
    justify-content: flex-end;
    margin-top: 5px;

    .count-info {
      font-size: 12px;
      color: #909399;

      &.exceeded {
        color: #f56c6c;
      }
    }
  }

  :deep(.el-textarea__inner) {
    font-family: monospace;
    line-height: 1.5;
  }
}
</style>
