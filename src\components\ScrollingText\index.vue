<template>
  <div class="scrolling-text-container" @click="handleClick">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <Icon :size="18" class="cursor-pointer mt-2px" icon="ep:bell" />
      <span class="item-title">加载中...</span>
    </div>

    <!-- 轮播图 - 只在有数据时渲染 -->
    <el-carousel
      v-else-if="noticeItems.length > 0 || items.length > 0"
      ref="carouselRef"
      class="announcement-carousel"
      direction="vertical"
      :autoplay="true"
      :interval="6000"
      indicator-position="none"
      :height="'24px'"
    >
      <el-carousel-item
        v-for="(item, index) in noticeItems.length > 0 ? noticeItems : items"
        :key="index"
      >
        <div class="carousel-item" @click="handleItemClick(item)">
          <Icon :size="18" class="cursor-pointer mt-2px" :icon="item.icon || 'ep:bell'" />
          <span class="item-title">{{ item.title }}</span>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 无数据状态 -->
    <div v-else class="no-data-container">
      <Icon :size="18" class="cursor-pointer mt-2px" icon="ep:bell" />
      <span class="item-title">暂无公告</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as NoticeApi from '@/api/system/notice'
import { defineEmits, defineProps, nextTick, onMounted, ref } from 'vue'

interface AnnouncementItem {
  title: string
  value: string
  icon?: string
}

const props = defineProps({
  items: {
    type: Array as () => AnnouncementItem[],
    default: () => []
  }
})

const emit = defineEmits(['notice-click'])

const noticeItems = ref<AnnouncementItem[]>([])
const carouselRef = ref()
const isLoading = ref(true)

// 获取公告数据
const getNoticeData = async () => {
  isLoading.value = true
  try {
    const res = await NoticeApi.getAnnouncement({ type: 2, pageNo: 1, pageSize: 10 })
    if (res && res.list && res.list.length > 0) {
      // 过滤只要type等于2的数据
      noticeItems.value = res.list.map((item: any) => ({
        ...item,
        title: item.title || '',
        value: item.id?.toString() || '',
        icon: 'ep:bell'
      }))

      // 确保DOM更新后重新初始化轮播图
      await nextTick()
      if (carouselRef.value && noticeItems.value.length > 1) {
        // 延迟一小段时间确保轮播图完全初始化
        setTimeout(() => {
          if (carouselRef.value) {
            // 手动触发轮播图开始自动播放
            carouselRef.value.setActiveItem(0)
          }
        }, 100)
      }
    }
  } catch (error) {
    console.error('获取公告数据失败:', error)
    // 如果接口失败，使用默认数据
    noticeItems.value = [
      {
        title: '预祝寒哥慕士塔格峰7546顺利登顶！',
        value: '2',
        icon: 'ep:bell'
      }
    ]
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getNoticeData()
})

const handleClick = () => {
  // 点击容器时，如果有公告数据，打开第一个公告
  const firstNotice =
    noticeItems.value.length > 0
      ? noticeItems.value[0]
      : props.items.length > 0
        ? props.items[0]
        : null
  if (firstNotice) {
    emit('notice-click', firstNotice.value)
  }
}

const handleItemClick = (item: AnnouncementItem) => {
  // 阻止事件冒泡，避免触发容器的点击事件
  event?.stopPropagation()
  emit('notice-click', item.value)
}
</script>

<style scoped>
.scrolling-text-container {
  width: 300px;
  padding: 4px 0;
  overflow: hidden;
  cursor: pointer;
  border-radius: 4px;
}

.announcement-carousel {
  width: 100%;
  height: 24px;
}

.carousel-item,
.loading-container,
.no-data-container {
  display: flex;
  width: 100%;
  height: 24px;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.item-title {
  overflow: hidden;
  color: var(--el-color-primary);
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-carousel__container) {
  height: 24px !important;
}

:deep(.el-carousel__item) {
  line-height: 24px;
}
</style>
