<template>
  <div class="flex flex-row items-center gap-2">
    <!-- 时间类型选择器 -->
    <div v-if="props.showTimeTypeSelector" class="time-type-tabs">
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'day' }"
        @click="handleTimeTypeChange('day')"
      >
        按天
      </div>
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'month' }"
        @click="handleTimeTypeChange('month')"
      >
        按月
      </div>
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'week' }"
        @click="handleTimeTypeChange('week')"
      >
        按周
      </div>
    </div>

    <el-radio-group
      v-if="props.showShortcutDays"
      v-model="shortcutDays"
      @change="handleShortcutDaysChange"
    >
      <el-radio-button :value="1">昨天</el-radio-button>
      <el-radio-button :value="7">最近7天</el-radio-button>
      <el-radio-button :value="30">最近30天</el-radio-button>
    </el-radio-group>

    <!-- 按天选择器 -->
    <el-date-picker
      v-if="!props.showTimeTypeSelector || currentTimeType === 'day'"
      v-model="times"
      :value-format="props.valueFormat"
      :type="props.isTimes ? 'datetimerange' : 'daterange'"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
      :shortcuts="dateShortcuts || dayShortcuts"
      :class="props.isTimes ? '!w-360px' : '!w-240px'"
      :disabled-date="disabledDate"
      @change="handleDateChange"
    />

    <!-- 按月选择器 -->
    <el-date-picker
      v-else-if="currentTimeType === 'month'"
      v-model="monthTimes"
      type="monthrange"
      range-separator="至"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      :shortcuts="monthShortcuts"
      format="YYYY-MM"
      value-format="YYYY-MM"
      class="!w-240px"
      :disabled-date="disabledDate"
      @change="handleMonthChange"
    />

    <!-- 按周选择器 -->
    <el-date-picker
      v-else-if="currentTimeType === 'week'"
      v-model="weekTimes"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :shortcuts="weekShortcuts"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      class="!w-240px"
      :disabled-date="disabledDate"
      @change="handleWeekChange"
    />

    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import * as DateUtil from '@/utils/formatTime'
import dayjs from 'dayjs'
import { onMounted, ref, watch, type PropType } from 'vue'

const props = defineProps({
  /** v-model 绑定的值 */
  modelValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  /** 是否显示快捷天数选择 */
  showShortcutDays: {
    type: Boolean,
    default: false
  },
  /** 是否默认选择今天 */
  isToday: {
    type: Boolean,
    default: false
  },
  /** 是否显示时间 */
  isTimes: {
    type: Boolean,
    default: false
  },
  /** 是否显示时间类型选择器 */
  showTimeTypeSelector: {
    type: Boolean,
    default: false
  },
  /** 默认时间类型 */
  defaultTimeType: {
    type: String as PropType<'day' | 'month' | 'week'>,
    default: 'day'
  },
  /** 月份范围值 */
  monthValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  /** 周范围值 */
  weekValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  dateShortcuts: {
    type: Array as PropType<any[]>,
    default: () => null
  },
  disabledDate: {
    type: Function as PropType<(time: Date) => boolean>,
    default: () => false
  },
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss'
  },
  /** 是否启用默认日期范围 */
  enableDefaultRange: {
    type: Boolean,
    default: true
  }
})

const emits = defineEmits<{
  'update:modelValue': [value: [string, string]]
  'update:monthValue': [value: [string, string]]
  'update:weekValue': [value: [string, string]]
  change: [times: [dayjs.ConfigType, dayjs.ConfigType]]
  'time-type-change': [timeType: 'day' | 'month' | 'week']
}>()

/** 快捷日期范围选择组件 */
defineOptions({ name: 'ShortcutDateRangePicker' })

const shortcutDays = ref(1) // 日期快捷天数（单选按钮组）, 默认1天（昨天）
const times = ref<[string, string]>(props.modelValue) // 时间范围参数
const monthTimes = ref<[string, string]>(props.monthValue) // 月份范围参数
const weekTimes = ref<[string, string]>(props.weekValue) // 周范围参数
const currentTimeType = ref<'day' | 'month' | 'week'>(props.defaultTimeType) // 当前时间类型

/** 重置到默认值 */
const resetToDefaults = () => {
  if (props.enableDefaultRange) {
    const defaultRanges = getDefaultDateRange(currentTimeType.value)

    if (currentTimeType.value === 'day' || currentTimeType.value === 'week') {
      times.value = defaultRanges.dateRange as [string, string]
      emits('update:modelValue', times.value)
    } else if (currentTimeType.value === 'month') {
      monthTimes.value = defaultRanges.monthRange as [string, string]
      emits('update:monthValue', monthTimes.value)
    }
  } else {
    // 如果未启用默认日期范围，则清空所有值
    times.value = ['', '']
    monthTimes.value = ['', '']
    weekTimes.value = ['', '']
    emits('update:modelValue', times.value)
    emits('update:monthValue', monthTimes.value)
    emits('update:weekValue', weekTimes.value)
  }
}

defineExpose({ times, monthTimes, weekTimes, currentTimeType, resetToDefaults }) // 暴露时间范围参数和重置方法

/** 按天日期快捷选择 */
const dayShortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)

      const end = new Date()
      end.setHours(23, 59, 59, 999)

      return [start, end]
    }
  },
  {
    text: '昨天',
    value: () => DateUtil.getDayRange(new Date(), -1)
  },
  {
    text: '最近7天',
    value: () => DateUtil.getLast7Days()
  },
  {
    text: '本月',
    value: () => [dayjs().startOf('M'), dayjs().subtract(1, 'd')]
  },
  {
    text: '最近30天',
    value: () => DateUtil.getLast30Days()
  },
  {
    text: '最近1年',
    value: () => DateUtil.getLast1Year()
  }
]

/** 按月快捷选择 */
const monthShortcuts = [
  {
    text: '本月',
    value: () => {
      const today = new Date()
      return [today, today]
    }
  },
  {
    text: '上月',
    value: () => {
      const lastMonth = new Date()
      lastMonth.setMonth(lastMonth.getMonth() - 1)
      return [lastMonth, lastMonth]
    }
  },
  {
    text: '最近3个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 2)
      return [start, end]
    }
  },
  {
    text: '最近6个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 5)
      return [start, end]
    }
  },
  {
    text: '今年',
    value: () => {
      const year = new Date().getFullYear()
      const start = new Date(year, 0, 1)
      const end = new Date(year, 11, 31)
      return [start, end]
    }
  },
  {
    text: '去年',
    value: () => {
      const year = new Date().getFullYear() - 1
      const start = new Date(year, 0, 1)
      const end = new Date(year, 11, 31)
      return [start, end]
    }
  }
]

/** 按周快捷选择 */
const weekShortcuts = [
  {
    text: '本周',
    value: () => {
      const today = new Date()
      const dayOfWeek = today.getDay()
      const start = new Date(today)
      start.setDate(today.getDate() - dayOfWeek + 1) // 周一
      const end = new Date(today)
      end.setDate(today.getDate() - dayOfWeek + 7) // 周日
      return [start, end]
    }
  },
  {
    text: '上周',
    value: () => {
      const today = new Date()
      const dayOfWeek = today.getDay()
      const start = new Date(today)
      start.setDate(today.getDate() - dayOfWeek - 6) // 上周一
      const end = new Date(today)
      end.setDate(today.getDate() - dayOfWeek) // 上周日
      return [start, end]
    }
  },
  {
    text: '最近4周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 27) // 4周前
      return [start, end]
    }
  },
  {
    text: '最近8周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 55) // 8周前
      return [start, end]
    }
  }
]

/** 获取默认日期范围 */
const getDefaultDateRange = (timeType: 'day' | 'month' | 'week') => {
  const today = dayjs()
  const yesterday = today.subtract(1, 'day')

  switch (timeType) {
    case 'day':
    case 'week':
      // 最近7天（从昨天往前推7天）
      const startDate = yesterday.subtract(6, 'day')
      return {
        dateRange: [startDate.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD')],
        monthRange: [],
        weekRange: []
      }
    case 'month':
      // 当前月
      const currentMonth = today.format('YYYY-MM')
      return {
        dateRange: [],
        monthRange: [currentMonth, currentMonth],
        weekRange: []
      }
    default:
      return {
        dateRange: [],
        monthRange: [],
        weekRange: []
      }
  }
}

/** 时间类型切换 */
const handleTimeTypeChange = (timeType: 'day' | 'month' | 'week') => {
  currentTimeType.value = timeType

  // 只有启用默认日期范围时才设置默认值
  if (props.enableDefaultRange) {
    const defaultRanges = getDefaultDateRange(timeType)

    if (timeType === 'day' || timeType === 'week') {
      times.value = defaultRanges.dateRange as [string, string]
      emits('update:modelValue', times.value)
    } else if (timeType === 'month') {
      monthTimes.value = defaultRanges.monthRange as [string, string]
      emits('update:monthValue', monthTimes.value)
    }
  }

  emits('time-type-change', timeType)
}

/** 设置时间范围 */
function setTimes() {
  const beginDate = dayjs().subtract(shortcutDays.value, 'd')
  const yesterday = dayjs().subtract(1, 'd')
  times.value = DateUtil.getDateRange(beginDate, yesterday)
}

/** 快捷日期单选按钮选中 */
const handleShortcutDaysChange = async () => {
  // 设置时间范围
  setTimes()
  // 发送时间范围选中事件
  await emitDateRangePicker()
}

/** 日期选择器变化事件 */
const handleDateChange = (value: [string, string]) => {
  times.value = value
  emits('update:modelValue', value)
  emits('change', value)
}

/** 月份选择器变化事件 */
const handleMonthChange = (value: [string, string]) => {
  monthTimes.value = value
  emits('update:monthValue', value)
  emits('change', value)
}

/** 周选择器变化事件 */
const handleWeekChange = (value: [string, string]) => {
  weekTimes.value = value
  emits('update:weekValue', value)
  emits('change', value)
}

/** 触发时间范围选中事件 */
const emitDateRangePicker = async () => {
  emits('update:modelValue', times.value)
  emits('change', times.value)
}

/** 监听 modelValue 变化 */
watch(
  () => props.modelValue,
  (newValue) => {
    times.value = newValue
  },
  { immediate: true }
)

/** 监听 monthValue 变化 */
watch(
  () => props.monthValue,
  (newValue) => {
    monthTimes.value = newValue
  },
  { immediate: true }
)

/** 监听 weekValue 变化 */
watch(
  () => props.weekValue,
  (newValue) => {
    weekTimes.value = newValue
  },
  { immediate: true }
)

/** 初始化 **/
onMounted(() => {
  if (props.isToday && (!props.modelValue || props.modelValue.every((v) => !v))) {
    // 设置为今天
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayEnd = new Date()
    todayEnd.setHours(23, 59, 59, 999)

    times.value = [
      dayjs(today).format('YYYY-MM-DD HH:mm:ss'),
      dayjs(todayEnd).format('YYYY-MM-DD HH:mm:ss')
    ]
    emitDateRangePicker()
  } else if (props.showTimeTypeSelector && props.enableDefaultRange) {
    // 如果显示时间类型选择器且启用默认日期范围且没有初始值，设置默认值
    const hasInitialValue =
      (currentTimeType.value === 'day' &&
        props.modelValue &&
        props.modelValue.length > 0 &&
        props.modelValue.some((v) => v)) ||
      (currentTimeType.value === 'month' &&
        props.monthValue &&
        props.monthValue.length > 0 &&
        props.monthValue.some((v) => v)) ||
      (currentTimeType.value === 'week' &&
        props.weekValue &&
        props.weekValue.length > 0 &&
        props.weekValue.some((v) => v))

    if (!hasInitialValue) {
      const defaultRanges = getDefaultDateRange(currentTimeType.value)

      if (currentTimeType.value === 'day' || currentTimeType.value === 'week') {
        times.value = defaultRanges.dateRange as [string, string]
        emits('update:modelValue', times.value)
      } else if (currentTimeType.value === 'month') {
        monthTimes.value = defaultRanges.monthRange as [string, string]
        emits('update:monthValue', monthTimes.value)
      }
    }
  }
})
</script>

<style scoped>
.time-type-tabs {
  display: flex;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.time-type-tab {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  background-color: #fff;
  border-right: 1px solid #dcdfe6;
  transition: all 0.3s;
  user-select: none;
}

.time-type-tab:last-child {
  border-right: none;
}

.time-type-tab:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.time-type-tab.active {
  color: #fff;
  background-color: #409eff;
}
</style>
