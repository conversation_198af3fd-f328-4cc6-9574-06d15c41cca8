<template>
  <div>
    <!-- 搜索区域 -->
    <div class="search-area" style="margin-bottom: 16px">
      <el-input
        v-model="searchForm.templateName"
        placeholder="模板名称"
        style="width: 200px; margin-right: 8px"
        clearable
      />
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleBatchTop">批量置顶</el-button>
      <el-button @click="handleBatchCancelTop">批量取消置顶</el-button>
    </div>

    <!-- 统计表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="模板名称" min-width="120">
        <template #default="{ row }">
          <span class="status-link" @click="handleRowClick(row, type)">{{
            props.type == 'audit'
              ? row.name +
                ' (' +
                (row.noApprovalNum != undefined ? row.noApprovalNum : row.pendingReviewQty || 0) +
                ') '
              : row.name
          }}</span>
        </template>
      </el-table-column>

      <!-- dev类型字段 -->
      <template v-if="type === 'dev'">
        <el-table-column label="已通过" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'passed')">{{
              row.approvalNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已拒绝" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'rejected')">{{
              row.rejectNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="待审核" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'pending')">{{
              row.noApprovalNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="草稿" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'draft')">{{
              row.draftNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已投产" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'deployed')">{{
              row.yesProductNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="未投产" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'undeployed')">{{
              row.noProductNum || 0
            }}</span>
          </template>
        </el-table-column>
      </template>

      <!-- audit类型字段 -->
      <template v-else-if="type === 'audit'">
        <el-table-column label="已通过" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'passed')">{{
              row.approvalNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已拒绝" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'rejected')">{{
              row.rejectNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="待审核" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'pending')">{{
              row.noApprovalNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已投产" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'deployed')">{{
              row.yesProductNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="未投产" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'undeployed')">{{
              row.noProductNum || 0
            }}</span>
          </template>
        </el-table-column>
      </template>

      <!-- prod类型字段 -->
      <template v-else-if="type === 'prod'">
        <el-table-column label="已上架" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'online')">{{
              row.yesListingNum || 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="未上架" min-width="80" align="center">
          <template #default="{ row }">
            <span class="status-link" @click="handleStatusClick(row, 'offline')">{{
              row.noListingNum || 0
            }}</span>
          </template>
        </el-table-column>
      </template>

      <el-table-column label="总条数" min-width="80" align="center">
        <template #default="{ row }">
          <span class="status-link total-count">{{ row.allNum || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleRowClick(row, type)">进入模板</el-button>
          <el-button :type="row.sort ? 'danger' : 'primary'" link @click="handleManage(row)">{{
            row.sort ? '取消置顶' : '置顶'
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 抽屉：复用现有组件 -->
    <el-drawer v-model="drawerVisible" size="92%" direction="rtl">
      <!-- 动态组件复用 -->
      <component
        v-if="drawerVisible"
        :is="currentComponent"
        :initial-template-id="selectedTemplateId"
        :initial-filters="initialFilters"
        :drawer-mode="true"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import * as TemplateApi from '@/api/tool/tableTemplateCenter'
import AuditIndex from '@/views/dev/audit/auditTable.vue'
import DevTableIndex from '@/views/dev/devTable/devTable.vue'
import ProdIndex from '@/views/operate/prod/prodTable.vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'

// Props
const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['dev', 'audit', 'prod'].includes(value)
  }
})

// 响应式数据
const loading = ref(false)
const drawerVisible = ref(false)
const currentComponent = ref(null)
const selectedTemplateId = ref(null)
const initialFilters = ref({})
const tableData = ref([])
const selectedIds = ref<number[]>([])

// 搜索表单
const searchForm = reactive({
  templateName: ''
})

// 获取统计数据
const getStatisticsData = async () => {
  try {
    loading.value = true

    // 获取模板列表
    const templateParams = {
      templateName: searchForm.templateName || undefined,
      templateTypeCode: props.type == 'prod' ? 2 : 1, //投产表是2，审核、开发都是1
      templateType: props.type == 'audit' ? 3 : 1
    }

    const templateRes = await TemplateApi.getTemplateList(templateParams)
    const data = templateRes || []
    tableData.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 打开抽屉并设置组件和过滤器
const openDrawer = (row, type, filters = {}) => {
  selectedTemplateId.value = row?.id
  initialFilters.value = filters
  console.log(initialFilters.value)
  switch (type) {
    case 'dev':
      currentComponent.value = DevTableIndex
      break
    case 'audit':
      currentComponent.value = AuditIndex
      break
    case 'prod':
      currentComponent.value = ProdIndex
      break
  }
  drawerVisible.value = true
}

// 处理行点击事件 (模板名称, 进入模板)
const handleRowClick = (row, type) => {
  openDrawer(row, type, {}) // 不带任何筛选条件
}

// 处理状态点击事件
const handleStatusClick = (row, status) => {
  let filters = {}
  if (props.type === 'dev') {
    const statusFilters = {
      passed: { auditStatus: ['已通过'] },
      rejected: { auditStatus: ['已拒绝'] },
      pending: { auditStatus: ['待审核'] },
      draft: { auditStatus: ['草稿'] },
      deployed: { production: '是' },
      undeployed: { production: '否' }
    }
    filters = statusFilters[status] || {}
  } else if (props.type === 'audit') {
    const statusFilters = {
      passed: { auditStatus: ['已通过'] },
      rejected: { auditStatus: ['已拒绝'] },
      pending: { auditStatus: ['待审核'] },
      deployed: { production: '是' },
      undeployed: { production: '否' }
    }
    filters = statusFilters[status] || {}
  } else if (props.type === 'prod') {
    const statusFilters = {
      online: { shelfStatus: '是' },
      offline: { shelfStatus: '否' }
    }
    filters = statusFilters[status] || {}
  }

  openDrawer(row, props.type, filters)
}

// 搜索
const handleSearch = () => {
  getStatisticsData()
}

// 重置
const handleReset = () => {
  searchForm.templateName = ''
  getStatisticsData()
}

// 批量置顶
const handleBatchTop = async () => {
  if (selectedIds.value.length === 0) {
    return ElMessage.warning('请选择要置顶的模板')
  }
  try {
    await TemplateApi.batchTop(selectedIds.value)
    ElMessage.success('批量置顶成功')
    await getStatisticsData()
  } catch (error) {
    console.error('批量置顶失败', error)
  }
}

// 批量取消置顶
const handleBatchCancelTop = async () => {
  if (selectedIds.value.length === 0) {
    return ElMessage.warning('请选择要取消置顶的模板')
  }
  try {
    await TemplateApi.batchCancelTop(selectedIds.value)
    ElMessage.success('批量取消置顶成功')
    await getStatisticsData()
  } catch (error) {
    console.error('批量取消置顶失败', error)
  }
}

// 置顶/取消置顶
const handleManage = async (row) => {
  try {
    if (row.sort) {
      await TemplateApi.cancelTop(row.id)
      ElMessage.success('取消置顶成功')
    } else {
      await TemplateApi.top(row.id)
      ElMessage.success('置顶成功')
    }
    getStatisticsData()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map((row) => row.id)
}

// 监听type变化
watch(
  () => props.type,
  () => {
    getStatisticsData()
  },
  { immediate: false }
)

// 组件挂载时获取数据
onMounted(() => {
  getStatisticsData()
})
</script>

<style scoped>
.status-link {
  display: block;
  width: 100%;
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
}

.status-link:hover {
  text-decoration: underline;
}

.total-count {
  font-weight: bold;
  color: #303133;
}
</style>
