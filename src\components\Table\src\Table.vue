<script lang="tsx">
import { Pagination, TableColumn, TableSetPropsType, TableSlotDefault } from '@/types/table'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { getSlot } from '@/utils/tsxHelper'
import { ElDatePicker, ElImage, ElInput, ElPagination, ElTable, ElTableColumn } from 'element-plus'
import { set } from 'lodash-es'
import {
  computed,
  defineComponent,
  nextTick,
  onBeforeUnmount,
  onMounted,
  PropType,
  ref,
  unref,
  watch
} from 'vue'
import { setIndex } from './helper'
import type { TableProps } from './types'
// 在原有imports基础上添加
import * as FileApi from '@/api/infra/file'
import * as ToolsTableHeadTemplateApi from '@/api/infra/toolsTableHeadTemplate'
import { debounce } from 'lodash-es'
import { useRoute } from 'vue-router'
import { useColumnConfig } from './composables/useColumnConfig'
import { useDragSort } from './composables/useDragSort'
import TableColumnConfig from './TableColumnConfig.vue'

export default defineComponent({
  // eslint-disable-next-line vue/no-reserved-component-names
  name: 'Table',
  components: { TableColumnConfig },
  props: {
    pageSize: propTypes.number.def(10),
    currentPage: propTypes.number.def(1),
    // 是否多选
    selection: propTypes.bool.def(false),
    // 是否所有的超出隐藏，优先级低于schema中的showOverflowTooltip,
    showOverflowTooltip: propTypes.bool.def(true),
    // 每页显示条目个数
    pageSizeLimit: propTypes.number.def(50),
    // 表头
    columns: {
      type: Array as PropType<TableColumn[]>,
      default: () => []
    },
    // 展开行
    expand: propTypes.bool.def(false),
    // 是否展示分页
    pagination: {
      type: Object as PropType<Pagination>,
      default: (): Pagination | undefined => undefined
    },
    // 仅对 type=selection 的列有效，类型为 Boolean，为 true 则会在数据更新之后保留之前选中的数据（需指定 row-key）
    reserveSelection: propTypes.bool.def(false),
    // 加载状态
    loading: propTypes.bool.def(false),
    // 是否叠加索引
    reserveIndex: propTypes.bool.def(false),
    // 对齐方式
    align: propTypes.string
      .validate((v: string) => ['left', 'center', 'right'].includes(v))
      .def('center'),
    // 表头对齐方式
    headerAlign: propTypes.string
      .validate((v: string) => ['left', 'center', 'right'].includes(v))
      .def('center'),
    data: {
      type: Array as PropType<Recordable[]>,
      default: () => []
    },
    showColumnConfig: propTypes.bool.def(true), // 是否显示列配置按钮
    columnConfig: {
      type: Object,
      default: () => ({})
    },
    // 拖拽排序相关
    draggable: propTypes.bool.def(false), // 是否启用拖拽排序
    dragField: {
      type: [String, Function],
      default: 'id'
    }, // 拖拽排序的字段名或函数，用于标识每行数据
    saveStatus: {
      type: Object as PropType<{
        saving: boolean
        lastSaved: Date | null
        error: string | null
      } | null>,
      default: null
    },
    type: propTypes.string.def(''),
    isSort: propTypes.bool.def(false), //是否启用列表字段正序倒序排序
    actionColumnWidth: propTypes.oneOfType([propTypes.string, propTypes.number]).def(undefined)
  },
  emits: [
    'update:pageSize',
    'update:currentPage',
    'register',
    'column-config-change',
    'update:data',
    'selection-change',
    'sort-change'
  ],
  setup(props, { attrs, slots, emit, expose }) {
    const elTableRef = ref<ComponentRef<typeof ElTable>>()
    const saveStatusRef = ref<HTMLElement>()
    const toolbarRef = ref<HTMLElement>()
    const rightSectionRef = ref<HTMLElement>()

    // 动态计算保存状态位置
    const updateSaveStatusPosition = debounce(() => {
      if (!saveStatusRef.value) return

      nextTick(() => {
        if (rightSectionRef.value) {
          const rightSectionWidth = rightSectionRef.value.getBoundingClientRect().width
          saveStatusRef.value!.style.right = `${rightSectionWidth + 10}px`
        } else if (toolbarRef.value) {
          // 如果有toolbar但没有右侧按钮，则使用默认位置
          saveStatusRef.value!.style.right = '10px'
        } else {
          // 如果没有toolbar，则使用默认位置
          saveStatusRef.value!.style.right = '10px'
        }
      })
    }, 300)

    // ResizeObserver实例
    let resizeObserver: ResizeObserver | null = null

    // 注册
    onMounted(() => {
      const tableRef = unref(elTableRef)
      emit('register', tableRef?.$parent, elTableRef.value)

      // 初始化保存状态位置
      updateSaveStatusPosition()

      // 延迟设置ResizeObserver，确保DOM已完全渲染
      nextTick(() => {
        if (rightSectionRef.value && window.ResizeObserver) {
          resizeObserver = new ResizeObserver(() => {
            updateSaveStatusPosition()
          })
          resizeObserver.observe(rightSectionRef.value)
        }
      })
    })

    // 组件卸载时清理ResizeObserver
    onBeforeUnmount(() => {
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
    })

    const pageSizeRef = ref(props.pageSize)

    const currentPageRef = ref(props.currentPage)

    // useTable传入的props
    const outsideProps = ref<TableProps>({})

    const mergeProps = ref<TableProps>({})

    const draggableData = ref([...props.data])

    const { internalColumns, handleColumnConfigUpdate } = useColumnConfig(props, emit)

    watch(
      internalColumns,
      () => {
        updateSaveStatusPosition()
      },
      { deep: true }
    )

    // 监听保存状态变化，重新计算位置
    watch(
      () => props.saveStatus,
      () => {
        updateSaveStatusPosition()
      },
      { deep: true }
    )

    watch(
      () => props.data,
      (newData) => {
        draggableData.value = [...newData]
      },
      { deep: true, immediate: true }
    )

    useDragSort(props, emit, elTableRef, draggableData)

    // 创建一个可重用的 span 元素用于计算文本宽度
    const widthCalculator = document.createElement('span')
    widthCalculator.style.visibility = 'hidden'
    widthCalculator.style.position = 'absolute'
    widthCalculator.style.whiteSpace = 'nowrap'
    document.body.appendChild(widthCalculator)

    // 监听表格列宽度变化，防止拖拽时宽度小于最小值
    let columnWidthObserver: MutationObserver | null = null

    const setupColumnWidthProtection = () => {
      const elTableInstance = unref(elTableRef)
      if (!elTableInstance || !elTableInstance.$el) return

      // 清理之前的观察器
      if (columnWidthObserver) {
        columnWidthObserver.disconnect()
      }

      columnWidthObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            const target = mutation.target as HTMLElement
            if (target.tagName === 'COL') {
              const width = target.style.width
              if (width && parseInt(width) < 85) {
                target.style.width = '85px'
                target.style.minWidth = '85px'
              }
            }
          }
        })
      })

      // 观察表格的colgroup变化
      const colgroups = elTableInstance.$el.querySelectorAll('colgroup')
      colgroups.forEach((colgroup) => {
        columnWidthObserver!.observe(colgroup, {
          attributes: true,
          attributeFilter: ['style'],
          subtree: true
        })
      })
    }

    // 在表格渲染完成后设置保护
    nextTick(() => {
      setupColumnWidthProtection()
    })

    // 监听内部列变化，重新设置保护
    watch(
      internalColumns,
      () => {
        nextTick(() => {
          setupColumnWidthProtection()
        })
      },
      { deep: true }
    )

    onBeforeUnmount(() => {
      if (widthCalculator.parentNode) {
        document.body.removeChild(widthCalculator)
      }
      if (columnWidthObserver) {
        columnWidthObserver.disconnect()
      }
    })

    const minWidthCache = new Map<string, number>()

    // 计算列的最小宽度以适应表头内容
    const calculateHeaderMinWidth = (label: string, subTitle?: string) => {
      const cacheKey = `${label || ''}#${subTitle || ''}`
      if (minWidthCache.has(cacheKey)) {
        return minWidthCache.get(cacheKey)!
      }

      const padding = 16 // 内边距
      const minWidth = 80 // 最小宽度
      // 计算标题宽度
      widthCalculator.style.fontSize = '13px'
      widthCalculator.style.fontWeight = 'bold'
      widthCalculator.innerText = label || ''
      const titleWidth = widthCalculator.offsetWidth

      // 计算副标题宽度
      let subTitleWidth = 0
      if (subTitle) {
        widthCalculator.style.fontSize = '11px'
        widthCalculator.style.fontWeight = 'normal'
        widthCalculator.innerText = subTitle
        subTitleWidth = widthCalculator.offsetWidth
      }

      const calculatedWidth = Math.max(titleWidth, subTitleWidth) + padding
      const finalWidth = Math.max(minWidth, calculatedWidth)

      minWidthCache.set(cacheKey, finalWidth)
      return finalWidth
    }

    const route = useRoute()

    // 处理表头拖拽开始事件
    const handleHeaderDragStart = (column: any, event: Event) => {
      // 存储原始宽度，用于后续比较
      const tableColumn = internalColumns.value.find((c) => c.field === column.property)
      if (tableColumn) {
        const label = tableColumn.label || ''
        const subTitle =
          tableColumn.unit && tableColumn.needUnit === 1
            ? getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, tableColumn.unit)
            : undefined
        const minW = tableColumn.minWidth || calculateHeaderMinWidth(label, subTitle)

        // 在列对象上存储最小宽度信息，供拖拽过程使用
        column._minWidth = minW
      }
    }

    const handleHeaderDragEnd = async (
      newWidth: number,
      oldWidth: number,
      column: any,
      event: Event
    ) => {
      const tableColumn = internalColumns.value.find((c) => c.field === column.property)
      if (tableColumn) {
        const label = tableColumn.label || ''
        const subTitle =
          tableColumn.unit && tableColumn.needUnit === 1
            ? getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, tableColumn.unit)
            : undefined
        const minW = tableColumn.minWidth || calculateHeaderMinWidth(label, subTitle)

        // 确保新宽度不小于最小宽度
        const finalWidth = Math.max(newWidth, minW)

        // 更新内部列配置
        const columnIndex = internalColumns.value.findIndex((c) => c.field === column.property)
        if (columnIndex !== -1) {
          internalColumns.value[columnIndex] = {
            ...internalColumns.value[columnIndex],
            width: finalWidth
          }
        }

        // 强制更新ElTable的列宽度
        const elTableInstance = unref(elTableRef)
        if (elTableInstance && elTableInstance.layout) {
          const internalColumn = elTableInstance.store.states.columns.value.find(
            (c) => c.property === column.property
          )
          if (internalColumn) {
            internalColumn.width = finalWidth
            internalColumn.realWidth = finalWidth
            internalColumn.minWidth = minW
            elTableInstance.doLayout()
          }
        }

        if (finalWidth !== newWidth) {
          console.warn(
            `Column '${label}' dragged to ${newWidth}px. Minimum width is ${minW}px. Resized to ${finalWidth}px.`
          )
        }

        // 调用API更新表头模板
        try {
          const currentConfigContent = JSON.stringify(internalColumns.value)
          const typeToUse = props.type || (route.name as string)

          // 获取当前的模板列表来找到需要更新的模板ID
          const templateList = await ToolsTableHeadTemplateApi.getToolsTableHeadTemplateList({
            type: typeToUse
          })

          if (templateList && templateList.length > 0) {
            const firstTemplate = templateList[0]
            await ToolsTableHeadTemplateApi.updateToolsTableHeadTemplate({
              id: firstTemplate.id,
              template: firstTemplate.template,
              type: typeToUse,
              content: currentConfigContent
            })
          }
        } catch (error) {
          console.error('更新表头模板失败:', error)
        }
      }
    }

    const getProps = computed(() => {
      const propsObj = { ...props }
      // 表格渲染时使用 internalColumns
      propsObj.columns = unref(internalColumns)
      // 如果启用拖拽，使用draggableData，否则使用原始data
      propsObj.data = props.draggable ? draggableData.value : props.data
      Object.assign(propsObj, unref(mergeProps))
      return propsObj
    })

    const setProps = (props: TableProps = {}) => {
      mergeProps.value = Object.assign(unref(mergeProps), props)
      outsideProps.value = props
    }

    const setColumn = (columnProps: TableSetPropsType[], columnsChildren?: TableColumn[]) => {
      const { columns } = unref(getProps)
      for (const v of columnsChildren || columns) {
        for (const item of columnProps) {
          if (v.field === item.field) {
            set(v, item.path, item.value)
          } else if (v.children?.length) {
            setColumn(columnProps, v.children)
          }
        }
      }
    }

    const selections = ref<Recordable[]>([])

    const selectionChange = (selection: Recordable[]) => {
      selections.value = selection
      emit('selection-change', selection)
    }

    const onSortChange = ({ prop, order }) => {
      emit('sort-change', { prop, order })
    }

    const handleLinkClick = (url: string) => {
      if (typeof url !== 'string' || !url.trim()) {
        return
      }
      let fullUrl = url.trim()
      if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
        fullUrl = `https://${fullUrl}`
      }
      window.open(fullUrl, '_blank')
    }

    expose({
      setProps,
      setColumn,
      selections
    })

    const pagination = computed(() => {
      return Object.assign(
        {
          small: false,
          background: true,
          pagerCount: document.body.clientWidth < 992 ? 5 : 7,
          layout: 'total, sizes, prev, pager, next, jumper',
          pageSizes: [10, 20, 30, 50, 100, 200],
          disabled: false,
          hideOnSinglePage: false,
          total:
            unref(getProps).data.length > unref(getProps).pageSizeLimit
              ? unref(getProps).data.length
              : 0 // 当数据超过pageSizeLimit时显示分页，否则不显示
        },
        unref(getProps).pagination
      )
    })

    watch(
      () => unref(getProps).pageSize,
      (val: number) => {
        pageSizeRef.value = val
      }
    )

    watch(
      () => unref(getProps).currentPage,
      (val: number) => {
        currentPageRef.value = val
      }
    )

    watch(
      () => pageSizeRef.value,
      (val: number) => {
        emit('update:pageSize', val)
      }
    )

    watch(
      () => currentPageRef.value,
      (val: number) => {
        emit('update:currentPage', val)
      }
    )

    const getBindValue = computed(() => {
      const bindValue: Recordable = { ...attrs, ...props }
      delete bindValue.columns
      delete bindValue.data
      return bindValue
    })

    const renderTableSelection = () => {
      const { selection, reserveSelection, align, headerAlign } = unref(getProps)
      // 渲染多选
      return selection ? (
        <ElTableColumn
          type="selection"
          reserveSelection={reserveSelection}
          align="center"
          headerAlign="center"
          width="50"
          fixed="left"
        ></ElTableColumn>
      ) : undefined
    }

    // 渲染拖拽列
    const renderDragColumn = () => {
      const { align, headerAlign } = unref(getProps)
      return props.draggable ? (
        <ElTableColumn label="" width="50" fixed="left" align={align} headerAlign={headerAlign}>
          {{
            default: () => (
              <div class="drag-handle" style="cursor: move; color: #909399;">
                <Icon icon="ic:round-drag-indicator" />
              </div>
            )
          }}
        </ElTableColumn>
      ) : undefined
    }

    const renderTableExpand = () => {
      const { align, headerAlign, expand } = unref(getProps)
      // 渲染展开行
      return expand ? (
        <ElTableColumn type="expand" align={align} headerAlign={headerAlign} fixed="left">
          {{
            default: (data: TableSlotDefault) => getSlot(slots, 'expand', data)
          }}
        </ElTableColumn>
      ) : undefined
    }

    const renderTableColumn = (columnsChildren?: TableColumn[]) => {
      const {
        columns,
        reserveIndex,
        pageSize,
        currentPage,
        align,
        headerAlign,
        showOverflowTooltip,
        actionColumnWidth,
        isSort,
        data
      } = unref(getProps)

      // Filter columns based on the visible property before mapping
      // Ensure that columns from getProps (which are internalColumns) are used here
      const actualColumnsToRender = columnsChildren || unref(getProps).columns
      const visibleColumns = actualColumnsToRender.filter((column) => column.visible !== false)

      return [
        ...[renderDragColumn()],
        ...[renderTableExpand()],
        ...[renderTableSelection()]
      ].concat(
        visibleColumns.map((v) => {
          // Iterate over visibleColumns
          // 自定生成序号
          if (v.type === 'index') {
            return (
              <ElTableColumn
                type="index"
                index={
                  v.index
                    ? v.index
                    : (index) => setIndex(reserveIndex, index, pageSize, currentPage)
                }
                align={v.align || align}
                headerAlign={v.headerAlign || headerAlign}
                label={v.label}
                width="65px"
              ></ElTableColumn>
            )
          } else {
            const props = { ...v }
            if (props.children) delete props.children

            if (props.field === 'action') {
              if (actionColumnWidth) {
                props.width = actionColumnWidth
              } else {
                delete props.width
              }
            }

            // 检查是否为数字字段，如果是则自动添加 sortable: 'custom'
            const isNumericField = (data) => {
              if (!data || data.length === 0) return false
              // 检查前几行数据来判断字段类型
              const sampleSize = Math.min(5, data.length)
              for (let i = 0; i < sampleSize; i++) {
                const value = data[i][v.field]
                if (value !== null && value !== undefined && value !== '') {
                  // 检查是否为数字（包括字符串形式的数字）
                  if (!isNaN(Number(value)) && isFinite(Number(value))) {
                    return true
                  }
                }
              }
              return false
            }
            // 如果没有明确设置 sortable 且字段是数字类型，则自动设置为 'custom'
            if (
              isSort &&
              props.sortable === undefined &&
              data &&
              isNumericField(data, v.field) &&
              v.paramTypeCode != 'file' &&
              v.type != 'image'
            ) {
              props.sortable = 'custom'
            }
            const useOverflow =
              v.showOverflowTooltip !== undefined ? v.showOverflowTooltip : showOverflowTooltip
            return (
              <ElTableColumn
                showOverflowTooltip={useOverflow}
                className={useOverflow ? 'multi-line-ellipsis' : ''}
                align={v.align || align}
                headerAlign={v.headerAlign || headerAlign}
                {...props}
                prop={v.field}
                width={v.width || 'auto'}
                minWidth={
                  // 优先使用外部传入的minWidth，如果没有则使用计算逻辑
                  v.minWidth
                    ? typeof v.minWidth === 'number'
                      ? v.minWidth + 'px'
                      : v.minWidth
                    : (() => {
                        const calculatedWidth = calculateHeaderMinWidth(
                          v.label,
                          v.unit && v.needUnit === 1
                            ? getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, v.unit)
                            : v.label == '图片'
                              ? '可粘贴上传'
                              : undefined
                        )
                        return (
                          (calculatedWidth < 85 ? 85 : calculatedWidth) +
                          (props.sortable ? 24 : 0) +
                          'px'
                        )
                      })()
                }
                fixed={v.fixed}
              >
                {{
                  default: (data: TableSlotDefault) => {
                    if (v.children && v.children.length) {
                      return renderTableColumn(v.children)
                    }

                    // 预定义需要左对齐的字段列表
                    const leftAlignFields = [
                      'sku',
                      'SKU',
                      'asin',
                      '关键词',
                      '店铺名称',
                      '产品',
                      '商品',
                      '操作',
                      '广告组',
                      '人',
                      '状态'
                    ]
                    // 需要显示复制按钮的字段列表
                    const copyFields = ['sku', 'SKU', 'asin', '关键词', '店铺名称', '产品', '商品']

                    let cellContent = null
                    if (v.formatter) {
                      cellContent = v.formatter(
                        data.row,
                        data.column,
                        data.row[v.field],
                        data.$index
                      )
                    } else {
                      cellContent = getSlot(slots, v.slotsName || v.field, {
                        ...data,
                        column: v
                      })
                    }

                    if (!cellContent) {
                      // 修改功能的渲染逻辑
                      if (v.edit === 1 && data.row.isEdit && v.paramTypeCode) {
                        // 几个类渲染
                        switch (v.paramTypeCode) {
                          case 'file':
                            v.showOverflowTooltip = false
                            cellContent = (
                              <el-tooltip
                                content="点击加号旁边空白处，即可Ctrl+V粘贴图片"
                                placement="top"
                              >
                                <VxeUpload
                                  mode="image"
                                  singleMode={true}
                                  urlMode={true}
                                  showButtonText={false}
                                  pasteToUpload={true}
                                  dragToUpload={true}
                                  showPreview={true}
                                  autoHiddenButton={true}
                                  imageConfig={{
                                    circle: false,
                                    width: 40,
                                    height: 40
                                  }}
                                  v-model={data.row[v.field]}
                                  uploadMethod={uploadMethod}
                                />
                              </el-tooltip>
                            )
                            break
                          case 'input':
                            cellContent = <ElInput size="small" v-model={data.row[v.field]} />
                            break
                          case 'inputNumber':
                            cellContent = (
                              <ElInput size="small" v-model={data.row[v.field]} type="number" />
                            )
                            break
                          case 'radio':
                            cellContent = data.row[v.field] // Recommend custom slot for interactive radio
                            break
                          case 'time':
                            cellContent = (
                              <ElDatePicker
                                size="small"
                                v-model={data.row[v.field]}
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="datetime"
                                default-time={new Date().getTime()}
                                placeholder="选择日期时间"
                                style="width:100%"
                              />
                            )
                            break
                          case 'href':
                            {
                              /* const hrefValue = data.row[v.field] */
                            }
                            {
                              /* if (typeof hrefValue === 'string' && hrefValue.trim() !== '') {
                              cellContent = (
                                <ElLink href={hrefValue} target="_blank">
                                  {hrefValue}
                                </ElLink>
                              )
                            } else {
                              cellContent = hrefValue
                            } */
                            }
                            cellContent = <ElInput size="small" v-model={data.row[v.field]} />
                            break
                          default:
                            cellContent =
                              v?.formatter?.(
                                data.row,
                                data.column,
                                data.row[v.field],
                                data.$index
                              ) || data.row[v.field]
                        }
                        // 特定列的特殊渲染逻辑
                        if (v.label === 'listing文案状态') {
                          cellContent = (
                            <el-select
                              size="small"
                              modelValue={data.row[v.field]}
                              onUpdate:modelValue={(val) => (data.row[v.field] = val)}
                            >
                              {getIntDictOptions(DICT_TYPE.INFRA_LISTING_STATUS).map((item) => (
                                <el-option key={item.value} label={item.label} value={item.label} />
                              ))}
                            </el-select>
                          )
                        } else if (v.label && v.label.includes('上架')) {
                          cellContent = (
                            <el-select
                              size="small"
                              modelValue={data.row[v.field]}
                              onUpdate:modelValue={(val) => (data.row[v.field] = val)}
                            >
                              <el-option label="是" value="是" />
                              <el-option label="否" value="否" />
                            </el-select>
                          )
                        }
                      } else {
                        // 检查是否为需要复制的内容
                        const isCopyFields = copyFields.some(
                          (alignField) => v.label && v.label.includes(alignField)
                        )
                        // 非修改功能的显示逻辑
                        if (v.paramTypeCode == 'href') {
                          // 链接可跳转
                          const hrefValue = data.row[v.field]
                          if (typeof hrefValue === 'string' && hrefValue.trim() !== '') {
                            cellContent = (
                              <div
                                onClick={() => handleLinkClick(hrefValue)}
                                style="cursor: pointer; color: var(--el-color-primary);"
                              >
                                {hrefValue}
                              </div>
                            )
                          } else {
                            cellContent = hrefValue
                          }
                        } else if (v.paramTypeCode == 'file' || v.type == 'image') {
                          v.showOverflowTooltip = false
                          {
                            /* 如果是开发表、审核表、投产表，就可以点击图片预览大图，否则不行 */
                          }
                          const typeToUse = props.type || (route.name as string)
                          if (['DevTabledex', 'DevAuditIndex', 'ProdIndex'].includes(typeToUse)) {
                            cellContent = (
                              <ElImage
                                src={data.row[v.field]}
                                loading={'lazy'}
                                width={40}
                                previewSrcList={[data.row[v.field]]}
                                previewTeleported={true}
                              />
                            )
                          } else {
                            cellContent = (
                              <ElImage src={data.row[v.field]} loading={'lazy'} width={40} />
                            )
                          }
                        } else if (
                          data.row[v.field] &&
                          data.row[v.field] != '' &&
                          (v.isCopy || isCopyFields)
                        ) {
                          // 这里处理显示复制按钮,如果有数据，且是复制字段
                          cellContent = (
                            <div v-clipboard={data.row[v.field]}>{data.row[v.field]}</div>
                          )
                        } else {
                          cellContent =
                            v?.formatter?.(data.row, data.column, data.row[v.field], data.$index) ||
                            data.row[v.field]
                        }
                      }
                      // 特定列的特殊渲染逻辑
                      if (v.label === '审核状态' || v.field === 'auditStatus') {
                        const statusItem = getIntDictOptions(
                          DICT_TYPE.INFRA_TABLE_APPROVAL_STATUS
                        ).find((item) => item.label == data.row[v.field])
                        const statusValue = statusItem ? statusItem.value : undefined
                        // 定义一个辅助函数来确定标签类型
                        const getAuditStatusType = (status) => {
                          switch (status) {
                            case 1: // 已通过
                              return 'success'
                            case 2: // 已拒绝
                              return 'danger'
                            case 3: // 待审核
                              return 'info'
                            case 4: // 草稿
                              return 'info'
                            default:
                              return 'info'
                          }
                        }
                        cellContent = (
                          <ElTag type={getAuditStatusType(statusValue)}>{data.row[v.field]}</ElTag>
                        )
                      } else if (v.label === '是否投产' || v.field === 'production') {
                        const statusValue = data.row[v.field] == '是' ? '1' : '3' // 假设 '是' 对应 1，其他对应 3
                        // 定义一个辅助函数来确定标签类型 (可与上面的函数合并或独立)
                        const getProductionStatusType = (status) => {
                          switch (status) {
                            case '1': // 是
                              return 'success'
                            case '3': // 否 或其他
                              return 'info'
                            default:
                              return 'info'
                          }
                        }
                        cellContent = (
                          <ElTag type={getProductionStatusType(statusValue)}>
                            {data.row[v.field]}
                          </ElTag>
                        )
                      } else if (v.label === 'ASIN' || v.field === 'asin') {
                        // 链接可跳转
                        const hrefValue = data.row['asinUrl']
                        if (typeof hrefValue === 'string' && hrefValue.trim() !== '') {
                          cellContent = (
                            <div
                              onClick={() => handleLinkClick(hrefValue)}
                              style="cursor: pointer; color: var(--el-color-primary);"
                            >
                              {data.row[v.field]}
                            </div>
                          )
                        } else {
                          cellContent = hrefValue
                        }
                      }
                      // 检查是否需要单独设置对齐方式
                      const isAlignFields = leftAlignFields.some(
                        (alignField) => v.label && v.label.includes(alignField)
                      )
                      if (isAlignFields) {
                        v.align = 'left'
                      }
                    }
                    return cellContent
                  },
                  header: (scope) => {
                    const customHeader = getSlot(slots, `${v.field}-header`, scope)
                    if (customHeader) return customHeader

                    const title = v.label
                    const subTitle =
                      v.unit && v.needUnit === 1
                        ? getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, v.unit)
                        : v.type != 'image' && title == '图片'
                          ? '可粘贴上传'
                          : ''

                    // 检查当前表格是否有任何列包含单位
                    const hasAnyUnit = unref(getProps).columns.some(
                      (col) => col.unit && col.needUnit === 1
                    )

                    return (
                      <div class={`column-header-content ${hasAnyUnit ? 'has-unit' : 'no-unit'}`}>
                        <el-tooltip
                          content={v.tips || title + (subTitle && `(${subTitle})`)}
                          placement="top"
                          rawContent={true}
                        >
                          <span
                            class="column-header-title flex items-center gap-4px"
                            style={v.style}
                          >
                            {title}
                            {v.icon && <Icon icon={v.icon} size="12" />}
                          </span>
                        </el-tooltip>
                        {subTitle && <div class="sub-title">{subTitle || ''}</div>}
                      </div>
                    )
                  }
                }}
              </ElTableColumn>
            )
          }
        })
      )
    }

    return () => (
      <div v-loading={unref(getProps).loading} style="position: relative;">
        {/* 自动保存状态提示 */}
        {props.saveStatus && (
          <div
            ref={saveStatusRef}
            class="save-status-container"
            style="position: absolute; top: 5px; z-index: 1000; pointer-events: none"
          >
            <div name="save-status" mode="out-in">
              <ElTag
                v-show={
                  props.saveStatus.lastSaved || props.saveStatus.saving || props.saveStatus.error
                }
                type={
                  props.saveStatus.error
                    ? 'danger'
                    : props.saveStatus.saving
                      ? 'warning'
                      : 'success'
                }
                size="small"
                class="save-status-tag flex"
                style="pointer-events: auto"
              >
                {props.saveStatus.saving && (
                  <>
                    <Icon icon="ep:loading" class="animate-spin mr-1" />
                    正在保存...
                  </>
                )}
                {!props.saveStatus.saving && props.saveStatus.error && (
                  <>
                    <Icon icon="ep:warning" class="mr-1" />
                    {props.saveStatus.error}
                  </>
                )}
                {!props.saveStatus.saving &&
                  !props.saveStatus.error &&
                  props.saveStatus.lastSaved && (
                    <>
                      <Icon icon="ep:check" class="mr-1" />
                      已自动保存 {new Date(props.saveStatus.lastSaved).toLocaleTimeString()}
                    </>
                  )}
              </ElTag>
            </div>
          </div>
        )}
        {/* 列配置按钮 */}
        <div class="table-toolbar" ref={toolbarRef}>
          <div class="flex">{getSlot(slots, 'top-btn')}</div>
          <div class="flex items-center gap-2" ref={rightSectionRef}>
            {/* 列配置按钮旁边的自定义插槽 */}
            {getSlot(slots, 'top-btn-right')}
            {/* 使用 v-model 直接绑定 internalColumns */}
            {props.showColumnConfig && (
              <TableColumnConfig
                columns={props.columns} /* 原始列定义，用于配置界面显示所有可选列 */
                modelValue={internalColumns.value} /* 当前生效的列配置 */
                onUpdate:modelValue={(newColumns: TableColumn[]) => {
                  handleColumnConfigUpdate(newColumns)
                  // 列配置变化后重新计算保存状态位置
                  nextTick(() => updateSaveStatusPosition())
                }} /* 更新列配置的回调 */
                type={props.type}
              />
            )}
          </div>
        </div>
        <ElTable
          ref={elTableRef}
          data={unref(getProps).data}
          onSelection-change={selectionChange}
          {...unref(getBindValue)}
          onHeaderDragstart={handleHeaderDragStart}
          onHeaderDragend={handleHeaderDragEnd}
          onSort-change={onSortChange}
          row-key={
            typeof props.dragField === 'function' ? props.dragField : props.dragField || 'id'
          }
        >
          {{
            default: () => renderTableColumn(),
            append: () => getSlot(slots, 'append')
          }}
        </ElTable>
        {unref(getProps).pagination ? (
          <ElPagination
            pageSize={pageSizeRef.value}
            onUpdate:pageSize={(val: number) => {
              pageSizeRef.value = val
              currentPageRef.value = 1
            }}
            v-model:currentPage={currentPageRef.value}
            class="float-right mb-15px mt-15px"
            {...unref(pagination)}
          />
        ) : undefined}
      </div>
    )
  }
})

// 上传文件
const uploadMethod = async ({ file, updateProgress }) => {
  const formData = new FormData()
  formData.append('file', file)
  const method = defaultUploadImage
  return method(formData, updateProgress)
}

// 上传图片默认方法
const defaultUploadImage = async (formData, updateProgress: (percent) => void) => {
  const res = await FileApi.updateFile(formData)
  updateProgress(100)
  return { ...res, url: res.data }
}
</script>
<style lang="scss" scoped>
:deep(.el-popper) {
  max-width: 700px !important;
}

/* 自动保存状态提示的过渡动画 */
.save-status-enter-active,
.save-status-leave-active {
  transition: all 0.3s ease;
}

.save-status-enter-from,
.save-status-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.save-status-tag {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);

  :deep(.el-icon span) {
    font-size: 12px !important;
  }
}

:deep(.el-button.is-text) {
  padding: 8px 4px;
  margin-left: 0;
}

:deep(.el-button.is-link) {
  padding: 8px 4px;
  margin-left: 0;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

// 表头内容样式
.column-header-content {
  display: flex;
  width: max-content;
  white-space: nowrap;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐

  // 默认居中对齐
  align-items: center;

  // 有单位时设置较大的最小高度
  &.has-unit {
    // min-height: 48px; // 增加最小高度以容纳两行内容  //这个是控制表头标题高度的，如果有单位的时候，不想波浪，想要想统一高度的话就用这个
  }

  // 无单位时设置较小的最小高度
  &.no-unit {
    min-height: unset; // 较小的最小高度，避免表头过高
  }

  // 根据父级对齐方式调整内容对齐
  .is-left & {
    align-items: flex-start;
  }

  .is-center & {
    align-items: center;
  }

  .is-right & {
    align-items: flex-end;
  }
}

.column-header-title {
  display: flex;
  height: 20px; // 固定标题高度
  padding: 0 4px;
  overflow: hidden;
  font-size: 13px;
  font-weight: bold;
  line-height: 1.2;
  color: #606266;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
  align-items: center; // 垂直居中对齐
}

.sub-title {
  display: flex;
  width: 100%;
  height: 16px; // 固定单位高度
  margin-top: 2px; // 标题和单位之间的间距
  overflow: hidden;
  font-size: 11px;
  line-height: 1;
  color: #999;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  align-items: center;
  justify-content: center;
}

// 拖拽相关样式
:deep(.drag-handle) {
  display: flex;
  color: #909399;
  cursor: move;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #409eff;
  }
}

:deep(.sortable-ghost) {
  background-color: #f5f7fa;
  opacity: 0.5;
}

:deep(.sortable-chosen) {
  background-color: #ecf5ff;
}

// 表格单元格内容两行超出省略显示
:deep(.el-table .cell) {
  display: -webkit-box;
  max-height: 46px;
  overflow: hidden;
  //line-height: 1.4;
  text-overflow: ellipsis;
  word-break: break-word;
  white-space: normal;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

:deep(.el-table__header .cell) {
  display: flex;
  padding: 0 4px; // 适当的内边距
  align-items: center;
  gap: 2px; // 标题和排序按钮之间的间距
}

// 根据对齐方式设置表头内容对齐
:deep(.el-table__header .el-table__cell.is-left .cell) {
  justify-content: flex-start;
}

:deep(.el-table__header .el-table__cell.is-center .cell) {
  justify-content: center;
}

:deep(.el-table__header .el-table__cell.is-right .cell) {
  justify-content: flex-end;
}

// 默认情况下表头居中对齐（当没有明确指定对齐方式时）
:deep(.el-table__header .el-table__cell:not(.is-left, .is-right) .cell) {
  justify-content: center;
}

:deep(.el-table__header th) {
  min-width: 50px !important; // 强制设置最小宽度
  padding: 8px 0; // 减少表头行高
  background-color: #f5f7fa !important; // 浅灰色背景
  border-right: 1px solid #dcdfe6 !important; // 添加右边框，使列分隔线更明显
}

// 强制限制所有列的最小宽度
:deep(.el-table__body col),
:deep(.el-table__header col) {
  min-width: 50px !important;
}

// 防止拖拽时列宽度小于最小值
:deep(.el-table .el-table__header-wrapper .el-table__header th) {
  min-width: 50px !important;
}

:deep(.el-table .el-table__body-wrapper .el-table__body td) {
  min-width: 50px !important;
}

:deep(.el-table__header .el-table-fixed-column--left) {
  background-color: #f5f7fa !important; // 浅灰色背景
  border-right: 1px solid #dcdfe6 !important; // 添加右边框，使列分隔线更明显
}

// 最后一列不需要右边框
:deep(.el-table__header th:last-child) {
  border-right: none;
}

:deep(.el-table__header .sort-caret) {
  left: 0;
  width: 0;
  height: 0;
  border: 4px solid transparent; // 减小排序箭头大小
}

:deep(.el-table__header .ascending) {
  // top: -2px !important;
  border-bottom-color: #c0c4cc;
}

:deep(.el-table__header .descending) {
  // bottom: -2px !important;
  border-top-color: #c0c4cc;
}
</style>
