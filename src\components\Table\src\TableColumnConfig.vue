<template>
  <div class="table-column-config">
    <el-tooltip :hide-after="0" effect="dark" content="列配置" placement="top">
      <!-- 触发按钮 -->
      <el-button type="text" class="table-column-config__button" @click="showDialog = true">
        <img @click="showDialog = true" :src="liepeizhi" width="18px" height="18px" />
        <div v-if="btnText" class="m-l-5px">{{ btnText }}</div>
      </el-button>
    </el-tooltip>

    <!-- 配置弹窗 -->
    <el-dialog v-model="showDialog" title="列配置" width="800px" :before-close="handleClose">
      <!-- 顶部操作区 -->
      <div class="config-header">
        <div class="config-header-left">
          <el-select
            v-model="selectedPreset"
            placeholder="选择保存的配置"
            style="width: 200px"
            clearable
            @change="loadPreset"
          >
            <el-option
              v-for="preset in presets"
              :key="preset.id"
              :label="preset.name"
              :value="preset.id"
            />
          </el-select>
        </div>
        <div class="config-header-right">
          <el-button type="primary" @click="saveAsPreset"> 保存为常用配置 </el-button>
        </div>
      </div>

      <!-- 主体内容区 -->
      <div class="config-content">
        <!-- 左侧分类区 -->
        <div class="config-left">
          <div class="category-title">表头分类</div>
          <div v-for="category in categories" :key="category.name" class="category-item">
            <div class="category-header">
              <el-checkbox
                size="small"
                :model-value="isCategoryAllSelected(category.name)"
                :indeterminate="isCategoryIndeterminate(category.name)"
                @change="toggleCategoryAll(category.name, $event)"
                @click.stop
              />
              <span class="category-name">{{ category.label }}</span>
              <el-button
                type="text"
                size="small"
                @click.stop="
                  toggleCategoryAll(category.name, !isCategoryAllSelected(category.name))
                "
              >
                {{ isCategoryAllSelected(category.name) ? '取消全选' : '全选' }}
              </el-button>
            </div>
            <div class="category-columns">
              <template v-for="column in category.columns" :key="column.field">
                <!-- {{column}} -->
                <div
                  class="column-item"
                  v-if="
                    column.field !== 'drag' && column.field !== 'action' && column.fixed != 'right'
                  "
                >
                  <el-checkbox
                    size="small"
                    :model-value="isColumnSelected(column.field)"
                    @change="toggleColumn(column, $event)"
                  >
                    <div class="max-w-120px whitespace-normal break-words">{{ column.label }}</div>
                  </el-checkbox>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 右侧已选列表 -->
        <div class="config-right">
          <div class="selected-title">
            <span>已选表头 ({{ filteredSelectedColumns.length }})</span>
            <el-input
              v-model="filterText"
              placeholder="搜索表头"
              size="small"
              style="width: 150px"
              :prefix-icon="Search"
            />
          </div>

          <draggable v-model="filteredSelectedColumns" class="selected-list" item-key="field">
            <template #item="{ element, index }">
              <div
                v-if="
                  element.field !== 'drag' && element.field !== 'action' && element.fixed != 'right'
                "
                class="selected-item"
                @mouseenter="hoveredIndex = index"
                @mouseleave="hoveredIndex = -1"
              >
                <div class="selected-item-content">
                  <div class="flex items-center gap-5px">
                    <Icon
                      icon="ic:round-drag-indicator"
                      style="font-size: 16px; color: #8a909c"
                      class="drag-handle"
                    />
                    <span class="column-label">{{ element.label }}</span>
                  </div>
                  <div class="flex items-center gap-10px content-right">
                    <div class="flex gap-10px" v-show="hoveredIndex === index">
                      <el-tooltip
                        :hide-after="0"
                        effect="dark"
                        content="删除"
                        placement="top"
                        :disabled="hoveredIndex !== index"
                      >
                        <el-icon size="16" class="hover-icon" @click="deleteColumn(element)">
                          <CircleClose />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip
                        :hide-after="0"
                        effect="dark"
                        content="置顶"
                        placement="top"
                        :disabled="hoveredIndex !== index || index === 0"
                      >
                        <el-icon
                          size="16"
                          class="hover-icon"
                          v-if="index !== 0"
                          @click="moveToTop(index)"
                        >
                          <Upload />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip
                        :hide-after="0"
                        effect="dark"
                        content="固定到左侧"
                        placement="top"
                        :disabled="hoveredIndex !== index"
                      >
                        <img
                          class="hover-icon"
                          v-if="!element.fixed || element.fixed === ''"
                          @click="toggleFixed(element, 'left')"
                          :src="fixedImg"
                          width="16px"
                          height="16px"
                        />
                      </el-tooltip>
                    </div>
                    <el-tooltip
                      :hide-after="0"
                      effect="dark"
                      content="取消固定"
                      placement="top"
                      :disabled="hoveredIndex !== index"
                    >
                      <img
                        class="hover-icon"
                        v-if="element.fixed === 'left' || element.fixed === 'right'"
                        @click="toggleFixed(element, '')"
                        :src="unfixedImg"
                        width="16px"
                        height="16px"
                      />
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>

      <!-- 底部操作区 -->
      <template #footer>
        <div class="config-footer">
          <el-button @click="resetToDefault">恢复默认</el-button>
          <div class="footer-right">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="applyConfig">保存应用</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import * as ToolsTableHeadTemplateApi from '@/api/infra/toolsTableHeadTemplate'
import { getTableHeadByTemplateId } from '@/api/tool/tableTemplateCenter'
import fixedImg from '@/assets/svgs/fixed.svg'
import liepeizhi from '@/assets/svgs/liepeizhi.svg'
import unfixedImg from '@/assets/svgs/unfixed.svg'
import type { TableColumn } from '@/types/table'
import { CircleClose, Search, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import draggable from 'vuedraggable'

interface CategoryConfig {
  name: string
  label: string
  columns: TableColumn[]
}

interface PresetConfig {
  id: string
  name: string
  columns: string[]
}

const route = useRoute()
// 添加标记，表示是否已从接口获取到数据
const hasLoadedFromApi = ref(false)
const props = defineProps<{
  columns: TableColumn[] // 原始列定义
  modelValue: TableColumn[] // 当前应用的列配置 (v-model)
  type?: string // 新增 type prop，用于接收外部传入的表格类型
  tableData?: any[] // 新增 tableData prop，用于接收列表数据
  btnText?: string // 新增 btnText prop，用于接收按钮文本
}>()

const emit = defineEmits<{
  'update:modelValue': [columns: TableColumn[]] // 更新父组件的列配置
}>()

// 统一的接口数据获取和处理方法
const initializeFromApi = async (isInitialLoad = false) => {
  try {
    let typeParam = route.name as string
    if (props.type) {
      typeParam = props.type
    }

    const res = await ToolsTableHeadTemplateApi.getToolsTableHeadTemplateList({
      type: typeParam
    })

    if (res && res.length > 0) {
      // 设置预设列表
      presets.value = res.map((item) => ({
        id: String(item.id),
        name: item.template,
        columns: item.content ? (JSON.parse(item.content) as TableColumn[]) : []
      }))

      // 获取第一个模板的内容作为表头数据
      const firstTemplate = res[0]
      if (firstTemplate.content) {
        try {
          const tableHeadData = JSON.parse(firstTemplate.content)
          if (Array.isArray(tableHeadData) && tableHeadData.length > 0) {
            // 获取外部列数据作为基准
            const externalColumns = filterConfigurableColumns(props.columns)

            // 优化后的数据合并逻辑：
            // 1. 字段定义以外部传入的为准（字段定义、标签等）
            // 2. 排序以接口返回的数据顺序为准，严格执行fixed：left的在最前面，没设置fixed的，排序在fixed后，按照顺序执行
            // 3. 宽度、visible、fixed状态以接口返回的数据为准
            // 4. 设置顺序字段，以fixed为优先级依次排序
            const mergedColumns: TableColumn[] = []

            // 首先按照接口数据的顺序处理，并添加sort字段
            tableHeadData.forEach((apiCol, index) => {
              const externalCol = externalColumns.find((extCol) => extCol.field === apiCol.field)
              if (externalCol) {
                // 找到对应的外部列，合并数据
                mergedColumns.push({
                  ...externalCol, // 以外部数据为基准（字段定义、标签等）
                  // 接口数据优先的属性（宽度、visible、fixed状态）
                  width: apiCol.width !== undefined ? apiCol.width : externalCol.width,
                  visible: apiCol.visible !== undefined ? apiCol.visible : externalCol.visible,
                  fixed: apiCol.fixed !== undefined ? apiCol.fixed : externalCol.fixed,
                  sort: index // 添加排序字段，记录接口返回的原始顺序
                })
              }
            })

            // 然后添加外部数据中存在但接口数据中不存在的列（保持在末尾）
            externalColumns.forEach((externalCol) => {
              const existsInApi = tableHeadData.some((apiCol) => apiCol.field === externalCol.field)
              if (!existsInApi) {
                mergedColumns.push({
                  ...externalCol,
                  sort: tableHeadData.length + mergedColumns.filter(col => !tableHeadData.some(api => api.field === col.field)).length // 新增列排在最后
                })
              }
            })

            // 应用合并后的数据并按照fixed优先级和接口顺序排序
            tempSelectedColumns.value = mergedColumns
            // 初始化时对固定列进行排序（按照新的排序逻辑）
            sortColumnsByFixedAndApiOrder()
              hasLoadedFromApi.value = true
              selectedPreset.value = presets.value[0].id

              if (isInitialLoad) {
                emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
                console.log('组件初始化时以外部数据为准，排序和宽度以接口数据为准')
              }
              return true
          }
        } catch (parseError) {
          console.error('解析表头数据失败:', parseError)
        }
      }
    } else {
      // 没有数据时重置预设
      presets.value = []
      selectedPreset.value = ''

      // 检查是否为特定页面，如果是则使用外部传入的表头数据
      const targetRoutes = ['DevAuditIndex', 'DevTabledex', 'ProdIndex']
      const currentRouteName = route.name as string

      if (targetRoutes.includes(currentRouteName) && props.columns && props.columns.length > 0) {
        // 使用外部传入的表头数据
        const externalColumns = props.columns.map((col: TableColumn) => ({
          ...col,
          visible: col.visible !== undefined ? col.visible : true
        }))

        tempSelectedColumns.value = externalColumns
        // 初始化时对固定列进行排序
        sortColumnsByFixed() // 这里使用原有排序逻辑，因为没有接口数据
        hasLoadedFromApi.value = true

        if (isInitialLoad) {
          emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
          console.log('组件初始化时使用外部传入的表头数据（API返回为空）')
        }
        return true
      }
    }
  } catch (error) {
    console.error('获取ToolsTableHeadTemplateApi.getToolsTableHeadTemplateList接口数据失败:', error)
    if (isInitialLoad) {
      ElMessage.error('获取表头模板列表失败')
    }
    presets.value = []
    selectedPreset.value = ''

    // 检查是否为特定页面，如果是则使用外部传入的表头数据作为降级方案
    const targetRoutes = ['DevAuditIndex', 'DevTabledex', 'ProdIndex']
    const currentRouteName = route.name as string

    if (targetRoutes.includes(currentRouteName) && props.columns && props.columns.length > 0) {
      // 使用外部传入的表头数据作为降级方案
      const externalColumns = props.columns.map((col: TableColumn) => ({
        ...col,
        visible: col.visible !== undefined ? col.visible : true
      }))

      tempSelectedColumns.value = externalColumns
      // 初始化时对固定列进行排序
      sortColumnsByFixed() // 这里使用原有排序逻辑，因为没有接口数据
      hasLoadedFromApi.value = true

      if (isInitialLoad) {
        emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
        console.log('组件初始化时使用外部传入的表头数据（API调用失败降级）')
      }
      return true
    }
  }
  return false
}

// 基础状态
const showDialog = ref(false) // 控制对话框显示
const filterText = ref('') // 已选列的筛选文本
const hoveredIndex = ref(-1) // 鼠标悬浮在已选列的索引
const selectedPreset = ref('') // 当前选中的预设配置ID

// 对话框内部临时的列配置状态，用于编辑，不直接修改 props.modelValue
const tempSelectedColumns = ref<TableColumn[]>([])

// 预设配置（模拟数据，实际应从后端获取或存储在本地）
const presets = ref<PresetConfig[]>([
  { id: '1', name: '默认配置', columns: [] }, // 示例预设
  { id: '2', name: '简化配置', columns: [] } // 示例预设
])

// 过滤掉不应在配置中显示的特殊列（如拖拽列、操作列），并初始化 visible 属性
const filterConfigurableColumns = (columns: TableColumn[]): TableColumn[] => {
  return JSON.parse(JSON.stringify(columns)).map((col: TableColumn) => ({
    ...col,
    visible: col.visible !== undefined ? col.visible : true
  }))
}

// 所有可配置的原始列（已过滤特殊列）
const allConfigurableColumns = computed(() => {
  // 如果已从接口获取到数据，则基于tempSelectedColumns来构建所有可配置列
  // 这样可以保持接口数据的完整性，避免被props.columns覆盖
  if (hasLoadedFromApi.value && tempSelectedColumns.value.length > 0) {
    return tempSelectedColumns.value
  }
  // 否则使用props.columns作为基础
  return filterConfigurableColumns(props.columns)
})

// 分类配置，基于所有可配置的原始列
const categories = computed<CategoryConfig[]>(() => {
  const categoryMap = new Map<string, TableColumn[]>()
  allConfigurableColumns.value.forEach((column) => {
    const categoryName = column.categoryName || '基本信息' // 默认分类
    if (!categoryMap.has(categoryName)) {
      categoryMap.set(categoryName, [])
    }
    categoryMap.get(categoryName)!.push(column)
  })
  return Array.from(categoryMap.entries()).map(([name, columns]) => ({
    name,
    label: name,
    columns
  }))
})

// 过滤后的已选列（实际是可见列），用于右侧显示，基于 tempSelectedColumns
const filteredSelectedColumns = computed({
  get: () => {
    const visibleColumns = tempSelectedColumns.value.filter((col) => col.visible)
    if (!filterText.value) return visibleColumns
    return visibleColumns.filter((col) => col.label?.includes(filterText.value))
  },
  set: (newOrder) => {
    // 当拖拽发生时，newOrder 是可见列的新顺序
    // 我们需要根据这个新顺序更新 tempSelectedColumns 中所有列的顺序
    const newTempSelectedColumns: TableColumn[] = []
    // 先把拖拽排序的可见列按新顺序放进去
    newOrder.forEach((orderedCol) => {
      const found = tempSelectedColumns.value.find((tc) => tc.field === orderedCol.field)
      if (found) newTempSelectedColumns.push(found)
    })
    // 再把不可见列按原顺序追加到后面
    tempSelectedColumns.value.forEach((tc) => {
      if (!tc.visible && !newTempSelectedColumns.some((ntc) => ntc.field === tc.field)) {
        newTempSelectedColumns.push(tc)
      }
    })
    tempSelectedColumns.value = newTempSelectedColumns
  }
})
// 注意：draggable 的 v-model 现在直接绑定到 tempSelectedColumns，
// 所以不再需要 filteredSelectedColumns 的 set 方法来间接修改 selectedColumns。

// 分类选择逻辑：判断列是否在临时的已选列中且 visible 为 true
const isColumnSelected = (field: string) => {
  const col = tempSelectedColumns.value.find((c) => c.field === field)
  return col ? col.visible : false
}

const isCategoryAllSelected = (categoryName: string) => {
  const category = categories.value.find((cat) => cat.name === categoryName)
  if (!category) return false
  return category.columns.every((col) => isColumnSelected(col.field))
}

const isCategoryIndeterminate = (categoryName: string) => {
  const category = categories.value.find((cat) => cat.name === categoryName)
  if (!category) return false
  const selectedCount = category.columns.filter((col) => isColumnSelected(col.field)).length
  return selectedCount > 0 && selectedCount < category.columns.length
}

// 切换列的选中状态（更新 visible 属性）
const toggleColumn = (column: TableColumn, selected: boolean) => {
  const targetColumn = tempSelectedColumns.value.find((col) => col.field === column.field)
  if (targetColumn) {
    targetColumn.visible = selected
  } else if (selected) {
    // 如果列不在 tempSelectedColumns 中（理论上初始化时都应该在），则添加并设置 visible
    tempSelectedColumns.value.push({
      ...column,
      visible: column.visible != undefined ? column.visible : true
    })
  }
  // 确保 tempSelectedColumns 包含所有 allConfigurableColumns 中的列，只是 visible 不同
  // 这一步会在初始化 tempSelectedColumns 时处理，这里只关注切换
}

const toggleCategoryAll = (categoryName: string, selectAll: boolean) => {
  const category = categories.value.find((cat) => cat.name === categoryName)
  if (!category) return

  category.columns.forEach((column) => {
    const targetColumn = tempSelectedColumns.value.find((col) => col.field === column.field)
    if (targetColumn) {
      targetColumn.visible = selectAll
    } else if (selectAll) {
      // 如果列不在 tempSelectedColumns 中，则添加并设置 visible
      tempSelectedColumns.value.push({
        ...column,
        visible: column.visible != undefined ? column.visible : true
      })
    }
  })
}

// 删除标题 (实际是取消勾选，设置 visible 为 false)
const deleteColumn = (column: TableColumn) => {
  const targetColumn = tempSelectedColumns.value.find((col) => col.field === column.field)
  if (targetColumn) {
    targetColumn.visible = false
  }
}

// 右侧操作逻辑：置顶临时的已选列中的项
const moveToTop = (index: number) => {
  // 获取可见列列表
  const visibleColumns = tempSelectedColumns.value.filter(col => col.visible)
  if (index < 0 || index >= visibleColumns.length) return
  
  // 获取要置顶的列
  const targetColumn = visibleColumns[index]
  const targetIndex = tempSelectedColumns.value.findIndex(col => col.field === targetColumn.field)
  
  if (targetIndex === -1) return
  
  // 从原位置移除
  const [removedColumn] = tempSelectedColumns.value.splice(targetIndex, 1)
  
  // 找到所有固定到左侧的列的最后位置
  const fixedLeftColumns = tempSelectedColumns.value.filter(col => col.fixed === 'left')
  const insertPosition = fixedLeftColumns.length
  
  // 插入到固定列之后的第一个位置
  tempSelectedColumns.value.splice(insertPosition, 0, removedColumn)
}

// 固定到左侧的逻辑：应该将列移动到所有固定列的最后位置
const moveToFixedLeft = (column: TableColumn) => {
  const targetColumn = tempSelectedColumns.value.find((col) => col.field === column.field)
  if (!targetColumn) return

  const currentIndex = tempSelectedColumns.value.indexOf(targetColumn)
  
  // 移除当前列
  const [removedColumn] = tempSelectedColumns.value.splice(currentIndex, 1)
  
  // 设置为左侧固定
  removedColumn.fixed = 'left'
  
  // 插入到最前面（所有固定列都应该在最前面）
  tempSelectedColumns.value.unshift(removedColumn)
  
  // 重新排序，确保固定列在最前面且保持原有顺序
  if (hasLoadedFromApi.value) {
    sortColumnsByFixedAndApiOrder()
  } else {
    sortColumnsByFixed()
  }
}

// 取消固定的逻辑
const cancelFixed = (column: TableColumn) => {
  const targetColumn = tempSelectedColumns.value.find((col) => col.field === column.field)
  if (!targetColumn) return

  const currentIndex = tempSelectedColumns.value.indexOf(targetColumn)
  
  // 移除当前列
  const [removedColumn] = tempSelectedColumns.value.splice(currentIndex, 1)
  
  // 取消固定状态
  removedColumn.fixed = ''
  delete removedColumn.fixed
  
  // 找到所有固定列的最后位置，插入到非固定区域的开始
  const fixedColumns = tempSelectedColumns.value.filter((col) => col.fixed === 'left' || col.fixed === 'right')
  const insertPosition = fixedColumns.length
  
  // 插入到非固定区域的开始位置
  tempSelectedColumns.value.splice(insertPosition, 0, removedColumn)
  
  // 重新排序，确保固定列在最前面
  if (hasLoadedFromApi.value) {
    sortColumnsByFixedAndApiOrder()
  } else {
    sortColumnsByFixed()
  }
}

// 新的列排序函数：严格按照fixed优先级和接口返回的顺序排序
const sortColumnsByFixedAndApiOrder = () => {
  // 分离固定列和非固定列
  const fixedLeftColumns = tempSelectedColumns.value.filter(col => col.fixed === 'left')
  const fixedRightColumns = tempSelectedColumns.value.filter(col => col.fixed === 'right')
  const normalColumns = tempSelectedColumns.value.filter(col => !col.fixed || col.fixed === '')
  
  // 按照接口返回的顺序排序（使用sort字段）
  const sortByApiOrder = (columns: TableColumn[]) => {
    return columns.sort((a, b) => {
      const sortA = a.sort !== undefined ? a.sort : Number.MAX_SAFE_INTEGER
      const sortB = b.sort !== undefined ? b.sort : Number.MAX_SAFE_INTEGER
      return sortA - sortB
    })
  }
  
  // 重新组合：固定左侧 + 普通列 + 固定右侧（每个分组内部按接口顺序排序）
  tempSelectedColumns.value = [
    ...sortByApiOrder(fixedLeftColumns),
    ...sortByApiOrder(normalColumns),
    ...sortByApiOrder(fixedRightColumns)
  ]
}

// 保留原有的排序函数作为降级方案（当没有接口数据时使用）
const sortColumnsByFixed = () => {
  // 分离固定列和非固定列
  const fixedLeftColumns = tempSelectedColumns.value.filter(col => col.fixed === 'left')
  const fixedRightColumns = tempSelectedColumns.value.filter(col => col.fixed === 'right')
  const normalColumns = tempSelectedColumns.value.filter(col => !col.fixed || col.fixed === '')
  
  // 获取外部列的原始顺序作为参考
  const originalOrder = props.columns.map(col => col.field)
  
  // 对每个分组内部按照原始顺序排序
  const sortByOriginalOrder = (columns: TableColumn[]) => {
    return columns.sort((a, b) => {
      const indexA = originalOrder.indexOf(a.field)
      const indexB = originalOrder.indexOf(b.field)
      return indexA - indexB
    })
  }
  
  // 重新组合：固定左侧 + 普通列 + 固定右侧
  tempSelectedColumns.value = [
    ...sortByOriginalOrder(fixedLeftColumns),
    ...sortByOriginalOrder(normalColumns),
    ...sortByOriginalOrder(fixedRightColumns)
  ]
}

// 切换临时的已选列中项的固定状态
const toggleFixed = (column: TableColumn, fixed?: 'left' | 'right' | '') => {
  if (fixed === 'left') {
    moveToFixedLeft(column)
  } else if (fixed === '') {
    cancelFixed(column)
  }
}

// 预设配置逻辑：加载预设
const loadPreset = async (presetId: string, isInitialLoad = false) => {
  const preset = presets.value.find((p) => p.id === presetId)
  if (preset) {
    // 检查当前路由是否为指定的页面
    const targetRoutes = ['DevAuditIndex', 'DevTabledex', 'ProdIndex']
    const currentRouteName = route.name as string

    // 需求2：在特定路由页面切换保存的配置时调用表头接口
    if (!isInitialLoad && targetRoutes.includes(currentRouteName)) {
      try {
        const templateId = props.type || currentRouteName
        const response = await getTableHeadByTemplateId(templateId, presetId)

        // 使用接口返回的tableHead数据作为表头数据源
        if (response && response.tableHead) {
          try {
            const tableHeadData = JSON.parse(response.tableHead)
            // 根据tableHead数据更新tempSelectedColumns
            if (Array.isArray(tableHeadData)) {
              // 获取外部列数据作为基准
              const externalColumns = filterConfigurableColumns(props.columns)

              // 优化后的数据合并逻辑：
              // 1. 字段定义以外部传入的为准（字段定义、标签等）
              // 2. 排序以接口返回的数据顺序为准，严格执行fixed：left的在最前面，没设置fixed的，排序在fixed后，按照顺序执行
              // 3. 宽度、visible、fixed状态以接口返回的数据为准
              // 4. 设置顺序字段，以fixed为优先级依次排序
              const mergedColumns: TableColumn[] = []

              // 首先按照接口数据的顺序处理，并添加sort字段
              tableHeadData.forEach((apiCol, index) => {
                const externalCol = externalColumns.find((extCol) => extCol.field === apiCol.field)
                if (externalCol) {
                  // 找到对应的外部列，合并数据
                  mergedColumns.push({
                    ...externalCol, // 以外部数据为基准（字段定义、标签等）
                    // 接口数据优先的属性（宽度、visible、fixed状态）
                    width: apiCol.width !== undefined ? apiCol.width : externalCol.width,
                    visible: apiCol.visible !== undefined ? apiCol.visible : true,
                    fixed: apiCol.fixed !== undefined ? apiCol.fixed : externalCol.fixed,
                    sort: index // 添加排序字段，记录接口返回的原始顺序
                  })
                }
              })

              // 然后添加外部数据中存在但接口数据中不存在的列（保持在末尾）
              externalColumns.forEach((externalCol) => {
                const existsInApi = tableHeadData.some(
                  (apiCol) => apiCol.field === externalCol.field
                )
                if (!existsInApi) {
                  mergedColumns.push({
                    ...externalCol,
                    visible: externalCol.visible != undefined ? externalCol.visible : true, // 新增列默认可见
                    sort: tableHeadData.length + mergedColumns.filter(col => !tableHeadData.some(api => api.field === col.field)).length // 新增列排在最后
                  })
                }
              })

              tempSelectedColumns.value = mergedColumns
              // 加载预设时对固定列进行排序（按照新的排序逻辑）
              sortColumnsByFixedAndApiOrder()

              if (!isInitialLoad) {
                ElMessage.success(`已加载配置: ${preset.name}`)
              }
              return // 使用合并后的数据，直接返回
            }
          } catch (parseError) {
            console.error('解析tableHead数据失败:', parseError)
          }
        }
        console.log('切换配置时表头接口调用成功')
      } catch (error) {
        console.error('切换配置时调用表头接口失败:', error)
      }
    }

    // 如果不是目标路由或接口调用失败，使用原有逻辑
    // 以 allConfigurableColumns 为基础，更新 visible 状态和顺序
    const newTempSelectedColumns = JSON.parse(JSON.stringify(allConfigurableColumns.value))
    const presetColumnData = preset.columns // 假设 preset.columns 现在是完整的 TableColumn[]

    // 如果预设的columns为空，则保持所有列可见
    if (presetColumnData && presetColumnData.length > 0) {
      // 更新 visible 状态并排序
      newTempSelectedColumns.forEach((tc: TableColumn) => {
        const presetCol = presetColumnData.find((pc) => pc.field === tc.field)
        tc.visible = presetCol ? presetCol.visible : false // 如果预设中没有，则默认为不可见
      })

      // 根据预设的顺序排序，预设中存在的列排在前面，并按预设顺序
      tempSelectedColumns.value = newTempSelectedColumns.sort((a: TableColumn, b: TableColumn) => {
        const indexA = presetColumnData.findIndex((p) => p.field === a.field)
        const indexB = presetColumnData.findIndex((p) => p.field === b.field)
        if (indexA !== -1 && indexB !== -1) return indexA - indexB // 都在预设中，按预设顺序
        if (indexA !== -1) return -1 // A在预设中，B不在，A在前
        if (indexB !== -1) return 1 // B在预设中，A不在，B在前
        return 0 // 都不在预设中，保持原相对顺序（或根据需要调整）
      })
    } else {
      // 预设的columns为空，保持所有列可见
      tempSelectedColumns.value = newTempSelectedColumns
    }

    if (!isInitialLoad) {
      ElMessage.success(`已加载配置: ${preset.name}`)
    }
  } else if (presetId === '') {
    // 清空选择时，恢复默认状态（所有列可见）
    tempSelectedColumns.value = JSON.parse(JSON.stringify(allConfigurableColumns.value))
  } else {
    ElMessage.warning('未找到对应的预设配置')
  }
}

// 修改 loadPreset 调用，移除旧的示例代码
// const oldLoadPreset = (presetId: string) => { // 旧函数已不再需要，可以安全删除
// console.log('加载预设配置ID:', presetId)
// 示例： const preset = presets.value.find(p => p.id === presetId);
// if (preset) { tempSelectedColumns.value = allConfigurableColumns.value.filter(col => preset.columns.includes(col.field)); }
//   ElMessage.info('加载预设功能待实现')
// }
// loadPreset 函数已经实现了需求3的核心逻辑：当 selectedPreset 变化时，它会更新 tempSelectedColumns。
// applyConfig 函数则负责在点击“保存应用”时，将 tempSelectedColumns 的状态同步到外部。

// 获取表头模板列表

onMounted(async () => {
  // 确保props.columns中的列默认可见
  props.columns.forEach((column) => {
    column.visible = column.visible != undefined ? column.visible : true
  })

  // 优先尝试从接口获取数据
  const hasApiData = await initializeFromApi(true)

  // 如果接口没有数据，则使用props.modelValue或props.columns初始化
  if (!hasApiData) {
    if (props.modelValue && props.modelValue.length > 0) {
      tempSelectedColumns.value = JSON.parse(
        JSON.stringify(filterConfigurableColumns(props.modelValue))
      )
      emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
      console.log('组件初始化时使用props.modelValue数据')
    } else {
      tempSelectedColumns.value = JSON.parse(JSON.stringify(allConfigurableColumns.value))
      emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
      console.log('组件初始化时使用props.columns数据')
    }
  }
})

// 保存配置（保存为常用配置按钮）
const saveAsPreset = async () => {
  // 对应需求 4
  try {
    const { value: name } = await ElMessageBox.prompt('请输入配置名称', '保存为常用配置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /\S/,
      inputErrorMessage: '配置名称不能为空'
    })

    if (name) {
      const newPresetData = {
        template: name,
        type: props.type || (route.name as string), // 使用外部传入的 type 或当前路由 name
        content: JSON.stringify(tempSelectedColumns.value) // 保存完整的列配置（包含 visible）
        // userId:  // 根据需要添加
      }
      const createdId = await ToolsTableHeadTemplateApi.createToolsTableHeadTemplate(newPresetData)
      ElMessage.success('配置保存成功！')
      await initializeFromApi() // 刷新列表
      // 默认选中新创建的配置
      if (createdId) {
        selectedPreset.value = String(createdId)
        loadPreset(selectedPreset.value, true) // 加载新配置，不提示
      }
    }
  } catch (error) {
    // 用户取消或API调用失败
    if (error !== 'cancel') {
      console.error('保存常用配置失败:', error)
      ElMessage.error('配置保存失败，请重试')
    }
  }
}

// 底部操作：恢复默认配置 (将临时的已选列重置为父组件传入的原始列)
const resetToDefault = () => {
  // 重置为父组件传入的原始columns，而不是接口数据
  tempSelectedColumns.value = filterConfigurableColumns(props.columns)
  // 清除接口数据标记和预设选择
  hasLoadedFromApi.value = false
  selectedPreset.value = ''
  ElMessage.success('已恢复为默认配置')
  console.log('🚀 ~ resetToDefault ~ props.columns:', props.columns)
}

// 应用配置：将临时的列配置通过 emit 更新到父组件，并关闭对话框 (保存应用按钮)
const applyConfig = async () => {
  // 对应需求 2 和 3 的部分（保存应用时）
  try {
    // 保存时使用 tempSelectedColumns 的完整数据
    const currentConfigContent = JSON.stringify(tempSelectedColumns.value)
    let typeToUse = props.type || (route.name as string)

    if (presets.value.length > 0 && selectedPreset.value) {
      // 有预设列表，且有选中的预设 -> 更新
      const selectedPresetData = presets.value.find((p) => p.id === selectedPreset.value)
      if (!selectedPresetData) {
        ElMessage.error('未找到选中的配置，无法更新。')
        return
      }

      await ToolsTableHeadTemplateApi.updateToolsTableHeadTemplate({
        id: parseInt(selectedPreset.value),
        template: selectedPresetData.name,
        type: typeToUse,
        content: currentConfigContent
      })
      ElMessage.success('配置更新成功！')
    } else {
      const { value: name } = await ElMessageBox.prompt('请输入新配置名称', '保存新配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S/,
        inputErrorMessage: '配置名称不能为空'
      })
      if (!name) return

      const createdTemplate = await ToolsTableHeadTemplateApi.createToolsTableHeadTemplate({
        template: name,
        type: typeToUse,
        content: currentConfigContent
      })
      ElMessage.success('新配置保存成功！')
      await initializeFromApi() // 刷新列表
      if (createdTemplate && createdTemplate.id) {
        selectedPreset.value = String(createdTemplate.id) // 选中新创建的配置
        await loadPreset(selectedPreset.value, true) // 使用新创建的配置更新 tempSelectedColumns
      }
    }

    // 统一将当前 tempSelectedColumns 应用到外部表格
    // 确保发出的是包含所有列（带 visible 状态）的数据
    emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
    showDialog.value = false
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应用配置失败:', error)
      ElMessage.error('应用配置失败，请重试')
    }
  }
}

// 关闭对话框前的处理，如果需要可以添加确认逻辑
const handleClose = () => {
  // 关闭对话框时，不保存 tempSelectedColumns 的更改，下次打开时会重新从 props.modelValue 初始化
  showDialog.value = false
}

// 监听 props.modelValue (父组件传入的当前配置)
watch(
  () => props.modelValue,
  (newModelValue) => {
    // 如果已从接口获取到数据，则不再被props.modelValue覆盖
    if (hasLoadedFromApi.value) {
      return
    }

    // 当 modelValue 改变时，需要将 modelValue 的状态（主要是 visible 和顺序）同步到 tempSelectedColumns
    // tempSelectedColumns 应始终包含所有 allConfigurableColumns
    const baseColumns = JSON.parse(JSON.stringify(allConfigurableColumns.value))
    if (newModelValue && newModelValue.length > 0) {
      baseColumns.forEach((bc: TableColumn) => {
        const mvCol = newModelValue.find((mvc) => mvc.field === bc.field)
        if (mvCol) {
          bc.visible = mvCol.visible // 同步 visible
          bc.fixed = mvCol.fixed // 同步 fixed
        } else {
          // 如果在 modelValue 中找不到对应的列，设置为默认可见
          // bc.visible = true
          bc.visible = bc.visible != undefined ? bc.visible : true
        }
      })
      // 根据 modelValue 的顺序排序
      baseColumns.sort((a: TableColumn, b: TableColumn) => {
        const indexA = newModelValue.findIndex((m) => m.field === a.field)
        const indexB = newModelValue.findIndex((m) => m.field === b.field)
        if (indexA !== -1 && indexB !== -1) return indexA - indexB
        if (indexA !== -1) return -1
        if (indexB !== -1) return 1
        return 0
      })
    } else {
      // 当 modelValue 为空时，确保所有列都可见
      baseColumns.forEach((bc: TableColumn) => {
        // bc.visible = true
        bc.visible = bc.visible != undefined ? bc.visible : true
      })
    }
    // 只有当计算出的新 baseColumns 与当前 tempSelectedColumns 不同时才更新
    if (JSON.stringify(baseColumns) !== JSON.stringify(tempSelectedColumns.value)) {
      tempSelectedColumns.value = baseColumns
    }
  },
  { deep: true, immediate: false }
)

// 获取列表表头数据的函数
const getListTableHeadData = async () => {
  try {
    let typeParam = route.name as string
    if (props.type) {
      typeParam = props.type
    }

    const res = await ToolsTableHeadTemplateApi.getToolsTableHeadTemplateList({
      type: typeParam
    })

    if (res && res.length > 0) {
      // 获取第一个模板的内容作为表头数据
      const firstTemplate = res[0]
      if (firstTemplate.content) {
        try {
          const tableHeadData = JSON.parse(firstTemplate.content)
          if (Array.isArray(tableHeadData) && tableHeadData.length > 0) {
            return tableHeadData.map((col: TableColumn) => ({
              ...col,
              visible: col.visible !== undefined ? col.visible : true
            }))
          }
        } catch (parseError) {
          console.error('解析表头数据失败:', parseError)
        }
      }
    }
  } catch (error) {
    console.error('获取ToolsTableHeadTemplateApi.getToolsTableHeadTemplateList接口数据失败:', error)
  }
  return null
}

// 监听对话框的显示状态
watch(showDialog, async (isVisible) => {
  if (isVisible) {
    // 重置接口数据加载标记
    hasLoadedFromApi.value = false

    // 获取外部列数据作为基准
    const externalColumns = filterConfigurableColumns(props.columns)
    const externalFields = externalColumns.map((col) => col.field)

    // 从接口获取表头数据
    const apiTableHeadData = await getListTableHeadData()

    if (apiTableHeadData && apiTableHeadData.length > 0) {
      // 优化后的数据合并逻辑：
      // 1. 表头数据以外部传入的为准（字段定义、标签等）
      // 2. 排序以接口返回的数据顺序为准
      // 3. 宽度以接口返回的数据为准
      const mergedColumns: TableColumn[] = []

      // 首先按照接口数据的顺序处理
      apiTableHeadData.forEach((apiCol) => {
        const externalCol = externalColumns.find((extCol) => extCol.field === apiCol.field)
        if (externalCol) {
          // 找到对应的外部列，合并数据
          mergedColumns.push({
            ...externalCol, // 以外部数据为基准
            // 接口数据优先的属性
            width: apiCol.width !== undefined ? apiCol.width : externalCol.width, // 宽度以接口为准
            visible: apiCol.visible !== undefined ? apiCol.visible : true,
            fixed: apiCol.fixed !== undefined ? apiCol.fixed : externalCol.fixed
          })
        }
      })

      // 然后添加外部数据中存在但接口数据中不存在的列（保持在末尾）
      externalColumns.forEach((externalCol) => {
        const existsInApi = apiTableHeadData.some((apiCol) => apiCol.field === externalCol.field)
        if (!existsInApi) {
          mergedColumns.push({
            ...externalCol,
            visible: externalCol.visible != undefined ? externalCol.visible : true // 新增列默认可见
          })
        }
      })

      tempSelectedColumns.value = mergedColumns
      // 弹窗打开时对固定列进行排序
              if (hasLoadedFromApi.value) {
                sortColumnsByFixedAndApiOrder()
              } else {
                sortColumnsByFixed()
              }
      hasLoadedFromApi.value = true
      console.log('以外部数据为准，排序和宽度以接口数据为准，合并完成')
    } else {
      // 接口数据为空或异常，使用外部传入的数据
      if (props.modelValue && props.modelValue.length > 0) {
        // 优先使用props.modelValue
        tempSelectedColumns.value = JSON.parse(
          JSON.stringify(filterConfigurableColumns(props.modelValue))
        )
        console.log('使用外部传入的modelValue数据初始化列配置')
      } else {
        // 回退到使用allConfigurableColumns
        tempSelectedColumns.value = JSON.parse(JSON.stringify(allConfigurableColumns.value))
        console.log('使用allConfigurableColumns数据初始化列配置')
      }
    }

    // 检查当前路由是否为指定的页面
    const targetRoutes = ['DevAuditIndex', 'DevTabledex', 'ProdIndex']
    const currentRouteName = route.name as string

    // 需求1：在特定路由页面的列配置组件弹窗出现时，调用获取表头接口（作为备用逻辑）
    if (targetRoutes.includes(currentRouteName) && !apiTableHeadData) {
      try {
        let headTemplateId = ''
        if (props.tableData && props.tableData.length > 0) {
          headTemplateId = props.tableData[0].id
        }

        const templateId = props.type || currentRouteName
        const response = await getTableHeadByTemplateId(templateId, headTemplateId)

        // 使用接口返回的tableHead数据作为表头数据源
        if (response && response.tableHead) {
          try {
            const tableHeadData = JSON.parse(response.tableHead)
            // 根据tableHead数据更新tempSelectedColumns
            if (Array.isArray(tableHeadData)) {
              tempSelectedColumns.value = tableHeadData.map((col) => ({
                ...col,
                visible: col.visible !== undefined ? col.visible : true
              }))
              // 弹窗打开时对固定列进行排序
              if (hasLoadedFromApi.value) {
                sortColumnsByFixedAndApiOrder()
              } else {
                sortColumnsByFixed()
              }
              hasLoadedFromApi.value = true // 标记已从接口获取到数据
            }
            console.log('弹窗打开时表头接口调用成功，已更新列配置')
          } catch (parseError) {
            console.error('解析tableHead数据失败:', parseError)
          }
        }
      } catch (error) {
        console.error('弹窗打开时调用表头接口失败:', error)
      }
    }

    await initializeFromApi() // 获取最新预设列表

    // initializeFromApi 内部会处理预设加载
    // 如果没有预设被加载（例如 initializeFromApi 返回空列表，或没有选中预设），
    // 且没有从接口获取到数据，则需要根据 modelValue 或默认情况来设置 tempSelectedColumns
    if (
      (!selectedPreset.value || !presets.value.some((p) => p.id === selectedPreset.value)) &&
      !hasLoadedFromApi.value
    ) {
      if (props.modelValue && props.modelValue.length > 0) {
        // 基于 modelValue 更新 tempSelectedColumns
        const baseColumns = JSON.parse(JSON.stringify(allConfigurableColumns.value))
        props.modelValue.forEach((mvCol) => {
          const bc = baseColumns.find((b: TableColumn) => b.field === mvCol.field)
          if (bc) {
            bc.visible = mvCol.visible
            bc.fixed = mvCol.fixed // 同步 fixed
          }
        })
        // 排序
        baseColumns.sort((a: TableColumn, b: TableColumn) => {
          const indexA = props.modelValue.findIndex((m) => m.field === a.field)
          const indexB = props.modelValue.findIndex((m) => m.field === b.field)
          if (indexA !== -1 && indexB !== -1) return indexA - indexB
          if (indexA !== -1) return -1
          if (indexB !== -1) return 1
          return 0
        })
        tempSelectedColumns.value = baseColumns
        // 弹窗打开时对固定列进行排序
        if (hasLoadedFromApi.value) {
          sortColumnsByFixedAndApiOrder()
        } else {
          sortColumnsByFixed()
        }
      } else {
        // 默认情况：所有列都可见，确保 tempSelectedColumns 中所有列的 visible 都为 true
        // 并且 fixed 状态也从 allConfigurableColumns 初始化（通常为 undefined 或默认值）
        tempSelectedColumns.value = JSON.parse(JSON.stringify(allConfigurableColumns.value))
        // 默认情况下对固定列进行排序
        if (hasLoadedFromApi.value) {
          sortColumnsByFixedAndApiOrder()
        } else {
          sortColumnsByFixed()
        }
      }
    }
  }
})

// 监听 props.columns 变化
watch(
  () => props.columns,
  (newColumns, oldColumns) => {
    if (!newColumns || newColumns.length === 0) return

    // 检查columns是否真的发生了变化（字段数量或字段名称变化）
    const hasColumnsChanged =
      !oldColumns ||
      oldColumns.length !== newColumns.length ||
      !oldColumns.every((oldCol) => newColumns.some((newCol) => newCol.field === oldCol.field))

    if (hasColumnsChanged) {
      console.log('检测到props.columns发生变化，同步更新组件数据')

      // 获取新的可配置列
      const newConfigurableColumns = filterConfigurableColumns(newColumns)

      // 如果当前有选中的预设或从接口加载的数据，需要合并新增的列
      if (hasLoadedFromApi.value || selectedPreset.value) {
        // 保留现有配置，但添加新增的列
        const currentFields = tempSelectedColumns.value.map((col) => col.field)
        const newFields = newConfigurableColumns.filter((col) => !currentFields.includes(col.field))

        if (newFields.length > 0) {
          // 将新增的列添加到tempSelectedColumns末尾，默认可见
          tempSelectedColumns.value = [
            ...tempSelectedColumns.value,
            ...newFields.map((col) => ({
              ...col,
              visible: col.visible != undefined ? col.visible : true
            }))
          ]
          console.log(`添加了${newFields.length}个新列到现有配置中`)
        }

        // 移除已不存在的列
        tempSelectedColumns.value = tempSelectedColumns.value.filter((col) =>
          newConfigurableColumns.some((newCol) => newCol.field === col.field)
        )

        // 设置标记防止被props.modelValue监听器覆盖
        hasLoadedFromApi.value = true
      } else {
        // 没有预设或接口数据时，直接使用新的columns
        tempSelectedColumns.value = JSON.parse(JSON.stringify(newConfigurableColumns))
        console.log('直接使用新的props.columns更新配置')
      }

      // 通知父组件更新
      emit('update:modelValue', JSON.parse(JSON.stringify(tempSelectedColumns.value)))
    }
  },
  { deep: true }
)

// 监听 props.type 变化，重新获取对应的表头配置
watch(
  () => props.type,
  async (newType, oldType) => {
    if (newType && newType !== oldType) {
      // 重置状态
      hasLoadedFromApi.value = false
      selectedPreset.value = ''

      // 重新获取表头配置
      await initializeFromApi(true)
    }
  },
  { immediate: false }
)
</script>

<style lang="scss" scoped>
.table-column-config {
  display: inline-block;

  .table-column-config__button {
    display: flex;
    padding: 10px;
    color: #909399;
    gap: 5px;
    align-items: center;
  }

  .table-column-config__button:hover {
    padding: 10px;
    background: #f1f1f1;
  }
}

.config-header {
  display: flex;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  justify-content: space-between;
  align-items: center;
}

.config-content {
  display: flex;
  height: 500px;
  gap: 20px;
}

.config-left {
  width: 450px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.category-title {
  padding: 17px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
}

.category-item {
  border-bottom: 1px solid #ebeef5;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  gap: 8px;

  &:hover {
    background-color: #f5f7fa;
  }
}

.category-name {
  flex: 1;
  font-weight: 500;
}

.category-columns {
  display: grid;
  padding-bottom: 10px;
  padding-left: 15px;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.column-item {
}

.config-right {
  display: flex;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  flex: 1;
  flex-direction: column;
}

.selected-title {
  display: flex;
  padding: 15px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  justify-content: space-between;
  align-items: center;
}

.selected-list {
  padding: 10px;
  overflow-y: auto;
  flex: 1;
}

.selected-item {
  display: flex;
  padding: 10px;
  margin-bottom: 5px;
  cursor: grab;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background-color: #f5f7fa;
    border-color: #c6e2ff;
  }

  .hover-icon:hover {
    color: #98c1ff;
    cursor: pointer;
  }
}

.selected-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  flex: 1;

  .content-right {
    cursor: auto;
  }
}

.drag-handle {
  color: #909399;
  cursor: move;
}

.column-label {
  flex: 1;
}

.selected-item-actions {
  display: flex;
  gap: 5px;
}

.config-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-right {
  display: flex;
  gap: 10px;
}
</style>
