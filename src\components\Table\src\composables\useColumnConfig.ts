import { ref, watch } from 'vue'
import type { TableColumn } from '@/types/table'

export function useColumnConfig(
  props: Readonly<{ columns: TableColumn[] }>,
  emit: (event: 'column-config-change', ...args: any[]) => void
) {
  const internalColumns = ref<TableColumn[]>([])
  const hasColumnsFromConfig = ref(false)

  watch(
    () => props.columns,
    (newVal) => {
      if (!hasColumnsFromConfig.value) {
        internalColumns.value = JSON.parse(JSON.stringify(newVal || []))
      }
    },
    { deep: true, immediate: true }
  )

  watch(
    internalColumns,
    (newConfiguredColumns) => {
      emit('column-config-change', newConfiguredColumns)
    },
    { deep: true }
  )

  const handleColumnConfigUpdate = (newColumns: TableColumn[]) => {
    const originalColumnsMap = new Map<string, TableColumn>()
    props.columns.forEach((col) => {
      if (col.field) {
        originalColumnsMap.set(col.field, col)
      }
    })

    const mergedColumns: TableColumn[] = []
    const processedFields = new Set<string>()

    // 优先处理 newColumns 中的列，以保留顺序和用户配置
    newColumns.forEach((savedCol) => {
      if (savedCol.field) {
        const originalCol = originalColumnsMap.get(savedCol.field)
        if (originalCol) {
          // 合并：以原始列为基础，应用保存的配置
          mergedColumns.push({ ...originalCol, ...savedCol })
          processedFields.add(savedCol.field)
        }
      }
    })

    // 添加在 props.columns 中存在但在 newColumns 中不存在的新列
    props.columns.forEach((originalCol) => {
      if (originalCol.field && !processedFields.has(originalCol.field)) {
        mergedColumns.push(originalCol)
      }
    })

    internalColumns.value = mergedColumns
    hasColumnsFromConfig.value = true
  }

  return {
    internalColumns,
    handleColumnConfigUpdate
  }
}