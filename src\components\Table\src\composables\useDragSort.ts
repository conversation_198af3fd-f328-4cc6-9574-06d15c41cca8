import { ref, watch, nextTick, unref, onBeforeUnmount } from 'vue'
import Sortable from 'sortablejs'
import type { ElTable } from 'element-plus'
import type { TableProps } from '../types'

interface SortableEvent {
  oldIndex?: number
  newIndex?: number
}

export function useDragSort(
  props: Readonly<TableProps>,
  emit: (event: 'update:data', ...args: any[]) => void,
  elTableRef: Ref<ComponentRef<typeof ElTable>>,
  draggableData: Ref<Recordable[]>
) {
  let sortableInstance: Sortable | null = null

  const initDragSort = () => {
    if (!props.draggable) return

    nextTick(() => {
      const tableEl = unref(elTableRef)?.$el
      const tbody = tableEl?.querySelector('.el-table__body-wrapper tbody')

      if (tbody && !sortableInstance) {
        sortableInstance = Sortable.create(tbody, {
          handle: '.drag-handle',
          animation: 150,
          onEnd: (evt: SortableEvent) => {
            const { oldIndex, newIndex } = evt
            if (oldIndex !== newIndex && oldIndex !== undefined && newIndex !== undefined) {
              if (
                oldIndex >= 0 &&
                oldIndex < draggableData.value.length &&
                newIndex >= 0 &&
                newIndex < draggableData.value.length
              ) {
                const newData = [...draggableData.value]
                const movedItem = newData.splice(oldIndex, 1)[0]
                newData.splice(newIndex, 0, movedItem)

                draggableData.value = newData
                emit('update:data', newData)
              }
            }
          }
        })
      }
    })
  }

  const destroyDragSort = () => {
    if (sortableInstance) {
      sortableInstance.destroy()
      sortableInstance = null
    }
  }

  watch(
    () => props.draggable,
    (newVal) => {
      if (newVal) {
        initDragSort()
      } else {
        destroyDragSort()
      }
    },
    { immediate: true }
  )

  watch(
    () => draggableData.value.length,
    () => {
      if (props.draggable) {
        destroyDragSort()
        initDragSort()
      }
    }
  )

  onBeforeUnmount(() => {
    destroyDragSort()
  })

  return {
    initDragSort,
    destroyDragSort
  }
}