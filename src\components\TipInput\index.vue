<template>
  <div class="tip-input-container">
    <el-input
      v-model="inputValue"
      :class="inputClass"
      :clearable="clearable"
      :placeholder="placeholder"
      @keyup.enter="handleEnterKey"
      @clear="handleClear"
      @change="handleChange"
    >
      <!-- 其余模板代码保持不变 -->
      <template #append>
        <el-tooltip
          teleported
          class="box-item"
          effect="dark"
          :content="tooltipContent"
          :placement="tooltipPlacement"
          :raw-content="rawContent"
        >
          <div class="ak-advanced-input">
            <!-- 多行输入弹出框 -->
            <el-popover
              :visible="popoverVisible"
              :width="220"
              trigger="manual"
              placement="bottom"
              popper-class="tip-input-popover"
            >
              <template #reference>
                <div class="ak-advanced-input" @click="openPopover">
                  <Icon :icon="tooltipIcon" />
                </div>
              </template>
              <template #default>
                <div class="popover-content">
                  <el-input
                    v-model="multilineInput"
                    type="textarea"
                    :rows="9"
                    placeholder="精确搜索，一行一项，最多支持1000行"
                  />
                  <div class="popover-footer">
                    <div class="popover-actions">
                      <el-button size="small" @click="clearInput">清空</el-button>
                      <div class="right-actions">
                        <el-button size="small" @click="popoverVisible = false">关闭</el-button>
                        <el-button size="small" type="primary" @click="confirmInput"
                          >确定</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-popover>
          </div>
        </el-tooltip>
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, ref, watch } from 'vue'

const props = defineProps({
  // 输入框的值
  modelValue: {
    type: String,
    default: ''
  },
  // 输入框类名
  inputClass: {
    type: String,
    default: '!w-150px'
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true
  },
  // 占位文本
  placeholder: {
    type: String,
    default: ''
  },
  // 提示图标
  tooltipIcon: {
    type: String,
    default: 'fa-solid:bars'
  },
  // 提示内容
  tooltipContent: {
    type: String,
    default: '多项精确搜索'
  },
  // 提示位置
  tooltipPlacement: {
    type: String,
    default: 'bottom'
  },
  // 是否使用原始HTML内容
  rawContent: {
    type: Boolean,
    default: false
  },
  // 对话框标题
  dialogTitle: {
    type: String,
    default: '多行输入'
  }
})

const emit = defineEmits(['update:modelValue', 'enter', 'change', 'onBtn'])

// 使用ref而不是computed来更灵活地控制输入值
const inputValue = ref(props.modelValue)
const popoverVisible = ref(false)
const multilineInput = ref('')

// 加强版的props.modelValue监听
watch(
  () => props.modelValue,
  (newVal) => {
    // 使用nextTick确保DOM更新完成后再比较
    nextTick(() => {
      if (newVal !== inputValue.value) {
        inputValue.value = newVal || '' // 确保即使是null或undefined也转为空字符串
        multilineInput.value = newVal ? newVal.split(',').join('\n') : ''
      }
    })
  },
  { immediate: true, deep: true }
)

// 监听inputValue变化，触发更新
watch(
  inputValue,
  (newVal) => {
    emit('update:modelValue', newVal)
    emit('change', newVal)
  },
  { deep: true }
)

// 处理清空操作
const handleClear = () => {
  inputValue.value = ''
  const values: string[] = []
  emit('enter', values)
  nextTick(() => {
    emit('onBtn', values)
  })
}

// 处理输入变化
const handleChange = (val: string) => {
  inputValue.value = val
  if (val) {
    emit(
      'enter',
      val.split(',').filter((item) => item.trim() !== '')
    )
  } else {
    emit('enter', [])
  }
}

// 打开弹出框
const openPopover = () => {
  // 添加去重逻辑
  if (inputValue.value) {
    // 将逗号分隔的字符串转为数组，过滤空值，然后通过 Set 去重，再转回字符串
    const uniqueValues = [
      ...new Set(inputValue.value.split(',').filter((item) => item.trim() !== ''))
    ]
    inputValue.value = uniqueValues.join(',')
    // 将去重后的值转为多行文本
    multilineInput.value = uniqueValues.join('\n')
  } else {
    multilineInput.value = ''
  }
  popoverVisible.value = true
}

// 清空输入
const clearInput = () => {
  multilineInput.value = ''
}

// 确认多行输入
const confirmInput = () => {
  // 获取所有非空行
  const lines = multilineInput.value.split('\n').filter((line) => line.trim() !== '')
  // 使用 Set 去重
  const uniqueLines = [...new Set(lines)]
  // 更新输入值
  inputValue.value = uniqueLines.join(',')
  // 触发事件
  emit('enter', uniqueLines)
  popoverVisible.value = false
  nextTick(() => {
    emit('onBtn', uniqueLines)
  })
}

// 处理回车键
const handleEnterKey = () => {
  const values = inputValue.value
    ? inputValue.value.split(',').filter((item) => item.trim() !== '')
    : []
  emit('enter', values)
  nextTick(() => {
    emit('onBtn', values)
  })
}
</script>

<style scoped>
/* 样式保持不变 */
.tip-input-container {
  display: inline-block;
}

.ak-advanced-input {
  position: absolute;
  top: 50%;
  z-index: 3;
  display: flex;
  width: 94%;
  height: 94%;
  cursor: pointer;
  background-color: #f0f2f5;
  border-radius: 4px;
  transform: translateY(-50%);
  align-items: center;
  justify-content: center;
}

:deep(.tip-input-popover) {
  padding: 0;
}

.popover-content {
  display: flex;
  flex-direction: column;
}

.popover-footer {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid #eee;
}

.popover-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-actions {
  display: flex;
}
</style>
