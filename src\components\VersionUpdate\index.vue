<template>
  <el-dialog
    v-model="dialogVisible"
    :title="noticeInfo.title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="530px"
    @close="handleClose"
  >
    <div class="version-update-content">
      <el-alert
        v-if="noticeInfo.loginAgain"
        title="重要通知：此消息处理后需要您重新登录系统"
        type="warning"
        show-icon
        :closable="false"
        class="mb-15px"
      />
      <LoginCarousel
        v-if="noticeInfo.imgUrlList && noticeInfo.imgUrlList.length > 0"
        :key="carouselKey"
        :height="500"
        :width="500"
        :interval="6000"
        :initial-index="0"
        :customImages="noticeInfo.imgUrlList"
        image-class="carousel-image"
      />
      <div
        class="mt-20px"
        v-if="noticeInfo.content"
        v-html="noticeInfo.content"
      ></div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue'
import { useWebSocket } from '@vueuse/core'
import { getRefreshToken } from '@/utils/auth'
import * as NoticeApi from '@/api/system/notice'
import LoginCarousel from '@/components/LoginCarousel/index.vue'

import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()
const { push, replace, currentRoute } = useRouter()

// 对话框显示状态
const dialogVisible = ref(false)

// 暴露一个方法给父组件调用
const openDialog = (notice: any) => {
  noticeInfo.value = {
    ...notice,
    title: notice.title,
    content: notice.content,
    imgUrlList: notice.imgUrl
      ? typeof notice.imgUrl === 'string'
        ? JSON.parse(notice.imgUrl)
        : notice.imgUrl
      : [], // imgUrl 可能是字符串化的数组或已经是数组
    loginAgain: notice.loginAgain, // 保留 loginAgain 字段
    id: notice.id // 保留 id 字段
  }
  // 如果有轮播图，则强制刷新LoginCarousel组件
  if (noticeInfo.value.imgUrlList && noticeInfo.value.imgUrlList.length > 0) {
    carouselKey.value++
  }
  dialogVisible.value = true
}

defineExpose({
  openDialog
})

// 通知信息
const noticeInfo = ref<any>({ title: '', content: '', imgUrlList: [] })
const carouselKey = ref(0) // 用于强制刷新轮播图

// WebSocket 相关
const server = ref(
  (import.meta.env.VITE_BASE_URL + '/infra/ws').replace('http', 'ws') +
    '?token=' +
    getRefreshToken()
)

const { status, data, send, close, open } = useWebSocket(server.value, {
  autoReconnect: true,
  heartbeat: true
})

// 关闭弹窗
const handleClose = async () => {
  let data = {
    ids: [noticeInfo.value.id],
    logout: noticeInfo.value.loginAgain
  }
  await NoticeApi.batchConfirm(data)
  // 增加判断条件：如果通知状态存在且不为0 (未读)，则即使 loginAgain 为 true 也不强制登出
  if (noticeInfo.value.loginAgain && (noticeInfo.value.status === undefined || noticeInfo.value.status === 0)) {
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    replace('/login?redirect=/index')
  } else {
    dialogVisible.value = false
  }
}

// watch(data, (newData) => { // 暂时注释掉WebSocket的监听，避免冲突，后续可以根据需求决定是否保留或合并
watch(data, (newData) => {
  if (!newData) {
    return
  }
  try {
    if (newData === 'pong') {
      return
    }
    const jsonMessage = JSON.parse(newData as string)
    const type = jsonMessage.type
    const content = JSON.parse(jsonMessage.content)
    console.log(jsonMessage)
    console.log(content)
    if (type === 'notice-push' && content.type == 1) {
      // 检查当前路由，如果在登录页面则不显示弹窗
      if (currentRoute.value.path === '/login') {
        return
      }
      // 公告类型为1，通常用于系统级强制通知
      // 公告类型为1
      noticeInfo.value = {
        ...content,
        title: content.title,
        content: content.content,
        imgUrlList: content.imgUrl ? JSON.parse(content.imgUrl) : [], // imgUrl 是字符串化的数组
        loginAgain: content.loginAgain, // 确保 loginAgain 字段被正确处理
        id: content.id // 确保 id 字段被正确处理
      }
      // 如果有轮播图，则强制刷新LoginCarousel组件
      if (noticeInfo.value.imgUrlList && noticeInfo.value.imgUrlList.length > 0) {
        carouselKey.value++
      }
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('处理WebSocket消息失败:', error, newData)
  }
})

// 组件挂载时启动版本检查和WebSocket连接
onMounted(() => {
  open() // 打开WebSocket连接
})

// 组件卸载前清除定时器和关闭WebSocket连接
onBeforeUnmount(() => {
  close() // 关闭WebSocket连接
})
</script>

<style scoped>
.version-update-content {
  max-height: 700px;
  min-height: 100px;
  margin: 20px 0;
  overflow: auto;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 或 contain，根据需要选择 */
}
</style>
