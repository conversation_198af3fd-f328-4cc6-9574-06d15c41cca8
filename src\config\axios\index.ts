import { service } from './service'

import { config } from './config'

const { default_headers } = config

const request = (option: any) => {
  const { headersType, headers, ...otherOption } = option
  
  // 处理sortingFields字段，去掉field中以Percent结尾的后缀
  if (otherOption.data && otherOption.data.sortingFields && Array.isArray(otherOption.data.sortingFields)) {
    otherOption.data.sortingFields = otherOption.data.sortingFields.map((item: any) => {
      if (item.field && typeof item.field === 'string' && item.field.endsWith('Percent')) {
        return {
          ...item,
          field: item.field.replace(/Percent$/, '')
        }
      }
      return item
    })
  }
  
  // 处理params中的sortingFields字段（GET请求参数）
  if (otherOption.params && otherOption.params.sortingFields && Array.isArray(otherOption.params.sortingFields)) {
    otherOption.params.sortingFields = otherOption.params.sortingFields.map((item: any) => {
      if (item.field && typeof item.field === 'string' && item.field.endsWith('Percent')) {
        return {
          ...item,
          field: item.field.replace(/Percent$/, '')
        }
      }
      return item
    })
  }
  
  return service({
    ...otherOption,
    headers: {
      'Content-Type': headersType || default_headers,
      ...headers
    }
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  post: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  }
}
