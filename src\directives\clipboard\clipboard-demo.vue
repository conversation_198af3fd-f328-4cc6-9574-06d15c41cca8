<template>
  <div class="clipboard-demo">
    <h3>复制指令演示</h3>

    <div class="demo-section">
      <h4>静态文本复制</h4>
      <div
        class="demo-item"
        v-clipboard="'这是一段静态文本，鼠标悬停时会显示复制按钮'"
      >
        这是一段静态文本，鼠标悬停时会显示复制按钮
      </div>
    </div>

    <div class="demo-section">
      <h4>动态文本复制</h4>
      <el-input
        v-model="dynamicText"
        placeholder="请输入要复制的内容"
      />
      <div
        class="demo-item mt-2"
        v-clipboard="dynamicText"
      >
        {{ dynamicText || '请在上方输入框中输入内容' }}
      </div>
    </div>

    <div class="demo-section">
      <h4>函数返回值复制</h4>
      <div
        class="demo-item"
        v-clipboard="() => `当前时间: ${new Date().toLocaleString()}`"
      >
        鼠标悬停复制当前时间
      </div>
    </div>

    <div class="demo-section">
      <h4>原始copyText函数使用</h4>
      <el-button
        type="primary"
        @click="handleCopy"
      >点击复制</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { copyText } from './clipboard' // 使用新指令中导出的copyText

const dynamicText = ref('')

const handleCopy = () => {
  copyText(
    '这是通过原始copyText函数复制的内容',
    () => ElMessage.success('复制成功'),
    (err) => ElMessage.error(`复制失败: ${err.message}`)
  )
}
</script>

<style scoped>
.clipboard-demo {
  max-width: 600px;
  padding: 20px;
  margin: 20px auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.demo-section {
  margin-bottom: 20px;
}

.demo-item {
  position: relative;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.mt-2 {
  margin-top: 8px;
}
</style>
