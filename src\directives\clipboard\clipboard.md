# Clipboard 复制指令

这个指令提供了一个简单的方式来实现鼠标悬停显示复制按钮的功能，使用Element Plus的图标。

## 功能特点

- 鼠标悬停时显示复制按钮
- 点击按钮复制内容
- 复制成功/失败时显示提示
- 支持静态文本和动态函数返回值

## 使用方法

### 全局注册

在main.ts中已经通过setupAuth函数注册了该指令，可以直接使用：

```vue
<template>
  <!-- 复制静态文本 -->
  <div v-clipboard="'要复制的文本'">鼠标悬停在此处显示复制按钮</div>

  <!-- 复制动态内容 -->
  <div v-clipboard="() => dynamicText">鼠标悬停在此处显示复制按钮</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const dynamicText = ref('动态内容')
</script>
```

### 按需引入

```vue
<template>
  <div v-clipboard="'要复制的文本'">鼠标悬停在此处显示复制按钮</div>
</template>

<script setup lang="ts">
import { vClipboard } from '@/directives'

// 在组件中注册指令
definedDirectives({
  clipboard: vClipboard
})
</script>
```

### 直接使用copyText函数

原有的copyText函数仍然可以正常使用，不受影响：

```vue
<template>
  <el-button @click="handleCopy">复制</el-button>
</template>

<script setup lang="ts">
import { copyText } from '@/directives'
// 或者直接从原始位置导入
// import { copyText } from '@/utils/clipboard'

const handleCopy = () => {
  copyText(
    '要复制的文本',
    () => ElMessage.success('复制成功'),
    (err) => ElMessage.error(`复制失败: ${err.message}`)
  )
}
</script>
```

## 注意事项

1. 指令会自动为目标元素添加相对定位，以便正确放置复制按钮
2. 复制按钮会在鼠标离开元素时自动隐藏
3. 如果传入的值不是字符串或函数，指令将不会生效
