import { copyText } from '@/utils/clipboard'
import { DocumentCopy } from '@element-plus/icons-vue'
import { ElMessage, ElTooltip } from 'element-plus'
import { AppContext, createVNode, Directive, DirectiveBinding, render } from 'vue'

// 全局状态管理
const clipboardState = {
  currentRow: null as HTMLElement | null,
  rows: new Map<HTMLElement, Set<HTMLElement>>()
}

// 初始化全局事件监听
const initGlobalEvents = () => {
  if ((window as any).__clipboardEventsInitialized) return
  ;(window as any).__clipboardEventsInitialized = true

  document.addEventListener('mouseover', handleMouseOver)
  document.addEventListener('mouseout', handleMouseOut)
}

// 处理鼠标移入事件
const handleMouseOver = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  const row = getParentRow(target)

  if (row && row !== clipboardState.currentRow) {
    // 隐藏之前行的按钮
    if (clipboardState.currentRow) {
      const prevButtons = clipboardState.rows.get(clipboardState.currentRow)
      prevButtons?.forEach((btn) => {
        btn.style.visibility = 'hidden'
        btn.style.opacity = '0'
      })
    }

    // 显示新行的按钮
    const currentButtons = clipboardState.rows.get(row)
    currentButtons?.forEach((btn) => {
      btn.style.visibility = 'visible'
      btn.style.opacity = '1'
    })
    clipboardState.currentRow = row
  }
}

// 处理鼠标移出事件
const handleMouseOut = (e: MouseEvent) => {
  const relatedTarget = e.relatedTarget as HTMLElement
  if (!clipboardState.currentRow?.contains(relatedTarget)) {
    const buttons = clipboardState.rows.get(clipboardState.currentRow!)
    buttons?.forEach((btn) => {
      btn.style.visibility = 'hidden'
      btn.style.opacity = '0'
    })
    clipboardState.currentRow = null
  }
}

// 获取最近的父行元素
const getParentRow = (el: HTMLElement): HTMLElement | null => {
  return el.closest('[data-clipboard-row]') || el.closest('tr') || el.closest('li')
}

// 创建复制按钮
const createCopyButton = (
  el: HTMLElement,
  binding: DirectiveBinding,
  appContext: AppContext | null
) => {
  const value = binding.value
  if (!value && typeof value !== 'string') return

  // 设置元素样式
  if (getComputedStyle(el).position === 'static') {
    el.style.position = 'relative'
  }
  el.classList.add('el-clipboard-element')
  // 设置最大宽度和文本溢出样式
  Object.assign(el.style, {
    display: 'inline-block',
    maxWidth: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    verticalAlign: 'bottom',
    paddingRight: '24px'
  })

  // 创建按钮容器
  const copyBtn = document.createElement('div')
  copyBtn.className = 'el-clipboard-btn'
  copyBtn.style.cssText = `
    position: absolute;
    top: 50%;
    right: 2px;
    transform: translateY(-50%);
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease;
    padding: 4px;
    color: #409eff;
    cursor: pointer;
    border-radius: 4px;
    z-index: 10;
    background-color: #F5F7FA;
    line-height: 1;
  `

  // 创建图标和提示
  const iconVNode = createVNode(DocumentCopy, { style: 'width: 12px; height: 12px;' })
  const tooltipVNode = createVNode(
    ElTooltip,
    {
      content: '复制',
      placement: 'top',
      popperClass: 'custom-tooltip',
      appendToBody: true
    },
    { default: () => [iconVNode] }
  )

  if (appContext) {
    tooltipVNode.appContext = appContext
  }

  // 点击事件处理
  copyBtn.addEventListener('click', (event) => {
    event.stopPropagation()
    const text = typeof value === 'function' ? value() : value
    copyText(
      text,
      () => ElMessage.success('复制成功'),
      (err) => ElMessage.error(`复制失败: ${err.message}`)
    )
  })

  // 渲染按钮
  el.appendChild(copyBtn)
  render(tooltipVNode, copyBtn)

  // 关联到父行
  const row = getParentRow(el)
  if (row) {
    if (!clipboardState.rows.has(row)) {
      clipboardState.rows.set(row, new Set())
    }
    clipboardState.rows.get(row)?.add(copyBtn)
  }

  initGlobalEvents()
}

// 定义指令
export const vClipboard: Directive = {
  mounted(el, binding) {
    createCopyButton(el, binding, binding.instance?.$?.appContext ?? null)
  },
  updated(el, binding) {
    const oldBtn = el.querySelector('.el-clipboard-btn')
    if (oldBtn) oldBtn.remove()
    createCopyButton(el, binding, binding.instance?.$?.appContext ?? null)
  },
  beforeUnmount(el) {
    el.classList.remove('el-clipboard-element')
    const btn = el.querySelector('.el-clipboard-btn')
    if (btn) btn.remove()

    // 清理行关联
    clipboardState.rows.forEach((buttons, row) => {
      buttons.delete(btn as HTMLElement)
      if (buttons.size === 0) {
        clipboardState.rows.delete(row)
      }
    })
  }
}

export default {
  install(app: any) {
    app.directive('clipboard', vClipboard)
  }
}

// 导出原始的copyText函数，保持原有功能不变
export { copyText } from '@/utils/clipboard'
