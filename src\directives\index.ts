import type { App } from 'vue'
import { hasRole } from './permission/hasRole'
import { hasPermi } from './permission/hasPermi'
import { vClipboard } from './clipboard/clipboard'

/**
 * 导出指令：v-xxx
 * @methods hasRole 用户权限，用法: v-hasRole
 * @methods hasPermi 按钮权限，用法: v-hasPermi
 * @methods vClipboard 复制功能，用法: v-clipboard
 */
export const setupAuth = (app: App<Element>) => {
  hasRole(app)
  hasPermi(app)
  app.directive('clipboard', vClipboard)
}

/**
 * 导出指令：v-mountedFocus
 */
export const setupMountedFocus = (app: App<Element>) => {
  app.directive('mountedFocus', {
    mounted(el) {
      el.focus()
    }
  })
}

/**
 * 导出所有指令，方便按需引入
 */
export {
  vClipboard
}

// 导出原始的copyText函数，保持原有功能不变
export { copyText } from './clipboard/clipboard'
