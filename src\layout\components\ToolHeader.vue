<script lang="tsx">
import Announcement from '@/components/Announcement/index.vue'
import { Icon } from '@/components/Icon'
import ScrollingText from '@/components/ScrollingText/index.vue'
import { useDesign } from '@/hooks/web/useDesign'
import { Breadcrumb } from '@/layout/components/Breadcrumb'
import { Collapse } from '@/layout/components/Collapse'
import { Screenfull } from '@/layout/components/Screenfull'
import { UserInfo } from '@/layout/components/UserInfo'
import { useAppStore } from '@/store/modules/app'
import { computed, defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'ToolHeader',
  setup() {
    const { getPrefixCls, variables } = useDesign()
    const prefixCls = getPrefixCls('tool-header')
    const appStore = useAppStore()

    // 面包屑
    const breadcrumb = computed(() => appStore.getBreadcrumb)
    // 折叠图标
    const hamburger = computed(() => appStore.getHamburger)
    // 全屏图标
    const screenfull = computed(() => appStore.getScreenfull)
    // 搜索图片
    const search = computed(() => appStore.search)
    // 尺寸图标
    const size = computed(() => appStore.getSize)
    // 布局
    const layout = computed(() => appStore.getLayout)
    // 多语言图标
    const locale = computed(() => appStore.getLocale)
    // 消息图标
    const message = computed(() => appStore.getMessage)

    // Announcement组件引用
    const announcementRef = ref()

    // 处理公告点击事件
    const handleNoticeClick = (noticeId: string) => {
      console.log(announcementRef.value)
      if (announcementRef.value) {
        announcementRef.value.openNoticeDetail(noticeId)
      }
    }

    // 打开操作手册
    const openManual = () => {
      window.open(
        'https://tcnvll3k38fz.feishu.cn/wiki/EmIEwbKLmit6Omk1Os6ckbStnme?from=from_copylink',
        '_blank'
      )
    }

    // 打开问题与需求收集表
    const openFeedback = () => {
      window.open(
        'https://hxpjzk2jv4k.feishu.cn/share/base/form/shrcn4bfp0QxNrHiwySauBMXmid',
        '_blank'
      )
    }

    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          'h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between',
          'dark:bg-[var(--el-bg-color)]'
        ]}
      >
        {layout.value !== 'top' ? (
          <div class="h-full flex items-center">
            {hamburger.value && layout.value !== 'cutMenu' ? (
              <Collapse class="custom-hover" color="var(--top-header-text-color)"></Collapse>
            ) : undefined}
            {breadcrumb.value ? <Breadcrumb class="lt-md:hidden"></Breadcrumb> : undefined}
          </div>
        ) : undefined}
        <div class="h-full flex items-center">
          <ScrollingText onNoticeClick={handleNoticeClick} />
          {screenfull.value ? (
            <Screenfull class="custom-hover" color="var(--top-header-text-color)"></Screenfull>
          ) : undefined}
          {/* 操作手册 */}
          <el-tooltip class="box-item" effect="dark" content="操作手册" placement="bottom">
            <div
              class="custom-hover flex items-center justify-center px-2 cursor-pointer"
              onClick={openManual}
            >
              <Icon icon="ep:document" color="var(--top-header-text-color)" size={18} />
            </div>
          </el-tooltip>

          {/* 问题与需求收集表 */}
          <el-tooltip class="box-item" effect="dark" content="问题与需求收集表" placement="bottom">
            <div
              class="custom-hover flex items-center justify-center px-2 cursor-pointer"
              onClick={openFeedback}
            >
              <Icon icon="ep:question-filled" color="var(--top-header-text-color)" size={18} />
            </div>
          </el-tooltip>

          {/* {search.value ? <RouterSearch isModal={false} /> : undefined}
          {size.value ? (
            <SizeDropdown class="custom-hover" color="var(--top-header-text-color)"></SizeDropdown>
          ) : undefined}
          {locale.value ? (
            <LocaleDropdown
              class="custom-hover"
              color="var(--top-header-text-color)"
            ></LocaleDropdown>
          ) : undefined}
          {message.value ? (
            <Message class="custom-hover" color="var(--top-header-text-color)"></Message>
          ) : undefined} */}
          <UserInfo></UserInfo>
        </div>
        {/* Announcement组件 */}
        <Announcement ref={announcementRef} />
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-tool-header;

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
}
</style>
