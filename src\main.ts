// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 指令
import { setupAuth, setupMountedFocus } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

// 开发环境下引入字典开发工具
if (import.meta.env.DEV) {
  import('@/utils/dictDevTools')
    .then(() => {
      // console.log('✅ 字典开发工具已加载')
    })
    .catch((err) => {
      console.error('❌ 字典开发工具加载失败:', err)
    })
}

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'

// 导入缓存相关
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

// 添加全局浏览器刷新监听
const setupBrowserRefreshListener = () => {
  const { wsCache } = useCache()

  window.addEventListener('beforeunload', () => {
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.USER)
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
    // 刷新浏览器
    // location.reload()
    window.location.reload(true)
  })
}

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  // directives 指令
  setupAuth(app)
  setupMountedFocus(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)

  app.use(VxeUIAll)

  app.use(VxeUITable)

  app.mount('#app')

  // 设置浏览器刷新监听
  setupBrowserRefreshListener()
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
