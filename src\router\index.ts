import { login } from './../api/login/index';
import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import remainingRouter from './modules/remaining'
import { useAnnouncementStore } from '@/store/modules/announcement'
import { getAccessToken } from '@/utils/auth'

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH), // createWebHashHistory URL带#，createWebHistory URL不带#
  strict: true,
  routes: remainingRouter as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router

router.beforeEach((to, from, next) => {
  // 如果用户已登录且进入应用主页面，显示公告
  const announcementStore = useAnnouncementStore()
  if (!getAccessToken() || to.path == '/login') {
    announcementStore.setShowAnnouncement(false)
  }
  next()
})
