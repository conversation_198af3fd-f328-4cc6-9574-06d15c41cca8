import { defineStore } from 'pinia'
import { store } from '@/store'

interface AnnouncementState {
  showAnnouncement: boolean
  announcementValue: string
}

export const useAnnouncementStore = defineStore({
  id: 'announcement',
  state: (): AnnouncementState => ({
    showAnnouncement: true, // 是否显示公告
    announcementValue: '' // 公告内容
  }),
  getters: {
    getShowAnnouncement(): boolean {
      return JSON.parse(localStorage.getItem('showAnnouncement') || true)
    }
  },
  actions: {
    setShowAnnouncement(show: boolean) {
      localStorage.setItem('showAnnouncement', JSON.stringify(show))
      this.showAnnouncement = JSON.parse(localStorage.getItem('showAnnouncement'))
    },
    setAnnouncementValue(value: string) {
      this.announcementValue = value
    }
  },
  persist: {
    enabled: true
  }
})

export function useAnnouncementStoreWithOut() {
  return useAnnouncementStore(store)
}
