// .text-color {
//   color: var(--el-text-color-regular);
// }
// .dark .dark\:text-color {
//   color: rgba(255, 255, 255, var(--dark-text-color));
// }

// .el-table thead th {
//   font-size: 12px !important;
// }

.rc-input-select .el-select-dropdown__list {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.v-tags-view__item {
  border-radius: var(--el-border-radius-base) !important;
}

.table_btn_box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rule-grid {
  overflow: hidden !important;
}

.rule-item {
  display: flex;
  padding: 2px 0;
  font-size: 12px;
  line-height: 18px;
  border-bottom: 1px dashed #eee;
  overflow: hidden !important;
  max-width: 500px !important;
  gap: 10px;

  .rule-key {
    white-space: nowrap; /* 防止 key 换行 */
    margin-right: 5px;
    font-weight: bold;
  }

  .rule-value {
    // width: 70%;
  }
}

ins,
.el-form-item__content,
.table__body,
.el-button,
.el-pagination,
.el-pager li,
.el-card span,
input::placeholder,
.input::placeholder,
.el-form-item__label {
  font-size: 12px !important;
}

.el-icon span {
  font-size: 16px !important;
}

.el-input__inner::placeholder {
  font-size: 12px; /* 或者你希望的其他大小 */
}

/* 修改 el-table 默认字体大小 */
.el-table {
  font-size: 12px !important;

  .cell {
    line-height: 1.2;
  }

  .el-button--text {
    font-size: 12px !important;
  }

  /* 表头字体 */
  .el-table__header th {
    font-size: 12px !important;
  }

  /* 表格内容字体 */
  .el-table__body td {
    font-size: 12px !important;
    // padding-top: 4px !important;
    // padding-bottom: 4px !important;
  }

  .el-table__body .cell {
    // min-height: 20px;
    // display: flex;
    // align-items: center;
  }

  /* 分页组件字体（如果用到） */
  .el-pagination {
    font-size: 12px !important;
  }
}

.el-cascader {
  width: 100%;
}

.el-cascader .el-input__wrapper {
  width: 100%;
}

.el-cascader__tags {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-wrap: nowrap;

  .el-tag {
    width: 110px;
  }
}
