import { useClipboard } from '@vueuse/core'

/**
 * 通用复制文本方法，兼容 HTTP 和 HTTPS 环境
 * @param text 要复制的文本
 * @param successCallback 复制成功的回调函数
 * @param errorCallback 复制失败的回调函数
 */
export function copyText(
  text: string,
  successCallback?: () => void,
  errorCallback?: (err: any) => void
): void {
  // 检查是否支持 navigator.clipboard API
  const isSecureContext = window.isSecureContext
  const hasClipboardAPI = navigator.clipboard && typeof navigator.clipboard.writeText === 'function'

  // 在安全上下文（HTTPS）中使用 Clipboard API
  if (isSecureContext && hasClipboardAPI) {
    const { copy } = useClipboard()
    copy(text)
      .then(() => {
        successCallback && successCallback()
      })
      .catch((err) => {
        console.error('使用 Clipboard API 复制失败:', err)
        // 如果 Clipboard API 失败，回退到传统方法
        fallbackCopyTextToClipboard(text, successCallback, errorCallback)
      })
  } else {
    // 在非安全上下文（HTTP）中使用传统方法
    fallbackCopyTextToClipboard(text, successCallback, errorCallback)
  }
}

/**
 * 传统的复制文本方法（兼容非 HTTPS 环境）
 * @param text 要复制的文本
 * @param successCallback 复制成功的回调函数
 * @param errorCallback 复制失败的回调函数
 */
function fallbackCopyTextToClipboard(
  text: string,
  successCallback?: () => void,
  errorCallback?: (err: any) => void
): void {
  try {
    // 创建临时文本区域元素
    const textArea = document.createElement('textarea')
    
    // 设置文本区域的样式，使其不可见
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'
    
    // 设置文本内容
    textArea.value = text
    
    // 添加到文档
    document.body.appendChild(textArea)
    
    // 选择文本
    textArea.select()
    
    // 尝试复制
    const successful = document.execCommand('copy')
    
    // 移除临时元素
    document.body.removeChild(textArea)
    
    if (successful) {
      successCallback && successCallback()
    } else {
      errorCallback && errorCallback(new Error('execCommand 复制失败'))
    }
  } catch (err) {
    console.error('传统复制方法失败:', err)
    errorCallback && errorCallback(err)
  }
}