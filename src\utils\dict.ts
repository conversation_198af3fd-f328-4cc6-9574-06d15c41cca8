/**
 * 数据字典工具类
 */
import { getSimpleDictDataList } from '@/api/system/dict/dict.data'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

// 动态字典类型缓存
const dynamicDictTypes = new Set<string>()

// 自动发现的字典类型存储
let autoDiscoveredTypes: string[] = []

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
  remark: string
  id: string
  parentId: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

/**
 * 自动发现并注册新的字典类型
 */
export const autoDiscoverDictTypes = async () => {
  try {
    const dictDataList = await getSimpleDictDataList()
    const discoveredTypes = new Set<string>()

    // 收集所有字典类型
    dictDataList.forEach((item: any) => {
      if (item.dictType) {
        discoveredTypes.add(item.dictType)
        dynamicDictTypes.add(item.dictType)
      }
    })
    // 找出新发现的类型（不在现有DICT_TYPE枚举中的）
    const existingTypes = new Set(Object.values(DICT_TYPE))
    const newTypes = Array.from(discoveredTypes).filter((type) => !existingTypes.has(type))

    if (newTypes.length > 0) {
      autoDiscoveredTypes = newTypes
      console.log('🔍 自动发现新的字典类型:', newTypes)

      // 可选：自动生成枚举代码建议
      generateDictTypeCode(newTypes)
    }

    return {
      total: discoveredTypes.size,
      existing: discoveredTypes.size - newTypes.length,
      new: newTypes.length,
      newTypes
    }
  } catch (error) {
    console.error('自动发现字典类型失败:', error)
    return { total: 0, existing: 0, new: 0, newTypes: [] }
  }
}

/**
 * 生成字典类型枚举代码建议
 */
const generateDictTypeCode = (newTypes: string[]) => {
  const codeLines = newTypes.map((type) => {
    // 将字典类型转换为枚举名称（大写+下划线格式）
    const enumName = type.toUpperCase().replace(/-/g, '_')
    return `  ${enumName} = '${type}', // 自动发现的字典类型`
  })

  console.log('📝 建议添加到DICT_TYPE枚举的代码:')
  console.log(codeLines.join('\n'))

  // 可选：将代码复制到剪贴板（需要浏览器支持）
  if (navigator.clipboard) {
    navigator.clipboard.writeText(codeLines.join('\n')).catch(() => {})
  }
}

/**
 * 智能获取字典选项（支持动态类型）
 */
export const getDictOptions = (dictType: string) => {
  const options = dictStore.getDictByType(dictType) || []

  // 如果是新发现的类型且没有数据，尝试重新加载
  if (options.length === 0 && dynamicDictTypes.has(dictType)) {
    console.log(`🔄 重新加载字典类型: ${dictType}`)
    dictStore.resetDict()
  }

  return options
}

/**
 * 检查字典类型是否存在
 */
export const isDictTypeExists = (dictType: string): boolean => {
  const options = getDictOptions(dictType)
  return options && options.length > 0
}

/**
 * 获取所有可用的字典类型
 */
export const getAllDictTypes = (): string[] => {
  return getAllDictTypesWithDynamic()
}

/**
 * 获取自动发现的新字典类型
 */
export const getAutoDiscoveredTypes = (): string[] => {
  return [...autoDiscoveredTypes]
}

/**
 * 自动同步字典类型到枚举（开发模式下使用）
 * 注意：这个功能需要在开发环境下谨慎使用
 */
export const autoSyncDictTypes = async (): Promise<{
  success: boolean
  message: string
  addedTypes?: string[]
}> => {
  try {
    const discovery = await autoDiscoverDictTypes()

    if (discovery.new === 0) {
      return {
        success: true,
        message: '没有发现新的字典类型，无需同步'
      }
    }

    // 在开发环境下，自动更新DICT_TYPE枚举
    if (import.meta.env.DEV) {
      console.log('🚀 开发模式：发现新字典类型，正在自动添加到DICT_TYPE枚举中')
      console.log('新类型:', discovery.newTypes)

      try {
        // 自动添加新的字典类型到枚举中
        await addNewDictTypesToEnum(discovery.newTypes)

        return {
          success: true,
          message: `发现 ${discovery.new} 个新字典类型，已动态添加并准备了源码更新`,
          addedTypes: discovery.newTypes
        }
      } catch (addError) {
        console.error('自动添加字典类型失败:', addError)

        // 如果自动添加失败，回退到提供代码建议
        const enumCode = generateFullEnumCode(discovery.newTypes)
        console.log('自动添加失败，请手动添加以下代码:')
        console.log(enumCode)

        return {
          success: false,
          message: `自动添加失败，发现 ${discovery.new} 个新字典类型，请查看控制台获取手动添加代码`,
          addedTypes: discovery.newTypes
        }
      }
    }

    return {
      success: true,
      message: `发现 ${discovery.new} 个新字典类型，已缓存到动态类型中`,
      addedTypes: discovery.newTypes
    }
  } catch (error) {
    console.error('自动同步字典类型失败:', error)
    return {
      success: false,
      message: '自动同步失败: ' + (error as Error).message
    }
  }
}

// 动态扩展的字典类型存储
const dynamicDictTypeExtensions: Record<string, string> = {}

/**
 * 自动添加新的字典类型到DICT_TYPE枚举中（动态扩展）
 */
const addNewDictTypesToEnum = async (newTypes: string[]): Promise<void> => {
  if (!newTypes || newTypes.length === 0) {
    return
  }

  try {
    // 动态添加到运行时对象中，确保立即可用
    newTypes.forEach((type) => {
      const enumName = type.toUpperCase().replace(/-/g, '_')

      // 添加到动态扩展对象中
      dynamicDictTypeExtensions[enumName] = type

      // 同时添加到dynamicDictTypes集合中
      dynamicDictTypes.add(type)

      console.log(`✅ 已动态添加字典类型: ${enumName} = '${type}'`)
    })

    // 准备源文件更新代码
    await updateDictTypeEnumInFile(newTypes)

    console.log(`🎉 成功处理 ${newTypes.length} 个新字典类型`)
    console.log('💡 新类型已添加到动态扩展中，可通过 getAllDictTypes() 获取完整列表')
  } catch (error) {
    console.error('添加字典类型到枚举失败:', error)
    throw error
  }
}

/**
 * 更新源文件中的DICT_TYPE枚举
 */
const updateDictTypeEnumInFile = async (newTypes: string[]): Promise<void> => {
  // 生成新的枚举项
  const newEnumItems = newTypes.map((type) => {
    const enumName = type.toUpperCase().replace(/-/g, '_')
    const comment = generateCommentFromDictType(type)
    return `  ${enumName} = '${type}',${comment ? ` // ${comment}` : ' // 自动发现的字典类型'}`
  })

  console.log('📝 准备添加到DICT_TYPE枚举的代码:')
  console.log(newEnumItems.join('\n'))

  try {
    // 尝试自动修改文件
    await autoUpdateDictTypeEnum(newEnumItems)
    console.log('✅ 已自动添加到DICT_TYPE枚举中')
  } catch (error) {
    console.log('⚠️ 自动修改失败，提供手动添加方案')

    // 将代码复制到剪贴板
    if (navigator.clipboard) {
      try {
        const fullCode = newEnumItems.join('\n')
        await navigator.clipboard.writeText(fullCode)
        console.log('📋 代码已复制到剪贴板，请手动粘贴到DICT_TYPE枚举的末尾')
      } catch (clipboardError) {
        console.log('📋 无法复制到剪贴板')
      }
    }

    // 在开发环境中，我们可以提供更详细的指导
    console.log('🔧 手动添加步骤:')
    console.log('1. 打开 src/utils/dict.ts 文件')
    console.log('2. 找到 DICT_TYPE 枚举的最后一项（OPERATION_CHANNEL）')
    console.log('3. 在其后添加逗号，然后粘贴上述代码')
    console.log('4. 保存文件，Vite会自动重新加载')
  }

  console.log(`🎉 已处理 ${newTypes.length} 个新字典类型`)
}

/**
 * 自动更新DICT_TYPE枚举（开发环境功能）
 */
const autoUpdateDictTypeEnum = async (newEnumItems: string[]): Promise<void> => {
  // 检查是否在开发环境
  if (import.meta.env.DEV) {
    try {
      // 通过开发服务器API更新文件
      const response = await fetch('/__dev_api/update-dict-enum', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newEnumItems })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('✅ 文件更新成功:', result.message)
        return
      } else {
        const error = await response.text()
        throw new Error(`服务器错误: ${error}`)
      }
    } catch (error) {
      console.log('🔧 开发API调用失败，可能需要重启开发服务器')
      throw error
    }
  } else {
    // 生产环境不支持自动修改
    throw new Error('生产环境不支持自动修改文件')
  }
}

/**
 * 获取所有字典类型（包括动态添加的）
 */
export const getAllDictTypesWithDynamic = (): string[] => {
  const enumTypes = Object.values(DICT_TYPE)
  const dynamicTypes = Object.values(dynamicDictTypeExtensions)
  const cachedTypes = Array.from(dynamicDictTypes)
  return [...new Set([...enumTypes, ...dynamicTypes, ...cachedTypes])]
}

/**
 * 生成完整的枚举代码
 */
const generateFullEnumCode = (newTypes: string[]): string => {
  const enumLines = newTypes.map((type) => {
    const enumName = type.toUpperCase().replace(/-/g, '_')
    // 尝试从字典类型推断注释
    const comment = generateCommentFromDictType(type)
    return `  ${enumName} = '${type}',${comment ? ` // ${comment}` : ' // 自动发现的字典类型'}`
  })

  return enumLines.join('\n')
}

/**
 * 从字典类型推断注释
 */
const generateCommentFromDictType = (dictType: string): string => {
  // 简单的注释推断逻辑
  const commentMap: Record<string, string> = {
    status: '状态',
    type: '类型',
    state: '状态',
    level: '级别',
    category: '分类',
    mode: '模式',
    method: '方法',
    result: '结果',
    channel: '渠道',
    platform: '平台'
  }

  for (const [key, value] of Object.entries(commentMap)) {
    if (dictType.toLowerCase().includes(key)) {
      return value
    }
  }

  return ''
}

/**
 * 批量验证字典类型是否存在
 */
export const validateDictTypes = (
  dictTypes: string[]
): {
  valid: string[]
  invalid: string[]
  missing: string[]
} => {
  const valid: string[] = []
  const invalid: string[] = []
  const missing: string[] = []

  dictTypes.forEach((dictType) => {
    const options = getDictOptions(dictType)
    if (options && options.length > 0) {
      valid.push(dictType)
    } else if (dynamicDictTypes.has(dictType)) {
      missing.push(dictType) // 类型存在但数据缺失
    } else {
      invalid.push(dictType) // 类型不存在
    }
  })

  return { valid, invalid, missing }
}

/**
 * 智能字典类型建议
 * 根据输入的部分字符串，建议可能的字典类型
 */
export const suggestDictTypes = (partial: string): string[] => {
  const allTypes = getAllDictTypes()
  const lowerPartial = partial.toLowerCase()

  return allTypes
    .filter((type) => type.toLowerCase().includes(lowerPartial))
    .sort((a, b) => {
      // 优先显示以输入开头的类型
      const aStarts = a.toLowerCase().startsWith(lowerPartial)
      const bStarts = b.toLowerCase().startsWith(lowerPartial)
      if (aStarts && !bStarts) return -1
      if (!aStarts && bStarts) return 1
      return a.localeCompare(b)
    })
    .slice(0, 10) // 限制建议数量
}

export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '') || dict.value
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex',
  SYSTEM_MENU_TYPE = 'system_menu_type',
  SYSTEM_ROLE_TYPE = 'system_role_type',
  SYSTEM_DATA_SCOPE = 'system_data_scope',
  SYSTEM_NOTICE_TYPE = 'system_notice_type',
  SYSTEM_LOGIN_TYPE = 'system_login_type',
  SYSTEM_LOGIN_RESULT = 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE = 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS = 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS = 'system_sms_receive_status',
  SYSTEM_OAUTH2_GRANT_TYPE = 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS = 'system_mail_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type',
  SYSTEM_SOCIAL_TYPE = 'system_social_type',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',
  INFRA_JOB_STATUS = 'infra_job_status',
  INFRA_JOB_LOG_STATUS = 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS = 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE = 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE = 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE = 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE = 'infra_codegen_scene',
  INFRA_FILE_STORAGE = 'infra_file_storage',
  INFRA_OPERATE_TYPE = 'infra_operate_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_TYPE = 'bpm_model_type',
  BPM_MODEL_FORM_TYPE = 'bpm_model_form_type',
  BPM_TASK_CANDIDATE_STRATEGY = 'bpm_task_candidate_strategy',
  BPM_PROCESS_INSTANCE_STATUS = 'bpm_process_instance_status',
  BPM_TASK_STATUS = 'bpm_task_status',
  BPM_OA_LEAVE_TYPE = 'bpm_oa_leave_type',
  BPM_PROCESS_LISTENER_TYPE = 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE = 'bpm_process_listener_value_type',

  // ========== PAY 模块 ==========
  PAY_CHANNEL_CODE = 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS = 'pay_order_status', // 商户支付订单状态
  PAY_REFUND_STATUS = 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS = 'pay_notify_status', // 商户支付回调状态
  PAY_NOTIFY_TYPE = 'pay_notify_type', // 商户支付回调状态
  PAY_TRANSFER_STATUS = 'pay_transfer_status', // 转账订单状态
  PAY_TRANSFER_TYPE = 'pay_transfer_type', // 转账订单状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH = 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE = 'mp_message_type', // 消息类型

  // ========== Member 会员模块 ==========
  MEMBER_POINT_BIZ_TYPE = 'member_point_biz_type', // 积分的业务类型
  MEMBER_EXPERIENCE_BIZ_TYPE = 'member_experience_biz_type', // 会员经验业务类型

  // ========== MALL - 商品模块 ==========
  PRODUCT_SPU_STATUS = 'product_spu_status', //商品状态

  // ========== MALL - 交易模块 ==========
  EXPRESS_CHARGE_MODE = 'trade_delivery_express_charge_mode', //快递的计费方式
  TRADE_AFTER_SALE_STATUS = 'trade_after_sale_status', // 售后 - 状态
  TRADE_AFTER_SALE_WAY = 'trade_after_sale_way', // 售后 - 方式
  TRADE_AFTER_SALE_TYPE = 'trade_after_sale_type', // 售后 - 类型
  TRADE_ORDER_TYPE = 'trade_order_type', // 订单 - 类型
  TRADE_ORDER_STATUS = 'trade_order_status', // 订单 - 状态
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS = 'trade_order_item_after_sale_status', // 订单项 - 售后状态
  TRADE_DELIVERY_TYPE = 'trade_delivery_type', // 配送方式
  BROKERAGE_ENABLED_CONDITION = 'brokerage_enabled_condition', // 分佣模式
  BROKERAGE_BIND_MODE = 'brokerage_bind_mode', // 分销关系绑定模式
  BROKERAGE_BANK_NAME = 'brokerage_bank_name', // 佣金提现银行
  BROKERAGE_WITHDRAW_TYPE = 'brokerage_withdraw_type', // 佣金提现类型
  BROKERAGE_RECORD_BIZ_TYPE = 'brokerage_record_biz_type', // 佣金业务类型
  BROKERAGE_RECORD_STATUS = 'brokerage_record_status', // 佣金状态
  BROKERAGE_WITHDRAW_STATUS = 'brokerage_withdraw_status', // 佣金提现状态

  // ========== MALL - 营销模块 ==========
  PROMOTION_DISCOUNT_TYPE = 'promotion_discount_type', // 优惠类型
  PROMOTION_PRODUCT_SCOPE = 'promotion_product_scope', // 营销的商品范围
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE = 'promotion_coupon_template_validity_type', // 优惠劵模板的有限期类型
  PROMOTION_COUPON_STATUS = 'promotion_coupon_status', // 优惠劵的状态
  PROMOTION_COUPON_TAKE_TYPE = 'promotion_coupon_take_type', // 优惠劵的领取方式
  PROMOTION_CONDITION_TYPE = 'promotion_condition_type', // 营销的条件类型枚举
  PROMOTION_BARGAIN_RECORD_STATUS = 'promotion_bargain_record_status', // 砍价记录的状态
  PROMOTION_COMBINATION_RECORD_STATUS = 'promotion_combination_record_status', // 拼团记录的状态
  PROMOTION_BANNER_POSITION = 'promotion_banner_position', // banner 定位

  // ========== CRM - 客户管理模块 ==========
  CRM_AUDIT_STATUS = 'crm_audit_status', // CRM 审批状态
  CRM_BIZ_TYPE = 'crm_biz_type', // CRM 业务类型
  CRM_BUSINESS_END_STATUS_TYPE = 'crm_business_end_status_type', // CRM 商机结束状态类型
  CRM_RECEIVABLE_RETURN_TYPE = 'crm_receivable_return_type', // CRM 回款的还款方式
  CRM_CUSTOMER_INDUSTRY = 'crm_customer_industry', // CRM 客户所属行业
  CRM_CUSTOMER_LEVEL = 'crm_customer_level', // CRM 客户级别
  CRM_CUSTOMER_SOURCE = 'crm_customer_source', // CRM 客户来源
  CRM_PRODUCT_STATUS = 'crm_product_status', // CRM 商品状态
  CRM_PERMISSION_LEVEL = 'crm_permission_level', // CRM 数据权限的级别
  CRM_PRODUCT_UNIT = 'crm_product_unit', // CRM 产品单位
  CRM_FOLLOW_UP_TYPE = 'crm_follow_up_type', // CRM 跟进方式

  // ========== ERP - 企业资源计划模块  ==========
  ERP_AUDIT_STATUS = 'erp_audit_status', // ERP 审批状态
  ERP_STOCK_RECORD_BIZ_TYPE = 'erp_stock_record_biz_type', // 库存明细的业务类型

  // ========== AI - 人工智能模块  ==========
  AI_PLATFORM = 'ai_platform', // AI 平台
  AI_MODEL_TYPE = 'ai_model_type', // AI 模型类型
  AI_IMAGE_STATUS = 'ai_image_status', // AI 图片状态
  AI_MUSIC_STATUS = 'ai_music_status', // AI 音乐状态
  AI_GENERATE_MODE = 'ai_generate_mode', // AI 生成模式
  AI_WRITE_TYPE = 'ai_write_type', // AI 写作类型
  AI_WRITE_LENGTH = 'ai_write_length', // AI 写作长度
  AI_WRITE_FORMAT = 'ai_write_format', // AI 写作格式
  AI_WRITE_TONE = 'ai_write_tone', // AI 写作语气
  AI_WRITE_LANGUAGE = 'ai_write_language', // AI 写作语言

  // ========== IOT - 物联网模块  ==========
  IOT_NET_TYPE = 'iot_net_type', // IOT 联网方式
  IOT_VALIDATE_TYPE = 'iot_validate_type', // IOT 数据校验级别
  IOT_PRODUCT_STATUS = 'iot_product_status', // IOT 产品状态
  IOT_PRODUCT_DEVICE_TYPE = 'iot_product_device_type', // IOT 产品设备类型
  IOT_DATA_FORMAT = 'iot_data_format', // IOT 数据格式
  IOT_PROTOCOL_TYPE = 'iot_protocol_type', // IOT 接入网关协议
  IOT_DEVICE_STATE = 'iot_device_state', // IOT 设备状态
  IOT_THING_MODEL_TYPE = 'iot_thing_model_type', // IOT 产品功能类型
  IOT_DATA_TYPE = 'iot_data_type', // IOT 数据类型
  IOT_THING_MODEL_UNIT = 'iot_thing_model_unit', // IOT 物模型单位
  IOT_RW_TYPE = 'iot_rw_type', // IOT 读写类型
  IOT_PLUGIN_DEPLOY_TYPE = 'iot_plugin_deploy_type', // IOT 插件部署类型
  IOT_PLUGIN_STATUS = 'iot_plugin_status', // IOT 插件状态
  IOT_PLUGIN_TYPE = 'iot_plugin_type', // IOT 插件类型
  IOT_DATA_BRIDGE_DIRECTION_ENUM = 'iot_data_bridge_direction_enum', // 桥梁方向
  IOT_DATA_BRIDGE_TYPE_ENUM = 'iot_data_bridge_type_enum', // 桥梁类型

  // ========== aim - 开发管理模块  ==========
  AIM_PROdUCT_INFO_STATUS = 'aim_product_info_status', // 文案生成状态
  AIM_LANGUAGE_STYLE = 'aim_language_style', // 语言风格
  AIM_LANGUAGE = 'aim_language', // 语言
  AIM_MODEL = 'aim_model_type', // 模型类型
  AIM_ADVERTISEMENT_STATUS = 'aim_advertisement_status', // 广告策略状态
  AIM_MATCH = 'aim_match', // 匹配方式
  AIM_IS_STATUS = 'aim_is_status', // 是否状态
  AIM_SEARRCH_DAY = 'aim_search_day', // 广告策略分析时间跨度
  OPERATION_STATUS = 'operation_status', // 操作状态
  OPERATION_ITEM = 'operation_item', // 优化项
  OPERATION_MATCH_TYPE = 'operation_match_type', // 匹配方式

  // ==========  运营管理  ==========
  OPERATION_CAMPAIGN_BIDDING_STRATEGY = 'operation_campaign_bidding_strategy', //广告活动的竞价策略
  OPERATION_SITE = 'operation_site', // 站点
  OPERATION_PLACEMENT_TYPE = 'operation_placement_type', //广告类型
  OPERATION_CREATION_STATUS = 'operation_creation_status', // 创建广告状态
  OPERATION_CURRENCY = 'operation_currency', // 币种
  THIRDPARTY_RPA_ROBOT_CLIENT_STATUS = 'thirdparty_rpa_robot_client_status', // 机器人状态
  THIRDPARTY_RPA_TASK_STATUS = 'thirdparty_rpa_task_status', // 任务状态
  OPERATION_TASK_STATUS = 'operation_task_status', //任务状态
  OPERATION_FORBIDDEN_WORDS_CATEGORY = 'operation_forbidden_words_category', //禁止词分类
  VERSION_UPDATE_CONTROL = 'version_update_control', //版本更新控制
  INFRA_PARAM_TYPE = 'infra_param_type', //创建表模板-数据类型
  INFRA_TEMPLATE_TYPE = 'infra_template_type', //创建表模板-模板类型
  INFRA_TEMPLATE_CATEGORY = 'infra_template_category', //创建表模板-工具分类
  INFRA_TREE_UNIT = 'infra_tree_unit', // 创建表模板-单位
  INFRA_TREE_COUNTRY = 'infra_tree_country', // 国家列表
  INFRA_TABLE_APPROVAL_STATUS = 'infra_table_approval_status', // 模板中心审核状态
  INFRA_LISTING_STATUS = 'infra_listing_status', // listing状态
  OPERATION_RESTORE_APPLY_APPROVAL_STATUS = 'operation_restock_apply_approval_status', // 补货申请审核状态
  OPERATION_ADVERTING_STATE = 'operation_adverting_state', // 广告状态
  OPERATION_CHANNEL = 'operation_channel', // 渠道
  A_AIM_MODEL = 'aim_model', // 模式
  PROMOTION_ACTIVITY_STATUS = 'promotion_activity_status', // 状态
  INFRA_SHOP_STATUS = 'infra_shop_status', // 状态
  INFRA_SHOP_TYPE = 'infra_shop_type', // 店铺类型
  MANAGEMENT_FEE_RULE = 'management_fee_rule', // 管理费分摊方式
  MANAGEMENT_FEE_TYPE = 'management_fee_type', // 管理费类型
  CURRENCYNAME_CURRENCYFUHAO = 'currencyname_currencyfuhao', // 币种(币种code-币种符号) java用的，和前端没关系
  CURRENCYNAME_CURRENCYNAMECODE = 'currencyname_currencynamecode' // 币种(币种中文-币种code) 这个是前端用的
}
