/**
 * 字典开发工具
 * 用于开发环境下的字典类型管理和同步
 */

import {
  autoDiscoverDictTypes,
  autoSyncDictTypes,
  getAutoDiscoveredTypes,
  suggestDictTypes,
  validateDictTypes
} from './dict'

/**
 * 字典开发工具类
 * 提供便捷的开发命令
 */
export class DictDevTools {
  /**
   * 扫描并同步新的字典类型
   * 使用方法：在浏览器控制台中执行 window.dictDevTools.sync()
   */
  static async sync() {
    console.log('%c🔍 开始扫描字典类型...', 'color: #409EFF; font-weight: bold;')
    const result = await autoSyncDictTypes()

    if (result.success) {
      console.log('%c✅ ' + result.message, 'color: #67C23A; font-weight: bold;')
      if (result.addedTypes && result.addedTypes.length > 0) {
        console.log('%c📝 新发现的字典类型:', 'color: #E6A23C; font-weight: bold;')
        result.addedTypes.forEach((type, index) => {
          console.log(
            `  ${index + 1}. %c${type}%c`,
            'color: #409EFF; font-weight: bold;',
            'color: inherit;'
          )
        })
        console.log('')
        console.log(
          '%c💡 请将以上类型手动添加到 DICT_TYPE 枚举中',
          'color: #E6A23C; background: #FDF6EC; padding: 4px 8px; border-radius: 4px;'
        )
        console.log('%c📋 查看控制台上方的完整代码建议', 'color: #909399;')
      }
    } else {
      console.error('%c❌ ' + result.message, 'color: #F56C6C; font-weight: bold;')
    }

    console.log('---')
    return result
  }

  /**
   * 发现新的字典类型（不同步）
   */
  static async discover() {
    console.log('%c🔍 发现字典类型...', 'color: #409EFF; font-weight: bold;')
    const result = await autoDiscoverDictTypes()

    console.log('%c📊 统计结果:', 'color: #67C23A; font-weight: bold;')
    console.log(
      `  • 总计: %c${result.total}%c 个字典类型`,
      'color: #409EFF; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      `  • 已存在: %c${result.existing}%c 个`,
      'color: #67C23A; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      `  • 新发现: %c${result.new}%c 个`,
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log('')

    if (result.newTypes.length > 0) {
      console.log('%c🆕 新字典类型:', 'color: #E6A23C; font-weight: bold;')
      result.newTypes.forEach((type, index) => {
        console.log(
          `  ${index + 1}. %c${type}%c`,
          'color: #409EFF; font-weight: bold;',
          'color: inherit;'
        )
      })
      console.log('')

      console.log('%c📝 代码建议:', 'color: #67C23A; font-weight: bold;')
      result.newTypes.forEach((type) => {
        const enumName = type.toUpperCase().replace(/-/g, '_')
        const code = `${enumName} = '${type}', // 自动发现的字典类型`
        console.log(`  %c${code}%c`, 'color: #909399; font-family: monospace;', 'color: inherit;')
      })
    } else {
      console.log('%c✨ 没有发现新的字典类型', 'color: #67C23A;')
    }

    console.log('---')
    return result
  }

  /**
   * 验证指定的字典类型
   */
  static validate(dictTypes: string[]) {
    console.log('%c🔍 验证字典类型...', 'color: #409EFF; font-weight: bold;')
    const result = validateDictTypes(dictTypes)

    console.log('%c📊 验证结果:', 'color: #67C23A; font-weight: bold;')

    if (result.valid.length > 0) {
      console.log(
        '%c✅ 有效类型 (%d个):',
        'color: #67C23A; font-weight: bold;',
        result.valid.length
      )
      result.valid.forEach((type, index) => {
        console.log(`  ${index + 1}. %c${type}%c`, 'color: #67C23A;', 'color: inherit;')
      })
    }

    if (result.invalid.length > 0) {
      console.log(
        '%c❌ 无效类型 (%d个):',
        'color: #F56C6C; font-weight: bold;',
        result.invalid.length
      )
      result.invalid.forEach((type, index) => {
        console.log(`  ${index + 1}. %c${type}%c`, 'color: #F56C6C;', 'color: inherit;')
      })
    }

    if (result.missing.length > 0) {
      console.log(
        '%c⚠️ 缺失数据 (%d个):',
        'color: #E6A23C; font-weight: bold;',
        result.missing.length
      )
      result.missing.forEach((type, index) => {
        console.log(
          `  ${index + 1}. %c${type}%c (类型存在但数据为空)`,
          'color: #E6A23C;',
          'color: inherit;'
        )
      })
    }

    console.log('---')
    return result
  }

  /**
   * 搜索字典类型
   */
  static search(keyword: string) {
    console.log(`%c🔍 搜索字典类型: "${keyword}"`, 'color: #409EFF; font-weight: bold;')
    const suggestions = suggestDictTypes(keyword)

    if (suggestions.length > 0) {
      console.log(
        '%c📋 匹配的字典类型 (%d个):',
        'color: #67C23A; font-weight: bold;',
        suggestions.length
      )
      suggestions.forEach((type, index) => {
        const isExactMatch = type.toLowerCase().includes(keyword.toLowerCase())
        const style = isExactMatch ? 'color: #409EFF; font-weight: bold;' : 'color: #909399;'
        console.log(`  ${index + 1}. %c${type}%c`, style, 'color: inherit;')
      })

      if (suggestions.length >= 10) {
        console.log('%c💡 显示前10个结果，输入更具体的关键词获取精确匹配', 'color: #909399;')
      }
    } else {
      console.log('%c❌ 未找到匹配的字典类型', 'color: #F56C6C;')
      console.log('%c💡 提示: 尝试使用更通用的关键词，如 "status"、"type" 等', 'color: #909399;')
    }

    console.log('---')
    return suggestions
  }

  /**
   * 显示自动发现的类型
   */
  static showAutoDiscovered() {
    const types = getAutoDiscoveredTypes()

    console.log('%c🤖 自动发现的字典类型:', 'color: #409EFF; font-weight: bold;')

    if (types.length > 0) {
      console.log(
        '%c📋 当前缓存的动态类型 (%d个):',
        'color: #67C23A; font-weight: bold;',
        types.length
      )
      types.forEach((type, index) => {
        console.log(`  ${index + 1}. %c${type}%c`, 'color: #409EFF;', 'color: inherit;')
      })
      console.log('')
      console.log('%c💡 这些类型可以直接使用，但建议添加到 DICT_TYPE 枚举中', 'color: #E6A23C;')
    } else {
      console.log('%c📭 暂无自动发现的类型', 'color: #909399;')
      console.log(
        '%c💡 运行 %cdictDevTools.sync()%c 来发现新类型',
        'color: #909399;',
        'color: #E6A23C; font-weight: bold;',
        'color: #909399;'
      )
    }

    console.log('---')
    return types
  }

  /**
   * 显示帮助信息
   */
  static help() {
    console.clear()
    console.log(
      '%c📚 字典开发工具使用指南',
      'color: #409EFF; font-weight: bold; font-size: 16px; margin-bottom: 10px;'
    )
    console.log('')

    console.log('%c🔧 可用命令:', 'color: #67C23A; font-weight: bold; font-size: 14px;')
    console.log(
      '  %cdictDevTools.sync()%c           - 扫描并同步新字典类型 %c(推荐)%c',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;',
      'color: #F56C6C; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      '  %cdictDevTools.discover()%c       - 仅发现新类型（不同步）',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      '  %cdictDevTools.validate([...])%c  - 验证字典类型数组',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      '  %cdictDevTools.search("关键词")%c  - 搜索字典类型',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      '  %cdictDevTools.showAutoDiscovered()%c - 显示自动发现的类型',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log(
      '  %cdictDevTools.help()%c           - 显示此帮助',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log('')

    console.log('%c💡 使用示例:', 'color: #67C23A; font-weight: bold; font-size: 14px;')
    console.log(
      '  %cdictDevTools.sync()%c                    // 自动同步新字典类型',
      'color: #909399; font-family: monospace;',
      'color: #606266;'
    )
    console.log(
      '  %cdictDevTools.validate(["user_status"])%c // 验证指定类型',
      'color: #909399; font-family: monospace;',
      'color: #606266;'
    )
    console.log(
      '  %cdictDevTools.search("status")%c          // 搜索包含status的类型',
      'color: #909399; font-family: monospace;',
      'color: #606266;'
    )
    console.log('')

    console.log('%c⚠️ 注意事项:', 'color: #E6A23C; font-weight: bold; font-size: 14px;')
    console.log('  • 这些工具仅在开发环境下使用')
    console.log('  • 同步功能会在控制台输出代码建议，需要手动复制到代码中')
    console.log(
      '  • 建议定期运行 %csync()%c 来发现新的字典类型',
      'color: #E6A23C; font-weight: bold;',
      'color: inherit;'
    )
    console.log('')

    console.log(
      '%c🚀 快速开始: 输入 %cdictDevTools.sync()%c 立即体验！',
      'color: #67C23A; font-weight: bold;',
      'color: #E6A23C; font-weight: bold;',
      'color: #67C23A; font-weight: bold;'
    )
    console.log('=')
  }
}

// 在开发环境下将工具挂载到全局对象
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).dictDevTools = DictDevTools
}

export default DictDevTools
