<template>
  <div>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="16" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="70" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-20px">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
                <div class="mt-10px text-14px text-gray-500">
                  {{ t('workplace.toady') }}，20℃ - 32℃！
                </div>
              </div>
            </div>
          </el-col>
          <!-- <el-col
            :xl="12"
            :lg="12"
            :md="12"
            :sm="24"
            :xs="24"
          >
            <div class="h-70px flex items-center justify-end lt-sm:mt-10px">
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.project') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.project"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.toDo') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.todo"
                  :duration="2600"
                />
              </div>
              <el-divider
                direction="vertical"
                border-style="dashed"
              />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.access') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.access"
                  :duration="2600"
                />
              </div>
            </div>
          </el-col> -->
        </el-row>
      </el-skeleton>
    </el-card>
  </div>

  <el-row class="mt-8px" :gutter="8">
    <!-- <el-col
      :xl="16"
      :lg="16"
      :md="24"
      :sm="24"
      :xs="24"
      class="mb-8px"
    >

      <el-card
        shadow="never"
        class="mt-8px"
      >
        <el-skeleton
          :loading="loading"
          animated
        >
          <el-row
            :gutter="20"
            justify="space-between"
          >
            <el-col
              :xl="10"
              :lg="10"
              :md="24"
              :sm="24"
              :xs="24"
            >
              <el-card
                shadow="hover"
                class="mb-8px"
              >
                <el-skeleton
                  :loading="loading"
                  animated
                >
                  <Echart
                    :options="pieOptionsData"
                    :height="280"
                  />
                </el-skeleton>
              </el-card>
            </el-col>
            <el-col
              :xl="14"
              :lg="14"
              :md="24"
              :sm="24"
              :xs="24"
            >
              <el-card
                shadow="hover"
                class="mb-8px"
              >
                <el-skeleton
                  :loading="loading"
                  animated
                >
                  <Echart
                    :options="barOptionsData"
                    :height="280"
                  />
                </el-skeleton>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
    </el-col> -->
    <el-col :xl="6" :lg="6" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>通知列表</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="noticeList.length > 0">
            <div
              v-for="(item, index) in noticeList"
              :key="item.id"
              class="notice-item"
              @click="handleNoticeDetail(item, index)"
            >
              <div class="flex items-center">
                <div>
                  <div class="notice-title-line">
                    <span class="text-14px notice-title">{{ item.title }}</span>
                    <div>
                      <el-tag v-if="item.loginAgain" type="danger" size="small" class="ml-8px">
                        需重新登录
                      </el-tag>
                      <el-tag
                        v-if="item.status !== undefined"
                        :type="
                          item.status === 0 ? 'warning' : item.status === 1 ? 'success' : 'info'
                        "
                        size="small"
                        class="ml-8px"
                      >
                        {{ item.status === 0 ? '未读' : item.status === 1 ? '已读' : '已撤销' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="mt-6px text-12px text-gray-400">
                    {{ formatTime(item.createTime, 'yyyy-MM-dd HH:mm:ss') }}
                  </div>
                </div>
              </div>
              <el-divider
                style="margin: 10px 0"
                v-if="noticeList.indexOf(item) < noticeList.length - 1"
              />
            </div>
            <el-pagination
              v-if="noticeTotal > noticeQueryParams.pageSize"
              small
              background
              layout="prev, pager, next"
              :total="noticeTotal"
              :page-size="noticeQueryParams.pageSize"
              :current-page="noticeQueryParams.pageNo"
              @current-change="
                (page) => {
                  noticeQueryParams.pageNo = page
                  getNoticeList()
                }
              "
              class="mt-16px justify-end"
            />
          </div>
          <el-empty v-else description="暂无公告" />
        </el-skeleton>
      </el-card>
    </el-col>
    <el-col :xl="6" :lg="6" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>公告列表</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="announcementList.length > 0">
            <div
              v-for="item in announcementList"
              :key="item.id"
              class="notice-item"
              @click="handleAnnouncementDetail(item)"
            >
              <div class="flex items-center">
                <div>
                  <div class="notice-title-line">
                    <span class="text-14px notice-title">{{ item.title }}</span>
                  </div>
                  <div class="mt-6px text-12px text-gray-400">
                    {{ formatTime(item.createTime, 'yyyy-MM-dd HH:mm:ss') }}
                  </div>
                </div>
              </div>
              <el-divider
                style="margin: 10px 0"
                v-if="announcementList.indexOf(item) < announcementList.length - 1"
              />
            </div>
            <el-pagination
              v-if="announcementTotal > announcementQueryParams.pageSize"
              small
              background
              layout="prev, pager, next"
              :total="announcementTotal"
              :page-size="announcementQueryParams.pageSize"
              :current-page="announcementQueryParams.pageNo"
              @current-change="
                (page) => {
                  announcementQueryParams.pageNo = page
                  getAnnouncementList()
                }
              "
              class="mt-16px justify-end"
            />
          </div>
          <el-empty v-else description="暂无公告" />
        </el-skeleton>
      </el-card>
    </el-col>
  </el-row>
  <VersionUpdateDialog ref="versionUpdateDialogRef" />
</template>
<script lang="ts" setup>
import VersionUpdateDialog from '@/components/VersionUpdate/index.vue' // 导入 VersionUpdate 组件
import { formatTime } from '@/utils'
import { EChartsOption } from 'echarts'
import { ElMessage } from 'element-plus' // ElMessageBox 不再需要，因为我们将使用 VersionUpdate 组件
import { set } from 'lodash-es'

import type { NoticeUserRespVO } from '@/api/system/notice' // 导入 NoticeUserRespVO 类型
import {
  batchConfirm,
  getAnnouncement,
  getNotice,
  getNoticeUser,
  getNoticeUserPage
} from '@/api/system/notice' // 导入获取用户通知的 API 和 batchConfirm
import { useWatermark } from '@/hooks/web/useWatermark'
import { useUserStore } from '@/store/modules/user'
import { barOptions, pieOptions } from './echarts-data'
import type { Shortcut, WorkplaceTotal } from './types' // Notice 类型不再需要，将使用 NoticeUserRespVO

defineOptions({ name: 'Home' })

const { t } = useI18n()
const versionUpdateDialogRef = ref<InstanceType<typeof VersionUpdateDialog>>()
const userStore = useUserStore()
const { setWatermark } = useWatermark()
const loading = ref(true)
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const pieOptionsData = reactive<EChartsOption>(pieOptions) as EChartsOption
// 获取统计数
let totalSate = reactive<WorkplaceTotal>({
  project: 0,
  access: 0,
  todo: 0
})

const getCount = async () => {
  const data = {
    project: 40,
    access: 2340,
    todo: 10
  }
  totalSate = Object.assign(totalSate, data)
}

// 获取通知公告
const noticeList = ref<NoticeUserRespVO[]>([])
const noticeTotal = ref(0)
const noticeLoading = ref(true)
const noticeQueryParams = reactive({
  pageNo: 1,
  pageSize: 5 // 默认每页显示5条
})

// 获取公告列表
const announcementList = ref<any[]>([])
const announcementTotal = ref(0)
const announcementLoading = ref(true)
const announcementQueryParams = reactive({
  type: 2,
  pageNo: 1,
  pageSize: 5
})

const getNoticeList = async () => {
  noticeLoading.value = true
  try {
    const res = await getNoticeUserPage(noticeQueryParams)
    noticeList.value = res.list || []
    noticeTotal.value = res.total || 0
  } catch (error) {
    console.error('获取通知列表失败', error)
  } finally {
    noticeLoading.value = false
  }
}

const handleNoticeDetail = async (notice: NoticeUserRespVO, index: number) => {
  if (!notice || notice.id === undefined) return
  try {
    let currentStatus = notice.status
    // 如果是未读状态，则调用接口标记为已读
    if (notice.status === 0) {
      await batchConfirm({ ids: [notice.noticeLogId!], logout: notice.loginAgain || false })
      // 更新列表中的状态
      if (noticeList.value[index]) {
        noticeList.value[index].status = 1
        currentStatus = 1 // 更新当前状态为已读，用于传递给弹窗
      }
    }
    const res = await getNoticeUser(notice.id!)
    if (res) {
      versionUpdateDialogRef.value?.openDialog({ ...res, status: currentStatus }) // 传递更新后的状态或原状态
    } else {
      ElMessage.error('公告不存在或已被删除')
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后再试')
    console.error('获取公告详情或标记已读失败', error)
  }
}

// 获取公告列表
const getAnnouncementList = async () => {
  announcementLoading.value = true
  try {
    const res = await getAnnouncement(announcementQueryParams)
    announcementList.value = res.list || []
    announcementTotal.value = res.total || 0
  } catch (error) {
    console.error('获取公告列表失败', error)
  } finally {
    announcementLoading.value = false
  }
}

// 处理公告详情
const handleAnnouncementDetail = async (announcement: any) => {
  if (!announcement || announcement.id === undefined) return
  try {
    const res = await getNotice(announcement.id)
    if (res) {
      versionUpdateDialogRef.value?.openDialog(res)
    } else {
      ElMessage.error('公告不存在或已被删除')
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后再试')
    console.error('获取公告详情失败', error)
  }
}

// 获取快捷入口
let shortcut = reactive<Shortcut[]>([])

const getShortcut = async () => {
  const data = [
    {
      name: 'Github',
      icon: 'akar-icons:github-fill',
      url: 'github.io'
    },
    {
      name: 'Vue',
      icon: 'logos:vue',
      url: 'vuejs.org'
    },
    {
      name: 'Vite',
      icon: 'vscode-icons:file-type-vite',
      url: 'https://vitejs.dev/'
    },
    {
      name: 'Angular',
      icon: 'logos:angular-icon',
      url: 'github.io'
    },
    {
      name: 'React',
      icon: 'logos:react',
      url: 'github.io'
    },
    {
      name: 'Webpack',
      icon: 'logos:webpack',
      url: 'github.io'
    }
  ]
  shortcut = Object.assign(shortcut, data)
}

// 用户来源
const getUserAccessSource = async () => {
  const data = [
    { value: 335, name: 'analysis.directAccess' },
    { value: 310, name: 'analysis.mailMarketing' },
    { value: 234, name: 'analysis.allianceAdvertising' },
    { value: 135, name: 'analysis.videoAdvertising' },
    { value: 1548, name: 'analysis.searchEngines' }
  ]
  set(
    pieOptionsData,
    'legend.data',
    data.map((v) => t(v.name))
  )
  pieOptionsData!.series![0].data = data.map((v) => {
    return {
      name: t(v.name),
      value: v.value
    }
  })
}
const barOptionsData = reactive<EChartsOption>(barOptions) as EChartsOption

// 周活跃量
const getWeeklyUserActivity = async () => {
  const data = [
    { value: 13253, name: 'analysis.monday' },
    { value: 34235, name: 'analysis.tuesday' },
    { value: 26321, name: 'analysis.wednesday' },
    { value: 12340, name: 'analysis.thursday' },
    { value: 24643, name: 'analysis.friday' },
    { value: 1322, name: 'analysis.saturday' },
    { value: 1324, name: 'analysis.sunday' }
  ]
  set(
    barOptionsData,
    'xAxis.data',
    data.map((v) => t(v.name))
  )
  set(barOptionsData, 'series', [
    {
      name: t('analysis.activeQuantity'),
      data: data.map((v) => v.value),
      type: 'bar'
    }
  ])
}

const getAllApi = async () => {
  await Promise.all([
    getCount(),
    getNoticeList(), // 修改为调用 getNoticeList
    getAnnouncementList(), // 添加获取公告列表
    getShortcut(),
    getUserAccessSource(),
    getWeeklyUserActivity()
  ])
  loading.value = false
}

getAllApi()

getAllApi()
</script>

<style lang="scss" scoped>
.notice-title-line {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 让标题和标签分别在两侧 */
}

.notice-item {
  cursor: pointer;
}

.notice-item:hover .notice-title {
  color: var(--el-color-primary);
}
</style>
