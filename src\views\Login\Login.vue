<template>
  <div
    :class="prefixCls"
    class="relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"
  >
    <div class="relative mx-auto h-full flex">
      <div
        :class="`${prefixCls}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden overflow-x-hidden overflow-y-auto`"
      >
        <!-- 左上角的 logo + 系统标题 -->
        <div class="relative flex items-center text-white">
          <!-- <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" /> -->
          <span class="text-20px font-bold">{{ underlineToHump(appStore.getTitle) }}</span>
        </div>
        <!-- 左边的背景图 + 欢迎语 -->
        <div class="h-[calc(100%-60px)] flex items-center justify-center">
          <LoginCarousel
            :interval="6000"
            :show-transition="true"
            transition-class="animate__animated animate__bounceInLeft"
            :custom-images="carouselImages"
            @image-click="handleCarouselImageClick"
          />
        </div>
      </div>
      <div
        class="relative flex-1 p-30px dark:bg-[var(--login-bg-color)] lt-sm:p-10px overflow-x-hidden overflow-y-auto"
      >
        <!-- 右上角的主题、语言选择 -->
        <div
          class="flex items-center justify-between at-2xl:justify-end at-xl:justify-end"
          style="color: var(--el-text-color-primary)"
        >
          <div class="flex items-center at-2xl:hidden at-xl:hidden">
            <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" />
            <span class="text-20px font-bold">{{ underlineToHump(appStore.getTitle) }}</span>
          </div>
          <div class="flex items-center justify-end space-x-10px h-48px">
            <!-- <ThemeSwitch /> -->
            <!-- <LocaleDropdown /> -->
          </div>
        </div>
        <!-- 右边的登录界面 -->
        <Transition appear enter-active-class="animate__animated animate__bounceInRight">
          <div
            class="m-auto h-[calc(100%-60px)] w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"
          >
            <!-- 账号登录 -->
            <LoginForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
            <!-- 手机登录 -->
            <!-- <MobileForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" /> -->
            <!-- 二维码登录 -->
            <!-- <QrCodeForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" /> -->
            <!-- 注册 -->
            <RegisterForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
            <!-- 三方登录 -->
            <!-- <SSOLoginVue class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" /> -->
            <!-- 忘记密码 -->
            <ForgetPasswordForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
          </div>
        </Transition>
      </div>
    </div>
    <Footer class="fixed bottom-0 left-0 w100%" />
  </div>
</template>
<script lang="ts" setup>
import * as NoticeApi from '@/api/system/notice'
import { underlineToHump } from '@/utils'
import { onMounted, ref } from 'vue'

import LoginCarousel from '@/components/LoginCarousel/index.vue'
import { useDesign } from '@/hooks/web/useDesign'
import { Footer } from '@/layout/components/Footer'
import { useAppStore } from '@/store/modules/app'

import { ForgetPasswordForm, LoginForm, RegisterForm } from './components'

defineOptions({ name: 'Login' })

// 轮播图数据
const carouselImages = ref([])

// 获取首页轮播图数据
const getHomePageData = async () => {
  try {
    const res = await NoticeApi.getHomePage({ type: 3 })
    if (res && res.list && res.list.length > 0) {
      // 只取第一项的imgUrl
      carouselImages.value = JSON.parse(res.list[0].imgUrl) || []
    }
  } catch (error) {
    console.error('获取首页轮播图失败:', error)
  }
}

// 处理轮播图点击事件
const handleCarouselImageClick = (index: number) => {
  // 轮播图组件内部已经处理了点击事件，这里可以添加额外的处理逻辑
  console.log('Carousel image clicked:', index)
}

// 组件挂载时获取数据
onMounted(() => {
  getHomePageData()
  sessionStorage.removeItem('announcementShown') //每次都清除sessionStorage.getItem('announcementShown')，实现进入系统就弹窗公告提示
})

const { t } = useI18n()
const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.#{$prefix-cls} {
  overflow: auto;

  &__left {
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background-image: url('@/assets/svgs/login-bg.svg');
      background-position: center;
      background-repeat: no-repeat;
      content: '';
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 10px;
}
</style>

<style lang="scss">
.dark .login-form {
  .el-divider__text {
    background-color: var(--login-bg-color);
  }

  .el-card {
    background-color: var(--login-bg-color);
  }
}
</style>
