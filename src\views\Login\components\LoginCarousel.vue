<template>
  <TransitionGroup
    appear
    enter-active-class="animate__animated animate__bounceInLeft"
    tag="div"
    style="width: 500px"
  >
    <el-carousel
      height="500px"
      width="500px"
      :interval="6000"
    >
      <el-carousel-item
        v-for="(item, index) in images"
        :key="item"
      >
        <img
          alt=""
          class="w-500px"
          :src="item"
          @click="handleImgClick(index)"
        />
      </el-carousel-item>
    </el-carousel>
  </TransitionGroup>
</template>

<script lang="ts" setup>
defineOptions({ name: 'LoginCarousel' })

interface Props {
  images: string[]
  clickUrl?: string
  clickIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  images: () => [],
  clickUrl: 'https://hxpjzk2jv4k.feishu.cn/share/base/form/shrcn4bfp0QxNrHiwySauBMXmid',
  clickIndex: 4
})

const handleImgClick = (index: number) => {
  if (index === props.clickIndex) {
    window.open(props.clickUrl)
  }
}
</script>
