<template>
  <ContentWrap>
    <el-descriptions :column="2" border>

      <el-descriptions-item label="团队">
        {{ detailData.tenantName }}
      </el-descriptions-item>

      <el-descriptions-item label="国家">
        {{ detailData.country }}
      </el-descriptions-item>

      <el-descriptions-item label="语言">
        {{ detailData.language }}
      </el-descriptions-item>

      <el-descriptions-item label="违禁词">
        {{ detailData.forbiddenWord }}
      </el-descriptions-item>

      <el-descriptions-item label="违禁词分类名称">
        {{ detailData.categoryName }}
      </el-descriptions-item>

      <el-descriptions-item label="中文翻译">
        {{ detailData.chineseTranslation }}
      </el-descriptions-item>

      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
    </el-descriptions>

  </ContentWrap>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import * as ForiddenWordApi from '@/api/bpm/foriddenWords'

defineOptions({ name: 'BpmOALeaveDetail' })

const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.string.def(undefined)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as string // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ForiddenWordApi.get(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(() => {
  getInfo()
})
</script>
