<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button type="primary" @click="handleBatchAudit">
          <Icon class="mr-5px" icon="ep:check" />
          批量审核
        </el-button>
      </el-form-item>
      <el-form-item label="" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择流程分类"
          clearable
          class="!w-155px"
          @change="handleQuery"
        >
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <!-- 高级筛选 -->
      <el-form-item>
        <el-popover
          :visible="showPopover"
          persistent
          :width="400"
          :show-arrow="false"
          placement="bottom-end"
        >
          <template #reference>
            <el-button @click="showPopover = !showPopover">
              <Icon icon="ep:plus" class="mr-5px" />高级筛选
            </el-button>
          </template>
          <el-form-item
            label="所属流程"
            class="font-bold"
            label-position="top"
            prop="processDefinitionKey"
          >
            <el-select
              v-model="queryParams.processDefinitionKey"
              placeholder="请选择流程定义"
              clearable
              @change="handleQuery"
              class="!w-390px"
            >
              <el-option
                v-for="item in processDefinitionList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发起时间" class="font-bold" label-position="top" prop="createTime">
            <el-date-picker
              v-model="queryParams.createTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="w-240px!"
            />
          </el-form-item>
          <el-form-item class="font-bold" label-position="top">
            <div class="flex justify-end w-full">
              <el-button @click="resetQuery">清空</el-button>
              <el-button @click="showPopover = false">取消</el-button>
              <el-button type="primary" @click="handleQuery">确认</el-button>
            </div>
          </el-form-item>
        </el-popover>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="流程" prop="processInstance.name" width="180" />
      <el-table-column label="摘要" prop="processInstance.summary" width="180">
        <template #default="scope">
          <div
            class="flex flex-col"
            v-if="scope.row.processInstance.summary && scope.row.processInstance.summary.length > 0"
          >
            <div v-for="(item, index) in scope.row.processInstance.summary" :key="index">
              <el-text type="info"> {{ item.key }} : {{ item.value }} </el-text>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="发起人"
        prop="processInstance.startUser.nickname"
        width="100"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="发起时间"
        prop="createTime"
        width="180"
      />
      <el-table-column align="center" label="当前任务" prop="name" width="180" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="任务时间"
        prop="createTime"
        width="180"
      />
      <el-table-column
        align="center"
        label="流程编号"
        prop="processInstanceId"
        :show-overflow-tooltip="true"
      />
      <el-table-column align="center" label="任务编号" prop="id" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="操作" fixed="right" width="160">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">办理</el-button>
          <el-button link type="primary" @click="handleSingleAudit(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 批量审核弹窗 -->
  <el-dialog v-model="batchAuditVisible" title="批量审核" width="500px">
    <el-form :model="auditForm" label-width="80px">
      <el-form-item label="审核动作">
        <el-radio-group v-model="auditForm.action">
          <el-radio value="approve">通过</el-radio>
          <el-radio value="reject">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" required>
        <el-input
          v-model="auditForm.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入审核意见"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelAudit">取消</el-button>
      <el-button type="primary" @click="confirmBatchAudit">确定</el-button>
    </template>
  </el-dialog>

  <!-- 单个审核弹窗 -->
  <el-dialog v-model="singleAuditVisible" title="审核" width="500px">
    <el-form :model="auditForm" label-width="80px">
      <el-form-item label="审核动作">
        <el-radio-group v-model="auditForm.action">
          <el-radio value="approve">通过</el-radio>
          <el-radio value="reject">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" required>
        <el-input
          v-model="auditForm.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入审核意见"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelAudit">取消</el-button>
      <el-button type="primary" @click="confirmSingleAudit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { CategoryApi, CategoryVO } from '@/api/bpm/category'
import * as DefinitionApi from '@/api/bpm/definition'
import * as TaskApi from '@/api/bpm/task'
import { dateFormatter } from '@/utils/formatTime'

defineOptions({ name: 'BpmTodoTask' })

const { push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const processDefinitionList = ref<any[]>([]) // 流程定义列表
const selectedRows = ref([]) // 选中的行数据
const batchAuditVisible = ref(false) // 批量审核弹窗显示状态
const singleAuditVisible = ref(false) // 单个审核弹窗显示状态
const currentAuditRow = ref(null) // 当前审核的行数据
const auditForm = reactive({
  reason: '',
  action: 'approve' // approve 或 reject
})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  category: undefined,
  processDefinitionKey: '',
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const categoryList = ref<CategoryVO[]>([]) // 流程分类列表
const showPopover = ref(false) // 高级筛选是否展示

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskTodoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id,
      taskId: row.id
    }
  })
}

/** 多选变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

/** 批量审核按钮 */
const handleBatchAudit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要审核的数据')
    return
  }
  auditForm.reason = ''
  auditForm.action = 'approve'
  batchAuditVisible.value = true
}

/** 单个审核按钮 */
const handleSingleAudit = (row: any) => {
  currentAuditRow.value = row
  auditForm.reason = ''
  auditForm.action = 'approve'
  singleAuditVisible.value = true
}

/** 确认批量审核 */
const confirmBatchAudit = async () => {
  if (!auditForm.reason.trim()) {
    ElMessage.warning('请输入审核意见')
    return
  }

  try {
    const ids = selectedRows.value.map((row) => row.id)
    if (auditForm.action === 'approve') {
      await TaskApi.batchApprove({ ids, reason: auditForm.reason })
      ElMessage.success('批量审批通过成功')
    } else {
      await TaskApi.batchReject({ ids, reason: auditForm.reason })
      ElMessage.success('批量审批拒绝成功')
    }
    batchAuditVisible.value = false
    selectedRows.value = []
    await getList()
  } catch (error) {
    console.error('批量审核失败:', error)
  }
}

/** 确认单个审核 */
const confirmSingleAudit = async () => {
  if (!auditForm.reason.trim()) {
    ElMessage.warning('请输入审核意见')
    return
  }

  try {
    const data = {
      id: currentAuditRow.value.id,
      reason: auditForm.reason
    }

    if (auditForm.action === 'approve') {
      await TaskApi.approveTask(data)
      ElMessage.success('审批通过成功')
    } else {
      await TaskApi.rejectTask(data)
      ElMessage.success('审批拒绝成功')
    }
    singleAuditVisible.value = false
    currentAuditRow.value = null
    await getList()
  } catch (error) {
    console.error('审核失败:', error)
  }
}

/** 取消审核 */
const cancelAudit = () => {
  batchAuditVisible.value = false
  singleAuditVisible.value = false
  currentAuditRow.value = null
  auditForm.reason = ''
  auditForm.action = 'approve'
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
  // 获取流程定义列表
  processDefinitionList.value = await DefinitionApi.getSimpleProcessDefinitionList()
})
</script>
