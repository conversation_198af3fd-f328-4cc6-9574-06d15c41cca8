<template>
  <el-dialog
    title="审核"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item
        label="审核结果"
        prop="status"
      >
        <el-radio-group v-model="formData.status">
          <el-radio label="已通过">通过</el-radio>
          <el-radio label="已拒绝">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="备注"
        prop="remark"
      >
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="submitting"
        >确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/config/axios'

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const submitting = ref(false) // 新增：提交状态
const formData = reactive({
  status: '已通过', // 1: 通过, 2: 拒绝
  remark: ''
})
const formRules = reactive({
  status: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
})
const formRef = ref()

const auditData = ref<any[]>([]) // 新增：用于存储待审核的数据
const templateId = ref<number>()
const tableHeadId = ref<number>()

const open = (data: any[], template?: { templateId: number; tableHeadId: number }) => {
  auditData.value = data // 存储传入的数据
  if (template) {
    templateId.value = template.templateId
    tableHeadId.value = template.tableHeadId
  }
  resetForm()
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  submitting.value = true // 设置提交状态为 true
  try {
    // 调用审核接口
    const rowIds = auditData.value.map((item) => item.id)
    await request.post({
      url: '/infra/tools-table-data/approval',
      data: {
        approvalRemark: formData.remark,
        approvalStatus: formData.status.toString(),
        rowIds,
        tableHeadId: tableHeadId.value,
        templateId: templateId.value
      }
    })
    ElMessage.success('审核提交成功')
    dialogVisible.value = false
    emits('success') // 触发成功事件
  } catch (error) {
    console.error('审核提交失败', error)
    ElMessage.error('审核提交失败')
  } finally {
    submitting.value = false // 无论成功或失败，都将提交状态重置为 false
  }
}

const resetForm = () => {
  formData.status = '已通过'
  formData.remark = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  open
})
</script>
