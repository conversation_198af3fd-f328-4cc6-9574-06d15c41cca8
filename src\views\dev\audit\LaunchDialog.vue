<template>
  <el-dialog
    title="投产"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item
        label="店铺"
        prop="storeId"
      >
        <el-select
          v-model="formData.storeId"
          placeholder="请选择店铺"
          clearable
          filterable
          class="!w-300px"
        >
          <el-option
            v-for="item in storeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="运营人员"
        prop="operatorIds"
      >
        <!-- multiple -->
        <el-select
          v-model="formData.operatorIds"
          placeholder="请选择运营人员"
          clearable
          filterable
          collapse-tags
          collapse-tags-tooltip
          class="!w-300px"
        >
          <el-option
            v-for="item in operatorOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="投产表"
        prop="launchTableId"
      >
        <el-select
          v-model="formData.launchTableId"
          placeholder="请选择投产表"
          clearable
          class="!w-300px"
        >
          <el-option
            v-for="item in launchTableOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="SKU"
        prop="sku"
      >
        <el-input
          v-model="formData.sku"
          placeholder="请输入SKU"
          clearable
          class="!w-300px"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="submitting"
        >确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/config/axios'
import { getUserListNoPermission } from '@/api/system/user'
import { getProdByDevList } from '@/api/tool/tableTemplateCenter'
import { ThirdPartyAccountApi } from '@/api/system/third'

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const submitting = ref(false) // 新增：提交状态
const formData = reactive({
  storeId: undefined,
  operatorIds: '',
  launchTableId: undefined,
  sku: '',
  templateId: ''
})
const formRules = reactive({
  launchTableId: [{ required: true, message: '请选择投产表', trigger: 'change' }],
  sku: [{ required: true, message: '请填写sku', trigger: 'blur' }]
})
const formRef = ref()

// 选项数据
const storeOptions = ref([])
const operatorOptions = ref([])
const launchTableOptions = ref([])
const rowIds = ref([])

// 获取店铺列表
const getStoreList = async () => {
  try {
    // 这里使用系统中已有的店铺接口
    var res = []
    if (
      localStorage.getItem('shopList') == null ||
      !localStorage.getItem('shopList') ||
      localStorage.getItem('shopList')?.length == 0
    ) {
      res = await ThirdPartyAccountApi.getShopList([], [])
    } else {
      res = JSON.parse(localStorage.getItem('shopList'))
    }

    if (res && Array.isArray(res)) {
      storeOptions.value = res.map((item) => ({
        label: item.sellerItem + '——' + item.country,
        value: item.sellerItem
      }))
    }
  } catch (error) {
    console.error('获取店铺列表失败', error)
  }
}

// 获取运营人员列表
const getOperatorList = async () => {
  try {
    const res = await getUserListNoPermission()
    if (res && Array.isArray(res)) {
      operatorOptions.value = res.map((item) => ({
        label: item.nickname,
        value: item.nickname
      }))
    }
  } catch (error) {
    console.error('获取运营人员列表失败', error)
  }
}

// 获取投产表列表
const getLaunchTableList = async () => {
  try {
    const res = await getProdByDevList({
      pageNo: 1,
      pageSize: 100,
      templateTypeCode: 2,
      templateId: formData.templateId
    })
    if (res && Array.isArray(res)) {
      launchTableOptions.value = res.map((item) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取投产表列表失败', error)
  }
}

// 根据行ID获取详情数据
const getDetailData = async (rowId: number) => {
  try {
    const res = await request.get({
      url: '/infra/tools-table-data/get-data-by-rowId?rowId=' + rowId
    })
    if (res) {
      const data = res
      // 回显数据到表单
      if (data.shopName) {
        formData.storeId = data.shopName
      }
      if (data.operationUserName) {
        formData.operatorIds = data.operationUserName
      }
      if (data.prodTemplateId) {
        formData.launchTableId = data.prodTemplateId
      }
      if (data.sku) {
        formData.sku = data.sku
      }
    }
  } catch (error) {
    console.error('获取详情数据失败', error)
  }
}

const open = async (data?: any, tmpId?: any, rowId?: any) => {
  rowIds.value = rowId
  resetForm()
  // 如果有SKU数据，填充到表单
  formData.templateId = tmpId

  // 加载选项数据
  await Promise.all([getStoreList(), getOperatorList(), getLaunchTableList()])

  // 如果传入了data且包含id，则调用详情接口获取数据
  if (data && data.id) {
    await getDetailData(data.id)
  }

  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  submitting.value = true // 设置提交状态为 true
  try {
    // 调用投产接口
    await request.post({
      url: '/infra/tools-table-data/prod',
      data: {
        // operationUserName: formData.operatorIds.join(','),
        operationUserName: formData.operatorIds,
        prodTemplateId: formData.launchTableId,
        rowIds: rowIds.value,
        shopName: formData.storeId,
        sku: formData.sku
      }
    })
    ElMessage.success('投产提交成功')
    dialogVisible.value = false
    emits('success') // 触发成功事件
  } catch (error) {
    console.error('投产提交失败', error)
    ElMessage.error('投产提交失败')
  } finally {
    submitting.value = false // 无论成功或失败，都将提交状态重置为 false
  }
}

const resetForm = () => {
  formData.storeId = undefined
  formData.operatorIds = ''
  formData.launchTableId = undefined
  formData.sku = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  open
})
</script>
