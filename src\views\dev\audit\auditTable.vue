<template>
  <ContentWrap v-loading="templateOptions.length === 0">
    <!-- 开发表选择器和折叠控制 -->
    <div class="flex items-center gap-10px mb-10px">
      <el-form-item prop="developmentTable" style="margin-bottom: 0 !important">
        <div class="flex items-center">
          <el-select
            v-model="queryParams.templateId"
            placeholder="请选择要审核的表"
            clearable
            class="!w-240px"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="item in templateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <!-- 统计条 -->
          <div class="flex flex-col items-start ml-10px line-height-normal">
            <span>全模板待审核: {{ totalPendingAudit }}</span>
            <span>当前模板待审核: {{ currentPendingAudit }}</span>
          </div>
        </div>
      </el-form-item>

      <!-- 基础操作按钮 -->
      <div class="flex items-center gap-10px">
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <QueryConditionTemplate
          :type="'auditTable'"
          :form-data="queryParams"
          @update:form-data="onUpdateFormData"
        />
        <el-button type="text" @click="searchCollapsed = !searchCollapsed" class="ml-10px">
          <Icon :icon="searchCollapsed ? 'ep:arrow-down' : 'ep:arrow-up'" class="mr-5px" />
          {{ searchCollapsed ? '展开' : '收起' }}
        </el-button>
      </div>
    </div>

    <!-- 搜索工作栏 -->
    <el-collapse-transition>
      <el-form
        v-show="!searchCollapsed"
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="80px"
      >
        <el-form-item prop="productName">
          <div class="flex gap-20px">
            <el-input
              v-model="queryParams.productName"
              placeholder="请输入产品名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
            <el-select
              v-model="queryParams.auditStatus"
              placeholder="请选择审核状态"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="!w-240px"
            >
              <el-option
                v-for="(item, index) in getIntDictOptions(DICT_TYPE.INFRA_TABLE_APPROVAL_STATUS)"
                :key="index"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
            <el-select
              v-model="queryParams.production"
              placeholder="请选择是否投产"
              clearable
              class="!w-240px"
            >
              <el-option label="全部" value="" />
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
            <el-select
              v-model="queryParams.approvalNameList"
              placeholder="请选择审核人"
              clearable
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
              class="!w-240px"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="user.nickname"
                :value="user.nickname"
              />
            </el-select>
            <el-select
              v-model="queryParams.creatorNameList"
              placeholder="请选择创建人"
              clearable
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
              class="!w-240px"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="user.nickname"
                :value="user.nickname"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <shortcut-date-range-picker v-model="queryParams.createTime" />
        </el-form-item>
        <el-form-item label="审核时间" prop="approvalTime">
          <shortcut-date-range-picker v-model="queryParams.approvalTime" />
        </el-form-item>
      </el-form>
    </el-collapse-transition>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap class="mt-10px">
    <Table
      border
      v-if="queryParams.templateId && dynamicColumns.length > 0"
      height="75vh"
      ref="tableRefs"
      :loading="loading"
      :columns="dynamicColumns.length > 0 ? dynamicColumns : columns"
      :data="list"
      selection
      :saveStatus="saveStatus"
      :type="String(queryParams.templateId)"
      :pagination="{
        total: totalCount,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      :dragField="(row, index) => (row.id ? row.id : `${index}_${row.name}`)"
      @selection-change="handleSelectChange"
      @update:page-size="handlePageSizeChange"
      @update:current-page="handlePageChange"
      @sort-change="handleSortChange"
      isSort
    >
      <!-- 顶部操作按钮 -->
      <template #top-btn>
        <el-button
          v-hasPermi="['infra:tools-table-data:approval']"
          type="primary"
          @click="handleBatchReview"
          >批量审核</el-button
        >
        <el-button v-hasPermi="['infra:tools-table-data:export']" plain @click="handleExport">
          <Icon icon="ep:download" class="mr-5px" />导出筛选数据
        </el-button>
      </template>
      <template #action="{ row }">
        <!-- 编辑/保存按钮 -->
        <el-button
          v-if="!row.isEdit"
          v-hasPermi="['infra:tools-table-data:save']"
          type="text"
          size="small"
          @click="row.isEdit = true"
        >
          编辑
        </el-button>
        <el-button
          v-else
          v-hasPermi="['infra:tools-table-data:save']"
          type="text"
          size="small"
          @click="saveChangedData(row)"
        >
          保存
        </el-button>
        <!-- 基础按钮 -->
        <el-button
          type="text"
          size="small"
          v-hasPermi="['infra:tools-table-data:approval']"
          @click="handleReview(row)"
          :disabled="isRowApproved(row)"
        >
          审核
        </el-button>
        <el-button
          type="text"
          size="small"
          v-hasPermi="['infra:tools-table-data:prod']"
          @click="handleLaunch(row)"
        >
          投产
        </el-button>
      </template>
    </Table>
    <el-empty v-else description="请选择要审核的表模板" />
  </ContentWrap>

  <!-- 弹窗 -->
  <AuditDialog ref="auditDialogRef" @success="handleAuditSuccess" />
  <LaunchDialog ref="launchDialogRef" @success="handleLaunchSuccess" />
</template>

<script setup lang="tsx">
import { getUserListNoPermission } from '@/api/system/user'
import {
  exportExcel, // 导入导出方法
  getPage as getTableDataPage,
  update as updateTableData
} from '@/api/tool/tableData'
import {
  getTableHeadByTemplateId,
  getTemplateList as getTemplateListApi
} from '@/api/tool/tableTemplateCenter'
import request from '@/config/axios'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import AuditDialog from './AuditDialog.vue'
import LaunchDialog from './LaunchDialog.vue'

defineOptions({ name: 'DevAuditIndex' })

const props = defineProps({
  drawerMode: {
    type: Boolean,
    default: false
  },
  initialTemplateId: {
    type: [String, Number],
    default: null
  },
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})

const onUpdateFormData = (newVal) => {
  Object.assign(queryParams, newVal)
  handleQuery()
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const tableRef = ref(null)
const loading = ref(false) // 列表的加载中
const searchCollapsed = ref(false) // 搜索区域折叠状态，默认折叠
const list = ref([]) // 列表的数据
const templateOptions = ref([])
const tableHeadData = ref(null)
const dynamicColumns = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  templateId: undefined, // 模板ID
  tableHeadId: undefined, // 表头ID
  productName: undefined,
  auditStatus: undefined, // 0: 待审核, 1: 已通过, 2: 已拒绝
  production: undefined, // 投产状态：1-已投产，0-未投产
  approvalNameList: [], // 审核人ID
  creatorNameList: [],
  sortingFields: [],
  createTime: [],
  approvalTime: [] // 新增审核时间
})

const totalCount = ref(0) // 列表数据的总条数
const queryFormRef = ref() // 搜索的表单
const selectedRows = ref([]) // 表格的选中列表

const totalPendingAudit = ref(0)
const currentPendingAudit = ref(0)

const auditDialogRef = ref()
const launchDialogRef = ref()
const saveStatus = ref({
  saving: false,
  lastSaved: null,
  error: null
})
// 列配置
const columns = [
  {
    field: 'action',
    align: 'left',
    title: '操作',
    label: '操作',
    type: 'action',
    width: 200
  }
]
const userOptions = ref([])
// 获取用户列表
const getOperatorList = async () => {
  try {
    const res = await getUserListNoPermission()
    if (res && Array.isArray(res)) {
      userOptions.value = res.map((item) => ({
        id: item.id,
        nickname: item.nickname
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
  }
}
// 格式化表格数据
const formatTableData = (dataList) => {
  if (!dataList || !dataList.length) return []

  return dataList.map((item) => {
    // 创建行数据对象，包含行ID
    const rowData = {
      id: item.rowId
    }

    // 将 resultMap 中的数据展开到行对象中
    Object.entries(item.resultMap).forEach(([fieldId, value]) => {
      rowData[fieldId] = value
    })

    return rowData
  })
}

/** 查询列表 */
const getList = async () => {
  if (!queryParams.tableHeadId) {
    ElMessage.warning('请先选择要审核的表')
    return
  }

  loading.value = true
  try {
    // 调用API获取表格数据
    const res = await getTableDataPage({
      ...queryParams,
      tableHeadId: queryParams.tableHeadId,
      createTime: queryParams.createTime,
      approvalTime: queryParams.approvalTime,
      templateType: 3 //开发1 投产2 审核2
    })

    if (res) {
      // 处理返回的数据，转换为表格需要的格式
      const formattedData = formatTableData(res.list || [])
      list.value = formattedData
      totalCount.value = res.total || 0
    } else {
      list.value = []
      totalCount.value = 0
    }
  } catch (error) {
    console.error('获取表格数据失败', error)
    list.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.pageNo = 1
  queryParams.pageSize = 50
  queryParams.templateId = undefined
  queryParams.tableHeadId = undefined
  queryParams.productName = undefined
  queryParams.auditStatus = undefined
  queryParams.production = undefined
  queryParams.approvalNameList = []
  queryParams.creatorNameList = []
  queryParams.createTime = []
  queryParams.approvalTime = []
  queryParams.tableHeadId = ''

  totalCount.value = 0

  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  handleQuery()
}

// 分页显示数量变化
function handlePageSizeChange(pageSize) {
  queryParams.pageSize = pageSize
  getList()
  console.log('分页显示数量变化', pageSize)
}

// 分页变化
function handlePageChange(page) {
  queryParams.pageNo = page
  getList()
  console.log('分页变化', page)
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}

import download from '@/utils/download'

/** 导出按钮操作 */
const handleExport = async () => {
  if (!queryParams.templateId) {
    message.warning('请先选择要审核的表模板')
    return
  }
  try {
    const data = await exportExcel({
      ...queryParams,
      templateType: 3
    })
    download.excel(data, '审核表导出数据.xls')
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  }
}

// 处理全选
const handleSelectAll = (selection) => {
  selectedRows.value = selection
}

// 处理选择变化
const handleSelectChange = (selection) => {
  selectedRows.value = selection
}

// 处理模板变更
const handleTemplateChange = async (templateId) => {
  if (!templateId) {
    tableHeadData.value = null
    dynamicColumns.value = []
    list.value = []
    totalCount.value = 0
    // 重新获取全部统计
    await getApprovalStatusCount()
    return
  }

  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  loading.value = true
  // 重置分页和数据状态
  queryParams.pageNo = 1
  queryParams.pageSize = 50
  list.value = []
  totalCount.value = 0

  try {
    // 调用API获取表头数据
    const res = await getTableHeadByTemplateId(templateId)
    if (res) {
      tableHeadData.value = res
      queryParams.tableHeadId = res.id

      // 解析表头JSON字符串
      if (res.tableHead) {
        const tableHeadObj = JSON.parse(res.tableHead)
        if (tableHeadObj && tableHeadObj.fields) {
          // 根据表头字段生成动态列配置
          dynamicColumns.value = generateDynamicColumns(tableHeadObj.fields)
          // 获取表格数据
          await getList()
          // 获取当前模板的审核统计
          await getApprovalStatusCount(templateId)
        }
      }
    }
  } catch (error) {
    console.error('获取表头数据失败', error)
  } finally {
    loading.value = false
  }
}

// 判断行是否已审核通过
const isRowApproved = (row) => {
  // 尝试从不同可能的字段中获取审核状态
  return Object.values(row).some((value) => value == '已通过')
}

// 根据表头字段生成动态列配置
const generateDynamicColumns = (fields) => {
  // 获取复选框列
  const checkboxColumn = columns.find((col) => col.field === 'checkbox')
  // 获取操作列
  const actionColumn = columns.find((col) => col.field === 'action')

  // 设置操作列固定在右侧
  if (actionColumn) {
    actionColumn.fixed = 'right'
  }

  // 根据表头字段生成动态列
  const fieldColumns = fields.map((field) => {
    // 基础列配置
    const column = {
      ...field,
      field: field.fieldId,
      align: 'left',
      title: field.fieldName,
      label: field.fieldName,
      slotsName: field.paramTypeCode == 'file' ? 'image' : ''
    }

    return column
  })

  // 复选框列在最前，字段列在中间，操作列在最后
  return checkboxColumn
    ? [checkboxColumn, ...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
    : [...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
}

// 获取模板列表
const getTemplateList = async () => {
  try {
    const res = await getTemplateListApi({
      pageNo: 1,
      pageSize: 100,
      templateTypeCode: 1
    })
    if (res) {
      templateOptions.value = res.map((item) => ({
        label:
          item.name +
          '  (' +
          (item.noApprovalNum != undefined ? item.noApprovalNum : item.pendingReviewQty || 0) +
          ')  ',
        value: item.id
      }))
      console.log(res)
    }
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

/** 批量审核操作 */
const handleBatchReview = () => {
  if (selectedRows.value.length === 0) {
    message.error('请先选择要审核的条目')
    return
  }
  auditDialogRef.value.open(selectedRows.value, {
    templateId: queryParams.templateId,
    tableHeadId: queryParams.tableHeadId
  })
}

/** 审核操作 */
const handleReview = (row) => {
  console.log('审核:', row)
  auditDialogRef.value.open([row], {
    templateId: queryParams.templateId,
    tableHeadId: queryParams.tableHeadId
  })
}

/** 投产操作 */
const handleLaunch = (row) => {
  launchDialogRef.value.open(row, queryParams.templateId, [row.id])
}

const getAuditStatusText = (status) => {
  switch (status) {
    case 0:
    case '0':
      return '待审核'
    case 1:
    case '1':
      return '已通过'
    case 2:
    case '2':
      return '已拒绝'
    default:
      return '未知状态'
  }
}

// 获取行的审核状态
const getRowAuditStatus = (row) => {
  // 尝试从不同可能的字段中获取审核状态
  return row.auditStatus || row.approvalStatus || row.审核状态 || row.状态
}

/** 获取审核统计数据 */
const getApprovalStatusCount = async (templateId) => {
  try {
    const res = await request.post({
      url: '/infra/tools-table-data/approval-status-count',
      data: templateId ? { templateId } : {}
    })
    if (res) {
      totalPendingAudit.value = res.allCount || 0
      currentPendingAudit.value = res.currentCount || 0
    }
  } catch (error) {
    console.error('获取审核统计数据失败', error)
  }
}

/** 初始化 */
onMounted(async () => {
  await getTemplateList()
  await getApprovalStatusCount() // 初始化时获取全部统计
  await getOperatorList()

  // 如果是抽屉模式，则应用初始值
  if (props.drawerMode) {
    if (props.initialTemplateId) {
      queryParams.templateId = props.initialTemplateId
      console.log(props.initialFilters)
      if (props.initialFilters) {
        Object.assign(queryParams, props.initialFilters)
      }
      await handleTemplateChange(queryParams.templateId)
    }
  }
})

const handleAuditSuccess = async () => {
  await getList()
  // 刷新审核统计数据
  await getApprovalStatusCount(queryParams.templateId)
}

const handleLaunchSuccess = () => {
  getList()
}

/** 处理列配置变更 */
const handleCustomChange = async () => {
  try {
    // 重新获取表头配置
    const headRes = await getTableHeadByTemplateId(queryParams.templateId)
    if (headRes && headRes.tableHead) {
      tableHeadData.value = headRes
      queryParams.tableHeadId = headRes.id

      // 解析表头JSON字符串
      const parsedTableHead = JSON.parse(headRes.tableHead)

      // 生成动态列配置
      dynamicColumns.value = generateDynamicColumns(parsedTableHead.fields)

      // 重新获取列表数据
      await getList()

      ElMessage.success('列配置已更新')
    }
  } catch (error) {
    console.error('更新列配置失败', error)
    ElMessage.error('更新列配置失败')
  } finally {
    // 确保loading状态被关闭
    loading.value = false
  }
}

// 保存变更的数据
const saveChangedData = async (changedRow) => {
  if (!queryParams.templateId || !tableHeadData.value) {
    return
  }

  // 防止重复调用
  if (saveStatus.value.saving) {
    return
  }

  saveStatus.value.saving = true
  saveStatus.value.error = null

  try {
    // 准备保存的数据
    const resultMap = {}

    // 获取表头字段信息
    const tableHeadObj = JSON.parse(tableHeadData.value.tableHead)
    if (!tableHeadObj || !tableHeadObj.fields) {
      return
    }
    let rowData = JSON.parse(JSON.stringify(changedRow))
    delete rowData.id
    delete rowData._X_ROW_KEY
    delete rowData.tableHeadId
    delete rowData.action
    delete rowData.checkbox
    delete rowData.isEdit

    // 将null值转换为空字符串
    Object.keys(rowData).forEach((key) => {
      if (rowData[key] === null || rowData[key] === undefined) {
        rowData[key] = ''
      }
    })

    // 调用保存接口
    await updateTableData({
      templateId: queryParams.templateId,
      rowId: changedRow.id,
      tableHeadId: queryParams.tableHeadId,
      resultMap: { ...rowData }
    })

    // 更新保存状态
    saveStatus.value.lastSaved = new Date()

    // 重新获取数据，更新算法后的参数
    queryParams.pageNo = 1
    await getList()
  } catch (error) {
    console.error('保存数据失败', error)
    saveStatus.value.error = '保存失败'
    ElMessage.error('自动保存失败')
  } finally {
    saveStatus.value.saving = false
  }
}
</script>
