<template>
  <el-drawer v-model="dialogVisible" :title="dialogTitle" size="90vw" @close="clearForm">
    <el-form v-loading="formLoading" :model="formData" :inline="true" label-width="80px">
      <div class="form_box">
        <div class="box_left">
          <el-descriptions class="margin-top" :column="2" border>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> sku </div>
              </template>
              {{ formData.sku }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 语言 </div>
              </template>
              {{ getDictLabel(DICT_TYPE.AIM_LANGUAGE, formData.language) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 核心词 </div>
              </template>
              {{ formData.coreKeywords }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 产品名称 </div>
              </template>
              {{ formData.productName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 关键词 </div>
              </template>
              {{ formData.keywords }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 品牌名称 </div>
              </template>
              {{ formData.brandName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 产品参数 </div>
              </template>
              {{ formData.productSpecifications }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 产品受众 </div>
              </template>
              {{ formData.targetAudience }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 使用场景 </div>
              </template>
              {{ formData.usageScenarios }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 规避词 </div>
              </template>
              {{ formData.country ? formData.country + ',' : '' }}{{ formData.avoidWords }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 语言风格 </div>
              </template>
              {{ getDictLabel(DICT_TYPE.AIM_LANGUAGE_STYLE, formData.languageStyle) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item"> 特殊说明 </div>
              </template>
              {{ formData.specialInstructions }}
            </el-descriptions-item>
            <template
              v-for="(item, index) in formData.automationProductInfoCompetitors"
              :key="index"
            >
              <el-descriptions-item>
                <template #label>
                  <div class="cell-item"> 竞品{{ index + 1 }}标题 </div>
                </template>
                {{ item.competitorTitle }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="cell-item"> 竞品{{ index + 1 }}五点 </div>
                </template>
                {{ item.competitorBullets }}
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
        <div class="box_right">
          <!-- 使用动态配置渲染不同区块 -->
          <template v-for="(section, idx) in activeSections" :key="section.type + idx">
            <listTextArea
              ref="textAreaRef"
              v-if="dialogVisible"
              v-loading="contentLoadig"
              element-loading-text="文案生成中，请稍后..."
              :allCopy="allCopyStatus(section)"
              :model-value="getContentValue(section)"
              :textNum="getWordCount(section)"
              :highlight-text="highlightKeywords"
              :title="getTitle(section, idx)"
              :content1="getTranslation(section, idx)"
              :type="section.type"
              @all-copy="handleAllCopy($event, activeSections)"
            />
          </template>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="danger" @click="dialogVisible = false">退 出</el-button>
        <el-button v-if="formData.status == '-1'" type="primary" @click="handleGoToEdit">
          前往生成文案
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import * as devCopywritingApi from '@/api/dev/copywriting'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { useClipboard } from '@vueuse/core'
import listTextArea from './common/listTextArea.vue'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'SystemDictTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const textAreaRef = ref(null) // 右侧文案的ref

const formData = ref({
  id: undefined, //主键id
  sku: '', //产品唯一标识
  titleFlag: '1', //是否生成标题，0否1是
  titleWordCount: '200', //标题字数
  bulletPointsFlag: '1', //是否生成五点，0否1是
  bulletPointsWordCount: '2000', //五点字数
  productDescriptionFlag: '1', //是否生成产品描述，0否1是
  descriptionWordCount: '1500', //产品描述字数
  language: '', //语言
  brandName: '', //品牌名称
  productName: '', //产品名称
  targetAudience: '', //产品受众
  usageScenarios: '', //使用场景
  productSpecifications: '', //产品参数
  coreKeywords: '', //核心词
  keywords: '', //关键词
  avoidWords: '', //规避词
  languageStyle: '', //语言风格
  specialInstructions: '', //特殊说明
  userId: '', //操作人
  userName: '', //操作人姓名
  deptId: '', //部门id
  model: '', //模型选择
  url: '', //商品链接
  asinLength: '', //竞品ASIN
  automationProductInfoCompetitors: [
    {
      asin: '',
      competitorTitle: '',
      competitorBullets: ''
    }
  ] //产品信息竞品列表
})

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      let data = await devCopywritingApi.getAutomationProductCopyGenLog(id)
      formData.value = data
      if (data.status == '3') {
        contentData.value = data
        contentData.value.bulletPoints = JSON.parse(data.bulletPoints)
        contentData.value.bulletPointsTranslation = JSON.parse(data.bulletPointsTranslation)
        contentLoadig.value = false
      } else if (data.status == '1') {
        contentLoadig.value = true
      } else if (data.status == '-1') {
        contentLoadig.value = false
        message.error('文案生成失败,请前往列表重新生成')
      }
      dialogTitle.value =
        '操作时间：' + formatDate(data.operationTime) + ' ' + ' 操作人：' + (data.userName || '--')
    } finally {
      formLoading.value = false
    }
  }
}

const clearForm = () => {
  dialogVisible.value = false
  contentLoadig.value = false
  contentData.value = {
    title: '',
    titleTranslation: '',
    bulletPoints: [''],
    bulletPointsTranslation: [''],
    productDescription: '',
    productDescriptionTranslation: ''
  }
}

defineExpose({ open, clearForm }) // 提供 open 方法，用于打开弹窗

const contentLoadig = ref(false) // 右侧文案的加载中
const contentData = ref({
  title: '',
  titleTranslation: '',
  bulletPoints: [''],
  bulletPointsTranslation: [''],
  productDescription: '',
  productDescriptionTranslation: ''
}) // 右侧文案数据

// 高亮关键词处理
const highlightKeywords = computed(() => {
  return [formData.value.coreKeywords, formData.value.keywords].filter(Boolean)
})

// 配置需要渲染的区块
const sectionConfigs = [
  {
    type: 'title',
    flag: computed(() => formData.value.titleFlag == '1'),
    content: computed(() => contentData.value.title),
    wordCount: computed(() => formData.value.titleWordCount),
    translation: computed(() => contentData.value.titleTranslation),
    title: '标题'
  },
  {
    type: 'bulletPoints',
    flag: computed(() => formData.value.bulletPointsFlag == '1'),
    content: computed(() => contentData.value.bulletPoints),
    wordCount: computed(() => formData.value.bulletPointsWordCount),
    translation: computed(() => contentData.value.bulletPointsTranslation),
    title: '五点'
  },
  {
    type: 'editor',
    flag: computed(() => formData.value.productDescriptionFlag == '1'),
    content: computed(() => contentData.value.productDescription),
    wordCount: computed(() => formData.value.bulletPointsWordCount),
    translation: computed(() => contentData.value.productDescriptionTranslation),
    title: '产品描述'
  }
]

// 计算需要激活的区块
const activeSections = computed(() => {
  return sectionConfigs
    .filter((section) => section.flag.value)
    .flatMap((section) => {
      if (section.type === 'bulletPoints' && Array.isArray(section.content.value)) {
        return section.content.value.map((_, idx) => ({
          ...section,
          index: idx
        }))
      }
      return [section]
    })
})
// 内容获取方法
const getContentValue = (section) => {
  return section.index !== undefined ? section.content.value[section.index] : section.content.value
}

// 字数限制获取
const getWordCount = (section) => section.wordCount.value

// 标题生成
const getTitle = (section, idx) => {
  return section.index !== undefined ? `${section.title} ${section.index + 1}` : section.title
}

// 翻译内容获取
const getTranslation = (section, idx) => {
  if (!section.translation.value) return ''
  return section.index !== undefined
    ? section.translation.value[section.index]
    : section.translation.value
}

// 全部复制状态
const allCopyStatus = (section) => {
  // 这里可以根据需要设置是否允许全部复制
  return true
}

// 处理全部复制
const handleAllCopy = (type) => {
  let copyContent = ''

  if (type === 'all') {
    // 如果type是"all"，全部加到复制内容中复制
    const textAreas = textAreaRef.value

    if (Array.isArray(textAreas)) {
      // 创建一个有序的内容数组
      const orderedContent = []

      // 收集所有内容并标记类型
      textAreas.forEach((area, index) => {
        if (!area || !area.data.contentText.value) return
        let priority = 999

        if (area.data.type === 'title') {
          priority = 0 // 标题最先
        } else if (area.data.type === 'bulletPoints') {
          priority = 100 + (area.data.index || 0) // 五点按照index排序
        } else if (area.data.type === 'editor') {
          priority = 200 // 产品描述最后
        }

        orderedContent.push({
          priority,
          content: area.data.contentText.value,
          type: area.data.type
        })
      })

      // 按优先级排序
      orderedContent.sort((a, b) => a.priority - b.priority)

      // 按排序后的顺序拼接内容
      orderedContent.forEach((item) => {
        if (item.type === 'title') {
          copyContent += item.content + '\n\n\n'
        } else if (item.type === 'bulletPoints') {
          copyContent += item.content + '\n\n\n'
        } else if (item.type === 'editor') {
          copyContent += item.content + '\n\n\n'
        }
      })
    }
  } else if (type === 'bulletPoints') {
    // 只复制五点内容
    const textAreas = textAreaRef.value

    if (Array.isArray(textAreas)) {
      // 筛选出所有五点内容并按索引排序
      const bulletPoints = []

      textAreas.forEach((area, index) => {
        if (!area || !area.contentText) return

        if (area.data.type === 'bulletPoints') {
          bulletPoints.push({
            index: index || 0,
            content: area.data.contentText.value
          })
        }
      })

      // 按索引排序
      bulletPoints.sort((a, b) => a.index - b.index)

      // 拼接内容
      copyContent = bulletPoints.map((item) => item.content).join('\n')
    }
  }

  if (!copyContent) {
    message.warning('没有内容可以复制')
    return
  }

  copy(copyContent)
  message.success('复制成功')
}

// 前往生成文案
const emit = defineEmits(['go-to-edit'])
const handleGoToEdit = () => {
  dialogVisible.value = false // 关闭当前弹窗
  emit('go-to-edit', formData.value.productInfoId) // 触发父组件的事件，传递当前数据ID
}
</script>

<style lang="scss" scoped>
::v-deep(.el-descriptions__content) {
  width: 230px !important;
}

::v-deep(.el-descriptions__label) {
  width: 90px !important;
}

.form_box {
  display: flex;
  width: 100%;
  max-height: 77vh;
  margin-bottom: 20px;
  overflow: hidden;
  box-sizing: border-box;
  justify-content: space-between;

  .cell-item {
    width: 70px !important;
  }

  .box_left {
    display: flex;
    width: 50%;
    overflow: hidden auto;
    // padding-right: 20px;
    box-sizing: border-box;
    justify-content: space-between;
    flex-direction: column;

    .form_item {
      display: flex;
      width: 100%;

      .form_item_line {
        ::v-deep(.el-form-item__content) {
          display: flex;
          flex-wrap: unset !important;
        }

        .text_box {
          display: flex;
          align-items: center;
          gap: 5px;
          margin-right: 10px;
        }
      }

      .el-form-item {
        width: 100%;
      }
    }
  }

  .box_right {
    width: 50%;
    padding-left: 30px;
    overflow: hidden auto;
    border-left: 1px solid #ddd;
    box-sizing: border-box;

    .el-form-item {
      width: calc(100% - 20px);
    }
  }
}
</style>
