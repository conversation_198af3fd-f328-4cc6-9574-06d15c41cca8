<template>
  <!-- 有实时翻译版本 -->
  <div class="list_text_area_box">
    <el-form-item
      label-width="100%"
      prop="specialInstructions"
      style="flex-direction: column !important;width: 100%;"
    >
      <template #label>
        <div style="display: flex;width: 100%;justify-content: space-between;">
          <div style="font-size: 15px; font-weight: 600;color: #505355;">
            {{ title }}：{{ textLength  }}字
            <el-text
              v-if="btnText !== '1'"
              size="small"
              type="info"
            >(温馨提示：编辑内容不做保存，请注意拷贝;)</el-text>
          </div>
          <div>
            <!-- <el-button
              type="text"
              size="mini"
              @click="copyText"
            >
              复制
            </el-button> -->
            <el-button
              type="text"
              size="mini"
              @click="toggleMode"
            >
              {{ btnText === '1' ? '编辑' : '关闭' }}
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleTranslate"
              :loading="translating"
            >
              <!-- v-if="hasContent && !content1" -->
              翻译
            </el-button>
          </div>
        </div>
      </template>

      <!-- 预览模式 -->
      <div
        v-if="btnText === '1'"
        class="input_text_area content-area"
        ref="content"
      >
        <p
          v-if="contentText"
          v-html="contentText"
        ></p>
        <div
          class="empty-tip"
          v-if="!contentText"
        >{{emptyTips[type] || emptyTips.default}}</div>
      </div>

      <!-- 编辑模式 -->
      <template v-else>
        <el-input
          v-if="showTextarea"
          ref="inputRef"
          v-loading="contentLoadig"
          type="textarea"
          :rows="4"
          v-model="contentText"
          :placeholder="emptyTips[type]"
        />
        <Editor
          v-else
          :model-value="contentText"
          height="150px"
          ref="editorInstance"
          style="width: 100%;border-color: #dcdfe6;"
          :toolbarConfig="newToolbar"
          @change="changeContent"
        />
      </template>

      <!-- 翻译内容 -->
      <template v-if="content1">
        <div class="translation-box">
          <el-text
            size="small"
            type="info"
            style="width: 100%;"
          >
            <el-tooltip
              content="注意：内容编辑后，翻译不会更新！"
              placement="right"
            >
              <el-icon>
                <Warning />
              </el-icon>
            </el-tooltip>
            翻译：
            <span
              v-if="type === 'editor'"
              v-html="content1"
              style="line-height: 2;"
            ></span>
            <span v-else>{{ content1 }}</span>
          </el-text>
        </div>
      </template>
    </el-form-item>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElTooltip, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
import axios from 'axios'
import MD5 from './md5.js'

const { copy } = useClipboard()

const props = defineProps({
  type: {
    type: String,
    default: 'text',
    validator: (v) => ['title', 'bulletPoints', 'editor'].includes(v)
  },
  title: String,
  textNum: {
    type: Number,
    default: 200
  },
  modelValue: {
    type: String,
    default: ''
  },
  content1: {
    type: String,
    default: ''
  },
  contentLoading: Boolean,
  highlightText: {
    type: Array,
    default: () => []
  }
})
const hasContent = computed(() => {
  // 严格空值检测：过滤空白字符和HTML空标签
  const cleanContent =
    contentText.value
      ?.replace(/<[^>]+>/g, '') // 去除HTML标签
      ?.trim() || ''
  return cleanContent.length > 0
})

const emit = defineEmits(['update:modelValue'])

const resetContent = () => {
  contentText.value = ''
  btnText.value = '1'
  clearHighlight()
}

// 空状态提示语配置
const emptyTips = {
  title: '为您的产品写一个标题吧！',
  bulletPoints: '产品卖点描述',
  editor: '请输入您的产品描述吧！',
  // 添加默认值防止未定义类型
  default: '请输入内容'
}

// 响应式数据
const contentText = ref(props.modelValue)
const btnText = ref('1') // 1-预览模式 2-编辑模式
const editorInstance = ref(null)
const inputRef = ref(null)
const translating = ref(false) // 翻译加载状态

// 计算属性
const showTextarea = computed(() => {
  return props.type !== 'editor' // 标题和五点使用textarea
})

// 模式切换
const toggleMode = () => {
  btnText.value = btnText.value === '1' ? '2' : '1'
  nextTick(() => {
    if (btnText.value === '2' && showTextarea.value) {
      inputRef.value?.focus()
    }
  })
}

// 复制功能
const copyText = async () => {
  if (!contentText.value) {
    ElMessage.warning('没有内容可以复制')
    return
  }

  try {
    if (props.type === 'editor') {
      // 富文本复制带格式内容
      const editor = await editorInstance.value?.getEditorRef?.()
      const htmlContent = editor?.getHtml() || contentText.value
      await copyFormattedContent(htmlContent)
    } else {
      // 普通文本直接复制
      await copy(contentText.value)
    }
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 富文本复制带格式
const copyFormattedContent = async (html) => {
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html

  const styles = `
    <style>
      * { font-family: Calibri, sans-serif; }
      p { margin: 0; line-height: 1.5; }
      ul { margin-left: 36px; }
    </style>
  `

  const fullHtml = `
    <html>
      <head><meta charset="UTF-8">${styles}</head>
      <body>${tempDiv.innerHTML}</body>
    </html>
  `

  const clipboardItem = new ClipboardItem({
    'text/html': new Blob([fullHtml], { type: 'text/html' }),
    'text/plain': new Blob([tempDiv.textContent], { type: 'text/plain' })
  })

  await navigator.clipboard.write([clipboardItem])
}

// 百度翻译API配置
const baiduTranslateConfig = {
  appid: '', // 请替换为您的百度翻译API appid
  key: '', // 请替换为您的百度翻译API key
  url: '/api/translate'
}

// 处理翻译
const handleTranslate = async () => {
  if (!hasContent.value) {
    ElMessage.warning('没有内容可以翻译')
    return
  }

  if (translating.value) return

  translating.value = true

  try {
    // 获取纯文本内容
    let textToTranslate = contentText.value
    if (props.type === 'editor') {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = contentText.value
      textToTranslate = tempDiv.textContent
    }

    // 清除HTML标签
    textToTranslate = textToTranslate.replace(/<[^>]+>/g, '')

    // 调用百度翻译API
    const salt = new Date().getTime()
    const sign = MD5(baiduTranslateConfig.appid + textToTranslate + salt + baiduTranslateConfig.key)

    // 默认从中文翻译到英文，可以根据需要调整
    const from = 'auto'
    const to = 'zh'
    // textToTranslate

    const response = await axios.get(baiduTranslateConfig.url, {
      params: {
        q: textToTranslate,
        from: from,
        to: to,
        appid: baiduTranslateConfig.appid,
        salt: salt,
        sign: sign
      }
    })
    // 使用代理服务
    // const response = await axios.post('/api/translate', {
    //   q: 'apple',
    //   appid: baiduTranslateConfig.appid,
    //   salt: salt,
    //   from: from,
    //   to: to,
    //   sign: sign
    // })

    if (response.data && response.data.trans_result && response.data.trans_result.length > 0) {
      // 发送翻译结果到父组件
      emit('update:content1', response.data.trans_result[0].dst)
      console.log(response)
      ElMessage.success('翻译成功')
    } else {
      ElMessage.error('翻译失败：' + (response.data.error_msg || '未知错误'))
    }
  } catch (error) {
    console.error('翻译出错:', error)
    ElMessage.error('翻译请求失败，请稍后再试')
  } finally {
    translating.value = false
  }
}

const contentLoadig = ref(false)
const content = ref(null)
const matches = ref([])
const currentIndex = ref(-1)

// 新增计算属性
const textLength = computed(() => {
  if (!contentText.value) return 0
  // 使用正则表达式去除所有HTML标签
  const cleanText = contentText.value.replace(/<[^>]+>/g, '')
  // 处理HTML实体（如&nbsp;）
  const decodedText = new DOMParser().parseFromString(cleanText, 'text/html').body.textContent
  // 返回最终长度
  return decodedText.length
})
// 分割字符串 根据'、'或','或'，'分割关键词
const splitKeywords = (str) => {
  return str
    .split(/[,、，]+/) // 只匹配英文逗号或中文顿号
    .map((item) => item.trim()) // 去除两端空格
    .filter((item) => item) // 过滤空字符串
}

//  转义正则特殊字符（处理连字符等）
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\-]/g, '\\$&') // 特别注意转义连字符 -
}

// 主处理函数
const processKeywords = (groups) => {
  // 展开+清洗+去重
  const uniqueKeywords = [...new Set(groups.flatMap(splitKeywords))]

  // 转义后生成正则
  const escapedKeywords = uniqueKeywords.map(escapeRegExp)
  return new RegExp(`(${escapedKeywords.join('|')})`, 'gi')
}

// 高亮所有匹配项
const highlightMatches = () => {
  if (!contentText.value) return // 确保 有数据
  clearHighlight()
  if (!content.value) return // 确保 DOM 存在

  // 如果contentText.value是html代码，则展示html代码，否则展示文本内容
  if (contentText.value) {
    if (contentText.value.match(/<[^>]+>/g)) {
      content.value.querySelector('p').innerHTML = contentText.value
    } else {
      content.value.querySelector('p').textContent = contentText.value
    }
  }
  try {
    const walker = document.createTreeWalker(content.value, NodeFilter.SHOW_TEXT)
    // 新增：转义正则特殊字符
    const regex = processKeywords(props.highlightText)

    const nodes = []

    while (walker.nextNode()) {
      const node = walker.currentNode
      if (node.textContent.match(regex)) {
        nodes.push(node)
      }
    }
    nodes.forEach((node) => {
      const text = node.textContent
      const matches = text.matchAll(regex)
      let lastIndex = 0
      const fragment = document.createDocumentFragment()
      for (const match of matches) {
        // 添加前段文本
        fragment.appendChild(document.createTextNode(text.slice(lastIndex, match.index)))
        // 创建高亮元素
        const highlight = document.createElement('span')
        highlight.className = 'native-highlight'
        highlight.textContent = match[0]
        fragment.appendChild(highlight)

        lastIndex = match.index + match[0].length
      }

      // 添加剩余文本
      fragment.appendChild(document.createTextNode(text.slice(lastIndex)))

      // 替换原节点
      node.parentNode.replaceChild(fragment, node)
    })

    // 收集所有高亮元素
    matches.value = [...content.value.getElementsByClassName('native-highlight')]
    currentIndex.value = matches.value.length > 0 ? 0 : -1
  } catch (e) {
    console.error('Invalid regular expression:', e)
  }
}

// 清除高亮
const clearHighlight = () => {
  const highlights = content.value?.getElementsByClassName('native-highlight') || []
  while (highlights.length > 0) {
    const parent = highlights[0].parentNode
    parent.replaceChild(document.createTextNode(highlights[0].textContent), highlights[0])
    parent.normalize()
  }
  matches.value = []
  currentIndex.value = -1
}

// 自定义富文本工具栏
const newToolbar = {
  toolbarKeys: [
    'fontSize', // 字体大小
    'bold', // 加粗
    '|', // 分割线（可选）
    'undo', // 后退（撤销）
    'redo', // 前进（重做）
    '|', // 分割线（可选）
    'italic',
    'underline',
    'blockquote'
  ]
}

// 监听内容变化
watch(
  () => contentText.value,
  async (newHtml) => {
    if (props.type === 'editor') {
      if (!editorInstance.value) return
      const editor = await editorInstance.value?.getEditorRef?.()
      if (!editor || editor.isDestroyed) return
      // 只有内容不同时才更新
      if (newHtml !== editor.getHtml()) {
        editor.setHtml(newHtml)
      }
    }
  },
  { immediate: true }
)
// 编辑器内容变化 → 同步到父组件
const changeContent = (editor) => {
  const newHtml = editor.getHtml()
  contentText.value = newHtml
}

// 同时监听内容和关键词变化
watch(
  [() => props.highlightText, () => contentText.value],
  debounce(([val]) => {
    if (btnText.value === '1') {
      val ? highlightMatches() : clearHighlight()
    }
  }, 300)
)
// 监听视图模式变化
watch(
  () => btnText.value,
  (newVal) => {
    if (newVal === '1') {
      nextTick(highlightMatches)
    } else {
      clearHighlight()
    }
  }
)

// 自动搜索防抖
watch(
  props.highlightText,
  debounce((val) => {
    if (val) highlightMatches()
    else clearHighlight()
  }, 300)
)

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return (...args) => {
    clearTimeout(timer)
    timer = setTimeout(() => fn.apply(this, args), delay)
  }
}

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal === '') {
      contentText.value = ''
    } else {
      contentText.value = newVal
    }
  },
  { immediate: true }
)

defineExpose({ resetContent })

// 监听
watch(
  () => contentText.value,
  (val) => {
    emit('update:modelValue', val)
  }
)

onMounted(() => {
  highlightMatches()
})
</script>
<style lang='scss' scoped>
.empty-tip {
  padding: 8px 0;
  font-size: 13px;
  color: #909399;
}

.translation-box {
  display: flex;
  width: 100%;
  padding: 6px;
  margin-top: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  box-sizing: border-box;
  align-items: flex-start;
  gap: 4px;

  :deep(.el-icon) {
    margin-top: 2px;
    color: #e6a23c;
  }
}

.list_text_area_box {
  width: 100%;
  padding-right: 20px;

  .input_text_area {
    display: flex;
    align-items: center;
    width: 100%;
    max-height: 100px;
    min-height: 40px;
    padding: 5px 10px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.5;
    color: #606266;
    background-color: #fff;
    background-image: none;
    // 写一个和输入框一样的样式
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    resize: none;
  }

  .input_text_area:hover {
    border-color: #c0c4cc;
  }

  .input_text_area:focus {
    border-color: #409eff;
    outline: none;
    box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
  }

  ::v-deep(.native-highlight) {
    display: inline-block; /* 修复行内元素问题 */
    color: #000;
    cursor: pointer;
    background-color: #ffeb3b !important; /* 覆盖 ElementPlus 默认样式 */
    border-radius: 3px;
    transition: background 0.3s;

    .current-highlight {
      background-color: #ff5722;
      box-shadow: 0 0 4px rgb(255 87 34 / 50%);
    }
  }

  .search-container {
    position: relative;
  }

  .search-box {
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    padding: 16px;
    background: white;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    gap: 8px;
    align-items: center;
  }

  .nav-buttons {
    display: flex;
    margin-left: auto;
    align-items: center;
    gap: 8px;
  }

  .content-area {
    display: flex;
    height: 100px;
    line-height: 1.6;
    align-items: start;
  }

  /* 保持文本格式 */
  .content-area p {
    white-space: pre-wrap;
  }
}
</style>
