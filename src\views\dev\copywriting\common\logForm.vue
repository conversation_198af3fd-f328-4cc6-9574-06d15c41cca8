<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    center
    class="logDialog"
  >
    <template v-if="productCopyList.length !== 0">
      <el-timeline style="max-height: 400px;overflow: auto;">
        <el-timeline-item
          v-for="(item, index) in productCopyList"
          :key="index"
          :timestamp="formatDate(item.operationTime)"
          @click="checkDetails(item.id)"
          class="hover_list"
        >
          <div class="content">
            <dict-tag
              :type="DICT_TYPE.AIM_PROdUCT_INFO_STATUS"
              :value="item.status"
            /> -- {{ item.userName }}
          </div>
          <el-text
            v-if="item.status == -1"
            size="small"
          >{{'失败原因：'+ (item.failureReason ||'') }}</el-text>
        </el-timeline-item>
      </el-timeline>
    </template>
    <template v-else>
      <div style="text-align: center;">
        <p>暂无记录（不展示待生成状态的数据）</p>
      </div>
    </template>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import * as devCopywritingApi from '@/api/dev/copywriting'
import { formatDate } from '@/utils/formatTime'

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('生成结果') // 弹窗的标题
const getId = ref<number>(0) // 记录当前的id

/** 打开弹窗 */
const open = async (id?: number) => {
  // 修改时，设置数据
  getId.value = id
  openList()
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

const productCopyList = ref([])
// 生成记录列表
const openList = async () => {
  let data = await devCopywritingApi.getAutomationLogList({ productInfoId: getId.value })
  productCopyList.value = data
}

const emit = defineEmits(['checkResult'])
// 查看详情
const checkDetails = async (id: number) => {
  // 传递给父组件
  emit('checkResult', id)
}

defineExpose({ open, close }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
.hover_list {
  cursor: pointer;

  .content {
    margin-bottom: 5px;
  }
}
</style>
