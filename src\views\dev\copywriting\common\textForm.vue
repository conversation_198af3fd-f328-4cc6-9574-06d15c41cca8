<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    size="90vw"
    @close="clearLogTimeOut"
    @open="openLoading"
    class="copywriting_drawer"
    center
  >
    <el-input
      v-model="contentText"
      placeholder=""
      ref="textFormInputRef"
      type="textarea"
      :rows="20"
    />
    <template #footer>
      <div style="text-align: center;">
        <el-button
          :disabled="formLoading"
          type="primary"
          @click="submitForm()"
        >保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'SystemDictTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const contentText = ref('')
const contentIndex = ref(0)

const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async (val, title, index) => {
  dialogVisible.value = true
  dialogTitle.value = title
  contentIndex.value = index
  contentText.value = val

  resetForm()
}
// 打开聚焦
const textFormInputRef = ref('')
const openLoading = () => {
  setTimeout(() => textFormInputRef.value?.focus(), 100)
}
// 备用方案：监听 visible 变化
watch(dialogVisible.value, (val) => {
  if (val) {
    setTimeout(() => textFormInputRef.value?.focus(), 100)
  }
})
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 弹窗关闭
const clearLogTimeOut = () => {
  dialogVisible.value = false
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async (type?: string) => {
  formLoading.value = true
  // 提交表单
  try {
    await formRef.value?.validate()
    // 提交成功
    message.success('操作成功')
    emit('success', contentText.value, contentIndex.value)
    clearLogTimeOut()
  } catch (error) {
  } finally {
    setTimeout(() => {
      formLoading.value = false
    }, 1000)
  }
}
/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
</style>
