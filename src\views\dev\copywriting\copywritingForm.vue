<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    size="90vw"
    @close="clearLogTimeOut"
    class="copywriting_drawer"
  >
    <el-form
      ref="formRef"
      v-loading="genStatus.isGenerating || contentLoadig"
      element-loading-text="文案生成中..."
      :model="formData"
      :rules="formRules"
      :inline="true"
      label-width="80px"
    >
      <div class="form_box">
        <div class="box_left">
          <div class="form_item">
            <el-form-item label="   " prop="sx" label-width="30px" class="form_item_line">
              <div class="text_box">
                <el-checkbox
                  v-model="formData.titleFlag"
                  label="标题"
                  size="large"
                  true-value="1"
                  false-value="0"
                />
                <el-input
                  v-model.number="formData.titleWordCount"
                  placeholder=""
                  style="width: 45px"
                  max="5000"
                  @input="
                    formData.titleWordCount =
                      formData.titleWordCount > 5000 ? 5000 : formData.titleWordCount
                  "
                />
                字
              </div>
              <div class="text_box">
                <el-checkbox
                  v-model="formData.bulletPointsFlag"
                  label="五点"
                  size="large"
                  true-value="1"
                  false-value="0"
                />
                <el-input
                  v-model.number="formData.bulletPointsWordCount"
                  placeholder=""
                  style="width: 55px"
                  max="5000"
                  @input="
                    formData.bulletPointsWordCount =
                      formData.bulletPointsWordCount > 5000 ? 5000 : formData.bulletPointsWordCount
                  "
                />
                字
              </div>
              <div class="text_box">
                <el-checkbox
                  v-model="formData.productDescriptionFlag"
                  label="产品描述"
                  size="large"
                  true-value="1"
                  false-value="0"
                />
                <el-input
                  v-model.number="formData.descriptionWordCount"
                  placeholder=""
                  style="width: 55px"
                  max="5000"
                  @input="
                    formData.descriptionWordCount =
                      formData.descriptionWordCount > 5000 ? 5000 : formData.descriptionWordCount
                  "
                />
                字
              </div>
            </el-form-item>
            <!-- <el-form-item
              label="抓取商品信息"
              prop=""
              label-width="130px"
            >
              <el-input
                v-model="formData.url"
                placeholder="请输入商品链接"
              />
            </el-form-item> -->
          </div>
          <div class="form_item">
            <el-form-item label="sku" prop="sku">
              <el-input
                :disabled="formType == 'update'"
                v-model="formData.sku"
                placeholder="产品唯一编码"
              />
            </el-form-item>
            <el-form-item label="语言" prop="language">
              <el-select
                filterable
                v-model="formData.language"
                placeholder="请选择语言"
                @-change="changeLanguage"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.AIM_LANGUAGE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="form_item">
            <el-form-item label="核心词" prop="coreKeywords">
              <el-input
                v-model="formData.coreKeywords"
                placeholder="必须放在标题首位的核心关键词"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="formData.productName" placeholder="商品的完整名称" />
            </el-form-item>
          </div>
          <div class="form_item">
            <el-form-item label="关键词" prop="keywords">
              <DynamicInput
                v-model="formData.keywords"
                placeholder="需埋入文案的关键词（多个回车换行，建议最少填5个）"
              />
              <!-- <el-input
                v-model="formData.keywords"
                placeholder="需埋入文案的关键词（多个用、分割，建议最少填5个）"
              /> -->
            </el-form-item>
            <el-form-item label="品牌名称" prop="brandName">
              <el-input v-model="formData.brandName" placeholder="产品所属品牌名称" />
            </el-form-item>
          </div>
          <div class="form_item">
            <el-form-item label="产品参数" prop="productSpecifications">
              <DynamicInput
                v-model="formData.productSpecifications"
                placeholder="核心参数详情（尺寸、材质、数量等，多个回车换行）"
              />
              <!-- <el-input
                v-model="formData.productSpecifications"
                placeholder="核心参数详情（尺寸、材质、数量等，多个用、分割）"
              /> -->
            </el-form-item>
            <el-form-item
              label="规避词"
              prop="avoidWords"
              :style="
                formData.countryList.length > 0
                  ? 'height: 75px; display: flex; align-items: baseline;'
                  : ''
              "
            >
              <el-tag
                v-for="tag in formData.countryList"
                :key="tag"
                class="mx-1"
                closable
                @close="handleCloseForbiddenWords(tag)"
              >
                {{ tag }}
              </el-tag>
              <DynamicInput
                :style="formData.countryList.length > 0 ? 'top:30px' : ''"
                v-model="formData.avoidWords"
                placeholder="禁止在文案中出现的词汇（多个回车换行）"
              />
            </el-form-item>
          </div>
          <div class="form_item">
            <el-form-item label="语言风格" prop="languageStyle">
              <el-select
                v-model="formData.languageStyle"
                placeholder="请选择语言"
                style="width: 150px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.AIM_LANGUAGE_STYLE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="特殊说明" prop="specialInstructions">
              <el-input
                v-model="formData.specialInstructions"
                placeholder="请在这里写清楚你要特别注意的要求，比如要把数字放在标签开头，比如：数量要放在标签最前面。这样AI就会按照你想要的输出结果。"
              />
            </el-form-item>
          </div>
          <el-form-item label="ASIN" prop="asin">
            <div class="flex flex-col w-100% gap-5px">
              <div class="flex items-center gap-10px">
                <el-select
                  v-model="formData.asinCountry"
                  placeholder="请选择国家"
                  style="width: 150px"
                  clearable
                >
                  <el-option
                    v-for="item in regionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                  <!-- <el-option
                    label="
                    美国"
                    value="美国"
                  /> -->
                </el-select>
              </div>
              <div class="flex gap-5px items-end w-100%">
                <el-input
                  v-model="formData.asin"
                  placeholder="输入ASIN，系统自动获取竞品标题和五点，回车分割，最多输入五条"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 6 }"
                  style="flex: 1"
                  :disabled="!formData.asinCountry"
                />
                <el-button
                  type="primary"
                  :loading="competitorLoading"
                  :disabled="!formData.asinCountry"
                  @click="getCompetitor"
                  >抓取</el-button
                >
              </div>
            </div>
          </el-form-item>
          <div
            class="form_item item_for_list"
            v-for="(item, index) in formData.automationProductInfoCompetitors"
            :key="index"
          >
            <el-form-item
              label-width="auto"
              :prop="`automationProductInfoCompetitors.${index}.competitorTitle`"
              :rules="competitorTitleRule"
            >
              <template #label> 竞品{{ index + 1 }}标题 </template>
              <el-input :readonly="chatLoading" v-model="item.competitorTitle" placeholder="" />
            </el-form-item>
            <el-form-item
              label-width="auto"
              :prop="`automationProductInfoCompetitors.${index}.competitorBullets`"
              :rules="competitorBulletsRule"
            >
              <template #label> 竞品{{ index + 1 }}五点 </template>
              <div class="jp_text_box flex items-center">
                <el-input
                  :readonly="chatLoading"
                  v-model="item.competitorBullets"
                  placeholder=""
                  @focus="openTextForm(item.competitorBullets, `竞品${index + 1}五点`, index)"
                />
                <el-button
                  type="danger"
                  size="small"
                  @click="adAutoFc('del', index)"
                  v-if="formData.automationProductInfoCompetitors.length > 1"
                  circle
                >
                  <Icon icon="ep:delete" />
                </el-button>
              </div>
            </el-form-item>
          </div>
          <div class="form_item">
            <el-form-item label="使用场景" prop="usageScenarios">
              <DynamicInput
                v-model="formData.usageScenarios"
                placeholder="产品主要使用场合（多个用回车换行）"
              />
              <!-- <el-input
                v-model="formData.usageScenarios"
                placeholder="产品主要使用场合（多个用、分割）"
              /> -->
            </el-form-item>
            <el-form-item label="产品受众" prop="targetAudience">
              <DynamicInput
                v-model="formData.targetAudience"
                placeholder="目标消费群体描述（多个回车换行）"
              />
              <!-- <el-input
                v-model="formData.targetAudience"
                placeholder="目标消费群体描述"
              /> -->
            </el-form-item>
          </div>
          <!-- 新增删除数据，最多五条，最少1条 -->
          <div class="form_item" style="height: 50px">
            <el-button
              type="primary"
              @click="adAutoFc('add')"
              v-if="formData.automationProductInfoCompetitors.length < 5"
            >
              新增竞品
            </el-button>
            <el-button
              :disabled="fxBtnStatus"
              type="primary"
              v-loading="chatLoading"
              @click="clickChatAnalysis()"
              >解析受众场景
              <ElTooltip content="根据竞品数据分析产品受众与使用场景" placement="right">
                <Icon class="ml-5px" icon="ep:warning" />
              </ElTooltip>
            </el-button>
          </div>
        </div>
        <div class="box_right">
          <!-- 使用动态配置渲染不同区块 -->
          <template v-for="(section, idx) in activeSections" :key="section.type + idx">
            <listTextArea
              v-if="dialogVisible"
              ref="textAreaRef"
              v-loading="contentLoadig"
              :allCopy="allCopyStatus(section)"
              :model-value="getContentValue(section)"
              :textNum="getWordCount(section)"
              :highlight-text="highlightKeywords"
              :forbidden-words="contentData.highlights"
              :title="getTitle(section, idx)"
              :content1="getTranslation(section, idx)"
              :type="section.type"
              :index="idx"
              @all-copy="handleAllCopy($event, activeSections)"
            />
          </template>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button :disabled="genStatus.isGenerating || contentLoadig" @click="resetForm"
          >清 空</el-button
        >
        <el-button type="danger" @click="(clearLogTimeOut, (dialogVisible = false))"
          >退 出</el-button
        >
        <el-button
          :disabled="genStatus.isGenerating || contentLoadig"
          @click="handleSubmit('close')"
          >保存并退出</el-button
        >
        <el-button
          type="primary"
          :disabled="formLoading || genStatus.isGenerating || contentLoadig"
          :loading="genStatus.isGenerating || contentLoadig"
          @click="handleSubmit('generate')"
          >{{ genStatus.isGenerating ? '生成中...' : '生成文案' }}</el-button
        >
        <el-button v-if="isNext" :disabled="!contentLoadig" @click="handleSubmit('addNext')"
          >新增下一条</el-button
        >
      </div>
    </template>
  </el-drawer>
  <TextForm ref="textFormRef" @success="saveText" />
</template>
<script lang="ts" setup>
import * as devCopywritingApi from '@/api/dev/copywriting'
import { getCountryByLanguage } from '@/api/operate/forbiddenWords'
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
import ListTextArea from './common/listTextArea.vue'
import TextForm from './common/textForm.vue'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'SystemDictTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 弹窗控制
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const isNext = ref(true) // 是否是新增下一条
// 状态管理
const genStatus = reactive({
  isGenerating: false,
  currentTaskId: null as number | null,
  allowNewCreate: false
})

const formData = ref({
  id: undefined, //主键id
  sku: '', //产品唯一标识
  titleFlag: '1', //是否生成标题，0否1是
  titleWordCount: '200', //标题字数
  bulletPointsFlag: '1', //是否生成五点，0否1是
  bulletPointsWordCount: '2000', //五点字数
  productDescriptionFlag: '1', //是否生成产品描述，0否1是
  descriptionWordCount: '1500', //产品描述字数
  language: '', //语言
  brandName: '', //品牌名称
  productName: '', //产品名称
  targetAudience: '', //产品受众
  usageScenarios: '', //使用场景
  productSpecifications: '', //产品参数
  coreKeywords: '', //核心词
  keywords: '', //关键词
  avoidWords: '', //规避词
  languageStyle: 1, //语言风格
  specialInstructions: '', //特殊说明
  userId: '', //操作人
  userName: '', //操作人姓名
  deptId: '', //部门id
  model: '', //模型选择
  url: '', //商品链接
  asin: '', //ASIN输入
  asinCountry: '', //ASIN国家
  countryList: [], //国家
  country: '', //国家json
  automationProductInfoCompetitors: [
    {
      asin: '',
      competitorTitle: '',
      competitorBullets: ''
    }
  ] //产品信息竞品列表
})

const formRules = reactive({
  sku: [{ required: true, message: '产品唯一编码不能为空', trigger: 'blur' }],
  titleWordCount: [{ required: true, message: '标题字数不能为空', trigger: 'blur' }],
  bulletPointsWordCount: [{ required: true, message: '五点字数不能为空', trigger: 'blur' }],
  descriptionWordCount: [{ required: true, message: '产品描述字数不能为空', trigger: 'blur' }],
  language: [{ required: true, message: '语言不能为空', trigger: 'blur' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  targetAudience: [{ required: false, message: '目标消费群体不能为空', trigger: 'blur' }],
  usageScenarios: [{ required: false, message: '使用场景不能为空', trigger: 'blur' }],
  productSpecifications: [{ required: true, message: '产品参数不能为空', trigger: 'blur' }],
  coreKeywords: [{ required: true, message: '核心词不能为空', trigger: 'blur' }],
  keywords: [{ required: true, message: '关键词不能为空', trigger: 'blur' }],
  url: [{ required: true, message: '商品链接不能为空', trigger: 'change' }],
  asinCountry: [{ required: true, message: '请选择国家', trigger: 'change' }]
})
const competitorTitleRule = { required: true, message: '竞品标题不能为空', trigger: 'blur' }
const competitorBulletsRule = { required: true, message: '竞品五点不能为空', trigger: 'blur' }
const textAreaRef = ref(null) // 右侧文案的ref
const formRef = ref() // 表单 Ref
/** 打开弹窗
 * type:弹窗类型
 * id:修改时，数据的id
 * nextStatus:是否显示新增下一条，默认true
 */
const open = async (type: string, id?: number, nextStatus?: boolean) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  createdData.value = ''
  resetForm()
  isNext.value = true
  // 修改时，设置数据
  if (nextStatus != undefined) {
    isNext.value = nextStatus
  }
  if (id) {
    formLoading.value = true
    try {
      let data = await devCopywritingApi.getProductInfo(id)
      formData.value = data.productInfo
      formData.value.countryList =
        (data.productInfo.country && data.productInfo.country.split(',')) || []
      // 把language、languageStyle转成Number类型
      formData.value.language = Number(formData.value.language)
      formData.value.languageStyle = Number(formData.value.languageStyle)
      // console.log(data.genLog)
      if (data.genLog) {
        // 如果有生成记录或者说生成记录的id不为空，调用生成文案的接口
        // getAutomationProductCopyGenLog(data.genLog.id)
        startPolling(data.genLog.id)
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const chatLoading = ref(false) // 解析受众场景的加载中
const competitorLoading = ref(false) // 抓取竞品的加载中
const regionOptions = ref([]) // 国家选择框选项

// 抓取竞品信息
const getCompetitor = async () => {
  if (!formData.value.asin || !formData.value.asin.trim()) {
    message.error('请输入ASIN')
    return
  }

  // 解析ASIN，按回车分割
  const asinList = formData.value.asin
    .split('\n')
    .filter((item) => item.trim())
    .map((item) => item.trim())

  if (asinList.length === 0) {
    message.error('请输入有效的ASIN')
    return
  }

  if (asinList.length > 5) {
    message.error('最多只能输入5个ASIN')
    return
  }

  competitorLoading.value = true
  try {
    const res = await devCopywritingApi.getCompetitorByAsins({
      asins: asinList,
      asinCountry: formData.value.asinCountry
    })

    if (res && res.length > 0) {
      // 清空现有竞品列表
      formData.value.automationProductInfoCompetitors = []

      // 根据返回的数据填充竞品列表
      res.forEach((item) => {
        formData.value.automationProductInfoCompetitors.push({
          asin: item.asin || '',
          competitorTitle: item.competitorTitle || '',
          competitorBullets: item.competitorBullets || ''
        })
      })

      // 如果没有数据，至少保留一个空的竞品
      if (formData.value.automationProductInfoCompetitors.length === 0) {
        formData.value.automationProductInfoCompetitors.push({
          asin: '',
          competitorTitle: '',
          competitorBullets: ''
        })
      }

      message.success(`成功抓取${res.length}条竞品信息`)
    } else {
      message.warning('未获取到竞品信息')
    }
  } catch (error) {
    console.error('抓取竞品信息失败:', error)
    message.error('抓取竞品信息失败，请稍后重试')
  } finally {
    competitorLoading.value = false
  }
}

// 解析受众场景
const clickChatAnalysis = async () => {
  chatLoading.value = true
  try {
    // 判断竞品list是否为空，不为空掉接口chatAnalysis，传入竞品list
    if (formData.value.automationProductInfoCompetitors.length > 0) {
      // 竞品列表
      const competitors = formData.value.automationProductInfoCompetitors.map((item) => ({
        competitorTitle: item.competitorTitle,
        competitorBullets: item.competitorBullets
      }))
      const res = await devCopywritingApi.chatAnalysis(competitors)
      chatLoading.value = false
      // if (res) {
      formData.value.targetAudience = res.targetAudienceTranslation
      formData.value.usageScenarios = res.usageScenariosTranslation
      formRef.value.validate()
      // }
    } else {
      message.error('请至少添加一条竞品')
    }
  } finally {
    chatLoading.value = false
  }
}

const createdData = ref() // 新增时返回的id
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
// 方法定义
import { debounce } from 'lodash-es' // 添加这行

// 保存方法
const handleSubmitImpl = async (type?: 'save' | 'generate' | 'close' | 'addNext') => {
  // 判断三项是否有一项勾选
  if (
    formData.value.titleFlag != '1' &&
    formData.value.bulletPointsFlag != '1' &&
    formData.value.productDescriptionFlag != '1'
  ) {
    message.error('请至少标题、五点、产品描述勾选一项')
    return
  }
  await message.confirm(
    '保存后状态将变更为"待生成"。如需继续添加，请先点击[生成文案]再[新增下一条]。',
    '是否保存当前数据？'
  )
  try {
    // 新增下一条特殊处理
    if (type === 'addNext') {
      if (genStatus.isGenerating) {
        await ElMessageBox.confirm(
          '当前有正在生成的文案，是否放于后台生成并新增下一条新数据？',
          '提示'
        )
        stopPolling()
      }
      handleCreateNew()
      return
    }
    if (!(await validateForm())) return
    // 统一保存逻辑
    const savedId = await handleSave()

    // 处理生成逻辑
    if (type === 'generate') {
      // 判断是否有轮询，有则清除
      if (pollingTimer) {
        stopPolling()
      }
      await handleGenerate(savedId)
    }

    // 关闭抽屉
    if (type === 'close') {
      dialogVisible.value = false
    }

    emit('success')
  } catch (error) {
    console.error(error)
  } finally {
    formLoading.value = false
  }
}

// 创建防抖版本的 handleSubmit
const handleSubmit = debounce(handleSubmitImpl, 1000, {
  leading: true, // 第一次调用立即执行
  trailing: false // 不执行最后一次调用
})

// 优化后的保存方法
const handleSave = async (): Promise<number> => {
  try {
    // 明确区分创建/更新操作
    const isCreateOperation = formType.value === 'create' && !formData.value.id
    const api = isCreateOperation
      ? devCopywritingApi.createProduct
      : devCopywritingApi.updateProduct

    // 执行保存操作
    const dataId = await api(formData.value)
    // 创建成功后更新状态
    if (isCreateOperation) {
      if (!dataId) {
        throw new Error('创建操作未返回有效ID')
      }
      formData.value.id = dataId
      formType.value = 'update' // 强制切换为修改模式
    }

    // 返回最终使用的ID（新增用返回ID，更新用现有ID）
    const finalId = isCreateOperation ? dataId : formData.value.id

    if (!finalId) {
      throw new Error('未能获取有效ID')
    }

    ElMessage.success(isCreateOperation ? '创建成功' : '更新成功')
    return finalId
  } catch (error) {
    ElMessage.error('保存失败，请检查数据')
    throw error
  }
}

// 优化后的生成逻辑
const handleGenerate = async (id: number) => {
  try {
    // 状态锁定
    genStatus.isGenerating = true
    contentLoadig.value = true
    genStatus.allowNewCreate = true

    // 根据当前状态选择接口
    const generateApi = devCopywritingApi.generate

    // 执行生成接口
    const generateId = await generateApi(id)

    // 启动轮询
    startPolling(generateId)
    ElMessage.success('生成任务已启动')
  } catch (error) {
    genStatus.isGenerating = false
    contentLoadig.value = false
    genStatus.allowNewCreate = false
    throw error
  }
}

const validateForm = async () => {
  try {
    await formRef.value?.validate()
    return true
  } catch {
    ElMessage.warning('请完善表单必填项')
    return false
  }
}

// 轮询控制相关状态
let pollingTimer: ReturnType<typeof setTimeout> | null = null
let abortController: AbortController | null = null
// 轮询
const startPolling = (generateId: number) => {
  abortController = new AbortController()
  contentLoadig.value = true
  const poll = async () => {
    try {
      const res = await devCopywritingApi.getAutomationProductCopyGenLog(generateId, {
        signal: abortController?.signal
      })

      if (res.status === '3') handleGenerateSuccess(res)
      else if (res.status === '-1') handleGenerateFail()
      else pollingTimer = setTimeout(poll, 5000)
    } catch (error) {
      if (error.name !== 'AbortError') handleGenerateFail()
    }
  }

  poll()
}

const handleGenerateSuccess = (data: any) => {
  genStatus.isGenerating = false
  contentLoadig.value = false
  genStatus.allowNewCreate = false
  // 更新内容数据
  contentData.value = {
    ...data,
    bulletPoints: JSON.parse(data.bulletPoints),
    bulletPointsTranslation: JSON.parse(data.bulletPointsTranslation),
    highlights: data.highlights || [] // 处理后端返回的高亮提示词数组
  }
}

const handleGenerateFail = () => {
  genStatus.isGenerating = false
  contentLoadig.value = false
  genStatus.allowNewCreate = false
  ElMessage.error('文案生成失败，请重试')
}

const stopPolling = () => {
  // 1. 清除定时器
  if (pollingTimer) {
    clearTimeout(pollingTimer)
    pollingTimer = null
  }

  abortController?.abort()
  abortController = null
  genStatus.isGenerating = false
  contentLoadig.value = false
  genStatus.allowNewCreate = false
}

const handleCreateNew = () => {
  stopPolling()
  resetForm()
  formType.value = 'create'
  dialogTitle.value = '新增'
  clearLogTimeOut()
  ElMessage.success('已重置表单')
}

// 新增删除竞品，最多5条，最少1条
const adAutoFc = (type: any, index?: number) => {
  if (type === 'add') {
    if (formData.value.automationProductInfoCompetitors.length >= 5) {
      message.error('最多只能添加5条竞品')
      return
    }
    formData.value.automationProductInfoCompetitors.push({
      asin: '',
      competitorTitle: '',
      competitorBullets: ''
    })
  } else {
    if (formData.value.automationProductInfoCompetitors.length <= 1) {
      message.error('至少保留一条竞品')
      return
    }

    // 获取要删除的竞品的ASIN
    const deletedAsin = formData.value.automationProductInfoCompetitors[index]?.asin

    // 删除竞品
    formData.value.automationProductInfoCompetitors.splice(index, 1)

    // 同步更新ASIN字段，移除对应的ASIN
    if (deletedAsin && formData.value.asin) {
      const asinList = formData.value.asin.split('\n').filter((item) => item.trim())
      const updatedAsinList = asinList.filter((asin) => asin.trim() !== deletedAsin.trim())
      formData.value.asin = updatedAsinList.join('\n')
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined, //主键id
    sku: '', //产品唯一标识
    titleFlag: '1', //是否生成标题，0否1是
    titleWordCount: '200', //标题字数
    bulletPointsFlag: '1', //是否生成五点，0否1是
    bulletPointsWordCount: '2000', //五点字数
    productDescriptionFlag: '1', //是否生成产品描述，0否1是
    descriptionWordCount: '1500', //产品描述字数
    language: '', //语言
    brandName: '', //品牌名称
    productName: '', //产品名称
    targetAudience: '', //产品受众
    usageScenarios: '', //使用场景
    productSpecifications: '', //产品参数
    coreKeywords: '', //核心词
    keywords: '', //关键词
    avoidWords: '', //规避词
    languageStyle: 1, //语言风格
    specialInstructions: '', //特殊说明
    userId: '', //操作人
    userName: '', //操作人姓名
    deptId: '', //部门id
    model: '', //模型选择
    url: '', //商品链接
    asin: '', //ASIN输入
    asinCountry: '', //asin国家
    countryList: [], //国家
    country: '', //国家json
    automationProductInfoCompetitors: [
      {
        asin: '',
        competitorTitle: '',
        competitorBullets: ''
      }
    ] //产品信息竞品列表
  }
  contentData.value = {
    title: '',
    titleTranslation: '',
    bulletPoints: [''],
    bulletPointsTranslation: [''],
    productDescription: '',
    productDescriptionTranslation: ''
  }
  formRef.value?.resetFields()
}

const contentLoadig = ref(false) // 右侧文案的加载中
const contentData = ref({
  title: '',
  titleTranslation: '',
  bulletPoints: [''],
  bulletPointsTranslation: [''],
  productDescription: '',
  productDescriptionTranslation: '',
  highlights: [] // 后端返回的高亮提示词
}) // 右侧文案数据

const clearLogTimeOut = () => {
  contentLoadig.value = false
  contentData.value = {
    title: '',
    titleTranslation: '',
    bulletPoints: [''],
    bulletPointsTranslation: [''],
    productDescription: '',
    productDescriptionTranslation: '',
    highlights: [] // 重置高亮提示词
  }
  // clearTimeout(pollingInterval.value)
  stopPolling() // 清除轮询
}

const textFormRef = ref() // 弹窗的ref

// 点击5点弹出大输入框
const openTextForm = (val, title, index) => {
  textFormRef.value.open(val, title, index)
}

// 配置需要渲染的区块
const sectionConfigs = [
  {
    type: 'title',
    flag: computed(() => formData.value.titleFlag == '1'),
    content: computed(() => contentData.value.title),
    wordCount: computed(() => formData.value.titleWordCount),
    translation: computed(() => contentData.value.titleTranslation),
    title: '标题'
  },
  {
    type: 'bulletPoints',
    flag: computed(() => formData.value.bulletPointsFlag == '1'),
    content: computed(() => contentData.value.bulletPoints),
    wordCount: computed(() => formData.value.bulletPointsWordCount),
    translation: computed(() => contentData.value.bulletPointsTranslation),
    title: '五点'
  },
  {
    type: 'editor',
    flag: computed(() => formData.value.productDescriptionFlag == '1'),
    content: computed(() => contentData.value.productDescription),
    wordCount: computed(() => formData.value.bulletPointsWordCount),
    translation: computed(() => contentData.value.productDescriptionTranslation),
    title: '产品描述'
  }
]

// 计算需要激活的区块
const activeSections = computed(() => {
  return sectionConfigs
    .filter((section) => section.flag.value)
    .flatMap((section) => {
      if (section.type === 'bulletPoints' && Array.isArray(section.content.value)) {
        return section.content.value.map((_, idx) => ({
          ...section,
          index: idx
        }))
      }
      return [section]
    })
})

// 关键词统一处理
const highlightKeywords = computed(() => {
  const keywords = [formData.value.coreKeywords, formData.value.keywords]
  return keywords
})

// 判断是否是第一个五点，是的话返回true，不是返回false
const allCopyStatus = (section) => {
  return section.type == 'title' || (section.type == 'bulletPoints' && section.index == 0)
}

// 批量复制
// 处理全部复制
const handleAllCopy = (type) => {
  let copyContent = ''

  if (type === 'all') {
    // 如果type是"all"，全部加到复制内容中复制
    const textAreas = textAreaRef.value

    if (Array.isArray(textAreas)) {
      // 创建一个有序的内容数组
      const orderedContent = []

      // 收集所有内容并标记类型
      textAreas.forEach((area, index) => {
        if (!area || !area.data.contentText.value) return
        let priority = 999

        if (area.data.type === 'title') {
          priority = 0 // 标题最先
        } else if (area.data.type === 'bulletPoints') {
          priority = 100 + (area.data.index || 0) // 五点按照index排序
        } else if (area.data.type === 'editor') {
          priority = 200 // 产品描述最后
        }

        orderedContent.push({
          priority,
          content: area.data.contentText.value,
          type: area.data.type
        })
      })

      // 按优先级排序
      orderedContent.sort((a, b) => a.priority - b.priority)

      // 按排序后的顺序拼接内容
      orderedContent.forEach((item) => {
        if (item.type === 'title') {
          copyContent += item.content + '\n\n\n'
        } else if (item.type === 'bulletPoints') {
          copyContent += item.content + '\n\n\n'
        } else if (item.type === 'editor') {
          copyContent += item.content + '\n\n\n'
        }
      })
    }
  } else if (type === 'bulletPoints') {
    // 只复制五点内容
    const textAreas = textAreaRef.value

    if (Array.isArray(textAreas)) {
      // 筛选出所有五点内容并按索引排序
      const bulletPoints = []

      textAreas.forEach((area, index) => {
        if (!area || !area.contentText) return

        if (area.data.type === 'bulletPoints') {
          bulletPoints.push({
            index: index || 0,
            content: area.data.contentText.value
          })
        }
      })

      // 按索引排序
      bulletPoints.sort((a, b) => a.index - b.index)

      // 拼接内容
      copyContent = bulletPoints.map((item) => item.content).join('\n')
    }
  }

  if (!copyContent) {
    message.warning('没有内容可以复制')
    return
  }

  copy(copyContent)
  message.success('复制成功')
}
// 内容获取方法
const getContentValue = (section) => {
  return section.index !== undefined ? section.content.value[section.index] : section.content.value
}

// 字数限制获取
const getWordCount = (section) => section.wordCount.value

// 标题生成
const getTitle = (section, idx) => {
  return section.index !== undefined ? `${section.title} ${section.index + 1}` : section.title
}

// 翻译内容获取
const getTranslation = (section, idx) => {
  if (!section.translation.value) return ''
  return section.index !== undefined
    ? section.translation.value[section.index]
    : section.translation.value
}

// 保存弹窗的内容
const saveText = (val, index) => {
  if (index === undefined) {
    formData.value.automationProductInfoCompetitors[0].competitorBullets = val
  } else {
    formData.value.automationProductInfoCompetitors[index].competitorBullets = val
  }
  formRef.value.validate()
}
// 语言变化，调用规避词接口，查询规避词库
const changeLanguage = async (val) => {
  const res = await getCountryByLanguage(getDictLabel(DICT_TYPE.AIM_LANGUAGE, val))
  formData.value.countryList = res || []
  formData.value.country = res.join(',')
}
// 删除规避词
const handleCloseForbiddenWords = (tag) => {
  formData.value.countryList.splice(formData.value.countryList.indexOf(tag), 1)
  formData.value.country = formData.value.countryList.join(',')
}

// 监听五点和产品描述选中两个checked，其中一个选中另一个选中，否则都不选择
watch(
  () => formData.value.bulletPointsFlag,
  (val) => {
    if (val == '1') {
      formData.value.productDescriptionFlag = '1'
    } else {
      formData.value.productDescriptionFlag = '0'
    }
  }
)
watch(
  () => formData.value.productDescriptionFlag,
  (val) => {
    if (val == '1') {
      formData.value.bulletPointsFlag = '1'
    } else {
      formData.value.bulletPointsFlag = '0'
    }
  }
)

// 控制解析受众场景按钮状态
const fxBtnStatus = computed(() => {
  // 如果formData.automationProductInfoCompetitors里面的内容为空，按钮不可点击
  return (
    formData.value.automationProductInfoCompetitors.some(
      (item) => item.competitorTitle === '' || item.competitorBullets === ''
    ) || formData.value.automationProductInfoCompetitors.length === 0
  )
})

// 获取国家列表
const getCountryList = async () => {
  try {
    const res = await ReplenishmentProposalApi.getCountryList()
    regionOptions.value = res
      .filter((item) => item.country !== '日本')
      .map((item) => ({
        label: item.country,
        value: item.country
      }))
  } catch (error) {
    console.error('获取国家列表失败:', error)
  }
}

// 组件挂载时获取国家列表
onMounted(() => {
  getCountryList()
})

onUnmounted(() => {
  stopPolling() // 清除轮询
  clearLogTimeOut()
})
</script>

<style lang="scss" scoped>
.copywriting_drawer {
  ::v-deep(.el-drawer__body) {
    overflow: hidden !important;
  }

  .el-drawer__body {
    overflow: hidden !important;
  }

  .form_box {
    display: flex;
    width: 100%;
    max-height: 77vh;
    margin-bottom: 20px;
    overflow: hidden;
    box-sizing: border-box;
    justify-content: space-between;

    .box_left {
      display: flex;
      width: 60%;
      height: 100%;
      // overflow: hidden auto;
      padding-bottom: 40px;
      box-sizing: border-box;
      justify-content: space-between;
      flex-direction: column;

      .form_item {
        display: flex;
        width: 100%;

        .form_item_line {
          ::v-deep(.el-form-item__content) {
            display: flex;
            flex-wrap: unset !important;
          }

          .text_box {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-right: 10px;
          }
        }

        .jp_text_box {
          display: flex;
          width: 100%;
          gap: 10px;
        }

        .el-form-item {
          width: 100%;
        }
      }

      // .item_for_list {
      //   ::v-deep(.el-form-item__content) {
      //     align-content: start !important;
      //   }
      // }
    }

    .box_right {
      width: 40%;
      padding-left: 30px;
      overflow: hidden auto;
      border-left: 1px solid #ddd;
      box-sizing: border-box;

      .el-form-item {
        width: calc(100% - 20px);
      }
    }
  }
}
</style>
