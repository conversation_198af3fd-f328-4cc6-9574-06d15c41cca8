<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-100px"
          clearable
          placeholder="请选状态"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.AIM_PROdUCT_INFO_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-340px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          :shortcuts="defaultShortcuts"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="语言" prop="language">
        <el-select
          v-model="queryParams.language"
          class="!w-100px"
          clearable
          placeholder="请选语言"
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.AIM_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="sku" prop="name">
        <el-input
          v-model="queryParams.sku"
          class="!w-240px"
          clearable
          placeholder="请输入sku"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="name">
        <el-input
          v-model="queryParams.productName"
          class="!w-240px"
          clearable
          placeholder="请输入产品名称"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人" prop="name">
        <el-input
          v-model="queryParams.userName"
          class="!w-240px"
          clearable
          placeholder="请输入操作人"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" v-hasPermi="['dev:automation-product-info:query']">
          <Icon class="mr-5px" icon="ep:search" />
          查询
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <div class="btn_box">
    <el-button
      v-hasPermi="['dev:automation-product-info:create']"
      plain
      type="primary"
      @click="openForm('create')"
    >
      <Icon class="mr-5px" icon="ep:plus" />
      新增
    </el-button>
    <el-button
      v-hasPermi="['dev:automation-product-info:create']"
      plain
      type="primary"
      @click="handleGenerates"
    >
      生成文案
    </el-button>
    <el-button
      type="warning"
      plain
      @click="handleImport"
      v-hasPermi="['dev:automation-product-info:create']"
    >
      <Icon icon="ep:upload" /> 导入
    </el-button>
    <el-button
      v-hasPermi="['dev:automation-product-info:delete']"
      plain
      type="danger"
      @click="handleDeletes"
    >
      <Icon class="mr-5px" icon="ep:delete" />
      批量删除
    </el-button>
  </div>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="copywritingTableRef"
      v-loading="loading"
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column align="left" label="sku" prop="sku" width="300">
        <!-- 加个复制功能 -->
        <template #default="{ row }">
          <div v-clipboard="() => row.sku">{{ row.sku || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column align="left" label="语言" prop="language" width="100" show-overflow-tooltip>
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AIM_LANGUAGE" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column align="left" label="产品名称" prop="productName">
        <template #default="{ row }">
          <span v-if="!row.latestLogId">{{ row.productName || '--' }}</span>
          <el-tooltip v-else placement="top" :content="row.productName || '--'">
            <el-text
              tag="ins"
              type="primary"
              style="cursor: pointer"
              @click="openCheckResult(row.latestLogId)"
              >{{ row.productName || '--' }}</el-text
            >
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="left" label="核心词" prop="coreKeywords" />
      <el-table-column align="left" label="状态" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AIM_PROdUCT_INFO_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作人" prop="userName" width="160">
        <template #default="{ row }">
          {{ row.userName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="创建时间"
        prop="createTime"
        width="180"
      />
      <el-table-column align="center" label="操作" width="200">
        <template #default="scope">
          <div class="flex justify-end">
            <!-- 如果是待生成状态就不展示 -->
            <!-- v-if="scope.row.status != 0" -->
            <el-button
              link
              type="primary"
              v-hasPermi="['dev:automation-product-info:query']"
              @click="openLogForm(scope.row.id)"
            >
              查看结果
            </el-button>
            <el-button
              v-hasPermi="['dev:automation-product-info:update']"
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPermi="['dev:automation-product-info:delete']"
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CopywritingForm ref="formRef" @success="getList" />
  <!-- 表单弹窗：查看记录 -->
  <LogForm ref="logFormRef" @check-result="openCheckResult($event)" />

  <!-- 结果详情弹窗 -->
  <CheckResult ref="checkResultRef" @go-to-edit="openForm('update', $event)" />
  <!-- 用户导入对话框 -->
  <CopywritingImportForm ref="importFormRef" @success="getList" />
  <el-backtop :right="100" :bottom="100" />
</template>

<script lang="ts" setup>
import * as devCopywritingApi from '@/api/dev/copywriting'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, defaultShortcuts } from '@/utils/formatTime'
import { useClipboard } from '@vueuse/core'
import CheckResult from './checkResult.vue'
import CopywritingImportForm from './common/copywritingImportForm.vue'
import LogForm from './common/logForm.vue'
import CopywritingForm from './copywritingForm.vue'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 字典表格数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  sku: '',
  status: undefined,
  language: undefined,
  productName: '',
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const multipleSelection = ref<{ id: number }[]>([]) // 选中的列表项

const copyText = async (text: string) => {
  const { copy, copied, isSupported } = useClipboard({ source: text })
  if (!isSupported) {
    message.error(t('common.copyError'))
    return
  }
  await copy()
  if (unref(copied)) {
    message.success(t('common.copySuccess'))
  }
}

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    const data = await devCopywritingApi.getProductList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
  logFormRef.value.close()
}

/** 查看结果 */
const logFormRef = ref()
const openLogForm = (id?: number) => {
  logFormRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await devCopywritingApi.deleteProduct(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 批量删除按钮操作 */
const handleDeletes = async () => {
  try {
    if (multipleSelection.value.length === 0) {
      message.warning('请至少选择一项')
      return
    }
    // 批量删除的二次确认
    await message.delConfirm()
    // 发起批量删除
    const ids: number[] = multipleSelection.value.map((item) => item.id)
    await devCopywritingApi.deleteBatch(ids)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 批量生成文案按钮操作 */
const handleGenerates = async () => {
  try {
    if (multipleSelection.value.length === 0) {
      message.warning('请至少选择一项')
      return
    }
    // 批量生成文案的二次确认
    await message.generateConfirm()
    // 发起批量生成文案
    const ids: number[] = multipleSelection.value.map((item) => item.id)
    await devCopywritingApi.generateBatch(ids)
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {}
}

const checkResultRef = ref()
// 查看结果
const openCheckResult = (id) => {
  checkResultRef.value.open(id)
}

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.btn_box {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;

  .el-button {
    margin-left: 0;
  }
}

::v-deep(.el-drawer__body) {
  overflow: hidden !important;
}

:deep(.el-drawer__body) {
  overflow: hidden !important;
}
</style>
