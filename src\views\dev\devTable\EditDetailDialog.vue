<template>
  <el-dialog v-model="visible" title="编辑详情" width="50%" :before-close="handleClose">
    <div style="max-height: 800px; overflow: hidden auto">
      <el-form
        ref="formRef"
        :model="form"
        label-width="120px"
        :rules="rules"
        @change="handleChange"
      >
        <div v-for="(group, groupName) in groupedFields" :key="groupName" class="mb-20px">
          <div class="text-16px font-bold mb-10px text-gray-700">{{ groupName }}</div>
          <el-row :gutter="20">
            <el-col
              v-for="field in group"
              :key="field.fieldId"
              :span="getFieldSpan(field.paramTypeCode)"
            >
              <el-form-item
                :prop="field.fieldId"
                :required="field.required && (!field.hasOwnProperty('edit') || field.edit)"
              >
                <template #label>
                  <span>{{ field.fieldName }}</span>
                </template>
                <!-- 根据type字段显示对应组件，同时判断edit字段是否可编辑 -->

                <!-- 文本输入框 -->
                <el-input
                  v-if="
                    field.paramTypeCode === 'input' && (!field.hasOwnProperty('edit') || field.edit)
                  "
                  v-model="form[field.fieldId]"
                  :placeholder="`请输入${field.fieldName}`"
                  clearable
                />
                <!-- 只读文本显示 -->
                <span
                  v-else-if="
                    field.paramTypeCode === 'input' && field.hasOwnProperty('edit') && !field.edit
                  "
                  class="readonly-text"
                >
                  {{ form[field.fieldId] || '-' }}
                </span>

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="
                    field.paramTypeCode === 'inputNumber' &&
                    (!field.hasOwnProperty('edit') || field.edit)
                  "
                  v-model="form[field.fieldId]"
                  :placeholder="`请输入${field.fieldName}`"
                  class="w-full"
                />
                <!-- 只读数字显示 -->
                <span
                  v-else-if="
                    field.paramTypeCode === 'inputNumber' &&
                    field.hasOwnProperty('edit') &&
                    !field.edit
                  "
                  class="readonly-text"
                >
                  {{ form[field.fieldId] || '-' }}
                </span>

                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="
                    field.paramTypeCode === 'time' && (!field.hasOwnProperty('edit') || field.edit)
                  "
                  v-model="form[field.fieldId]"
                  type="datetime"
                  :placeholder="`请选择${field.fieldName}`"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="w-full"
                />
                <!-- 只读日期时间显示 -->
                <span
                  v-else-if="
                    field.paramTypeCode === 'time' && field.hasOwnProperty('edit') && !field.edit
                  "
                  class="readonly-text"
                >
                  {{ form[field.fieldId] || '-' }}
                </span>

                <!-- 图片上传 -->
                <div
                  v-else-if="
                    field.paramTypeCode === 'file' && (!field.hasOwnProperty('edit') || field.edit)
                  "
                  class="w-full"
                >
                  <VxeUpload
                    mode="image"
                    :singleMode="true"
                    :urlMode="true"
                    :showButtonText="false"
                    :pasteToUpload="true"
                    :dragToUpload="true"
                    :showPreview="true"
                    :autoHiddenButton="true"
                    :imageConfig="{
                      circle: false,
                      width: 100,
                      height: 100
                    }"
                    v-model="form[field.fieldId]"
                    :uploadMethod="uploadMethod"
                  />
                  <div class="upload-tip">
                    <el-icon class="tip-icon">
                      <InfoFilled />
                    </el-icon>
                    <span class="tip-text">点击加号旁边空白处，即可Ctrl+V粘贴图片</span>
                  </div>
                </div>
                <!-- 只读图片显示 -->
                <div
                  v-else-if="
                    field.paramTypeCode === 'file' && field.hasOwnProperty('edit') && !field.edit
                  "
                  class="readonly-image"
                >
                  <el-image
                    v-if="form[field.fieldId]"
                    :src="form[field.fieldId]"
                    fit="cover"
                    class="w-100px h-100px"
                    :preview-src-list="[form[field.fieldId]]"
                  />
                  <span v-else class="readonly-text">-</span>
                </div>

                <!-- 下拉选择器 -->
                <el-select
                  v-else-if="
                    field.type === 'select' && (!field.hasOwnProperty('edit') || field.edit)
                  "
                  v-model="form[field.fieldId]"
                  :placeholder="`请选择${field.fieldName}`"
                  clearable
                  class="w-full"
                >
                  <el-option
                    v-for="option in field.options || []"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <!-- 只读选择器显示 -->
                <span
                  v-else-if="field.type === 'select' && field.hasOwnProperty('edit') && !field.edit"
                  class="readonly-text"
                >
                  {{ getSelectLabel(field, form[field.fieldId]) || '-' }}
                </span>

                <!-- 开关 -->
                <el-switch
                  v-else-if="
                    field.type === 'radio' && (!field.hasOwnProperty('edit') || field.edit)
                  "
                  v-model="form[field.fieldId]"
                />
                <!-- 只读开关显示 -->
                <span
                  v-else-if="field.type === 'radio' && field.hasOwnProperty('edit') && !field.edit"
                  class="readonly-text"
                >
                  {{ form[field.fieldId] ? '是' : '否' }}
                </span>

                <!-- 默认文本输入框 -->
                <el-input
                  v-else-if="!field.hasOwnProperty('edit') || field.edit"
                  v-model="form[field.fieldId]"
                  :placeholder="`请输入${field.fieldName}`"
                  clearable
                />
                <!-- 默认只读显示 -->
                <span v-else class="readonly-text">
                  {{ form[field.fieldId] || '-' }}
                </span>

                <span v-if="field.unit" class="field-unit"
                  >({{ getDictLabel(DICT_TYPE.INFRA_TREE_UNIT, field.unit) }})</span
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <!-- <el-button @click="handleCalculate" :loading="saving"> 计算 </el-button> -->
        <el-button type="primary" @click="handleSave" :loading="saving"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as FileApi from '@/api/infra/file'
import { getAccessToken } from '@/utils/auth'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { InfoFilled } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  tableHeadData: {
    type: Object,
    default: () => ({})
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'calculate'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const form = ref({})
const formRef = ref(null)
const saving = ref(false)
const rules = ref({})

// 文件上传相关
const uploadAction = ref(import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload')
const uploadHeaders = ref({
  Authorization: 'Bearer ' + getAccessToken()
})

// 根据字段类型分组
const groupedFields = computed(() => {
  if (!props.tableHeadData || !props.tableHeadData.tableHead) {
    return {}
  }

  const tableHeadObj = JSON.parse(props.tableHeadData.tableHead)
  if (!tableHeadObj || !tableHeadObj.fields) {
    return {}
  }

  const groups = {
    基本信息: [],
    采购管理: [],
    物流与成本: [],
    其他: []
  }

  tableHeadObj.fields.forEach((field) => {
    const fieldName = field.fieldName || ''
    if (fieldName.includes('核心词') || fieldName.includes('基本') || fieldName.includes('名称')) {
      groups['基本信息'].push(field)
    } else if (
      fieldName.includes('采购') ||
      (fieldName.includes('成本') && !fieldName.includes('物流'))
    ) {
      groups['采购管理'].push(field)
    } else if (
      fieldName.includes('物流') ||
      fieldName.includes('重量') ||
      fieldName.includes('尺寸') ||
      fieldName.includes('运费')
    ) {
      groups['物流与成本'].push(field)
    } else {
      groups['其他'].push(field)
    }
  })

  // 过滤掉空的分组
  const filteredGroups = {}
  Object.keys(groups).forEach((key) => {
    if (groups[key].length > 0) {
      filteredGroups[key] = groups[key]
    }
  })

  return filteredGroups
})

// 获取字段跨度
const getFieldSpan = (paramTypeCode) => {
  if (paramTypeCode === 'text' || paramTypeCode === 'file') {
    return 24 // 文本域和图片上传占满一行
  }
  return 12 // 其他字段占半行
}

// 生成校验规则
const generateRules = () => {
  const newRules = {}

  if (props.tableHeadData && props.tableHeadData.tableHead) {
    const tableHeadObj = JSON.parse(props.tableHeadData.tableHead)
    if (tableHeadObj && tableHeadObj.fields) {
      tableHeadObj.fields.forEach((field) => {
        // 只有可编辑且必填的字段才添加校验规则
        if (field.required && (!field.hasOwnProperty('edit') || field.edit)) {
          newRules[field.fieldId] = [
            { required: true, message: `请输入${field.fieldName}`, trigger: 'blur' }
          ]
        }
      })
    }
  }

  rules.value = newRules
}

// 上传文件
const uploadMethod = async ({ file, updateProgress }) => {
  const formData = new FormData()
  formData.append('file', file)
  const method = defaultUploadImage
  return method(formData, updateProgress)
}

// 上传图片默认方法
const defaultUploadImage = async (formData, updateProgress: (percent) => void) => {
  const res = await FileApi.updateFile(formData)
  updateProgress(100)
  return { ...res, url: res.data }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 不要清空form数据，保持数据状态
  // form.value = {}
  // formRef.value?.resetFields()
}

// 计算
const handleCalculate = async () => {
  if (!formRef.value) return

  try {
    // 准备数据
    let rowData = JSON.parse(JSON.stringify(form.value))
    delete rowData.id
    delete rowData._X_ROW_KEY
    delete rowData.tableHeadId
    delete rowData.action
    delete rowData.checkbox
    delete rowData.isEdit

    // 将null值转换为空字符串
    Object.keys(rowData).forEach((key) => {
      if (rowData[key] === null || rowData[key] === undefined) {
        rowData[key] = ''
      }
    })

    // 触发计算事件
    emit('calculate', {
      rowData,
      originalRow: props.rowData
    })
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 保存数据
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    saving.value = true

    // 准备保存的数据
    let rowData = JSON.parse(JSON.stringify(form.value))
    delete rowData.id
    delete rowData._X_ROW_KEY
    delete rowData.tableHeadId
    delete rowData.action
    delete rowData.checkbox
    delete rowData.isEdit

    // 将null值转换为空字符串
    Object.keys(rowData).forEach((key) => {
      if (rowData[key] === null || rowData[key] === undefined) {
        rowData[key] = ''
      }
    })

    // 触发保存事件
    emit('save', {
      rowData,
      originalRow: props.rowData
    })
    handleClose()
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    saving.value = false
  }
}

// 获取选择器标签
const getSelectLabel = (field, value) => {
  if (!field.options || !value) return value
  const option = field.options.find((opt) => opt.value === value)
  return option ? option.label : value
}

// 监听行数据变化，初始化表单
watch(
  () => props.rowData,
  (newRowData) => {
    if (newRowData && Object.keys(newRowData).length > 0) {
      form.value = JSON.parse(JSON.stringify(newRowData))
      generateRules()
    }
  },
  { immediate: true, deep: true }
)

// 监听弹窗显示状态，确保数据正确初始化
watch(
  () => props.modelValue,
  (newVisible) => {
    if (newVisible && props.rowData && Object.keys(props.rowData).length > 0) {
      // 弹窗打开时重新初始化表单数据
      form.value = { ...props.rowData }
      generateRules()
    }
  },
  { immediate: true }
)

// 监听表头数据变化，重新生成规则
watch(
  () => props.tableHeadData,
  () => {
    generateRules()
  },
  { immediate: true, deep: true }
)

// 监听form调用计算接口
const handleChange = (val) => {
  // 准备数据
  let rowData = JSON.parse(JSON.stringify(form.value))
  delete rowData.id
  delete rowData._X_ROW_KEY
  delete rowData.tableHeadId
  delete rowData.action
  delete rowData.checkbox
  delete rowData.isEdit

  // 将null值转换为空字符串
  Object.keys(rowData).forEach((key) => {
    if (rowData[key] === null || rowData[key] === undefined) {
      rowData[key] = ''
    }
  })

  // 触发计算事件
  emit('calculate', {
    rowData,
    originalRow: props.rowData
  })
}
</script>

<style scoped>
.upload-placeholder {
  display: flex;
  width: 100px;
  height: 100px;
  cursor: pointer;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-placeholder:hover {
  border-color: #409eff;
}

.image-preview {
  position: relative;
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  color: white;
  cursor: pointer;
  background: rgb(0 0 0 / 50%);
  opacity: 0;
  transition: opacity 0.3s;
  align-items: center;
  justify-content: center;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-overlay .icon {
  cursor: pointer;
  transition: transform 0.2s;
}

.image-overlay .icon:hover {
  transform: scale(1.2);
}

/* 只读样式 */
.readonly-text {
  display: inline-block;
  min-height: 32px;
  font-size: 14px;
  line-height: 32px;
  color: #606266;
}

.readonly-textarea {
  min-height: 80px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  word-break: break-all;
  white-space: pre-wrap;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.readonly-image {
  display: flex;
  align-items: center;
}

/* 字段单位样式 */
.field-unit {
  margin-left: 4px;
  font-size: 12px;
  font-weight: normal;
  color: #909399;
}

/* 上传提示样式 */
.upload-tip {
  display: flex;
  width: max-content;
  padding: 6px 8px;
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  align-items: center;
}

.tip-icon {
  margin-right: 4px;
  font-size: 14px;
  color: #409eff;
}

.tip-text {
  width: max-content;
  line-height: 1.2;
}

.save-status-tag {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);

  :deep(.el-icon span) {
    font-size: 12px !important;
  }
}
</style>
