<template>
  <div>
    <ContentWrap style="padding-top: 0 !important" v-loading="templateOptions.length === 0">
      <!-- 开发表选择器和折叠控制 -->
      <div class="flex items-center gap-10px mb-10px">
        <el-form-item prop="templateId" style="margin-bottom: 0 !important">
          <el-select
            v-model="queryParams.templateId"
            placeholder="请选择开发表"
            clearable
            class="!w-240px"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="item in templateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 基础操作按钮 -->
        <div class="flex items-center gap-10px">
          <el-button @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            查询
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
          <QueryConditionTemplate
            :type="'devTable'"
            :form-data="queryParams"
            @update:form-data="onUpdateFormData"
          />
          <el-button link @click="searchCollapsed = !searchCollapsed" class="ml-10px">
            <Icon :icon="searchCollapsed ? 'ep:arrow-down' : 'ep:arrow-up'" class="mr-5px" />
            {{ searchCollapsed ? '展开' : '收起' }}
          </el-button>
        </div>
      </div>

      <!-- 搜索工作栏 -->
      <el-collapse-transition>
        <el-form
          v-show="!searchCollapsed"
          ref="queryFormRef"
          :inline="true"
          :model="queryParams"
          class="-mb-15px"
          label-width="80px"
        >
          <el-form-item prop="productName">
            <div class="flex gap-20px">
              <el-input
                v-model="queryParams.productName"
                placeholder="请输入产品名称"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
              <el-select
                v-model="queryParams.auditStatus"
                placeholder="请选择审核状态"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-240px"
              >
                <el-option
                  v-for="(item, index) in getIntDictOptions(DICT_TYPE.INFRA_TABLE_APPROVAL_STATUS)"
                  :key="index"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
              <el-select
                v-model="queryParams.production"
                placeholder="请选择是否投产"
                clearable
                class="!w-240px"
              >
                <el-option label="全部" value="" />
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
              <el-select
                v-model="queryParams.creatorNameList"
                placeholder="请选择创建人"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-240px"
              >
                <el-option
                  v-for="(item, index) in userList"
                  :key="index"
                  :label="item.nickname"
                  :value="item.nickname"
                />
              </el-select>
              <el-select
                v-model="queryParams.approvalNameList"
                placeholder="请选择审核人"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="(item, index) in userList"
                  :key="index"
                  :label="item.nickname"
                  :value="item.nickname"
                />
              </el-select>
              <shortcut-date-range-picker isTimes v-model="queryParams.createTime" />
              <shortcut-date-range-picker isTimes v-model="queryParams.updateTime" />
            </div>
          </el-form-item>
        </el-form>
      </el-collapse-transition>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <Table
        border
        v-if="queryParams.templateId && dynamicColumns.length > 0"
        height="75vh"
        ref="tableRefs"
        :loading="loading"
        :columns="dynamicColumns.length > 0 ? dynamicColumns : columns"
        :data="list"
        selection
        :saveStatus="saveStatus"
        :type="String(queryParams.templateId)"
        :pagination="{
          total: totalCount,
          pageSize: queryParams.pageSize,
          currentPage: queryParams.pageNo,
          layout: 'total, sizes, prev, pager, next, jumper'
        }"
        :dragField="(row, index) => (row.id ? row.id : `${index}_${row.name}`)"
        @selection-change="handleSelectChange"
        @update:page-size="handlePageSizeChange"
        @update:current-page="handlePageChange"
        @sort-change="handleSortChange"
        isSort
      >
        <!-- 顶部操作按钮 -->
        <template #top-btn>
          <el-button v-hasPermi="['infra:tools-table-data:save']" type="primary" @click="addEvent"
            >新增数据</el-button
          >
          <el-button
            v-hasPermi="['infra:tools-table-data:dev-delete']"
            type="danger"
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <Icon icon="ep:delete" class="mr-5px" />批量删除
          </el-button>
          <el-button
            v-hasPermi="['infra:tools-table-data:submit-approval']"
            :disabled="selectedRows.length === 0 || selectedRows.some((row) => isRowApproved(row))"
            :title="
              selectedRows.length === 0
                ? '提交审核'
                : selectedRows.some((row) => isRowApproved(row))
                  ? '选中的数据中包含已审核通过的数据，请重新选择'
                  : '提交审核'
            "
            @click="handleSubmitAudit()"
            >提交审核</el-button
          >
          <el-button
            v-hasPermi="['infra:tools-table-data:prod']"
            :disabled="
              selectedRows.length === 0 ||
              selectedRows.some(
                (row) => !isRowApproved(row) || row.auditStatus === 0 || row.auditStatus === 1
              )
            "
            :title="
              selectedRows.length === 0
                ? '投产'
                : selectedRows.some(
                      (row) => !isRowApproved(row) || row.auditStatus === 0 || row.auditStatus === 1
                    )
                  ? '选中的数据中包含未审核或草稿数据，请重新选择'
                  : '投产'
            "
            @click="handleProduction()"
            >投产</el-button
          >
          <el-button
            v-hasPermi="['infra:tools-table-data:export']"
            :disabled="!queryParams.templateId"
            plain
            @click="handleImport"
          >
            <Icon icon="ep:upload" class="mr-5px" />导入数据
          </el-button>
          <el-button
            v-hasPermi="['infra:tools-table-data:export']"
            :disabled="exportLoading"
            :loading="exportLoading"
            plain
            @click="handleExport"
          >
            <Icon icon="ep:download" class="mr-5px" />导出筛选数据
          </el-button>
        </template>
        <template #top-btn-right>
          <el-button
            v-hasPermi="['infra:tools-table-data:export']"
            plain
            @click="handleUpdateExchangeRate"
          >
            更新汇率计算
          </el-button>
        </template>

        <template #action="{ row }">
          <!-- 编辑/保存按钮 -->
          <el-button
            v-if="!row.isEdit"
            v-hasPermi="['infra:tools-table-data:save']"
            link
            type="primary"
            size="small"
            @click="handleEditRow(row)"
            :disabled="isRowApproved(row)"
          >
            编辑
          </el-button>
          <el-button
            v-else
            v-hasPermi="['infra:tools-table-data:save']"
            link
            size="small"
            type="primary"
            @click="saveChangedData(row)"
            :disabled="isRowApproved(row)"
          >
            保存
          </el-button>

          <!-- 操作下拉菜单 -->
          <el-dropdown trigger="hover">
            <el-button link type="primary" style="padding: 6px !important">
              更多操作
              <Icon icon="ep:arrow-down" class="ml-5px" />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- 编辑详情按钮 -->
                <el-dropdown-item @click="handleEditDetail(row)" :disabled="isRowApproved(row)">
                  <div> 编辑详情 </div>
                </el-dropdown-item>
                <el-dropdown-item @click="handleDelete(row.id)">
                  <div v-hasPermi="['infra:tools-table-data:dev-delete']"> 删除 </div>
                </el-dropdown-item>
                <el-dropdown-item @click="handleProduction(row)">
                  <div v-hasPermi="['infra:tools-table-data:prod']"> 投产 </div>
                </el-dropdown-item>
                <el-dropdown-item :disabled="isRowApproved(row)" @click="handleSubmitAudit(row.id)">
                  <div v-hasPermi="['infra:tools-table-data:submit-approval']"> 提交审核 </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </Table>
      <el-empty v-else description="请选择要开发的表模板" />
    </ContentWrap>

    <LaunchDialog ref="launchDialogRef" @success="getList" />

    <!-- 编辑详情弹窗组件 -->
    <EditDetailDialog
      v-model="editDetailVisible"
      :table-head-data="tableHeadData"
      :row-data="currentEditRow"
      @save="handleSaveEditDetail"
      @calculate="handleCalculate"
    />

    <!-- 导入弹窗组件 -->
    <ImportForm ref="importFormRef" @success="getList" />
  </div>
</template>

<script setup lang="tsx">
import { getUserListNoPermission } from '@/api/system/user'
import {
  addData,
  calculate as calculateApi,
  deleteData,
  exportExcel, // 导入导出方法
  getPage as getTableDataPage,
  refreshRate,
  submitApproval,
  update as updateTableData
} from '@/api/tool/tableData'
import { getTableHeadByTemplateId, getTemplateList } from '@/api/tool/tableTemplateCenter'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { nextTick } from 'vue'

import LaunchDialog from '../audit/LaunchDialog.vue'
import EditDetailDialog from './EditDetailDialog.vue'
import ImportForm from './importForm.vue'
defineOptions({ name: 'DevTabledex' })

// 处理计算事件
const handleCalculate = async ({ rowData, originalRow }) => {
  const tableHeadObj = JSON.parse(tableHeadData.value.tableHead)
  const resultNameMap = {}
  if (tableHeadObj && tableHeadObj.fields) {
    tableHeadObj.fields.forEach((field) => {
      resultNameMap[field.fieldId] = field.fieldName
    })
  }

  try {
    // 对传递给 API 的数据进行深拷贝，避免影响原始数据
    const rowDataCopy = JSON.parse(JSON.stringify(rowData))
    const res = await calculateApi({
      templateId: queryParams.templateId,
      rowId: originalRow.id,
      tableHeadId: tableHeadData.value.id,
      resultMap: rowDataCopy,
      resultNameMap
    })
    // 使用返回的数据更新当前编辑行
    if (res) {
      // 使用深拷贝更新当前编辑行，避免影响原始数据
      currentEditRow.value = JSON.parse(
        JSON.stringify({
          ...currentEditRow.value,
          ...res.resultMap
        })
      )
    } else {
      console.log('计算完成，但未返回新数据')
    }
  } catch (error) {
    console.error('计算失败', error)
    message.error('计算失败')
  }
}

const props = defineProps({
  drawerMode: {
    type: Boolean,
    default: false
  },
  initialTemplateId: {
    type: [String, Number],
    default: null
  },
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})

const onUpdateFormData = (newVal) => {
  Object.assign(queryParams, newVal)
  handleQuery()
}
const tableRef = ref(null)
const launchDialogRef = ref(null)
const importFormRef = ref(null)
const message = useMessage() // 消息弹窗
const searchCollapsed = ref(false) // 搜索区域折叠状态，默认折叠

// 列配置
const columns = [
  {
    field: 'action',
    align: 'left',
    title: '操作',
    label: '操作',
    type: 'action',
    width: 230
  }
]
const userList = ref([])
// 获取用户列表
const getOperatorList = async () => {
  try {
    const res = await getUserListNoPermission()
    if (res && Array.isArray(res)) {
      userList.value = res.map((item) => ({
        id: item.id,
        nickname: item.nickname
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
  }
}

// 校验规则
const editRules = {
  name: [{ required: true, message: '请输入名字' }]
}

// 当前编辑行的ID，用于在刷新数据后恢复编辑状态
const currentEditingRowId = ref(null)

// 保存变更的数据
const saveChangedData = async (changedRow) => {
  if (!queryParams.templateId || !tableHeadData.value) {
    return
  }

  // 防止重复调用
  if (saveStatus.value.saving) {
    return
  }

  saveStatus.value.saving = true
  saveStatus.value.error = null

  try {
    // 准备保存的数据
    const resultMap = {}

    // 获取表头字段信息
    const tableHeadObj = JSON.parse(tableHeadData.value.tableHead)
    if (!tableHeadObj || !tableHeadObj.fields) {
      return
    }
    let rowData = JSON.parse(JSON.stringify(changedRow))
    delete rowData.id
    delete rowData._X_ROW_KEY
    delete rowData.tableHeadId
    delete rowData.action
    delete rowData.checkbox
    delete rowData.isEdit

    // 将null值转换为空字符串
    Object.keys(rowData).forEach((key) => {
      if (rowData[key] === null || rowData[key] === undefined) {
        rowData[key] = ''
      }
    })

    // 调用保存接口
    await updateTableData({
      templateId: queryParams.templateId,
      rowId: changedRow.id,
      tableHeadId: queryParams.tableHeadId,
      resultMap: { ...rowData }
    })

    // 更新保存状态
    saveStatus.value.lastSaved = new Date()

    // 保存成功后退出编辑状态
    changedRow.isEdit = false

    // 如果没有记录要编辑的行ID，说明是用户主动保存，清除编辑状态
    if (!currentEditingRowId.value) {
      // 用户主动保存，重新获取数据，不恢复编辑状态
      queryParams.pageNo = 1
      await getList()
    } else {
      // 有记录要编辑的行ID，说明是自动保存，需要在刷新后恢复编辑状态
      queryParams.pageNo = 1
      await getList()
    }
  } catch (error) {
    console.error('保存数据失败', error)
    saveStatus.value.error = '保存失败'
    ElMessage.error('自动保存失败')
  } finally {
    saveStatus.value.saving = false
  }
}

// 分页显示数量变化
function handlePageSizeChange(pageSize) {
  queryParams.pageSize = pageSize
  getList()
  console.log('分页显示数量变化', pageSize)
}

// 分页变化
function handlePageChange(page) {
  queryParams.pageNo = page
  getList()
  console.log('分页变化', page)
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}

import download from '@/utils/download'
// 处理导出逻辑
const handleExport = async () => {
  if (!queryParams.templateId) {
    message.warning('请先选择开发表模板')
    return
  }
  if (exportLoading.value) {
    return
  }

  exportLoading.value = true
  try {
    const data = await exportExcel({
      ...queryParams,
      templateType: 1
    })
    download.excel(data, '开发表导出数据.xls')
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  } finally {
    exportLoading.value = false
  }
}

// 处理导入
const handleImport = () => {
  if (!queryParams.templateId) {
    message.warning('请先选择开发表模板')
    return
  }
  importFormRef.value?.open(queryParams.templateId)
}

// 处理更新汇率计算
const handleUpdateExchangeRate = async () => {
  if (!queryParams.templateId) {
    message.warning('请先选择开发表模板')
    return
  }

  // 显示确认对话框
  try {
    await ElMessageBox.confirm(
      '该操作将使用最新汇率重新计算所有数据，由于数据量较大，处理可能需要较长时间。请确认是否继续？',
      '更新汇率计算确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 用户确认后，开始处理
    loading.value = true

    try {
      await refreshRate(queryParams.templateId)

      message.success('汇率计算更新成功')

      // 重新获取列表数据
      await getList()
    } catch (error) {
      console.error('更新汇率计算失败:', error)
      message.error('更新汇率计算失败：' + (error.message || '未知错误'))
    } finally {
      loading.value = false
    }
  } catch {
    // 用户取消操作，不做任何处理
  }
}

// 数据属性
const loading = ref(false)
const exportLoading = ref(false)
const list = ref([])
const templateOptions = ref([])
const tableHeadData = ref(null)
const dynamicColumns = ref([])
const saveStatus = ref({
  saving: false,
  lastSaved: null,
  error: null
})

// 提交审核状态标识
const isSubmittingAudit = ref(false)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  templateId: undefined, // 模板ID
  tableHeadId: undefined, // 表头ID
  productName: null,
  auditStatus: null,
  production: null,
  creatorNameList: [],
  creatorNameList: [],
  approvalNameList: [],
  createTime: [],
  sortingFields: [],
  approvalTime: [],
  updateTime: []
})

// 分页相关状态
const totalCount = ref(0)
const allData = ref([]) // 存储所有已加载的数据
const queryFormRef = ref()
const selectedRows = ref([])

// 切换模板
const handleTemplateChange = async (templateId) => {
  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  // 重置分页和数据状态
  queryParams.pageNo = 1
  queryParams.pageSize = 50
  allData.value = []
  list.value = []
  totalCount.value = 0

  if (!templateId) {
    tableHeadData.value = null
    dynamicColumns.value = []
    return
  }

  loading.value = true
  try {
    // 调用API获取表头数据
    const res = await getTableHeadByTemplateId(templateId)
    if (res) {
      tableHeadData.value = res
      queryParams.tableHeadId = res.id

      // 解析表头JSON字符串
      if (res.tableHead) {
        const tableHeadObj = JSON.parse(res.tableHead)
        if (tableHeadObj && tableHeadObj.fields) {
          // 根据表头字段生成动态列配置
          dynamicColumns.value = generateDynamicColumns(tableHeadObj.fields)
          console.log(dynamicColumns.value)
          // 获取表格数据
          await getList()
        }
      }
    }
  } catch (error) {
    console.error('获取表头数据失败', error)
  } finally {
    loading.value = false
  }
}

// 根据表头字段生成动态列配置
const generateDynamicColumns = (fields) => {
  // 获取复选框列
  const checkboxColumn = columns.find((col) => col.field === 'checkbox')
  // 获取操作列
  const actionColumn = columns.find((col) => col.field === 'action')

  // 设置操作列固定在右侧
  if (actionColumn) {
    actionColumn.fixed = 'right'
  }

  // 根据表头字段生成动态列
  const fieldColumns = fields.map((field) => {
    // 基础列配置
    const column = {
      ...field,
      field: field.fieldId,
      align: 'left',
      title: field.fieldName,
      label: field.fieldName,
      slotsName: field.paramTypeCode == 'file' ? 'image' : ''
    }

    return column
  })

  // 复选框列在最前，字段列在中间，操作列在最后
  return checkboxColumn
    ? [checkboxColumn, ...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
    : [...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
}

// 方法
const getList = async (isApproval, isLoadMore = false) => {
  if (!queryParams.tableHeadId) {
    ElMessage.warning('请先选择开发表')
    return
  }

  loading.value = true
  try {
    // 调用API获取表格数据
    const res = await getTableDataPage({
      ...queryParams,
      tableHeadId: queryParams.tableHeadId,
      templateType: 1 //开发1 投产2 审核3
    })

    if (res) {
      // 处理返回的数据，转换为表格需要的格式
      const formattedData = formatTableData(res.list || [])
      totalCount.value = res.total || 0

      if (isLoadMore) {
        // 触底加载更多：追加数据
        allData.value = [...allData.value, ...formattedData]
        list.value = allData.value
      } else {
        // 首次加载或刷新：重置数据
        allData.value = formattedData
        list.value = formattedData

        // 如果有需要恢复编辑状态的行，重新设置编辑状态
        if (currentEditingRowId.value) {
          const editingRow = list.value.find((row) => row.id == currentEditingRowId.value)
          console.log(editingRow)
          if (editingRow) {
            editingRow.isEdit = true
          }
          // 清除记录的编辑行ID
          currentEditingRowId.value = null
        }
      }
    } else {
      if (!isLoadMore) {
        allData.value = []
        list.value = []
        totalCount.value = 0
      }
    }

    if (isApproval) {
      // 重置提交审核状态
      setTimeout(() => {
        isSubmittingAudit.value = false
      }, 300)
    }
  } catch (error) {
    console.error('获取表格数据失败', error)
    if (!isLoadMore) {
      allData.value = []
      list.value = []
      totalCount.value = 0
    }
  } finally {
    loading.value = false
  }
}

// 格式化表格数据
const formatTableData = (dataList) => {
  if (!dataList || !dataList.length) return []

  return dataList.map((item) => {
    // 创建行数据对象，包含行ID
    const rowData = {
      id: item.rowId
    }

    // 将 resultMap 中的数据展开到行对象中
    Object.entries(item.resultMap).forEach(([fieldId, value]) => {
      rowData[fieldId] = value
    })

    return rowData
  })
}

// 查询
const handleQuery = () => {
  queryParams.pageNo = 1
  allData.value = [] // 重置已加载数据
  getList()
}

// 重置
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.productName = null
  queryParams.auditStatus = null
  queryParams.production = null
  queryParams.creatorNameList = []
  queryParams.creatorNameList = []
  queryParams.approvalNameList = []
  queryParams.sortingFields = []
  queryParams.createTime = []
  queryParams.approvalTime = []
  queryParams.updateTime = []
  queryParams.templateId = ''
  queryParams.tableHeadId = ''
  queryParams.pageNo = 1
  queryParams.pageSize = 50

  // 清理数据
  allData.value = []
  list.value = []
  totalCount.value = 0

  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  handleQuery()
}

// 判断行是否已审核通过
const isRowApproved = (row) => {
  // 尝试从不同可能的字段中获取审核状态
  return Object.values(row).some((value) => value == '已通过')
}

// 处理提交审核逻辑
const handleSubmitAudit = (id) => {
  const ids = id ? [id] : selectedRows.value.map((row) => row.id)
  if (ids.length === 0) {
    ElMessage.warning('请选择要提交审核的数据')
    return
  }

  if (!queryParams.templateId) {
    ElMessage.warning('请先选择开发表模板')
    return
  }

  // 检查选中的行中是否有已审核通过的数据
  if (id) {
    // 单行操作时，检查该行是否已审核通过
    const row = list.value.find((item) => item.id === id)
    if (row && isRowApproved(row)) {
      ElMessage.warning('已审核通过的数据不能再次提交审核')
      return
    }
  } else {
    // 批量操作时，检查选中的行中是否有已审核通过的数据
    const hasApproved = selectedRows.value.some((row) => isRowApproved(row))
    if (hasApproved) {
      ElMessage.warning('选中的数据中包含已审核通过的数据，请重新选择')
      return
    }
  }

  ElMessageBox.confirm(`确认提交审核选中的数据吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 设置提交审核状态，防止watch触发自动保存
        isSubmittingAudit.value = true
        // 显示加载中
        loading.value = true

        // 调用提交审核API
        await submitApproval({
          rowIds: ids,
          templateId: queryParams.templateId
        })

        // 提示成功
        ElMessage.success('提交审核成功')

        // 重新加载列表
        getList(true)
      } catch (error) {
        console.error('提交审核失败', error)
        ElMessage.error('提交审核失败：' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}

// 处理投产逻辑
const handleProduction = (data) => {
  const ids = data ? [data.id] : selectedRows.value.map((row) => row.id)
  if (ids.length === 0) {
    ElMessage.warning('请选择要投产的数据')
    return
  }
  launchDialogRef.value.open(data, queryParams.templateId, ids) //data 回显数据，templateId 模板id，ids 数据id
}

// 删除数据
const handleDelete = (id) => {
  // 处理删除逻辑
  if (!id) {
    ElMessage.warning('删除的数据ID不能为空')
    return
  }

  if (!queryParams.templateId) {
    ElMessage.warning('请先选择开发表模板')
    return
  }

  // 二次确认删除
  ElMessageBox.confirm(`确认删除编号为 ${id} 的数据吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 显示加载中
        loading.value = true

        // 调用删除API
        await deleteData({
          rowIds: [id],
          templateId: queryParams.templateId
        })

        // 提示成功
        ElMessage.success('删除成功')

        // 重新加载列表
        getList()
      } catch (error) {
        console.error('删除失败', error)
        ElMessage.error('删除失败：' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  if (!queryParams.templateId) {
    ElMessage.warning('请先选择开发表模板')
    return
  }

  // 获取选中行的ID数组
  const rowIds = selectedRows.value.map(row => row.id).filter(id => id)
  
  if (rowIds.length === 0) {
    ElMessage.warning('选中的数据中没有有效的ID')
    return
  }

  // 二次确认删除
  ElMessageBox.confirm(`确认删除选中的 ${rowIds.length} 条数据吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 显示加载中
        loading.value = true

        // 调用删除API
        await deleteData({
          rowIds: rowIds,
          templateId: queryParams.templateId
        })

        // 提示成功
        ElMessage.success(`成功删除 ${rowIds.length} 条数据`)

        // 清空选中状态
        selectedRows.value = []

        // 重新加载列表
        getList()
      } catch (error) {
        console.error('批量删除失败', error)
        ElMessage.error('批量删除失败：' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    })
}

// 获取模板列表
const getTemplateListFc = async () => {
  try {
    // 调用API获取模板列表
    const res = await getTemplateList({
      pageNo: 1,
      pageSize: 100,
      templateTypeCode: '1' // 1代表开发表,2代表投产表
    })
    if (res) {
      templateOptions.value = res.map((item) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

// 处理表格选择变化事件
const handleSelectChange = (selection) => {
  selectedRows.value = selection
}

// 检查是否有未保存的数据
const hasUnsavedData = (excludeRow = null) => {
  return list.value.some((row) => row.isEdit && row !== excludeRow)
}

// 保存所有未保存的数据
const saveAllUnsavedData = async (excludeRow = null) => {
  const unsavedRows = list.value.filter((row) => row.isEdit && row !== excludeRow)
  if (unsavedRows.length === 0) {
    return true
  }

  try {
    // 逐个保存未保存的数据
    for (const row of unsavedRows) {
      // 临时清除编辑状态记录，避免在保存其他行时影响当前要编辑的行
      const tempEditingRowId = currentEditingRowId.value
      currentEditingRowId.value = null

      await saveChangedData(row)
      row.isEdit = false // 保存成功后退出编辑状态

      // 恢复编辑状态记录
      currentEditingRowId.value = tempEditingRowId
    }
    return true
  } catch (error) {
    console.error('保存未保存数据失败:', error)
    ElMessage.error('保存未保存数据失败，请手动保存后再操作')
    return false
  }
}

// 处理行编辑
const handleEditRow = async (row) => {
  // 先记录要编辑的新行ID
  const newEditingRowId = row.id

  // 检查是否有其他行正在编辑，如果有则先保存（排除当前行）
  if (hasUnsavedData(row)) {
    // 记录新要编辑的行ID
    currentEditingRowId.value = newEditingRowId

    const savedSuccessfully = await saveAllUnsavedData(row)
    if (!savedSuccessfully) {
      // 保存失败，清除记录的ID
      currentEditingRowId.value = null
      return // 保存失败，不继续编辑
    }
    // 其他行已保存。 currentEditingRowId.value 现在是 newEditingRowId。
    // 调用 getList() 来刷新列表，这将根据 currentEditingRowId.value 设置正确的编辑行。
    await getList()
  } else {
    // 没有其他行在编辑，直接设置当前行为编辑状态
    // 不设置currentEditingRowId.value，这样用户主动保存时就不会重新进入编辑状态
    row.isEdit = true
  }
}

// 新增数据
const addEvent = async () => {
  if (!queryParams.templateId) {
    ElMessage.warning('请先选择开发表')
    return
  }

  // 检查是否有未保存的数据，如果有则先保存
  if (hasUnsavedData()) {
    const saved = await saveAllUnsavedData()
    if (!saved) {
      return // 保存失败，不继续新增
    }
  }

  loading.value = true
  try {
    // 调用API新增数据，获取新数据的ID和表头ID
    const newDataRes = await addData({ templateId: queryParams.templateId })
    if (newDataRes && newDataRes.rowId) {
      // 构建新行数据，包含API返回的默认值
      const newRow = {
        id: newDataRes.rowId,
        tableHeadId: newDataRes.tableHeadId,
        ...newDataRes.resultMap, // API可能返回一些默认字段值
        isEdit: true // 设置为编辑状态
      }

      // 将新行添加到列表的开头
      list.value.unshift(newRow)
      totalCount.value += 1

      // 确保表格实例存在并更新
      await nextTick()
      ElMessage.success('新增成功，请编辑数据')
    } else {
      ElMessage.error('新增数据失败，无法获取新数据ID')
    }
  } catch (error) {
    console.error('新增数据失败:', error)
    ElMessage.error('新增数据失败')
  } finally {
    loading.value = false
  }
}

// 编辑详情弹窗相关状态
const editDetailVisible = ref(false)
const currentEditRow = ref(null)

// 处理编辑详情
const handleEditDetail = (row) => {
  console.log(row)
  currentEditRow.value = row
  editDetailVisible.value = true
}

// 保存编辑详情
const handleSaveEditDetail = async (saveData) => {
  try {
    // 调用保存接口
    await updateTableData({
      templateId: queryParams.templateId,
      rowId: saveData.originalRow.id,
      tableHeadId: queryParams.tableHeadId,
      resultMap: { ...saveData.rowData }
    })

    ElMessage.success('保存成功')

    // 关闭弹窗
    editDetailVisible.value = false
    currentEditRow.value = null

    // 重新获取数据
    await getList()
  } catch (error) {
    console.error('保存失败', error)
    ElMessage.error('保存失败：' + (error.message || '未知错误'))
  }
}

onMounted(async () => {
  // 获取模板列表
  const res = await getTemplateList({ templateTypeCode: 1 })
  templateOptions.value = res.map((item) => ({
    label: item.name,
    value: item.id
  }))
  getOperatorList()

  // 如果是抽屉模式，则应用初始值
  if (props.drawerMode) {
    if (props.initialTemplateId) {
      queryParams.templateId = props.initialTemplateId
      // 在调用 handleTemplateChange 之前应用其他过滤器
      if (props.initialFilters) {
        Object.assign(queryParams, props.initialFilters)
      }
      // handleTemplateChange 会获取列并调用 getList
      await handleTemplateChange(queryParams.templateId)
    } else if (props.initialFilters) {
      // 如果没有模板ID，但有过滤器，则直接调用 getList
      Object.assign(queryParams, props.initialFilters)
      await getList()
    }
  }
})
</script>

<style scoped>
/* 自动保存状态提示的过渡动画 */
.save-status-enter-active,
.save-status-leave-active {
  transition: all 0.3s ease;
}

.save-status-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.save-status-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.save-status-tag {
  transition: all 0.2s ease;
}

.save-status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}
</style>

<style scoped>
/* 在这里添加样式（如需） */
</style>
