<template>
  <div class="daily-summary-container">
    <!-- 筛选条件 -->
    <el-form
      class="filter-card"
      :model="summaryFilters"
      :inline="true"
      style="display: flex; flex-wrap: wrap"
    >
      <el-form-item label="币种">
        <el-select v-model="summaryFilters.currencyCode" class="!w-140px" placeholder="请选择币种">
          <el-option
            v-for="currency in currencyOptions"
            :key="currency.value"
            :label="currency.label"
            :value="currency.value"
          />
        </el-select>
      </el-form-item>
      <!-- 账户店铺选择器 -->
      <AccountShopSelector
        ref="accountShopSelectorRef"
        :show-account="false"
        v-model:shop-values="summaryFilters.sidQuerySet"
        v-model:country-values="summaryFilters.countrysList"
        v-model:country-names="summaryFilters.countries"
      />
      <el-form-item label="负责人" prop="principalNamesQuerySet">
        <RcInputSelect
          v-model="summaryFilters.principalNamesQuerySet"
          class="!w-240px"
          :options="principalOptions"
        />
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="summaryFilters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          valueFormat="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
          @change="(value) => handleDateRangeChange(value, 'summary')"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label-width="0">
        <el-button type="primary" @click="searchSummaryData">查询</el-button>
        <el-button @click="resetSummaryFilters">重置</el-button>
        <!-- <el-button @click="exportSummaryData">导出</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 每日汇总数据展示 -->
    <div class="daily-summary-display">
      <!-- 横向滚动的表格布局 -->
      <div class="horizontal-scroll-container">
        <div class="horizontal-table-wrapper">
          <!-- 表头 -->
          <div class="table-header">
            <div
              class="item-name-header resizable-column"
              data-column="item-name"
              :style="{
                width: getColumnWidth('item-name') + 'px',
                minWidth: getColumnWidth('item-name') + 'px'
              }"
            >
              项目
              <!-- 添加列宽调整器 -->
              <div class="column-resizer" @mousedown="startResize($event, 'item-name')"></div>
            </div>
            <div class="date-headers">
              <div
                v-for="dateItem in listDateHeaders"
                :key="dateItem.date"
                class="date-header resizable-column"
                :class="{ 'total-column': dateItem.isTotal }"
                :data-column="dateItem.date"
                :style="{
                  width: getColumnWidth(dateItem.date) + 'px',
                  minWidth: getColumnWidth(dateItem.date) + 'px'
                }"
              >
                {{ dateItem.display }}
                <!-- 添加列宽调整器，所有列都可以调整 -->
                <div class="column-resizer" @mousedown="startResize($event, dateItem.date)"></div>
              </div>
            </div>
          </div>

          <!-- 数据行（按类型分组显示） -->
          <div class="data-rows" v-if="!loading && listDateHeaders.length > 0">
            <!-- 利润组 -->
            <div class="data-group">
              <div class="group-header">
                <div
                  class="group-title"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                  >💰 利润</div
                >
              </div>
              <div class="data-row" v-for="dataItem in profitItems" :key="dataItem.key">
                <div
                  class="item-name profit-item"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                >
                  {{ dataItem.label }}
                </div>
                <div class="item-values">
                  <div
                    v-for="dateItem in listDateHeaders"
                    :key="`${dataItem.key}-${dateItem.date}`"
                    class="item-value two-line-value"
                    :class="[dataItem.type, { 'total-column': dateItem.isTotal }]"
                    :data-column-data="dateItem.date"
                    :style="{
                      width: getColumnWidth(dateItem.date) + 'px',
                      minWidth: getColumnWidth(dateItem.date) + 'px'
                    }"
                  >
                    <div class="value-amount">
                      <el-skeleton-item
                        v-if="
                          dateItem.isTotal &&
                          (totalLoading || getDateValue(dateItem.date, dataItem.key) === 'loading')
                        "
                        variant="text"
                        style="width: 60px; height: 16px"
                      />
                      <span v-else>{{
                        formatCurrency(getDateValue(dateItem.date, dataItem.key))
                      }}</span>
                    </div>
                    <div
                      class="value-ratio"
                      v-if="calculateRatio(dateItem.date, dataItem.key) !== null"
                    >
                      {{ formatRatio(calculateRatio(dateItem.date, dataItem.key)) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 收入组 -->
            <div class="data-group">
              <div class="group-header">
                <div
                  class="group-title"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                  >📊 收入</div
                >
              </div>
              <div class="data-row" v-for="dataItem in revenueItems" :key="dataItem.key">
                <div
                  class="item-name revenue-item"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                >
                  {{ dataItem.label }}
                </div>
                <div class="item-values">
                  <div
                    v-for="dateItem in listDateHeaders"
                    :key="`${dataItem.key}-${dateItem.date}`"
                    class="item-value two-line-value"
                    :class="[dataItem.type, { 'total-column': dateItem.isTotal }]"
                    :data-column-data="dateItem.date"
                    :style="{
                      width: getColumnWidth(dateItem.date) + 'px',
                      minWidth: getColumnWidth(dateItem.date) + 'px'
                    }"
                  >
                    <div class="value-amount">
                      <el-skeleton-item
                        v-if="
                          dateItem.isTotal &&
                          getDateValue(dateItem.date, dataItem.key) === 'loading'
                        "
                        variant="text"
                        style="width: 60px; height: 16px"
                      />
                      <span v-else>{{
                        dataItem.key === 'volume'
                          ? formatQuantity(getDateValue(dateItem.date, dataItem.key))
                          : formatCurrency(getDateValue(dateItem.date, dataItem.key))
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 成本支出组 -->
            <div class="data-group">
              <div class="group-header">
                <div
                  class="group-title"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                  >📉 成本支出</div
                >
              </div>
              <div class="data-row" v-for="dataItem in costItems" :key="dataItem.key">
                <div
                  class="item-name cost-item"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                >
                  {{ dataItem.label }}
                </div>
                <div class="item-values">
                  <div
                    v-for="dateItem in listDateHeaders"
                    :key="`${dataItem.key}-${dateItem.date}`"
                    class="item-value two-line-value"
                    :class="[dataItem.type, { 'total-column': dateItem.isTotal }]"
                    :data-column-data="dateItem.date"
                    :style="{
                      width: getColumnWidth(dateItem.date) + 'px',
                      minWidth: getColumnWidth(dateItem.date) + 'px'
                    }"
                  >
                    <div class="value-amount">
                      <span v-if="dataItem.key === 'managementFee'" class="management-fee-value">
                        <el-skeleton-item
                          v-if="
                            dateItem.isTotal &&
                            (totalLoading ||
                              getDateValue(dateItem.date, dataItem.key) === 'loading')
                          "
                          variant="text"
                          style="width: 60px; height: 16px"
                        />
                        <el-popover
                          v-else
                          placement="bottom"
                          title="管理费分摊详情"
                          :width="280"
                          trigger="click"
                        >
                          <template #reference>
                            <span class="management-fee-clickable">
                              {{ formatCurrency(getDateValue(dateItem.date, dataItem.key)) }}
                              <el-icon class="fee-detail-arrow inline-arrow">
                                <ArrowDown />
                              </el-icon>
                            </span>
                          </template>
                          <div class="management-fee-detail-content">
                            <div class="detail-row">
                              <span class="label">人工费:</span>
                              <span class="value">{{
                                formatCurrency(getManagementFeeDetails(dateItem.date).laborCost)
                              }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="label">设计费:</span>
                              <span class="value">{{
                                formatCurrency(getManagementFeeDetails(dateItem.date).designCost)
                              }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="label">场地费:</span>
                              <span class="value">{{
                                formatCurrency(getManagementFeeDetails(dateItem.date).venueCost)
                              }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="label">其它均摊费:</span>
                              <span class="value">{{
                                formatCurrency(getManagementFeeDetails(dateItem.date).otherCost)
                              }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="label">VAT:</span>
                              <span class="value">{{
                                formatCurrency(getManagementFeeDetails(dateItem.date).vat)
                              }}</span>
                            </div>
                            <div class="detail-row total-row">
                              <span class="label">总计:</span>
                              <span class="value">
                                {{ formatCurrency(getDateValue(dateItem.date, dataItem.key)) }}
                              </span>
                            </div>
                          </div>
                        </el-popover>
                      </span>
                      <span v-else>
                        <el-skeleton-item
                          v-if="
                            dateItem.isTotal &&
                            (totalLoading ||
                              getDateValue(dateItem.date, dataItem.key) === 'loading')
                          "
                          variant="text"
                          style="width: 60px; height: 16px"
                        />
                        <span v-else>{{
                          formatCurrency(getDateValue(dateItem.date, dataItem.key))
                        }}</span>
                      </span>
                    </div>
                    <div
                      class="value-ratio"
                      v-if="calculateRatio(dateItem.date, dataItem.key) !== null"
                    >
                      {{ formatRatio(calculateRatio(dateItem.date, dataItem.key)) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && listDateHeaders.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal/index'
import { getDailyReportPage, getDailyReportStatistic } from '@/api/profit/report'
import AccountShopSelector from '@/components/AccountShopSelector/index.vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { ArrowDown } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const loading = ref(false)
const totalLoading = ref(false) // 总计数据加载状态
//默认昨天
// 获取昨天日期
const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
// 筛选条件
const summaryFilters = reactive({
  currencyCode: '原币种',
  sidQuerySet: [], // 店铺ID
  shopNames: [], // 店铺名称
  countries: [], // 国家代码
  countrysList: [], // 国家名称
  principalNamesQuerySet: [], // 负责人ID
  dateRange: [yesterday, yesterday], //默认昨天
  pageNo: 1,
  pageSize: 50
})

// AccountShopSelector组件引用
const accountShopSelectorRef = ref()

// 选项数据
const principalOptions = ref([])

const currencyOptions = ref([])

// 加载币种选项
const loadCurrencyOptions = async () => {
  try {
    const response = getDictOptions(DICT_TYPE.CURRENCYNAME_CURRENCYNAMECODE)
    currencyOptions.value = response || []
  } catch (error) {
    console.error('加载币种选项失败:', error)
  }
}
// 获取负责人列表
const getPrincipalListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getPrincipalList()
    if (res) {
      principalOptions.value = res.map((item) => ({
        label: item.principalName,
        value: item.principalName
      }))
    }
  } catch (error) {
    console.error('获取负责人列表失败', error)
  }
}

// 表格数据
const summaryData = ref<any>({})

// 日期快捷选项
const dateShortcuts = [
  // {
  //   text: '今天',
  //   value: () => {
  //     const start = new Date()
  //     start.setHours(0, 0, 0, 0)

  //     const end = new Date()
  //     end.setHours(23, 59, 59, 999)

  //     return [start, end]
  //   }
  // },
  {
    text: '昨天',
    value: () => DateUtil.getDayRange(new Date(), -1)
  },
  {
    text: '最近7天',
    value: () => DateUtil.getLast7Days()
  },
  // {
  //   text: '本月',
  //   value: () => [dayjs().startOf('M'), dayjs().subtract(1, 'd')]
  // },
  // {
  //   text: '最近30天',
  //   value: () => DateUtil.getLast30Days()
  // }
  {
    text: '最近15天',
    value: () => {
      const end = dayjs().subtract(1, 'day')
      const start = end.subtract(14, 'day')
      return [start.toDate(), end.toDate()]
    }
  }
]

// 数据项定义 - 按照原型UI分组
const profitItems = ref([])

const revenueItems = ref([
  { key: 'amount', label: '销售收入', type: 'revenue' },
  { key: 'volume', label: '销量', type: 'revenue' }
])

const costItems = ref([])

// 计算属性
const listDateHeaders = computed(() => {
  if (!summaryData.value || !summaryData.value.dates) return []
  const headers = []

  // 先添加总计列 - 只要有日期数据就显示总计列
  // if (summaryData.value.dates && summaryData.value.dates.length > 0) {
  //   headers.push({ date: 'total', display: '总计', isTotal: true })
  // }

  // 再添加日期列
  const dates = summaryData.value.dates.map((d) => ({
    date: d,
    display: formatDateDisplay(d),
    isTotal: false
  }))

  headers.push(...dates)
  return headers
})

// 方法
const handleDateRangeChange = (value: any, type: string) => {
  if (type === 'summary') {
    // 验证日期范围不能超过15天
    if (value && value.length === 2) {
      const startDate = dayjs(value[0])
      const endDate = dayjs(value[1])
      const daysDiff = endDate.diff(startDate, 'day')

      if (daysDiff > 15) {
        ElMessage.warning('日期范围不能超过15天，请重新选择')
        return
      }
    }
    summaryFilters.dateRange = value
  }
}

const disabledDate = (time: Date) => {
  // 禁用未来日期
  const now = new Date()
  now.setHours(0, 0, 0, 0) //应产品25.7.14要求系统不做15分钟拉去一次数据，只做一天拉去一次，禁用选择今天
  if (time.getTime() >= now.getTime()) {
    return true
  }

  // 禁用2025年6月份之前的日期
  const minDate = new Date(2025, 5, 1) // 2025年6月1日 (月份从0开始，5表示6月)
  return time.getTime() < minDate.getTime()
}

const searchSummaryData = () => {
  // 验证日期范围是否已选择
  if (!summaryFilters.dateRange || summaryFilters.dateRange.length !== 2) {
    ElMessage.warning('请选择日期范围后再进行查询')
    return
  }

  // 验证日期范围不能超过15天
  const startDate = dayjs(summaryFilters.dateRange[0])
  const endDate = dayjs(summaryFilters.dateRange[1])
  const daysDiff = endDate.diff(startDate, 'day')

  if (daysDiff > 15) {
    ElMessage.warning('日期范围不能超过15天，请重新选择')
    return
  }

  loadData()
}

const resetSummaryFilters = () => {
  Object.assign(summaryFilters, {
    currencyCode: '',
    sidQuerySet: [], // 店铺ID
    shopNames: [], // 店铺名称
    countries: [], // 国家代码
    countrysList: [], // 国家名称
    principalNamesQuerySet: [], // 负责人ID
    dateRange: [],
    pageNo: 1,
    pageSize: 50
  })
  // 重置AccountShopSelector组件
  if (accountShopSelectorRef.value) {
    accountShopSelectorRef.value.resetSelection('account')
    accountShopSelectorRef.value.resetSelection('shop')
    accountShopSelectorRef.value.resetSelection('country')
  }
  loadData()
}

const exportSummaryData = () => {
  ElMessage.success('导出功能开发中...')
}

const formatCurrency = (value) => {
  if (value === 'loading') return ''
  if (value === null || value === undefined || value === '') return '-'
  return value
}

const formatQuantity = (value) => {
  if (value === 'loading') return ''
  if (value === null || value === undefined || value === '') return '-'
  const num = parseInt(value)
  if (isNaN(num)) return value
  return num.toString()
}

const formatRatio = (value) => {
  if (value === null || value === undefined || value === '') return ''
  return value
}

const formatDateDisplay = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}-${day}`
}

const getDateValue = (date, key) => {
  if (date === 'total') {
    // 总计数据加载中
    if (summaryData.value.total === null) {
      return 'loading'
    }
    return summaryData.value.total ? summaryData.value.total[key] : null
  }
  const source = summaryData.value.daily?.[date]
  return source ? source[key] : null
}

const calculateRatio = (date, key) => {
  // 根据需要计算比率，这里暂时返回null
  return null
}

// 获取管理费详情数据
const getManagementFeeDetails = (date) => {
  const managementFeeTotal = getDateValue(date, 'managementFee') || 0
  const everyManagementFeeData = getDateValue(date, 'everyManagementFee')

  if (!managementFeeTotal || managementFeeTotal === 0) {
    return {
      laborCost: 0,
      designCost: 0,
      venueCost: 0,
      otherCost: 0,
      vat: 0
    }
  }

  // 如果有everyManagementFee数据，使用该数据
  if (everyManagementFeeData && typeof everyManagementFeeData === 'object') {
    return {
      laborCost: everyManagementFeeData[0] || 0, // 人工费
      designCost: everyManagementFeeData[1] || 0, // 设计费
      venueCost: everyManagementFeeData[2] || 0, // 场地费
      otherCost: everyManagementFeeData[3] || 0, // 其它均摊费
      vat: everyManagementFeeData[4] || 0 // VAT
    }
  }

  // 如果没有详细数据，按比例分配管理费
  const laborCost = Math.round(managementFeeTotal * 0.45) // 人工费45%
  const designCost = Math.round(managementFeeTotal * 0.15) // 设计费15%
  const venueCost = Math.round(managementFeeTotal * 0.2) // 场地费20%
  const vat = Math.round(managementFeeTotal * 0.08) // VAT 8%
  const otherCost = managementFeeTotal - laborCost - designCost - venueCost - vat // 其它均摊费

  return {
    laborCost,
    designCost,
    venueCost,
    otherCost,
    vat
  }
}

// 列宽调整逻辑
const columnWidths = ref<Record<string, number>>({})
const isResizing = ref(false)
const resizingColumn = ref('')
const startX = ref(0)
const startWidth = ref(0)

const initializeColumnWidths = () => {
  const initialWidths = {}
  const dateHeaders = listDateHeaders.value
  initialWidths['item-name'] = 200 // 项目列默认宽度
  dateHeaders.forEach((header) => {
    initialWidths[header.date] = 150 // 日期列默认宽度
  })
  columnWidths.value = initialWidths
}

const getColumnWidth = (columnKey) => {
  return columnWidths.value[columnKey] || 150 // 默认宽度
}

const startResize = (event, columnKey) => {
  isResizing.value = true
  resizingColumn.value = columnKey
  startX.value = event.clientX
  startWidth.value = getColumnWidth(columnKey)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event) => {
  if (!isResizing.value) return
  const diffX = event.clientX - startX.value
  const newWidth = startWidth.value + diffX
  if (newWidth > 50) {
    // 最小宽度
    columnWidths.value[resizingColumn.value] = newWidth
  }
}

const handleMouseUp = () => {
  isResizing.value = false
  resizingColumn.value = ''
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const loadData = async () => {
  loading.value = true
  totalLoading.value = true
  try {
    const [begin, end] = summaryFilters.dateRange || []

    const params = {
      ...summaryFilters,
      type: 3, // 每日汇总
      beginOrderDate: begin ? dayjs(begin).format('YYYY-MM-DD') : undefined, //去掉时分秒
      endOrderDate: end ? dayjs(end).format('YYYY-MM-DD') : undefined, //去掉时分秒
      pageNo: summaryFilters.pageNo,
      pageSize: summaryFilters.pageSize
    }
    delete params.dateRange

    // 先加载列表数据，优先渲染
    const pageRes = await getDailyReportPage(params)
    const apiData = pageRes.list

    if (!apiData || apiData.length === 0) {
      summaryData.value = {}
      profitItems.value = []
      costItems.value = []
      initializeColumnWidths()
      loading.value = false
      totalLoading.value = false
      return
    }

    // 重置数据项
    profitItems.value = []
    costItems.value = []

    const newDailyData = {}
    const dates = []

    // 从第一条数据的extraData中获取利润字段和成本字段
    if (apiData[0].extraData) {
      const extraDataKeys = Object.keys(apiData[0].extraData)

      // 定义利润相关字段
      const profitKeywords = ['利润', '毛利']
      extraDataKeys.forEach((key) => {
        if (profitKeywords.some((keyword) => key.includes(keyword))) {
          profitItems.value.push({
            key: key,
            label: key,
            type: 'profit'
          })
        } else {
          costItems.value.push({
            key: key,
            label: key,
            type: 'cost'
          })
        }
      })
    }

    // 处理列表数据
    apiData.forEach((item) => {
      if (item.orderDate) {
        dates.push(item.orderDate)
        const dayData = {}

        // 从extraData中获取数据
        if (item.extraData) {
          Object.keys(item.extraData).forEach((key) => {
            const value = item.extraData[key] || 0
            dayData[key] = value
          })
        }

        // 从主数据中获取
        dayData['amount'] = item.amount || 0
        dayData['volume'] = parseInt(item.volume) || 0
        dayData['purchaseCosts'] = Math.abs(item.purchaseCosts || 0) // 取绝对值显示
        dayData['sellingFee'] = Math.abs(item.sellingFee || 0) // 取绝对值显示
        dayData['fulfillmentFee'] = Math.abs(item.fulfillmentFee || 0) // 取绝对值显示
        dayData['managementFee'] = item.managementFee || 0

        newDailyData[item.orderDate] = dayData
      }
    })

    // 先渲染列表数据，总计数据设为null表示加载中
    summaryData.value = {
      dates: dates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime()), // 日期升序
      daily: newDailyData,
      total: null // 总计数据加载中
    }

    initializeColumnWidths()
    loading.value = false // 列表数据加载完成

    // 异步加载总计数据
    loadTotalData(params)
  } catch (error) {
    ElMessage.error('加载汇总数据失败')
    console.error('Failed to load summary data:', error)
    summaryData.value = {}
    loading.value = false
    totalLoading.value = false
  }
}

// 异步加载总计数据
const loadTotalData = async (params: any) => {
  try {
    const statisticRes = await getDailyReportStatistic({ ...params })
    const statisticData = statisticRes
    const totalData = {}

    // 使用统计接口返回的总计数据
    if (statisticData) {
      // 从统计数据中获取总计
      if (statisticData.extraData) {
        Object.keys(statisticData.extraData).forEach((key) => {
          totalData[key] = statisticData.extraData[key] || 0
        })
      }
      // 从主数据中获取总计
      totalData['amount'] = statisticData.amount || 0
      totalData['volume'] = parseInt(statisticData.volume) || 0
      totalData['purchaseCosts'] = Math.abs(statisticData.purchaseCosts || 0)
      totalData['sellingFee'] = Math.abs(statisticData.sellingFee || 0)
      totalData['fulfillmentFee'] = Math.abs(statisticData.fulfillmentFee || 0)
      totalData['managementFee'] = statisticData.managementFee || 0
    } else {
      // 如果没有统计数据，则初始化为0
      if (summaryData.value.daily && Object.keys(summaryData.value.daily).length > 0) {
        const firstDayData = Object.values(summaryData.value.daily)[0] as any
        if (firstDayData) {
          Object.keys(firstDayData).forEach((key) => {
            if (
              ![
                'amount',
                'volume',
                'purchaseCosts',
                'sellingFee',
                'fulfillmentFee',
                'managementFee'
              ].includes(key)
            ) {
              totalData[key] = 0
            }
          })
        }
      }
      totalData['amount'] = 0
      totalData['volume'] = 0
      totalData['purchaseCosts'] = 0
      totalData['sellingFee'] = 0
      totalData['fulfillmentFee'] = 0
      totalData['managementFee'] = 0
    }

    // 更新总计数据
    if (summaryData.value) {
      summaryData.value.total = totalData
    }
  } catch (error) {
    console.error('Failed to load total data:', error)
    // 总计数据加载失败时，设置为空对象
    if (summaryData.value) {
      summaryData.value.total = {}
    }
  } finally {
    totalLoading.value = false
  }
}

onMounted(() => {
  getPrincipalListFc()
  loadCurrencyOptions()
  loadData()
})
</script>

<style lang="scss" scoped>
// 响应式设计
@media (width <= 768px) {
  .horizontal-scroll-container {
    padding-bottom: 20px;
  }

  .item-name-header,
  .date-header,
  .item-name,
  .item-value {
    padding: 8px 6px;
    font-size: 12px;
  }

  .group-title {
    padding: 8px 6px;
    font-size: 12px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.daily-summary-display {
  width: 100%;
  overflow: hidden;
}

.horizontal-scroll-container {
  padding-bottom: 15px; /* for scrollbar */
  overflow-x: auto;
}

.horizontal-table-wrapper {
  display: inline-block;
  min-width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.table-header {
  display: flex;
  font-weight: bold;
  color: #909399;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.item-name-header,
.date-header {
  position: relative; /* For resizer */
  padding: 8px 6px;
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  border-right: 1px solid #ebeef5;
}

.item-name-header {
  position: sticky;
  left: 0;
  z-index: 2;
  background-color: #f5f7fa;
}

.date-headers {
  display: flex;
}

.total-column {
  font-weight: bold !important;
  background-color: #f0f9eb;
  border-right: 2px solid #007bff !important;
}

.data-rows {
  /* styles for data rows container */
}

.data-group {
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.group-header {
  display: flex;
  background-color: #fafafa;
}

.group-title {
  position: sticky;
  left: 0;
  z-index: 1;
  padding: 10px;
  font-size: 12px;
  font-weight: bold;
  color: #606266;
  background-color: #fafafa;
  border-right: 1px solid #ebeef5;
}

.data-row {
  display: flex;

  &:not(:last-child) {
    border-bottom: 1px solid #ebeef5;
  }
}

.item-name {
  position: sticky;
  left: 0;
  z-index: 1;
  padding: 10px;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
}

.profit-item {
  // font-weight: 500;
  color: #67c23a;
  border-left: 4px solid #27ae60;
}

.revenue-item {
  // font-weight: 500;
  color: #409eff;
  border-left: 4px solid #3498db;
}

.cost-item {
  // font-weight: 500;
  color: #f56c6c;
  border-left: 4px solid #f39c12;
}

.item-values {
  display: flex;
}

.item-value {
  display: flex;
  padding: 10px;
  font-size: 12px;
  // text-align: right;
  white-space: nowrap;
  border-right: 1px solid #ebeef5;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.two-line-value {
  .value-amount {
    font-size: 12px;
    // font-weight: 500;
    color: #303133;
  }

  .value-ratio {
    margin-top: 2px;
    font-size: 12px;
    color: #909399;
  }
}

.profit .value-amount {
  color: #67c23a;
}

.revenue .value-amount {
  color: #409eff;
}

.cost .value-amount {
  color: #f56c6c;
}

.management-fee-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.management-fee-clickable {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.inline-arrow {
  margin-left: 4px;
  color: #409eff;
  cursor: pointer;

  &:hover {
    color: #66b1ff;
  }
}

.fee-detail-arrow {
  font-size: 12px;
}

.management-fee-detail-content {
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f2f5;

    &:last-child {
      border-bottom: none;
    }

    &.total-row {
      padding-top: 12px;
      margin-top: 8px;
      font-weight: 600;
      border-top: 2px solid #e4e7ed;

      .label,
      .value {
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
      }
    }

    .label {
      font-size: 13px;
      color: #606266;
    }

    .value {
      font-size: 13px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.column-resizer {
  position: absolute;
  top: 0;
  right: -5px;
  z-index: 10;
  width: 10px;
  height: 100%;
  cursor: col-resize;

  &:hover {
    background-color: rgb(64 158 255 / 20%);
  }
}

.loading-container,
.empty-container {
  padding: 20px;
  text-align: center;
}
</style>
