<template>
  <div class="daily-profit-container">
    <!-- 数据说明与提示 -->
    <div v-if="showDataNotice" class="data-notice-container">
      <div class="notice-header">
        <div class="notice-title">
          <el-icon class="notice-icon"><InfoFilled /></el-icon>
          <span>数据说明与提示</span>
        </div>
        <Icon class="close-icon" @click="closeNotice" icon="ep:close" />
      </div>
      <div class="notice-content">
        <div class="notice-item">
          <span class="item-number">1.</span>
          <span class="item-label">报表性质：</span>
          <span class="item-text"
            >本报表为
            <strong>利润预估</strong>
            数据，用于运营参考。最终精准利润请以【月度利润报表】为准。</span
          >
        </div>
        <div class="notice-item">
          <span class="item-number">2.</span>
          <span class="item-label">仓储费说明：</span>
          <span class="item-text"
            >仓储费取自领星【统计】-【结算利润】的"FBA仓储费"。</span
          >
        </div>
        <div class="notice-item">
          <span class="item-number">3.</span>
          <span class="item-label">管理费用：</span>
          <span class="item-text"
            >如果没有设置，则管理费用项为0。</span
          >
        </div>
        <div class="notice-item">
          <span class="item-number">4.</span>
          <span class="item-label">数据范围：</span>
          <span class="item-text"
            >当前时间范围的最长跨度为本月的时间区间。如果此时是月初，那么点击上周的话，时间跨度涉及到上个月底。另考虑当天数据的不确定性，目前不开放当天订单利润的预估。</span
          >
        </div>
        <div class="notice-item">
          <span class="item-number">5.</span>
          <span class="item-label"></span>
          <span class="item-text"
            >我们正在努力积累您的历史数据，未来将逐步开放更长的时间跨度查询。</span
          >
        </div>
      </div>
      <div class="notice-footer">
        <el-checkbox v-model="dontShowAgain" @change="handleDontShowAgain"> 不再提示 </el-checkbox>
      </div>
    </div>

    <!-- 主要内容国家 -->
    <div class="main-content">
      <!-- 维度选项卡 -->
      <el-tabs v-model="activeDimension" type="card" @tab-change="handleDimensionChange">
        <!-- 父ASIN维度 -->
        <el-tab-pane label="按父ASIN" name="asin">
          <DynamicDimension v-if="activeDimension === 'asin'" key="asin" dimension="asin" />
        </el-tab-pane>
        <el-tab-pane label="按店铺" name="store">
          <DynamicDimension v-if="activeDimension === 'store'" key="store" dimension="store" />
        </el-tab-pane>
        <el-tab-pane label="按负责人" name="manager">
          <DynamicDimension v-if="activeDimension === 'manager'" key="manager" dimension="manager" />
        </el-tab-pane>
        <el-tab-pane label="每日汇总" name="DailySummary">
          <DailySummary v-if="activeDimension === 'DailySummary'" key="dailySummary" dimension="dailySummary" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import { ElTabPane } from 'element-plus'
import { nextTick, onMounted, ref } from 'vue'
import DailySummary from './common/DailySummary.vue'
import DynamicDimension from './common/DynamicDimension.vue'

defineOptions({ name: 'ProfitReportDaily' })
//
// 响应式数据
const showDataNotice = ref(true)
const dontShowAgain = ref(false)
const activeDimension = ref('asin')

// 方法
const closeNotice = () => {
  showDataNotice.value = false
}

const handleDontShowAgain = (value: boolean) => {
  if (value) {
    localStorage.setItem('hideDailyProfitNotice', 'true')
  } else {
    localStorage.removeItem('hideDailyProfitNotice')
  }
}

const handleDimensionChange = (dimension: string) => {
  activeDimension.value = dimension

  // 重置页面滚动条到顶部
  resetPageScroll()

  // 重置表格滚动条
  resetTableScroll()
}

// 重置页面滚动条到顶部
const resetPageScroll = () => {
  // 重置页面滚动条
  window.scrollTo(0, 0)
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0
}

// 重置表格滚动条
const resetTableScroll = () => {
  // 使用nextTick确保DOM更新后再重置滚动条
  nextTick(() => {
    // 重置所有表格的水平滚动条
    const tableWrappers = document.querySelectorAll('.el-table__body-wrapper')
    tableWrappers.forEach((wrapper) => {
      wrapper.scrollLeft = 0
    })

    // 重置所有表格容器的滚动条
    const tableContainers = document.querySelectorAll('.table-card .el-table')
    tableContainers.forEach((container) => {
      const bodyWrapper = container.querySelector('.el-table__body-wrapper')
      const headerWrapper = container.querySelector('.el-table__header-wrapper')

      if (bodyWrapper) {
        bodyWrapper.scrollLeft = 0
        bodyWrapper.scrollTop = 0
      }
      if (headerWrapper) {
        headerWrapper.scrollLeft = 0
      }
    })
  })
}

// 生命周期
onMounted(() => {
  // 检查是否显示提示
  const hideNotice = localStorage.getItem('hideDailyProfitNotice')
  if (hideNotice === 'true') {
    showDataNotice.value = false
  }
})
</script>

<style lang="scss" scoped>
/* 响应式设计 */
@media (width <= 768px) {
  .data-notice-container {
    margin-bottom: 16px;
  }

  .notice-header {
    padding: 12px 16px 8px;
  }

  .notice-title {
    font-size: 14px;
  }

  .notice-content {
    padding: 16px;
  }

  .notice-item {
    margin-bottom: 12px;
  }

  .item-text {
    font-size: 13px;
  }

  .notice-footer {
    padding: 10px 16px 12px;
  }
}

.daily-profit-container {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

/* 数据说明提示样式 */
.data-notice-container {
  margin-bottom: 20px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
}

.notice-header {
  display: flex;
  padding: 16px 20px 12px;
  color: white;
  background: #4a90e2;
  justify-content: space-between;
  align-items: center;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.notice-icon {
  font-size: 18px;
  color: #fff;
}

.close-icon {
  padding: 4px;
  // font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-icon:hover {
  background-color: rgb(255 255 255 / 10%);
}

.notice-content {
  padding: 20px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  line-height: 1.6;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.item-number {
  margin-right: 8px;
  font-weight: 600;
  color: #4a90e2;
  flex-shrink: 0;
}

.item-label {
  margin-right: 8px;
  font-weight: 600;
  color: #4a90e2;
  flex-shrink: 0;
}

.item-text {
  font-size: 14px;
  color: #495057;
}

.item-text strong {
  font-weight: 600;
  color: #dc3545;
}

.notice-footer {
  padding: 12px 20px 16px;
  background: #fff;
  border-top: 1px solid #e9ecef;
}

.notice-footer .el-checkbox {
  font-size: 13px;
}

.notice-footer .el-checkbox__label {
  font-weight: 500;
  color: #6c757d;
}

.main-content {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
}

.filter-section {
  padding: 16px;
  margin-bottom: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
}

.combined-search {
  display: flex;

  .search-type-select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .search-input {
    :deep(.el-input__wrapper) {
      border-left: none;
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
  }
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.metrics-section {
  margin-bottom: 20px;
}

.metrics-header {
  display: flex;
  padding: 12px 16px;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

.metrics-title {
  font-size: 16px;
  font-weight: 600;
}

.collapse-icon {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.metrics-content {
  padding: 20px;
  background: white;
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

.core-metrics {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.metric-card {
  min-width: 140px;
  padding: 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.profit {
    border-left: 4px solid #67c23a;
  }

  &.revenue {
    border-left: 4px solid #409eff;
  }

  &.orders {
    border-left: 4px solid #e6a23c;
  }

  &.health {
    border-left: 4px solid #f56c6c;
  }
}

.metric-main {
  .metric-value {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 700;
    color: #303133;
  }

  .metric-label {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
  }
}

.calculator-symbol {
  display: flex;
  min-width: 30px;
  font-size: 24px;
  font-weight: 700;
  color: #909399;
  align-items: center;
  justify-content: center;
}

.table-container {
  overflow: hidden;
  background: white;
  border-radius: 8px;
}

.table-toolbar {
  position: relative;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.table-header-groups {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 100%;
  pointer-events: none;
}

.group-label {
  position: absolute;
  top: 50%;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 4px;
  transform: translateY(-50%);

  &.profit-label {
    color: #67c23a;
    background: rgb(103 194 58 / 10%);
  }

  &.revenue-label {
    color: #409eff;
    background: rgb(64 158 255 / 10%);
  }

  &.cost-label {
    color: #e6a23c;
    background: rgb(230 162 60 / 10%);
  }

  &.management-label {
    color: #f56c6c;
    background: rgb(245 108 108 / 10%);
  }
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.profit) {
  font-weight: 600;
  color: #67c23a;
}

:deep(.loss) {
  font-weight: 600;
  color: #f56c6c;
}

.product-image {
  width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 4px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.product-info {
  .product-name {
    margin-bottom: 4px;
    overflow: hidden;
    font-weight: 500;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .product-sku {
    overflow: hidden;
    font-size: 12px;
    color: #909399;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.store-country-cell {
  .store-name {
    margin-bottom: 4px;
    font-weight: 500;
  }

  .country-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;

    .country-flag {
      font-size: 14px;
    }
  }
}

.asin-link {
  color: #409eff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
</style>
