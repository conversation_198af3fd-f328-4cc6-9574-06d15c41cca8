<template>
  <div class="monthly-dimension-container">
    <!-- 筛选条件 -->
    <div class="filter-card">
      <el-form :model="filters" :inline="true" style="display: flex; flex-wrap: wrap">
        <el-form-item label="币种">
          <el-select v-model="filters.currencyCode" class="!w-140px" placeholder="请选择币种">
            <el-option
              v-for="currency in currencyOptions"
              :key="currency.value"
              :label="currency.label"
              :value="currency.value"
            />
          </el-select>
        </el-form-item>
        <!-- 账户店铺选择器 -->
        <AccountShopSelector
          ref="accountShopSelectorRef"
          :show-account="false"
          v-model:shop-values="filters.sidQuerySet"
          v-model:country-values="filters.countrysList"
          v-model:country-names="filters.countries"
        />

        <el-form-item label="负责人" prop="principalNamesQuerySet" v-if="dimension !== 'store'">
          <RcInputSelect
            v-model="filters.principalNamesQuerySet"
            class="!w-240px"
            :options="principalOptions"
          />
        </el-form-item>

        <el-form-item label-width="0" v-if="dimension === 'asin'">
          <el-input
            v-model="filters.searchValue"
            placeholder="请输入搜索内容"
            class="input-with-select w-300px"
            clearable
          >
            <template #prepend>
              <el-select v-model="filters.searchType" style="width: 115px">
                <el-option label="父ASIN" value="parentAsin" />
                <el-option label="ASIN" value="asin" />
                <el-option label="SKU" value="sku" />
                <el-option label="MSKU" value="msku" />
                <el-option label="品名" value="itemName" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="时间范围">
          <MonthlyDateRangePicker
            v-model="filters.dateRange"
            v-model:month-value="filters.monthRange"
            v-model:week-value="filters.weekRange"
            :show-time-type-selector="true"
            :default-time-type="filters.timeType"
            :enable-default-range="true"
            :date-shortcuts="dateShortcuts"
            :disabled-date="disabledDate"
            @change="(value) => handleDateRangeChange(value, dimension)"
            @month-change="handleMonthRangeChange"
            @week-change="handleWeekRangeChange"
            @time-type-change="handleTimeTypeChange"
          />
        </el-form-item>
        <el-form-item label-width="0">
          <el-button type="primary" @click="searchData">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
          <!-- <el-button @click="exportData">导出</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <!-- ASIN数据统计卡片 -->
    <!-- <div class="summary-card">
      <div class="metrics-section">
        <div class="metrics-header asin-header" @click="toggleAsinMetrics">
          <span class="metrics-title asin-title">数据概览</span>
          <el-icon class="collapse-icon" :class="{ collapsed: !asinMetricsVisible }">
            <ArrowDown />
          </el-icon>
        </div>

        <el-collapse-transition>
          <div v-show="asinMetricsVisible" class="metrics-content">
            <div class="core-metrics">
              <div class="metric-card profit">
                <div class="metric-main">
                  <div class="metric-value">{{ formatNumber(asinStats.totalProfit) }}</div>
                  <div class="metric-label">毛利润</div>
                </div>
              </div>

              <div class="calculator-symbol">=</div>

              <div class="metric-card revenue">
                <div class="metric-main">
                  <div class="metric-value">{{ formatNumber(asinStats.totalRevenue) }}</div>
                  <div class="metric-label">销售收入</div>
                </div>
              </div>

              <div class="calculator-symbol">-</div>

              <div class="metric-card orders">
                <div class="metric-main">
                  <div class="metric-value">{{ formatNumber(asinStats.totalExpenseCost) }}</div>
                  <div class="metric-label">支出成本</div>
                </div>
              </div>

              <div class="calculator-symbol">-</div>

              <div class="metric-card health">
                <div class="metric-main">
                  <div class="metric-value">{{ formatNumber(asinStats.totalManagementFee) }}</div>
                  <div class="metric-label">管理费</div>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div> -->
    <!-- ASIN数据表格 -->
    <div class="table-card">
      <!-- 表格工具栏 -->
      <div class="table-toolbar" v-if="!loading">
        <div class="table-toolbar-left">
          <!-- 可以添加其他工具按钮 -->
        </div>
        <div class="table-toolbar-right">
          <!-- 列配置组件 -->
          <TableColumnConfig
            :columns="allTableColumns"
            v-model="visibleTableColumns"
            :type="getTableType()"
            :table-data="tableData"
            @column-config-change="handleColumnConfigChange"
            btnText="列配置"
          />
        </div>
      </div>

      <el-table
        :data="tableData"
        v-loading="loading"
        :height="asinTableHeight"
        border
        stripe
        style="width: 100%"
        :scroll-x="true"
        :lazy="true"
        :row-key="(row) => row.msku || row.asin || Math.random()"
        :default-sort="{ prop: 'grossProfit', order: 'descending' }"
        @sort-change="handleSortChange"
      >
        <el-table-column
          v-for="column in visibleTableColumns"
          :key="column.field || column.prop"
          :prop="column.field || column.prop"
          :label="column.label"
          :fixed="column.fixed"
          :sortable="column.sortable !== false"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
          :min-width="getColumnWidth(column.field || column.prop)"
          :width="column.width"
        >
          <!-- :header-class-name="column.headerClassName"
          :class-name="column.headerClassName" -->
          <template #header>
            <div class="column-header">
              <span>{{ column.label }}</span>
              <el-tooltip
                v-if="columnTooltips[column.label] || columnTooltips[column.field || column.prop]"
                :content="
                  columnTooltips[column.label] || columnTooltips[column.field || column.prop]
                "
                placement="top"
                effect="dark"
              >
                <el-icon class="tooltip-icon">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <!-- 产品图片特殊渲染 -->
          <template #default="scope" v-if="(column.field || column.prop) === 'smallImageUrl'">
            <div class="product-image">
              <img
                v-if="scope.row.smallImageUrl"
                :src="scope.row.smallImageUrl"
                :alt="scope.row.itemName || 'Product Image'"
                @mouseenter="showImagePreview(scope.row.smallImageUrl, $event)"
                @mouseleave="hideImagePreview"
                style="cursor: pointer"
                loading="lazy"
              />
              <span v-else>-</span>
            </div>
          </template>
          <!-- 店铺/国家字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'shopCountry'">
            <div v-if="scope.row.shopName && scope.row.country" class="flex flex-col gap-6px">
              <div>{{ scope.row.shopName }}</div>
              <div>{{ scope.row.country }}</div>
            </div>
            <span v-else>{{ scope.row.storeName || scope.row.country || '-' }}</span>
          </template>
          <!-- 品名/SKU字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'skuInfo'">
            <div v-if="scope.row.localSku && scope.row.localName" class="flex flex-col gap-6px">
              <div>{{ scope.row.localName }}</div>
              <div v-clipboard="scope.row.localSku">{{ scope.row.localSku }}</div>
            </div>
            <span v-else>{{ scope.row.localSku || scope.row.localName || '-' }}</span>
          </template>
          <!-- ASIN字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'asin'">
            <div v-if="scope.row.asin" style="display: flex; align-items: center; gap: 4px">
              <div v-clipboard="scope.row.asin">
                <a
                  :href="scope.row.asinUrl"
                  target="_blank"
                  style="color: #409eff; text-decoration: none; flex: 1"
                >
                  {{ scope.row.asin }}
                </a>
              </div>
            </div>
            <span v-else>-</span>
          </template>
          <!-- 父ASIN字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'parentAsin'">
            <div v-if="scope.row.parentAsin" style="display: flex; align-items: center; gap: 4px">
              <div v-clipboard="scope.row.asin">
                {{ scope.row.parentAsin }}
              </div>
            </div>
            <span v-else>-</span>
          </template>
          <!-- MSKU字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'msku'">
            <el-text v-clipboard="scope.row.msku" class="w-100%" truncated>
              {{ scope.row.msku || '-' }}
            </el-text>
          </template>
          <!-- SKU字段特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'sku'">
            <el-text v-clipboard="scope.row.sku" class="w-100%" truncated>
              {{ scope.row.sku || '-' }}
            </el-text>
          </template>
          <!-- 管理费详情特殊渲染 -->
          <template #default="scope" v-else-if="(column.field || column.prop) === 'managementFee'">
            <span
              v-if="scope.row.managementFee && scope.row.managementFee > 0"
              class="management-fee-value"
            >
              <el-popover placement="bottom" title="管理费分摊详情" :width="280" trigger="click">
                <template #reference>
                  <span
                    class="clickable-fee"
                    :class="{ 'negative-amount': scope.row.managementFee < 0 }"
                  >
                    {{ scope.row.managementFee }}
                    <el-icon class="fee-detail-arrow inline-arrow">
                      <ArrowDown />
                    </el-icon>
                  </span>
                </template>
                <div class="management-fee-detail-content">
                  <div class="detail-row">
                    <span class="label">人工费:</span>
                    <span class="value">{{ getManagementFeeDetails(scope.row).laborCost }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">设计费:</span>
                    <span class="value">{{ getManagementFeeDetails(scope.row).designCost }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">场地费:</span>
                    <span class="value">{{ getManagementFeeDetails(scope.row).venueCost }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">其它均摊费:</span>
                    <span class="value">{{ getManagementFeeDetails(scope.row).otherCost }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">VAT:</span>
                    <span class="value">{{ getManagementFeeDetails(scope.row).vat }}</span>
                  </div>
                  <div class="detail-row total-row">
                    <span class="label">总计:</span>
                    <span class="value">{{ scope.row.managementFee }}</span>
                  </div>
                </div>
              </el-popover>
            </span>
            <span v-else :class="{ 'negative-amount': scope.row.managementFee < 0 }">
              {{ scope.row.managementFee }}
            </span>
          </template>
          <!-- 其他金额字段格式化 -->
          <template
            #default="scope"
            v-else-if="['grossProfit', 'totalSalesAmount'].includes(column.field || column.prop)"
          >
            <span :class="{ 'negative-amount': scope.row[column.field || column.prop] < 0 }">
              {{ scope.row[column.field || column.prop] }}
            </span>
          </template>
          <!-- 销量字段格式化 -->
          <template
            #default="scope"
            v-else-if="(column.field || column.prop) === 'totalSalesQuantity'"
          >
            {{ scope.row[column.field || column.prop] || 0 }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="filters.pageNo"
          v-model:page-size="filters.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal/index'
import { getMonthlyReportPage } from '@/api/profit/report'
import AccountShopSelector from '@/components/AccountShopSelector/index.vue'
import TableColumnConfig from '@/components/Table/src/TableColumnConfig.vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { ArrowDown, QuestionFilled } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, defineProps, nextTick, onMounted, reactive, ref, watch } from 'vue'
import MonthlyDateRangePicker from '../components/MonthlyDateRangePicker.vue'

defineOptions({ name: 'ProfitReportMonthly' })

const props = defineProps({
  dimension: {
    type: String,
    required: true,
    validator: (value: string) => ['asin', 'store', 'manager', 'MonthlySummary'].includes(value)
  }
})

// 响应式数据
const loading = ref(false)
const asinMetricsVisible = ref(false)

// 筛选条件
const filters = reactive({
  currencyCode: '原币种',
  sidQuerySet: [], // 店铺ID
  shopNames: [], // 店铺名称
  countries: [], // 国家代码
  countrysList: [], // 国家名称
  principalNamesQuerySet: [], // 负责人ID
  timeType: 'day', // 时间类型：day, month, week
  dateRange: [
    dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  ], //默认前一天
  monthRange: [], // 月份范围
  weekRange: [], // 周范围
  searchType: 'parentAsin',
  searchValue: '',
  pageNo: 1,
  pageSize: 50,
  sortingFields: [] as { field: string; order: string }[] // 排序字段
})

// AccountShopSelector组件引用
const accountShopSelectorRef = ref()

// 选项数据
const principalOptions = ref([])

const currencyOptions = ref([])

// 加载币种选项
const loadCurrencyOptions = async () => {
  try {
    const response = getDictOptions(DICT_TYPE.CURRENCYNAME_CURRENCYNAMECODE)
    currencyOptions.value = response || []
  } catch (error) {
    console.error('加载币种选项失败:', error)
  }
}

// 获取负责人列表
const getPrincipalListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getPrincipalList()
    if (res) {
      principalOptions.value = res.map((item) => ({
        label: item.principalName,
        value: item.principalName
      }))
    }
  } catch (error) {
    console.error('获取负责人列表失败', error)
  }
}

// 列tooltip映射
const columnTooltips = {
  经营利润: '经营利润 = 收入 - 总投入 - 管理费',
  'extraData.经营利润': '经营利润 = 收入 - 总投入 - 管理费',
  实际销量: '实际销量 = 销量 - 退货量',
  'extraData.实际销量': '实际销量 = 销量 - 退货量',
  收入: '收入 = 销售额 + 买家运费 + 促销折扣 + FBA库存赔偿 + COD + 其他收入 + 收入退款额 + 费用退款额 + 销售税 + 销售税退款额 + 市场税 + 市场税退款额',
  'extraData.收入':
    '收入 = 销售额 + 买家运费 + 促销折扣 + FBA库存赔偿 + COD + 其他收入 + 收入退款额 + 费用退款额 + 销售税 + 销售税退款额 + 市场税 + 市场税退款额',
  其他平台支出费用:
    '其他平台支出费用 = 其他订单费用 + 推广费 + 合仓费（入库配置费） + FBA国际物流货运费 + 调整费用 + 平台其他费',
  'extraData.其他平台支出费用':
    '其他平台支出费用 = 其他订单费用 + 推广费 + 合仓费（入库配置费） + FBA国际物流货运费 + 调整费用 + 平台其他费',
  平台支出: '平台支出 = 平台费 + 广告费 + FBA发货费 + FBA仓储费 + 其他平台支出费用',
  'extraData.平台支出': '平台支出 = 平台费 + 广告费 + FBA发货费 + FBA仓储费 + 其他平台支出费用',
  平均采购成本: '平均采购成本 = 采购成本 / 实际销量',
  'extraData.平均采购成本': '平均采购成本 = 采购成本 / 实际销量',
  平均头程成本: '平均头程成本 = 头程成本 / 实际销量',
  'extraData.平均头程成本': '平均头程成本 = 头程成本 / 实际销量',
  包材: '根据日报利润模版公式计算，默认为：包材费 = 销量 * ￥2',
  'extraData.包材': '根据日报利润模版公式计算，默认为：包材费 = 销量 * ￥2',
  服务商扣点: '根据日报利润模版公式计算',
  'extraData.服务商扣点': '根据日报利润模版公式计算',
  店铺租金: '根据日报利润模版公式计算',
  'extraData.店铺租金': '根据日报利润模版公式计算',
  总投入: '总投入 = 平台支出 + 采购成本 + 头程成本 + 包材 + 服务商扣点 + 店铺租金',
  'extraData.总投入': '总投入 = 采购成本 + 头程成本 + 包材',
  毛利润: '毛利润 = 收入 - 总投入',
  grossProfit: '毛利润 = 收入 - 总投入',
  管理费: `无论选择哪种分摊方式，系统首先会计算出 "某项费用的平均每日管理费"。</br>
- 公式:</br>
- 某项费用平均每日管理费 = 当前月份某项费用的总额 / 当前月份的总天数</br>
- 示例:</br>
  - 假设当前是 6月 (共30天)。</br>
  - 从上面的费用池表格中得知，6月的 某项费用的管理费 为 ￥68,000。</br>
  - 则，平均每日某项管理费 = 68,000 / 30 = ￥2,266.67。</br>
按销售额占比分摊: 卖得越多的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 "当日总销售额"。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销售额 / 当日总销售额)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的"当日总销售额"为 ￥150,000。</br>
  - 计算ASIN-A:</br>
    - ASIN-A 当日销售额 = ￥15,000。</br>
    - ASIN-A 应分摊的某项管理费 = 2,266.67 * (15,000 / 150,000) = 2,266.67 * 10% = ￥226.67。</br>
  - 计算ASIN-B:</br>
    - ASIN-B 当日销售额 = ￥7,500。</br>
    - ASIN-B 应分摊的某项管理费 = 2,266.67 * (7,500 / 150,000) = 2,266.67 * 5% = ￥113.33。</br>
按销量占比分摊: 销量越高的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 "当日总销量"。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销量 / 当日总销量)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的"当日总销量"为 500件。</br>
  - 计算ASIN-C:</br>
    - ASIN-C 当日销量 = 25件。</br>
    - ASIN-C 应分摊的某项管理费 = 2,266.67 * (25 / 500) = 2,266.67 * 5% = ￥113.33。</br>
均摊: 只要当天出单了，每个ASIN都平等地分摊管理费，无论其销售额或销量大小。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）出单的ASIN总数量。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 / 当日出单的ASIN总数</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日共有 100个 不同的ASIN产生了订单。</br>
  - 计算任意一个出单的ASIN（如ASIN-D）:</br>
    - ASIN-D 应分摊的某项管理费 = 2,266.67 / 100 = ￥22.67。</br>
    - （所有其他99个出单ASIN分摊到的费用也都是￥22.67）。</br>`,
  managementFee: `无论选择哪种分摊方式，系统首先会计算出 "某项费用的平均每日管理费"。</br>
- 公式:</br>
- 某项费用平均每日管理费 = 当前月份某项费用的总额 / 当前月份的总天数</br>
- 示例:</br>
  - 假设当前是 6月 (共30天)。</br>
  - 从上面的费用池表格中得知，6月的 某项费用的管理费 为 ￥68,000。</br>
  - 则，平均每日某项管理费 = 68,000 / 30 = ￥2,266.67。</br>
按销售额占比分摊: 卖得越多的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 "当日总销售额"。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销售额 / 当日总销售额)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的"当日总销售额"为 ￥150,000。</br>
  - 计算ASIN-A:</br>
    - ASIN-A 当日销售额 = ￥15,000。</br>
    - ASIN-A 应分摊的某项管理费 = 2,266.67 * (15,000 / 150,000) = 2,266.67 * 10% = ￥226.67。</br>
  - 计算ASIN-B:</br>
    - ASIN-B 当日销售额 = ￥7,500。</br>
    - ASIN-B 应分摊的某项管理费 = 2,266.67 * (7,500 / 150,000) = 2,266.67 * 5% = ￥113.33。</br>
按销量占比分摊: 销量越高的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 "当日总销量"。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销量 / 当日总销量)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的"当日总销量"为 500件。</br>
  - 计算ASIN-C:</br>
    - ASIN-C 当日销量 = 25件。</br>
    - ASIN-C 应分摊的某项管理费 = 2,266.67 * (25 / 500) = 2,266.67 * 5% = ￥113.33。</br>
均摊: 只要当天出单了，每个ASIN都平等地分摊管理费，无论其销售额或销量大小。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）出单的ASIN总数量。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 / 当日出单的ASIN总数</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日共有 100个 不同的ASIN产生了订单。</br>
  - 计算任意一个出单的ASIN（如ASIN-D）:</br>
    - ASIN-D 应分摊的某项管理费 = 2,266.67 / 100 = ￥22.67。</br>
    - （所有其他99个出单ASIN分摊到的费用也都是￥22.67）。</br>`,
  回款: '取自领星【利润报表】订单维度的"亚马逊结算小计"，取值状态约束为：转账中&&已转账',
  settlementTotal: '取自领星【利润报表】订单维度的"亚马逊结算小计"，取值状态约束为：转账中&&已转账',
  总成本合计: '总成本合计 = 平台支出 + 总投入 + 广告费 + 服务商扣点 + 店铺租金',
  'extraData.总成本合计': '总成本合计 = 平台支出 + 总投入 + 广告费 + 服务商扣点 + 店铺租金',
  回款率: '回款率 = 回款 / 销售额',
  'extraData.回款率': '回款率 = 回款 / 销售额'
}

// 统计数据
const asinStats = reactive({
  totalProfit: 0,
  totalRevenue: 0,
  totalExpenseCost: 0,
  totalManagementFee: 0
})

// 表格数据
const tableData = ref([])
const extraData = ref({})
const allTableColumns = computed(() => {
  switch (props.dimension) {
    case 'asin':
      return [
        {
          prop: 'smallImageUrl',
          field: 'smallImageUrl',
          label: '产品图片',
          width: 80,
          fixed: 'left',
          sortable: false
        },
        { prop: 'msku', field: 'msku', label: 'MSKU', width: 120, fixed: 'left', sortable: true },
        { prop: 'asin', field: 'asin', label: 'ASIN', width: 120, fixed: 'left', sortable: true },
        {
          prop: 'parentAsin',
          field: 'parentAsin',
          label: '父ASIN',
          width: 120,
          fixed: 'left',
          sortable: true
        },
        {
          field: 'skuInfo',
          label: '品名/SKU',
          width: 260,
          fixed: 'left',
          align: 'left',
          showOverflowTooltip: true
        },
        { prop: 'storeName', field: 'storeName', label: '店铺/国家', width: 140, sortable: true },
        {
          prop: 'principalRealname',
          field: 'principalRealname',
          label: '负责人',
          width: 100,
          sortable: true
        },
        {
          prop: 'extraData.经营利润',
          field: 'extraData.经营利润',
          label: '经营利润',
          width: 120,
          align: 'right',
          sortable: false
        },
        {
          prop: 'totalSalesQuantity',
          field: 'totalSalesQuantity',
          label: '销量',
          width: 90,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaReturnsQuantity',
          field: 'fbaReturnsQuantity',
          label: '退货量',
          width: 90,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.实际销量',
          field: 'extraData.实际销量',
          label: '实际销量',
          width: 120,
          align: 'right',
          sortable: false
        },
        {
          prop: 'totalSalesAmount',
          field: 'totalSalesAmount',
          label: '销售额',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.收入',
          field: 'extraData.收入',
          width: 120,
          align: 'right',
          sortable: false,
          label: '💰 收入'
        },
        {
          prop: 'platformFee',
          field: 'platformFee',
          label: '平台费',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalAdsCost',
          field: 'totalAdsCost',
          label: '广告费',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaDeliveryFee',
          field: 'fbaDeliveryFee',
          label: 'FBA发货费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalStorageFee',
          field: 'totalStorageFee',
          label: 'FBA仓储费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.其他平台支出费用',
          field: 'extraData.其他平台支出费用',
          label: '其他平台支出费用',
          width: 170,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.平台支出',
          field: 'extraData.平台支出',
          width: 140,
          align: 'right',
          sortable: false,
          label: '📊 平台支出'
        },
        {
          prop: 'extraData.平均采购成本',
          field: 'extraData.平均采购成本',
          label: '平均采购成本',
          width: 140,
          align: 'right',
          sortable: false
        },
        {
          prop: 'cgPriceTotal',
          field: 'cgPriceTotal',
          label: '采购成本',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.平均头程成本',
          field: 'extraData.平均头程成本',
          label: '平均头程成本',
          width: 140,
          align: 'right',
          sortable: false
        },
        {
          prop: 'cgTransportCostsTotal',
          field: 'cgTransportCostsTotal',
          label: '头程成本',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.包材',
          field: 'extraData.包材',
          label: '包材',
          width: 110,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.服务商扣点',
          field: 'extraData.服务商扣点',
          label: '服务商扣点',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.店铺租金',
          field: 'extraData.店铺租金',
          label: '店铺租金',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.总投入',
          field: 'extraData.总投入',
          width: 120,
          align: 'right',
          sortable: false,
          label: '总投入'
        },
        {
          prop: 'extraData.毛利润',
          field: 'extraData.毛利润',
          width: 120,
          align: 'right',
          sortable: true,
          label: '毛利润'
        },
        {
          prop: 'managementFee',
          field: 'managementFee',
          label: '管理费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'settlementTotal',
          field: 'settlementTotal',
          label: '回款',
          width: 120,
          align: 'right',
          sortable: true
        }
      ]

    case 'store':
      return [
        {
          prop: 'storeName',
          field: 'storeName',
          label: '店铺/国家',
          width: 170,
          fixed: 'left',
          sortable: true
        },
        {
          prop: 'extraData.经营利润',
          field: 'extraData.经营利润',
          label: '经营利润',
          width: 120,
          align: 'right',
          sortable: false
        },
        {
          prop: 'totalSalesQuantity',
          field: 'totalSalesQuantity',
          label: '销量',
          width: 100,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaReturnsQuantity',
          field: 'fbaReturnsQuantity',
          label: '退货量',
          width: 90,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.实际销量',
          field: 'extraData.实际销量',
          label: '实际销量',
          width: 120,
          align: 'right',
          sortable: false
        },
        {
          prop: 'totalSalesAmount',
          field: 'totalSalesAmount',
          label: '销售额',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalSalesRefunds',
          label: '收入退款额',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.收入',
          field: 'extraData.收入',
          width: 120,
          align: 'right',
          sortable: false,
          label: '💰 收入'
        },
        {
          prop: 'platformFee',
          field: 'platformFee',
          label: '平台费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaDeliveryFee',
          field: 'fbaDeliveryFee',
          label: 'FBA发货费',
          width: 140,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.其他平台支出费用',
          field: 'extraData.其他平台支出费用',
          label: '其他平台支出费用',
          width: 170,
          align: 'right',
          sortable: false
        },
        {
          prop: 'totalStorageFee',
          field: 'totalStorageFee',
          label: 'FBA仓储费',
          width: 140,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.平台支出',
          field: 'extraData.平台支出',
          width: 140,
          align: 'right',
          sortable: false,
          label: '📊 平台支出'
        },
        {
          prop: 'cgPriceTotal',
          field: 'cgPriceTotal',
          label: '采购成本',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'cgTransportCostsTotal',
          field: 'cgTransportCostsTotal',
          label: '头程成本',
          width: 100,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.包材',
          field: 'extraData.包材',
          label: '包材',
          width: 100,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.总投入',
          field: 'extraData.总投入',
          width: 110,
          align: 'right',
          sortable: false,
          label: '💼 总投入'
        },
        {
          prop: 'totalAdsCost',
          field: 'totalAdsCost',
          label: '广告费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.服务商扣点',
          field: 'extraData.服务商扣点',
          label: '服务商扣点',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.店铺租金',
          field: 'extraData.店铺租金',
          label: '店铺租金',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.总成本合计',
          field: 'extraData.总成本合计',
          width: 140,
          align: 'right',
          sortable: false,
          label: '💰 总成本合计'
        },
        {
          prop: 'grossProfit',
          field: 'grossProfit',
          label: '毛利润',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.毛利率',
          field: 'extraData.毛利率',
          label: '毛利率',
          width: 90,
          align: 'right',
          sortable: false
        },
        {
          prop: 'settlementTotal',
          field: 'settlementTotal',
          label: '回款',
          width: 110,
          align: 'right',
          sortable: true
        },
        // { prop: 'paybackRate', label: '回款率', width: 90, align: 'right', sortable: true },
        {
          prop: 'managementFee',
          field: 'managementFee',
          label: '管理费',
          width: 110,
          align: 'right',
          sortable: true
        }
      ]
    case 'manager':
      return [
        {
          prop: 'principalRealname',
          field: 'principalRealname',
          label: '负责人',
          width: 100,
          fixed: 'left',
          sortable: true
        },
        {
          prop: 'extraData.经营利润',
          field: 'extraData.经营利润',
          label: '经营利润',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'grossProfit',
          field: 'grossProfit',
          label: '毛利润',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'settlementTotal',
          field: 'settlementTotal',
          label: '回款',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalSalesQuantity',
          field: 'totalSalesQuantity',
          label: '销量',
          width: 90,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaReturnsQuantity',
          field: 'fbaReturnsQuantity',
          label: '退货量',
          width: 90,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.实际销量',
          field: 'extraData.实际销量',
          label: '实际销量',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalSalesAmount',
          field: 'totalSalesAmount',
          label: '销售额',
          width: 120,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.收入',
          field: 'extraData.收入',
          width: 120,
          align: 'right',
          sortable: true,
          label: '💰 收入'
        },
        {
          prop: 'platformFee',
          field: 'platformFee',
          label: '平台费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalAdsCost',
          field: 'totalAdsCost',
          label: '广告费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'fbaDeliveryFee',
          field: 'fbaDeliveryFee',
          label: 'FBA发货费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'totalStorageFee',
          field: 'totalStorageFee',
          label: 'FBA仓储费',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.其他平台支出费用',
          field: 'extraData.其他平台支出费用',
          label: '其他平台支出费用',
          width: 170,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.平台支出',
          field: 'extraData.平台支出',
          width: 140,
          align: 'right',
          sortable: false,
          label: '📊 平台支出'
        },
        {
          prop: 'extraData.平均采购成本',
          field: 'extraData.平均采购成本',
          label: '平均采购成本',
          width: 140,
          align: 'right',
          sortable: false
        },
        {
          prop: 'cgPriceTotal',
          field: 'cgPriceTotal',
          label: '采购成本',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.平均头程成本',
          field: 'extraData.平均头程成本',
          label: '平均头程成本',
          width: 140,
          align: 'right',
          sortable: false
        },
        {
          prop: 'cgTransportCostsTotal',
          field: 'cgTransportCostsTotal',
          label: '头程成本',
          width: 110,
          align: 'right',
          sortable: true
        },
        {
          prop: 'extraData.包材',
          field: 'extraData.包材',
          label: '包材',
          width: 110,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.服务商扣点',
          field: 'extraData.服务商扣点',
          label: '服务商扣点',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.店铺租金',
          field: 'extraData.店铺租金',
          label: '店铺租金',
          width: 130,
          align: 'right',
          sortable: false
        },
        {
          prop: 'extraData.总投入',
          field: 'extraData.总投入',
          width: 120,
          align: 'right',
          sortable: false,
          label: '总投入'
        },
        {
          prop: 'managementFee',
          field: 'managementFee',
          label: '管理费',
          width: 110,
          align: 'right',
          sortable: true
        }
      ]
    default:
      return []
  }
})
const visibleTableColumns = ref<any[]>([]) // 可见列配置
const asinTableHeight = ref(600)
const total = ref(0)

// 监听dimension变化，重置滚动条
watch(
  () => props.dimension,
  () => {
    resetAllScrollbars()
  },
  { immediate: false }
)

// 重置所有滚动条
const resetAllScrollbars = () => {
  nextTick(() => {
    // 重置表格水平滚动条
    const tableWrappers = document.querySelectorAll('.table-card .el-table__body-wrapper')
    tableWrappers.forEach((wrapper) => {
      wrapper.scrollLeft = 0
      wrapper.scrollTop = 0
    })

    // 重置表格头部滚动条
    const headerWrappers = document.querySelectorAll('.table-card .el-table__header-wrapper')
    headerWrappers.forEach((wrapper) => {
      wrapper.scrollLeft = 0
    })

    // 重置固定列滚动条
    const fixedWrappers = document.querySelectorAll(
      '.table-card .el-table__fixed .el-table__fixed-body-wrapper'
    )
    fixedWrappers.forEach((wrapper) => {
      wrapper.scrollTop = 0
    })
  })
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const today = dayjs().format('YYYY-MM-DD')
      return [today, today]
    }
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(29, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').format('YYYY-MM-DD')
      const end = dayjs().endOf('month').format('YYYY-MM-DD')
      return [start, end]
    }
  }
]

// 方法
const toggleAsinMetrics = () => {
  asinMetricsVisible.value = !asinMetricsVisible.value
}

const handleTimeTypeChange = (timeType) => {
  filters.timeType = timeType
  // 根据时间类型设置默认时间范围
  if (timeType === 'day') {
    // 默认前一天
    const yesterday = dayjs().subtract(1, 'day')
    filters.dateRange = [yesterday.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD')]
    filters.monthRange = []
    filters.weekRange = []
  } else if (timeType === 'month') {
    // 默认当前月
    const currentMonth = dayjs().format('YYYY-MM')
    filters.dateRange = []
    filters.monthRange = [currentMonth, currentMonth]
    filters.weekRange = []
  } else if (timeType === 'week') {
    // 默认本周
    const startOfWeek = dayjs().startOf('week').add(1, 'day') // 周一
    const endOfWeek = dayjs().endOf('week').add(1, 'day') // 周日
    filters.dateRange = []
    filters.monthRange = []
    filters.weekRange = [startOfWeek.format('YYYY-MM-DD'), endOfWeek.format('YYYY-MM-DD')]
  }
  // loadData()
}

const handleDateRangeChange = (value: any) => {
  filters.dateRange = value
}

const handleMonthRangeChange = (value: any) => {
  filters.monthRange = value
}

const handleWeekRangeChange = (value: any) => {
  filters.weekRange = value
}

const disabledDate = (time: Date) => {
  // 禁用未来日期
  const now = new Date()
  if (time.getTime() > now.getTime()) {
    return true
  }

  // 禁用2025年6月份之前的日期
  const minDate = new Date(2025, 5, 1) // 2025年6月1日 (月份从0开始，5表示6月)
  return time.getTime() < minDate.getTime()
}

// 排序处理方法
const handleSortChange = ({ column, prop, order }: any) => {
  // 检查是否为extraData字段，如果是则不进行排序
  if (prop && prop.startsWith('extraData.')) {
    return
  }

  if (order) {
    filters.sortingFields = [{ field: prop, order: order === 'ascending' ? 'asc' : 'desc' }]
  } else {
    filters.sortingFields = []
  }
  filters.pageNo = 1
  loadData()
}

// 分页处理方法
const handleSizeChange = (val: number) => {
  filters.pageSize = val
  filters.pageNo = 1
  loadData()
  resetAllScrollbars()
}

const handleCurrentChange = (val: number) => {
  filters.pageNo = val
  loadData()
  resetAllScrollbars()
}

// 性能优化：防抖搜索
const searchData = () => {
  // 验证日期范围是否已选择
  let hasDateRange = false

  if (filters.timeType === 'day' && filters.dateRange && filters.dateRange.length === 2) {
    hasDateRange = true
  } else if (
    filters.timeType === 'month' &&
    filters.monthRange &&
    filters.monthRange.length === 2
  ) {
    hasDateRange = true
  } else if (filters.timeType === 'week' && filters.weekRange && filters.weekRange.length === 2) {
    hasDateRange = true
  }

  if (!hasDateRange) {
    ElMessage.warning('请选择日期范围后再进行查询')
    return
  }

  // 重置到第一页
  filters.pageNo = 1
  // 模拟搜索
  setTimeout(() => {
    loadData()
  }, 300) // 减少延迟时间
}

const resetFilters = () => {
  // 根据当前时间类型设置默认时间范围
  let defaultDateRange = []
  let defaultMonthRange = []
  let defaultWeekRange = []

  if (filters.timeType === 'day') {
    // 默认前一天
    const yesterday = dayjs().subtract(1, 'day')
    defaultDateRange = [yesterday.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD')]
  } else if (filters.timeType === 'month') {
    // 默认当前月 - 开始时间为月初，结束时间为月末
    const startOfMonth = dayjs().startOf('month').format('YYYY-MM')
    const endOfMonth = dayjs().endOf('month').format('YYYY-MM')
    defaultMonthRange = [startOfMonth, endOfMonth]
  } else if (filters.timeType === 'week') {
    // 默认本周
    const startOfWeek = dayjs().startOf('week').add(1, 'day') // 周一
    const endOfWeek = dayjs().endOf('week').add(1, 'day') // 周日
    defaultWeekRange = [startOfWeek.format('YYYY-MM-DD'), endOfWeek.format('YYYY-MM-DD')]
  }

  Object.assign(filters, {
    currencyCode: '',
    sidQuerySet: [],
    shopNames: [], // 店铺名称
    countries: [], // 国家代码
    countrysList: [], // 国家名称
    principalNamesQuerySet: [],
    // 保持当前时间类型不变
    dateRange: defaultDateRange,
    monthRange: defaultMonthRange,
    weekRange: defaultWeekRange,
    searchType: 'parentAsin',
    searchValue: '',
    pageNo: 1,
    pageSize: 50
  })
  // 重置AccountShopSelector组件
  if (accountShopSelectorRef.value) {
    accountShopSelectorRef.value.resetSelection('account')
    accountShopSelectorRef.value.resetSelection('shop')
    accountShopSelectorRef.value.resetSelection('country')
  }
  loadData()
}

const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

const formatNumber = (num: number) => {
  if (num === null || num === undefined || isNaN(num)) return '0'
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  })
}

const showImagePreview = (imageUrl: string, event: Event) => {
  // 图片预览功能
  console.log('显示图片预览:', imageUrl)
}

const hideImagePreview = () => {
  // 隐藏图片预览
  console.log('隐藏图片预览')
}

const getAmazonUrl = (asin: string, region: string) => {
  const domainMap: Record<string, string> = {
    US: 'amazon.com',
    UK: 'amazon.co.uk',
    DE: 'amazon.de',
    FR: 'amazon.fr',
    IT: 'amazon.it',
    ES: 'amazon.es',
    JP: 'amazon.co.jp',
    CA: 'amazon.ca'
  }
  const domain = domainMap[region] || 'amazon.com'
  return `https://www.${domain}/dp/${asin}`
}

// 动态计算表头宽度
const calculateColumnWidth = (label: string, hasIcon: boolean = false) => {
  // 基础宽度：每个中文字符约14px，英文字符约8px
  let width = 0
  let inBrackets = false

  for (let i = 0; i < label.length; i++) {
    const char = label.charAt(i)

    // 检测括号状态
    if (char === '(' || char === '（') {
      inBrackets = true
    } else if (char === ')' || char === '）') {
      inBrackets = false
    }

    // 判断是否为中文字符
    if (/[\u4e00-\u9fa5]/.test(char)) {
      // 括号内的中文字符权重减少
      width += inBrackets ? 12 : 17
    } else {
      // 括号内的英文字符权重减少
      width += inBrackets ? 6 : 10
    }
  }

  // 如果有图标，增加图标宽度
  if (hasIcon) {
    width += 20
  }

  // 添加内边距和边框等额外空间
  width += 46

  // 设置最小宽度和最大宽度
  const minWidth = 110
  const maxWidth = 250

  return Math.max(Math.min(width, maxWidth), minWidth)
}

const getColumnWidth = (prop: string) => {
  // 特殊字段的固定宽度
  const fixedWidthMap: Record<string, number> = {
    smallImageUrl: 100,
    skuInfo: 260,
    shopCountry: 150
  }

  if (fixedWidthMap[prop]) {
    return fixedWidthMap[prop]
  }

  // 根据列配置动态计算宽度
  const column = allTableColumns.value.find((col) => col.prop === prop || col.field === prop)
  if (column) {
    const hasIcon =
      column.label &&
      (column.label.includes('💰') ||
        column.label.includes('📊') ||
        column.label.includes('💼') ||
        column.label.includes('?') ||
        column.label.includes('ℹ') ||
        column.label.includes('💡') ||
        column.label.includes('❓'))
    return calculateColumnWidth(column.label, hasIcon)
  }

  // 默认宽度
  return 150
}

// 获取表格类型，用于列配置
const getTableType = () => {
  const typeMap = {
    asin: 'MonthlyDimensionAsin',
    store: 'MonthlyDimensionStore',
    manager: 'MonthlyDimensionManager',
    MonthlySummary: 'MonthlyDimensionSummary'
  }
  return typeMap[props.dimension] || 'MonthlyDimension'
}

// 处理列配置变化
const handleColumnConfigChange = (newColumns: any[]) => {
  visibleTableColumns.value = newColumns.filter((col) => col.visible !== false)
}

// 计算统计数据
const calculateSummaryStats = (dataList: any[]) => {
  // 重置统计数据
  asinStats.totalProfit = 0
  asinStats.totalRevenue = 0
  asinStats.totalExpenseCost = 0
  asinStats.totalManagementFee = 0

  if (!dataList || dataList.length === 0) {
    return
  }

  // 遍历数据计算总和
  dataList.forEach((item) => {
    // 毛利润
    const profit = item.grossProfit || 0
    asinStats.totalProfit += profit

    // 销售额
    const revenue = item.totalSalesAmount || 0
    asinStats.totalRevenue += revenue

    // 管理费
    const managementFee = item.managementFee || 0
    asinStats.totalManagementFee += managementFee

    // 支出成本 = 销售收入 - 毛利润 - 管理费
    const expenseCost = revenue - profit - managementFee
    asinStats.totalExpenseCost += expenseCost
  })

  // 确保支出成本不为负数（如果计算结果为负，可能是数据结构问题）
  if (asinStats.totalExpenseCost < 0) {
    // 如果计算出的支出成本为负，尝试从extraData中查找相关字段
    asinStats.totalExpenseCost = 0
    dataList.forEach((item) => {
      // 尝试从extraData或其他字段中获取成本数据
      const extraData = item.extraData || {}
      const cost =
        extraData['成本'] || extraData['支出成本'] || extraData['总成本'] || item.totalCost || 0
      asinStats.totalExpenseCost += cost
    })
  }
}

const loadData = async () => {
  loading.value = true
  try {
    const dimensionTypeMap = {
      asin: 0,
      store: 1,
      manager: 2,
      MonthlySummary: 3,
      day: 3,
      month: 5,
      week: 4
    }

    // 构建符合接口规范的查询参数
    const params = {
      ...filters,
      pageNo: filters.pageNo,
      pageSize: filters.pageSize,
      type: dimensionTypeMap[props.dimension]
    }

    // 根据时间类型设置对应的日期参数
    if (filters.timeType === 'day' && filters.dateRange && filters.dateRange.length === 2) {
      params.beginOrderDate = dayjs(filters.dateRange[0]).format('YYYY-MM-DD')
      params.endOrderDate = dayjs(filters.dateRange[1]).format('YYYY-MM-DD')
    } else if (
      filters.timeType === 'month' &&
      filters.monthRange &&
      filters.monthRange.length === 2
    ) {
      // 对于月份范围，开始时间为月初，结束时间为月末
      const startMonth = dayjs(filters.monthRange[0]).startOf('month')
      const endMonth = dayjs(filters.monthRange[1]).endOf('month')
      params.beginOrderDate = startMonth.format('YYYY-MM-DD')
      params.endOrderDate = endMonth.format('YYYY-MM-DD')
    } else if (filters.timeType === 'week' && filters.weekRange && filters.weekRange.length === 2) {
      params.beginOrderDate = dayjs(filters.weekRange[0]).format('YYYY-MM-DD')
      params.endOrderDate = dayjs(filters.weekRange[1]).format('YYYY-MM-DD')
    }

    // 根据skuType设置对应的查询参数
    if (params.searchType) {
      if (params.searchValue !== '') {
        params[params.searchType] = params.searchValue
      }
    }

    delete params.timeType
    delete params.searchType
    delete params.searchValue
    delete params.dateRange
    delete params.monthRange
    delete params.weekRange
    delete params.countrysList
    delete params.shopNames

    const res = await getMonthlyReportPage(params)

    if (res.list && res.list.length > 0) {
      // 提取extraData用于动态列生成
      const firstItemExtraData = res.list[0].extraData || {}
      extraData.value = firstItemExtraData

      // 动态生成表格列配置
      const dynamicColumns = allTableColumns.value
      visibleTableColumns.value = dynamicColumns.filter((col) => col.visible !== false)

      // 准备表格数据
      tableData.value = res.list.map((item) => {
        // 组合店铺/国家字段
        const shopCountry =
          item.storeName && item.country
            ? `${item.storeName}/${item.country}`
            : item.storeName || item.country || ''
        // 品名/sku字段
        const skuInfo =
          item.localSku && item.localName
            ? `${item.localName}/${item.localSku}`
            : item.localName || item.localSku || ''

        // 处理extraData字段
        const processedExtraData = {}
        // if (item.extraData) {
        //   Object.keys(item.extraData).forEach((key) => {
        //     processedExtraData[key] = getExtraDataValue(item, key)
        //   })
        // }
        return {
          ...item,
          ...processedExtraData,
          shopCountry, // 添加组合字段
          skuInfo, // 添加组合字段
          // 兼容旧字段名
          volume: item.totalSalesQuantity,
          taxAmount: item.totalSalesAmount,
          principalNames: item.principalRealname
        }
      })
      total.value = res.total

      // 计算统计数据
      calculateSummaryStats(res.list)

      loading.value = false
      // 数据加载完成后重置滚动条
      resetAllScrollbars()
    } else {
      // 处理无数据或 extraData 为 null 的情况
      tableData.value = []
      extraData.value = {}
      visibleTableColumns.value = allTableColumns.value.filter((col) => col.visible !== false)
      total.value = 0

      // 重置统计数据
      asinStats.totalProfit = 0
      asinStats.totalRevenue = 0
      asinStats.totalExpenseCost = 0
      asinStats.totalManagementFee = 0

      loading.value = false
    }
  } catch (error) {
    console.error('Failed to load Monthly report data:', error)
    ElMessage.error('数据加载失败')
    tableData.value = []
    extraData.value = {}
    visibleTableColumns.value = []
    total.value = 0

    // 重置统计数据
    asinStats.totalProfit = 0
    asinStats.totalRevenue = 0
    asinStats.totalExpenseCost = 0
    asinStats.totalManagementFee = 0

    loading.value = false
  }
}

// 获取管理费详情
const getManagementFeeDetails = (row: any) => {
  // 如果有everyManagementFee数据，使用该数据
  if (row.everyManagementFee && typeof row.everyManagementFee === 'object') {
    return {
      laborCost: row.everyManagementFee[0] || 0, // 人工费
      designCost: row.everyManagementFee[1] || 0, // 设计费
      venueCost: row.everyManagementFee[2] || 0, // 场地费
      otherCost: row.everyManagementFee[3] || 0, // 其它均摊费
      vat: row.everyManagementFee[4] || 0 // VAT
    }
  }

  // 如果没有详细数据，按比例分配
  const totalFee = row.managementFee || 0
  if (totalFee === 0) {
    return {
      laborCost: 0,
      designCost: 0,
      venueCost: 0,
      otherCost: 0,
      vat: 0
    }
  }

  // 默认比例分配（可根据实际业务调整）
  return {
    laborCost: totalFee * 0.4,
    designCost: totalFee * 0.2,
    venueCost: totalFee * 0.15,
    otherCost: totalFee * 0.15,
    vat: totalFee * 0.1
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
  getPrincipalListFc()
  loadCurrencyOptions()
  loadData()
})
</script>

<style lang="scss" scoped>
.filter-card,
.summary-card,
.table-card {
  margin-bottom: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow-up {
  transform: rotate(180deg);
}

.metrics-container {
  display: flex;
  justify-content: space-around;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
}

.metric-label {
  color: #606266;
}

.product-image {
  width: 38px;
  height: 38px;
}

/* 折叠数据概览样式 */
.metrics-section {
  margin: 8px 0;
}

.metrics-header {
  display: flex;
  width: fit-content;
  min-height: 28px;
  padding: 6px 16px;
  margin: 0 auto;
  cursor: pointer;
  background: transparent;
  border: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  user-select: none;
  align-items: center;
  justify-content: center;
}

.metrics-header:hover {
  background: #f6f8fa;
  border-radius: 8px;
}

.metrics-title {
  display: flex;
  margin-right: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #656d76;
  align-items: center;
}

.asin-title::before {
  margin-right: 6px;
  font-size: 14px;
  content: '🎯';
}

.store-title::before {
  margin-right: 6px;
  font-size: 14px;
  content: '🏪';
}

.manager-title::before {
  margin-right: 6px;
  font-size: 14px;
  content: '👥';
}

.collapse-icon {
  font-size: 14px;
  color: #656d76;
  transition: transform 0.2s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.metrics-content {
  margin-top: 8px;
}

/* 数据统计卡片样式 - 采用数据看板风格 */
.core-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  align-items: center;
  justify-content: center;
}

.metric-card {
  position: relative;
  display: flex;
  min-width: 160px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 8%);
  flex-direction: column;
}

.metric-main {
  text-align: center;
}

.metric-card.profit {
  border-left: 4px solid #27ae60;
}

.metric-card.revenue {
  border-left: 4px solid #3498db;
}

.metric-card.orders {
  border-left: 4px solid #f39c12;
}

.metric-card.health {
  border-left: 4px solid #9b59b6;
}

.metric-value {
  margin-bottom: 8px;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.metric-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: #7f8c8d;
}

.calculator-symbol {
  display: flex;
  width: 40px;
  height: 40px;
  margin: 0 10px;
  font-size: 24px;
  font-weight: bold;
  color: #64748b;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  align-items: center;
  justify-content: center;
}

.product-image {
  display: flex;
  width: 38px;
  height: 38px;
  margin: 0 auto;
  align-items: center;
  justify-content: center;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.negative-amount {
  font-weight: 500;
  color: #f56c6c;
}

/* 表格链接样式 */
.el-table a:hover {
  text-decoration: underline;
}

/* 表头样式 - 不同类型字段的顶部边框 */

:deep(.fixed-header.is-leaf) {
  border-top: 3px solid #64748b !important;
}

:deep(.profit-header.is-leaf) {
  border-top: 3px solid #27ae60 !important;
}

:deep(.extra-header.is-leaf) {
  border-top: 3px solid #f39c12 !important;
}

:deep(.management-header.is-leaf) {
  border-top: 3px solid #9b59b6 !important;
}

.profit {
  font-weight: 600;
  color: #27ae60;
}

.loss {
  font-weight: 600;
  color: #e74c3c;
}

/* Element Plus滚动条组件样式 */
:deep(.el-table__body-wrapper .el-scrollbar__bar.is-horizontal) {
  height: 7px !important;
  opacity: 1 !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__bar.is-vertical) {
  width: 7px !important;
  opacity: 1 !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__thumb) {
  background-color: #666 !important;
  border-radius: 7px !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__thumb:hover) {
  background-color: #333 !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__track) {
  background-color: #f5f5f5 !important;
  border-radius: 7px !important;
}

/* 表格滚动优化 */
:deep(.el-table__body-wrapper) {
  overflow: auto;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden;
}

/* 分页组件样式 */
.pagination-container {
  display: flex;
  padding: 20px 0;
  justify-content: center;
  align-items: center;
}

:deep(.el-pagination) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  height: 32px;
  min-width: 32px;
  line-height: 30px;
  border-radius: 4px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

/* 表格性能优化样式 */
:deep(.el-table) {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

:deep(.el-table__body-wrapper) {
  /* 优化滚动性能 */
  will-change: scroll-position;
  transform: translateZ(0);
}

/* 图片懒加载优化 */
.product-image img {
  transition: opacity 0.3s ease;
}

.product-image img[loading='lazy'] {
  opacity: 0.8;
}

.product-image img[loading='lazy']:not([src]) {
  background: #f5f5f5;
  opacity: 0.3;
}

.table-card {
  overflow-x: auto;
}

.table-card .el-table {
  min-width: 100%;
}

/* 列标题tooltip样式 */
.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.tooltip-icon {
  font-size: 14px;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s ease;
}

.tooltip-icon:hover {
  color: #409eff;
}

:deep(.el-table) {
  font-size: 12px !important;

  .cell {
    display: flex;
    align-items: center;
    line-height: 1.2;
  }
}

/* 管理费详情样式 */
.management-fee-value {
  display: inline-block;
}

.clickable-fee {
  display: inline-flex;
  color: #409eff;
  cursor: pointer;
  transition: color 0.3s ease;
  align-items: center;
  gap: 4px;
}

.clickable-fee:hover {
  color: #66b1ff;
}

.fee-detail-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.inline-arrow {
  display: inline-flex;
  align-items: center;
}

.management-fee-detail-content {
  padding: 8px 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row.total-row {
  padding-top: 12px;
  margin-top: 8px;
  font-weight: 600;
  border-top: 2px solid #e6e6e6;
  border-bottom: none;
}

.detail-row .label {
  min-width: 80px;
  font-size: 13px;
  color: #666;
}

.detail-row .value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.total-row .label,
.total-row .value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
</style>
