<template>
  <div class="monthly-summary-container">
    <!-- 筛选条件 -->
    <el-form
      class="filter-card"
      :model="summaryFilters"
      :inline="true"
      style="display: flex; flex-wrap: wrap"
    >
      <el-form-item label="币种">
        <el-select v-model="summaryFilters.currencyCode" class="!w-140px" placeholder="请选择币种">
          <el-option
            v-for="currency in currencyOptions"
            :key="currency.value"
            :label="currency.label"
            :value="currency.value"
          />
        </el-select>
      </el-form-item>
      <!-- 账户店铺选择器 -->
      <AccountShopSelector
        ref="accountShopSelectorRef"
        :show-account="false"
        v-model:shop-values="summaryFilters.sidQuerySet"
        v-model:country-values="summaryFilters.countrysList"
        v-model:country-names="summaryFilters.countries"
      />
      <el-form-item label="负责人" prop="principalNamesQuerySet">
        <RcInputSelect
          v-model="summaryFilters.principalNamesQuerySet"
          class="!w-240px"
          :options="principalOptions"
        />
      </el-form-item>

      <el-form-item label="日期范围">
        <MonthlyDateRangePicker
          v-model="summaryFilters.dateRange"
          v-model:month-value="summaryFilters.monthRange"
          v-model:week-value="summaryFilters.weekRange"
          :show-time-type-selector="true"
          :default-time-type="summaryFilters.timeType"
          :enable-default-range="true"
          :dateShortcuts="dateShortcuts"
          @change="(value) => handleDateRangeChange(value, 'summary')"
          @time-type-change="(timeType) => handleTimeTypeChange(timeType, 'summary')"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label-width="0">
        <el-button type="primary" @click="searchSummaryData">查询</el-button>
        <el-button @click="resetSummaryFilters">重置</el-button>
        <!-- <el-button @click="exportSummaryData">导出</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 每日汇总数据展示 -->
    <div class="monthly-summary-display">
      <!-- 横向滚动的表格布局 -->
      <div class="horizontal-scroll-container">
        <div class="horizontal-table-wrapper">
          <!-- 表头 -->
          <div class="table-header">
            <div
              class="item-name-header resizable-column"
              data-column="item-name"
              :style="{
                width: getColumnWidth('item-name') + 'px',
                minWidth: getColumnWidth('item-name') + 'px'
              }"
            >
              项目
              <!-- 添加列宽调整器 -->
              <div class="column-resizer" @mousedown="startResize($event, 'item-name')"></div>
            </div>
            <div class="date-headers">
              <div
                v-for="dateItem in listDateHeaders"
                :key="dateItem.date"
                class="date-header resizable-column"
                :class="{ 'total-column': dateItem.isTotal }"
                :data-column="dateItem.date"
                :style="{
                  width: getColumnWidth(dateItem.date) + 'px',
                  minWidth: getColumnWidth(dateItem.date) + 'px'
                }"
              >
                {{ dateItem.display }}
                <!-- 添加列宽调整器，所有列都可以调整 -->
                <div class="column-resizer" @mousedown="startResize($event, dateItem.date)"></div>
              </div>
            </div>
          </div>

          <!-- 数据行（使用动态汇总项） -->
          <div class="data-rows" v-if="!loading && listDateHeaders.length > 0">
            <!-- 动态汇总项 -->
            <div class="data-group">
              <div class="data-row" v-for="dataItem in dynamicSummaryItems" :key="dataItem.key">
                <div
                  class="item-name"
                  :class="{
                    'highlight-item': dataItem.highlight,
                    'profit-item': dataItem.type === 'profit',
                    'revenue-item': dataItem.type === 'currency' || dataItem.type === 'quantity',
                    'cost-item': dataItem.type === 'cny'
                  }"
                  :style="{
                    width: getColumnWidth('item-name') + 'px',
                    minWidth: getColumnWidth('item-name') + 'px'
                  }"
                >
                  {{ dataItem.label }}
                </div>
                <div class="item-values">
                  <div
                    v-for="dateItem in listDateHeaders"
                    :key="`${dataItem.key}-${dateItem.date}`"
                    class="item-value two-line-value"
                    :class="[
                      dataItem.type,
                      {
                        'total-column': dateItem.isTotal,
                        'highlight-value': dataItem.highlight
                      }
                    ]"
                    :data-column-data="dateItem.date"
                    :style="{
                      width: getColumnWidth(dateItem.date) + 'px',
                      minWidth: getColumnWidth(dateItem.date) + 'px'
                    }"
                  >
                    <div class="value-amount">
                      <el-popover
                        v-if="dataItem.key === 'managementFee'"
                        placement="bottom"
                        title="管理费分摊详情"
                        :width="280"
                        trigger="click"
                      >
                        <template #reference>
                          <span class="management-fee-value">
                            <el-skeleton-item
                              v-if="
                                dateItem.isTotal &&
                                (totalLoading ||
                                  getDateValue(dateItem.date, dataItem.key) === 'loading')
                              "
                              variant="text"
                              style="width: 60px; height: 16px"
                            />
                            <span v-else>{{
                              formatValueByType(
                                getDateValue(dateItem.date, dataItem.key),
                                dataItem.type
                              )
                            }}</span>
                            <el-icon class="fee-detail-arrow inline-arrow">
                              <ArrowDown />
                            </el-icon>
                          </span>
                        </template>
                        <div class="management-fee-detail-content">
                          <div class="detail-row">
                            <span class="label">人工费:</span>
                            <span class="value">{{
                              formatCurrency(getManagementFeeDetails(dateItem.date).laborCost)
                            }}</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">设计费:</span>
                            <span class="value">{{
                              formatCurrency(getManagementFeeDetails(dateItem.date).designCost)
                            }}</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">场地费:</span>
                            <span class="value">{{
                              formatCurrency(getManagementFeeDetails(dateItem.date).venueCost)
                            }}</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">其它均摊费:</span>
                            <span class="value">{{
                              formatCurrency(getManagementFeeDetails(dateItem.date).otherCost)
                            }}</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">VAT:</span>
                            <span class="value">{{
                              formatCurrency(getManagementFeeDetails(dateItem.date).vat)
                            }}</span>
                          </div>
                          <div class="detail-row total-row">
                            <span class="label">总计:</span>
                            <span class="value">
                              {{ formatCurrency(getDateValue(dateItem.date, dataItem.key)) }}
                            </span>
                          </div>
                        </div>
                      </el-popover>
                      <span v-else>
                        <el-skeleton-item
                          v-if="
                            dateItem.isTotal &&
                            (totalLoading ||
                              getDateValue(dateItem.date, dataItem.key) === 'loading')
                          "
                          variant="text"
                          style="width: 60px; height: 16px"
                        />
                        <span v-else>{{
                          formatValueByType(
                            getDateValue(dateItem.date, dataItem.key),
                            dataItem.type
                          )
                        }}</span>
                      </span>
                    </div>
                    <!-- 计算百分比 -->
                    <!-- <div
                      class="value-ratio"
                      v-if="
                        dataItem.percentage &&
                        calculatePercentage(
                          dateItem.date,
                          dataItem.key,
                          dataItem.percentageBase
                        ) !== null
                      "
                    >
                      {{
                        formatRatio(
                          calculatePercentage(dateItem.date, dataItem.key, dataItem.percentageBase)
                        )
                      }}
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && listDateHeaders.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal/index'
import { getMonthlyReportPage, getMonthlyReportStatistic } from '@/api/profit/report'
import AccountShopSelector from '@/components/AccountShopSelector/index.vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { ArrowDown } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import MonthlyDateRangePicker from '../components/MonthlyDateRangePicker.vue'

// 响应式数据
const loading = ref(false)
const totalLoading = ref(false) // 总计数据加载状态
//默认昨天
// 获取昨天日期
const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
// 筛选条件
const summaryFilters = reactive({
  currencyCode: '原币种',
  sidQuerySet: [], // 店铺ID
  shopNames: [], // 店铺名称
  countries: [], // 国家代码
  countrysList: [], // 国家名称
  principalNamesQuerySet: [], // 负责人ID
  timeType: 'day', // 时间类型：day, month, week
  dateRange: [yesterday, yesterday], //默认昨天
  monthRange: [], // 月份范围
  weekRange: [], // 周范围
  pageNo: 1,
  pageSize: 50
})

// AccountShopSelector组件引用
const accountShopSelectorRef = ref()

// 选项数据
const principalOptions = ref([])

const currencyOptions = ref([])

// 加载币种选项
const loadCurrencyOptions = async () => {
  try {
    const response = getDictOptions(DICT_TYPE.CURRENCYNAME_CURRENCYNAMECODE)
    currencyOptions.value = response || []
  } catch (error) {
    console.error('加载币种选项失败:', error)
  }
}
// 获取负责人列表
const getPrincipalListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getPrincipalList()
    if (res) {
      principalOptions.value = res.map((item) => ({
        label: item.principalName,
        value: item.principalName
      }))
    }
  } catch (error) {
    console.error('获取负责人列表失败', error)
  }
}

// 表格数据
const summaryData = ref<any>({})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const today = dayjs().format('YYYY-MM-DD')
      return [today, today]
    }
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').format('YYYY-MM-DD')
      const end = dayjs().endOf('month').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(29, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  }
]

// 数据项定义 - 按照原型UI分组

// 动态汇总项
const extraData = ref({})

// 所有可能的汇总项定义
const allSummaryItems = [
  { key: 'salesQuantity', label: '销量', type: 'quantity' },
  { key: 'returnQuantity', label: '退货量', type: 'quantity' },
  { key: 'actualSales', label: '实际销量', type: 'quantity', highlight: true },
  { key: 'salesAmount', label: '销售额', type: 'currency' },
  { key: 'salesRefund', label: '收入退款额', type: 'currency' },
  { key: 'platformIncome', label: '平台收入', type: 'currency', highlight: true },
  {
    key: 'commission',
    label: '平台费',
    type: 'currency',
    percentage: true,
    percentageBase: 'salesAmount'
  },
  {
    key: 'amazonShippingFee',
    label: 'FBA发货费',
    type: 'currency',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'amazonStorageFee',
    label: 'FBA仓储费',
    type: 'currency',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'totalPlatformExpense',
    label: '亚马逊平台总支出',
    type: 'currency',
    percentage: true,
    percentageBase: 'platformIncome',
    highlight: true
  },
  {
    key: 'productCost',
    label: '采购成本',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'shippingCost',
    label: '头程成本',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'totalInvestment',
    label: '总投入',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome',
    highlight: true
  },
  {
    key: 'adSpend',
    label: '广告费',
    type: 'currency',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'serviceCommission',
    label: '服务商扣点',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'storeRent',
    label: '店铺租金',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'totalCostTotal',
    label: '总成本合计',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome',
    highlight: true
  },
  {
    key: 'grossProfit',
    label: '毛利润',
    type: 'profit',
    percentage: true,
    percentageBase: 'platformIncome',
    highlight: true
  },
  { key: 'grossProfitRate', label: '毛利率', type: 'percent' },
  {
    key: 'paybackAmount',
    label: '回款',
    type: 'currency',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  { key: 'paybackRate', label: '回款率', type: 'percent' },
  {
    key: 'managementFee',
    label: '管理费',
    type: 'cny',
    percentage: true,
    percentageBase: 'platformIncome'
  },
  {
    key: 'operatingProfit',
    label: '经营利润',
    type: 'profit',
    highlight: true
  }
  // {
  //   key: 'otherPlatformExpense',
  //   label: '其他平台支出费用',
  //   type: 'currency',
  //   percentage: true,
  //   percentageBase: 'platformIncome'
  // },
  // {
  //   key: 'avgProductCost',
  //   label: '平均采购成本',
  //   type: 'cny'
  // },
  // {
  //   key: 'avgShippingCost',
  //   label: '平均头程成本',
  //   type: 'cny'
  // },
  // {
  //   key: 'packagingMaterial',
  //   label: '包材',
  //   type: 'cny',
  //   percentage: true,
  //   percentageBase: 'platformIncome'
  // }
]

// 动态过滤的汇总项 - 只显示在数据中存在的字段
const dynamicSummaryItems = computed(() => {
  if (!summaryData.value || !summaryData.value.total) {
    return allSummaryItems
  }

  const totalData = summaryData.value.total
  const availableKeys = Object.keys(totalData)

  // 过滤出在数据中存在的字段
  return allSummaryItems.filter((item) => {
    return (
      availableKeys.includes(item.key) &&
      totalData[item.key] !== undefined &&
      totalData[item.key] !== null
    )
  })
})

// 计算属性
const listDateHeaders = computed(() => {
  if (!summaryData.value || !summaryData.value.dates) return []
  const headers = []

  // 先添加总计列 - 只要有日期数据就显示总计列
  // if (summaryData.value.dates && summaryData.value.dates.length > 0) {
  //   headers.push({ date: 'total', display: '总计', isTotal: true })
  // }

  // 再添加日期列 - 处理uniqueKey格式
  const dates = summaryData.value.dates.map((uniqueKey) => {
    // 从uniqueKey中提取原始日期（去掉_index部分）
    const originalDate = uniqueKey.split('_')[0]
    return {
      date: uniqueKey, // 保持uniqueKey用于数据查找
      display: formatDateDisplay(originalDate), // 显示原始日期
      isTotal: false
    }
  })

  headers.push(...dates)
  return headers
})

// 方法
const handleTimeTypeChange = (timeType) => {
  summaryFilters.timeType = timeType
  // 根据时间类型设置默认时间范围
  if (timeType === 'day') {
    // 默认前一天
    const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    summaryFilters.dateRange = [yesterday, yesterday]
    summaryFilters.monthRange = []
    summaryFilters.weekRange = []
  } else if (timeType === 'month') {
    // 默认当前月
    const currentMonth = dayjs().format('YYYY-MM')
    summaryFilters.dateRange = []
    summaryFilters.monthRange = [currentMonth, currentMonth]
    summaryFilters.weekRange = []
  } else if (timeType === 'week') {
    // 默认本周（周一到周日）
    const startOfWeek = dayjs().startOf('week').add(1, 'day').format('YYYY-MM-DD') // 周一
    const endOfWeek = dayjs().endOf('week').add(1, 'day').format('YYYY-MM-DD') // 周日
    summaryFilters.dateRange = []
    summaryFilters.monthRange = []
    summaryFilters.weekRange = [startOfWeek, endOfWeek]
  }
  // loadData()
}

const handleDateRangeChange = (value: any, type: string) => {
  if (type === 'summary') {
    summaryFilters.dateRange = value
  }
}

const handleMonthRangeChange = (value: any) => {
  summaryFilters.monthRange = value
}

const handleWeekRangeChange = (value: any) => {
  summaryFilters.weekRange = value
}

const disabledDate = (time: Date) => {
  // 禁用未来日期
  const now = new Date()
  if (time.getTime() > now.getTime()) {
    return true
  }

  // 禁用2025年6月份之前的日期
  const minDate = new Date(2025, 5, 1) // 2025年6月1日 (月份从0开始，5表示6月)
  return time.getTime() < minDate.getTime()
}

const searchSummaryData = () => {
  // 验证日期范围是否已选择
  let hasDateRange = false

  if (
    summaryFilters.timeType === 'day' &&
    summaryFilters.dateRange &&
    summaryFilters.dateRange.length === 2
  ) {
    hasDateRange = true
  } else if (
    summaryFilters.timeType === 'month' &&
    summaryFilters.monthRange &&
    summaryFilters.monthRange.length === 2
  ) {
    hasDateRange = true
  } else if (
    summaryFilters.timeType === 'week' &&
    summaryFilters.weekRange &&
    summaryFilters.weekRange.length === 2
  ) {
    hasDateRange = true
  }

  if (!hasDateRange) {
    ElMessage.warning('请选择日期范围后再进行查询')
    return
  }

  loadData()
}

const resetSummaryFilters = () => {
  // 根据当前时间类型设置默认时间范围
  let defaultDateRange = []
  let defaultMonthRange = []
  let defaultWeekRange = []

  if (summaryFilters.timeType === 'day') {
    // 默认显示昨天
    const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    defaultDateRange = [yesterday, yesterday]
  } else if (summaryFilters.timeType === 'month') {
    // 默认当前月 - 开始时间为月初，结束时间为月末
    const startOfMonth = dayjs().startOf('month').format('YYYY-MM')
    const endOfMonth = dayjs().endOf('month').format('YYYY-MM')
    defaultMonthRange = [startOfMonth, endOfMonth]
  } else if (summaryFilters.timeType === 'week') {
    // 默认显示本周（周一到周日）
    const startOfWeek = dayjs().startOf('week').add(1, 'day').format('YYYY-MM-DD') // 周一
    const endOfWeek = dayjs().endOf('week').add(1, 'day').format('YYYY-MM-DD') // 周日
    defaultWeekRange = [startOfWeek, endOfWeek]
  }

  Object.assign(summaryFilters, {
    currencyCode: '',
    sidQuerySet: [], // 店铺ID
    shopNames: [], // 店铺名称
    countries: [], // 国家代码
    countrysList: [], // 国家名称
    principalNamesQuerySet: [], // 负责人ID
    // 保持当前时间类型不变
    dateRange: defaultDateRange,
    monthRange: defaultMonthRange,
    weekRange: defaultWeekRange,
    pageNo: 1,
    pageSize: 50
  })
  // 重置AccountShopSelector组件
  if (accountShopSelectorRef.value) {
    accountShopSelectorRef.value.resetSelection('account')
    accountShopSelectorRef.value.resetSelection('shop')
    accountShopSelectorRef.value.resetSelection('country')
  }
  loadData()
}

const exportSummaryData = () => {
  ElMessage.success('导出功能开发中...')
}

const formatCurrency = (value) => {
  // 如果是loading状态，返回空字符串以显示骨架屏
  if (value === 'loading') return ''
  // if (value === null || value === undefined || value === '') return '-'
  // const num = parseFloat(value)
  // if (isNaN(num)) return value // 如果转换后不是数字，直接返回原始值
  // return num.toFixed(2)
  return value
}

const formatQuantity = (value) => {
  // 如果是loading状态，返回空字符串以显示骨架屏
  if (value === 'loading') return ''
  if (value === null || value === undefined || value === '') return '-'
  const num = parseInt(value)
  if (isNaN(num)) return value
  return num.toString()
}

const formatRatio = (value) => {
  if (value === null || value === undefined || value === '') return ''
  return value
}

const formatDateDisplay = (dateStr) => {
  if (!dateStr) return ''

  // 如果是uniqueKey格式（包含下划线），提取原始日期部分
  const originalDate = dateStr.includes('_') ? dateStr.split('_')[0] : dateStr

  // 如果是时间戳，转换为日期
  if (/^\d+$/.test(originalDate)) {
    const date = new Date(parseInt(originalDate))
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${month}-${day}`
  }

  // 如果已经是日期格式（如01-01），直接返回
  return originalDate
}

const getDateValue = (date, key) => {
  if (date === 'total') {
    // 如果总计数据为null，表示正在加载中
    if (summaryData.value.total === null) {
      return 'loading'
    }
    return summaryData.value.total ? summaryData.value.total[key] : null
  }
  const source = summaryData.value.monthly?.[date]
  return source ? source[key] : null
}

// 根据数据类型格式化值
const formatValueByType = (value, type) => {
  if (value === null || value === undefined || value === '') return '-'

  switch (type) {
    case 'quantity':
      return formatQuantity(value)
    case 'currency':
    case 'cny':
    case 'profit':
      return formatCurrency(value)
    case 'percent':
      return formatRatio(value)
    default:
      return formatCurrency(value)
  }
}

// 计算百分比
const calculatePercentage = (date, key, baseKey) => {
  if (!baseKey) return null

  const value = getDateValue(date, key)
  const baseValue = getDateValue(date, baseKey)

  if (!value || !baseValue || baseValue === 0) return null

  return value / baseValue
}

const calculateRatio = (date, key) => {
  // 根据需要计算比率，这里暂时返回null
  return null
}

// 获取管理费详情数据
const getManagementFeeDetails = (date) => {
  const managementFeeTotal = getDateValue(date, 'managementFee') || 0
  const everyManagementFeeData = getDateValue(date, 'everyManagementFee')
  return generateManagementFeeDetails(managementFeeTotal, everyManagementFeeData)
}

// 生成管理费详情数据
const generateManagementFeeDetails = (totalAmount, everyManagementFeeData) => {
  if (!totalAmount || totalAmount === 0) {
    return {
      laborCost: 0,
      designCost: 0,
      venueCost: 0,
      otherCost: 0,
      vat: 0
    }
  }

  // 如果有everyManagementFee数据，使用该数据
  if (everyManagementFeeData && typeof everyManagementFeeData === 'object') {
    return {
      laborCost: everyManagementFeeData[0] || 0, // 人工费
      designCost: everyManagementFeeData[1] || 0, // 设计费
      venueCost: everyManagementFeeData[2] || 0, // 场地费
      otherCost: everyManagementFeeData[3] || 0, // 其它均摊费
      vat: everyManagementFeeData[4] || 0 // VAT
    }
  }

  // 如果没有详细数据，按比例分配管理费
  const laborCost = Math.round(totalAmount * 0.45) // 人工费45%
  const designCost = Math.round(totalAmount * 0.15) // 设计费15%
  const venueCost = Math.round(totalAmount * 0.2) // 场地费20%
  const vat = Math.round(totalAmount * 0.08) // VAT 8%
  const otherCost = totalAmount - laborCost - designCost - venueCost - vat // 其它均摊费

  return {
    laborCost,
    designCost,
    venueCost,
    otherCost,
    vat
  }
}

// 列宽调整逻辑
const columnWidths = ref<Record<string, number>>({})
const isResizing = ref(false)
const resizingColumn = ref('')
const startX = ref(0)
const startWidth = ref(0)

const initializeColumnWidths = () => {
  const initialWidths = {}
  const dateHeaders = listDateHeaders.value
  initialWidths['item-name'] = 200 // 项目列默认宽度
  dateHeaders.forEach((header) => {
    initialWidths[header.date] = 150 // 日期列默认宽度
  })
  columnWidths.value = initialWidths
}

const getColumnWidth = (columnKey) => {
  return columnWidths.value[columnKey] || 150 // 默认宽度
}

const startResize = (event, columnKey) => {
  isResizing.value = true
  resizingColumn.value = columnKey
  startX.value = event.clientX
  startWidth.value = getColumnWidth(columnKey)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event) => {
  if (!isResizing.value) return
  const diffX = event.clientX - startX.value
  const newWidth = startWidth.value + diffX
  if (newWidth > 50) {
    // 最小宽度
    columnWidths.value[resizingColumn.value] = newWidth
  }
}

const handleMouseUp = () => {
  isResizing.value = false
  resizingColumn.value = ''
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const loadData = async () => {
  loading.value = true
  totalLoading.value = true
  try {
    // 构建天、月、周查询参数
    const dimensionTypeMap = {
      day: 3,
      month: 5,
      week: 4
    }

    // 构建符合接口规范的查询参数
    const params = {
      ...summaryFilters,
      pageNo: summaryFilters.pageNo,
      pageSize: summaryFilters.pageSize,
      type: dimensionTypeMap[summaryFilters.timeType] || 3
    }

    // 根据时间类型设置对应的日期参数
    if (
      summaryFilters.timeType === 'day' &&
      summaryFilters.dateRange &&
      summaryFilters.dateRange.length === 2
    ) {
      params.beginOrderDate = dayjs(summaryFilters.dateRange[0]).format('YYYY-MM-DD')
      params.endOrderDate = dayjs(summaryFilters.dateRange[1]).format('YYYY-MM-DD')
    } else if (
      summaryFilters.timeType === 'month' &&
      summaryFilters.monthRange &&
      summaryFilters.monthRange.length === 2
    ) {
      // 对于月份范围，开始时间为月初，结束时间为月末
      const startMonth = dayjs(summaryFilters.monthRange[0]).startOf('month')
      const endMonth = dayjs(summaryFilters.monthRange[1]).endOf('month')
      params.beginOrderDate = startMonth.format('YYYY-MM-DD')
      params.endOrderDate = endMonth.format('YYYY-MM-DD')
    } else if (
      summaryFilters.timeType === 'week' &&
      summaryFilters.weekRange &&
      summaryFilters.weekRange.length === 2
    ) {
      params.beginOrderDate = dayjs(summaryFilters.weekRange[0]).format('YYYY-MM-DD')
      params.endOrderDate = dayjs(summaryFilters.weekRange[1]).format('YYYY-MM-DD')
    }

    // 根据skuType设置对应的查询参数
    if (params.searchType) {
      if (params.searchValue !== '') {
        params[params.searchType] = params.searchValue
      }
    }

    delete params.timeType
    delete params.searchType
    delete params.searchValue
    delete params.dateRange
    delete params.monthRange
    delete params.weekRange
    delete params.countrysList
    delete params.shopNames

    // 先调用分页数据接口
    const pageRes = await getMonthlyReportPage(params)
    const apiData = pageRes.list

    if (!apiData || apiData.length === 0) {
      summaryData.value = {}
      extraData.value = {}
      loading.value = false
      totalLoading.value = false
      initializeColumnWidths()
      return
    }

    const newMonthlyData = {}
    const dates = []
    const totalData = {}

    // 提取extraData用于动态汇总项生成
    const firstItemExtraData = apiData[0].extraData || {}
    extraData.value = firstItemExtraData

    // 异步加载总计数据
    getMonthlyReportStatistic({ ...params, isStatistic: true })
      .then((statisticData) => {
        if (statisticData) {
          // API字段映射到dynamicSummaryItems的key
          const fieldMapping = {
            salesQuantity: statisticData.totalSalesQuantity,
            returnQuantity: statisticData.fbaReturnsQuantity,
            salesAmount: statisticData.totalSalesAmount,
            commission: statisticData.platformFee,
            amazonShippingFee: statisticData.fbaDeliveryFee,
            amazonStorageFee: statisticData.totalStorageFee,
            productCost: statisticData.cgPriceTotal,
            shippingCost: statisticData.cgTransportCostsTotal,
            adSpend: statisticData.totalAdsCost,
            grossProfit: statisticData.grossProfit,
            managementFee: statisticData.managementFee,
            paybackAmount: statisticData.settlementTotal,
            grossProfitRate: statisticData.grossRate
          }

          // 处理直接字段映射
          Object.keys(fieldMapping).forEach((key) => {
            const value = fieldMapping[key]
            if (value !== undefined && value !== null) {
              totalData[key] = value || 0
            }
          })

          // 从统计数据的extraData中获取总计
          if (statisticData.extraData) {
            const extraDataMapping = {
              经营利润: 'operatingProfit',
              实际销量: 'actualSales',
              收入: 'platformIncome',
              平台支出: 'totalPlatformExpense',
              总投入: 'totalInvestment',
              服务商扣点: 'serviceCommission',
              店铺租金: 'storeRent',
              总成本合计: 'totalCostTotal',
              毛利润: 'grossProfit',
              毛利率: 'grossProfitRate',
              回款: 'paybackAmount',
              其他平台支出费用: 'otherPlatformExpense',
              平均采购成本: 'avgProductCost',
              平均头程成本: 'avgShippingCost',
              包材: 'packagingMaterial'
            }

            Object.keys(statisticData.extraData).forEach((chineseKey) => {
              const englishKey = extraDataMapping[chineseKey] || chineseKey
              const skipFields = ['grossProfit', 'grossProfitRate', 'paybackAmount']
              if (!skipFields.includes(englishKey)) {
                totalData[englishKey] = statisticData.extraData[chineseKey] || 0
              }
            })
          }

          // 重新计算实际销量和回款率
          if (totalData.salesQuantity !== undefined && totalData.returnQuantity !== undefined) {
            totalData.actualSales = totalData.salesQuantity - totalData.returnQuantity
          }
          if (
            totalData.paybackAmount !== undefined &&
            totalData.salesAmount !== undefined &&
            totalData.salesAmount > 0
          ) {
            totalData.paybackRate = (totalData.paybackAmount / totalData.salesAmount) * 100
          }

          // 更新总计数据
          summaryData.value.total = totalData
        } else {
          // 如果没有统计数据，则初始化为0
          if (firstItemExtraData) {
            Object.keys(firstItemExtraData).forEach((key) => {
              totalData[key] = 0
            })
          }
          allSummaryItems.forEach((item) => {
            totalData[item.key] = 0
          })
          summaryData.value.total = totalData
        }
      })
      .catch((error) => {
        console.error('加载总计数据失败:', error)
        // 如果统计接口失败，初始化为0
        if (firstItemExtraData) {
          Object.keys(firstItemExtraData).forEach((key) => {
            totalData[key] = 0
          })
        }
        allSummaryItems.forEach((item) => {
          totalData[item.key] = 0
        })
        summaryData.value.total = totalData
      })
      .finally(() => {
        totalLoading.value = false
      })

    apiData.forEach((item, index) => {
      // 根据时间类型获取对应的日期字段
      let dateKey
      if (summaryFilters.timeType === 'day') {
        dateKey = item.postedDateLocale
      } else if (summaryFilters.timeType === 'week') {
        dateKey = item.weekOfYear
      } else if (summaryFilters.timeType === 'month') {
        dateKey = item.monthOfYear
      } else {
        dateKey = item.orderDate // 兼容旧逻辑
      }

      if (dateKey) {
        // 为每条记录创建唯一标识符，即使日期相同也要分别显示
        const uniqueKey = `${dateKey}_${index}`

        // 添加唯一标识符到dates数组
        dates.push(uniqueKey)

        // 为每条记录创建独立的数据对象
        newMonthlyData[uniqueKey] = {}

        const dayData = newMonthlyData[uniqueKey]

        // API字段映射到dynamicSummaryItems的key
        const fieldMapping = {
          // 直接字段映射
          salesQuantity: item.totalSalesQuantity,
          returnQuantity: item.fbaReturnsQuantity,
          salesAmount: item.totalSalesAmount,
          commission: item.platformFee,
          amazonShippingFee: item.fbaDeliveryFee,
          amazonStorageFee: item.totalStorageFee,
          productCost: item.cgPriceTotal,
          shippingCost: item.cgTransportCostsTotal,
          adSpend: item.totalAdsCost,
          grossProfit: item.grossProfit,
          managementFee: item.managementFee,
          paybackAmount: item.settlementTotal,
          grossProfitRate: item.grossRate
        }

        // 处理直接字段映射 - 直接赋值，不累加
        Object.keys(fieldMapping).forEach((key) => {
          const value = fieldMapping[key]
          if (value !== undefined && value !== null) {
            const numValue = value || 0
            dayData[key] = numValue
            // 注意：总计数据已从统计接口获取，这里不再累加
          }
        })

        // 从extraData中获取动态汇总项数据
        if (item.extraData) {
          // extraData中文字段名到英文key的映射
          const extraDataMapping = {
            经营利润: 'operatingProfit',
            实际销量: 'actualSales',
            收入: 'platformIncome',
            平台支出: 'totalPlatformExpense',
            总投入: 'totalInvestment',
            服务商扣点: 'serviceCommission',
            店铺租金: 'storeRent',
            总成本合计: 'totalCostTotal',
            毛利润: 'grossProfit',
            毛利率: 'grossProfitRate',
            回款: 'paybackAmount',
            其他平台支出费用: 'otherPlatformExpense',
            平均采购成本: 'avgProductCost',
            平均头程成本: 'avgShippingCost',
            包材: 'packagingMaterial'
          }

          Object.keys(item.extraData).forEach((chineseKey) => {
            const englishKey = extraDataMapping[chineseKey] || chineseKey

            // 跳过已经在直接字段映射中处理的字段，避免重复
            const skipFields = ['grossProfit', 'grossProfitRate', 'paybackAmount']
            if (skipFields.includes(englishKey)) {
              return
            }

            const value = item.extraData[chineseKey] || 0
            dayData[englishKey] = value
            // 注意：总计数据已从统计接口获取，这里不再累加
          })
        }

        // 重新计算实际销量 (销量 - 退货量)
        if (dayData.salesQuantity !== undefined && dayData.returnQuantity !== undefined) {
          dayData.actualSales = dayData.salesQuantity - dayData.returnQuantity
        }

        // 重新计算回款率
        if (
          dayData.paybackAmount !== undefined &&
          dayData.salesAmount !== undefined &&
          dayData.salesAmount > 0
        ) {
          dayData.paybackRate = (dayData.paybackAmount / dayData.salesAmount) * 100
        }
      }
    })

    // 先设置列表数据，总计数据异步加载
    summaryData.value = {
      dates: dates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime()), // 日期升序
      monthly: newMonthlyData,
      total: null // 初始化为null，表示正在加载中
    }

    initializeColumnWidths()
  } catch (error) {
    ElMessage.error('加载汇总数据失败')
    console.error('Failed to load summary data:', error)
    summaryData.value = {}
    extraData.value = {}
    totalLoading.value = false
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getPrincipalListFc()
  loadCurrencyOptions()
  loadData()
})
</script>

<style lang="scss" scoped>
// 响应式设计
@media (width <= 768px) {
  .horizontal-scroll-container {
    padding-bottom: 20px;
  }

  .item-name-header,
  .date-header,
  .item-name,
  .item-value {
    padding: 8px 6px;
    font-size: 12px;
  }

  .group-title {
    padding: 8px 6px;
    font-size: 12px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.monthly-summary-display {
  width: 100%;
  overflow: hidden;
}

.horizontal-scroll-container {
  padding-bottom: 15px; /* for scrollbar */
  overflow-x: auto;
}

.horizontal-table-wrapper {
  display: inline-block;
  min-width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.table-header {
  display: flex;
  font-weight: bold;
  color: #909399;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.item-name-header,
.date-header {
  position: relative; /* For resizer */
  padding: 8px 6px;
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  border-right: 1px solid #ebeef5;
}

.item-name-header {
  position: sticky;
  left: 0;
  z-index: 2;
  background-color: #f5f7fa;
}

.date-headers {
  display: flex;
}

.total-column {
  font-weight: bold !important;
  background-color: #f0f9eb;
  border-right: 2px solid #007bff !important;
}

.data-rows {
  /* styles for data rows container */
}

.data-group {
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.group-header {
  display: flex;
  background-color: #fafafa;
}

.group-title {
  position: sticky;
  left: 0;
  z-index: 1;
  padding: 10px;
  font-size: 12px;
  font-weight: bold;
  color: #606266;
  background-color: #fafafa;
  border-right: 1px solid #ebeef5;
}

.data-row {
  display: flex;

  &:not(:last-child) {
    border-bottom: 1px solid #ebeef5;
  }
}

.item-name {
  position: sticky;
  left: 0;
  z-index: 1;
  padding: 10px;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
}

.profit-item {
  // font-weight: 500;
  color: #67c23a;
  border-left: 4px solid #27ae60;
}

.revenue-item {
  // font-weight: 500;
  color: #409eff;
  border-left: 4px solid #3498db;
}

.cost-item {
  // font-weight: 500;
  color: #f56c6c;
  border-left: 4px solid #f39c12;
}

.highlight-item {
  font-weight: bold;
  color: #d46b08 !important;
  background-color: #fff7e6;
  border-left: 4px solid #faad14 !important;
}

.item-values {
  display: flex;
}

.item-value {
  display: flex;
  padding: 10px;
  font-size: 12px;
  // text-align: right;
  white-space: nowrap;
  border-right: 1px solid #ebeef5;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.two-line-value {
  .value-amount {
    font-size: 12px;
    // font-weight: 500;
    color: #303133;
  }

  .value-ratio {
    margin-top: 2px;
    font-size: 12px;
    color: #909399;
  }
}

.profit .value-amount {
  color: #67c23a;
}

.revenue .value-amount {
  color: #409eff;
}

.cost .value-amount {
  color: #f56c6c;
}

.highlight-value .value-amount {
  padding: 2px 4px;
  font-weight: bold;
  color: #d46b08;
  background-color: #fff7e6;
  border-radius: 4px;
}

.management-fee-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.inline-arrow {
  margin-left: 4px;
  color: #409eff;
  cursor: pointer;

  &:hover {
    color: #66b1ff;
  }
}

.fee-detail-arrow {
  font-size: 12px;
}

.column-resizer {
  position: absolute;
  top: 0;
  right: -5px;
  z-index: 10;
  width: 10px;
  height: 100%;
  cursor: col-resize;

  &:hover {
    background-color: rgb(64 158 255 / 20%);
  }
}

.loading-container,
.empty-container {
  padding: 20px;
  text-align: center;
}

/* 管理费详情内容样式 */
.management-fee-detail-content {
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f2f5;

    &:last-child {
      border-bottom: none;
    }

    &.total-row {
      padding-top: 12px;
      margin-top: 8px;
      font-weight: 600;
      border-top: 2px solid #e4e7ed;

      .label,
      .value {
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
      }
    }

    .label {
      font-size: 13px;
      color: #606266;
    }

    .value {
      font-size: 13px;
      font-weight: 500;
      color: #303133;
    }
  }
}
</style>
