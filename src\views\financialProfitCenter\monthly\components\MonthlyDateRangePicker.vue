<template>
  <div class="flex flex-row items-center gap-2">
    <!-- 时间类型选择器 -->
    <div class="time-type-tabs">
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'day' }"
        @click="handleTimeTypeChange('day')"
      >
        按天
      </div>
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'month' }"
        @click="handleTimeTypeChange('month')"
      >
        按月
      </div>
      <div
        class="time-type-tab"
        :class="{ active: currentTimeType === 'week' }"
        @click="handleTimeTypeChange('week')"
      >
        按周
      </div>
    </div>

    <!-- 按天选择器 -->
    <el-date-picker
      v-if="currentTimeType === 'day'"
      v-model="times"
      type="daterange"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :shortcuts="dayShortcuts"
      class="!w-240px"
      :disabled-date="disabledDate"
      value-format="YYYY-MM-DD"
      @change="handleDateChange"
    />

    <!-- 按月选择器 -->
    <el-date-picker
      v-else-if="currentTimeType === 'month'"
      v-model="monthTimes"
      type="monthrange"
      range-separator="至"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      :shortcuts="monthShortcuts"
      format="YYYY-MM"
      value-format="YYYY-MM"
      class="!w-240px"
      :disabled-date="disabledDate"
      @change="handleMonthChange"
    />

    <!-- 按周选择器 -->
    <el-date-picker
      v-else-if="currentTimeType === 'week'"
      v-model="weekTimes"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :shortcuts="weekShortcuts"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      class="!w-240px"
      :disabled-date="disabledDate"
      @change="handleWeekChange"
    />

    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { onMounted, ref, watch, type PropType } from 'vue'

const props = defineProps({
  /** v-model 绑定的值 */
  modelValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  /** 默认时间类型 */
  defaultTimeType: {
    type: String as PropType<'day' | 'month' | 'week'>,
    default: 'day'
  },
  /** 月份范围值 */
  monthValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  /** 周范围值 */
  weekValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  /** 禁用日期函数 */
  disabledDate: {
    type: Function as PropType<(time: Date) => boolean>,
    default: () => false
  }
})

const emits = defineEmits<{
  'update:modelValue': [value: [string, string]]
  'update:monthValue': [value: [string, string]]
  'update:weekValue': [value: [string, string]]
  change: [times: [string, string]]
  'time-type-change': [timeType: 'day' | 'month' | 'week']
}>()

/** 月度财务日期范围选择组件 */
defineOptions({ name: 'MonthlyDateRangePicker' })

const times = ref<[string, string]>(props.modelValue) // 时间范围参数
const monthTimes = ref<[string, string]>(props.monthValue) // 月份范围参数
const weekTimes = ref<[string, string]>(props.weekValue) // 周范围参数
const currentTimeType = ref<'day' | 'month' | 'week'>(props.defaultTimeType) // 当前时间类型

/** 按天日期快捷选择 */
const dayShortcuts = [
  {
    text: '今天',
    value: () => {
      const today = dayjs().format('YYYY-MM-DD')
      return [today, today]
    }
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').format('YYYY-MM-DD')
      const end = dayjs().endOf('month').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(29, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  }
]

/** 按月快捷选择 */
const monthShortcuts = [
  {
    text: '本月',
    value: () => {
      const currentMonth = dayjs().format('YYYY-MM')
      return [currentMonth, currentMonth]
    }
  },
  {
    text: '上月',
    value: () => {
      const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM')
      return [lastMonth, lastMonth]
    }
  },
  {
    text: '最近3个月',
    value: () => {
      const end = dayjs().format('YYYY-MM')
      const start = dayjs().subtract(2, 'month').format('YYYY-MM')
      return [start, end]
    }
  },
  {
    text: '最近6个月',
    value: () => {
      const end = dayjs().format('YYYY-MM')
      const start = dayjs().subtract(5, 'month').format('YYYY-MM')
      return [start, end]
    }
  },
  {
    text: '今年',
    value: () => {
      const start = dayjs().startOf('year').format('YYYY-MM')
      const end = dayjs().endOf('year').format('YYYY-MM')
      return [start, end]
    }
  },
  {
    text: '去年',
    value: () => {
      const start = dayjs().subtract(1, 'year').startOf('year').format('YYYY-MM')
      const end = dayjs().subtract(1, 'year').endOf('year').format('YYYY-MM')
      return [start, end]
    }
  }
]

/** 按周快捷选择 */
const weekShortcuts = [
  {
    text: '本周',
    value: () => {
      const startOfWeek = dayjs().startOf('week').add(1, 'day').format('YYYY-MM-DD') // 周一
      const endOfWeek = dayjs().endOf('week').add(1, 'day').format('YYYY-MM-DD') // 周日
      return [startOfWeek, endOfWeek]
    }
  },
  {
    text: '上周',
    value: () => {
      const startOfLastWeek = dayjs()
        .subtract(1, 'week')
        .startOf('week')
        .add(1, 'day')
        .format('YYYY-MM-DD')
      const endOfLastWeek = dayjs()
        .subtract(1, 'week')
        .endOf('week')
        .add(1, 'day')
        .format('YYYY-MM-DD')
      return [startOfLastWeek, endOfLastWeek]
    }
  },
  {
    text: '最近4周',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(27, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  },
  {
    text: '最近8周',
    value: () => {
      const end = dayjs().format('YYYY-MM-DD')
      const start = dayjs().subtract(55, 'day').format('YYYY-MM-DD')
      return [start, end]
    }
  }
]

/** 获取默认日期范围 */
const getDefaultDateRange = (timeType: 'day' | 'month' | 'week') => {
  const today = dayjs()
  const yesterday = today.subtract(1, 'day')

  switch (timeType) {
    case 'day':
      // 默认昨天
      return {
        dateRange: [yesterday.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD')],
        monthRange: [],
        weekRange: []
      }
    case 'month':
      // 默认当前月 - 开始时间为月初，结束时间为月末
      const startOfMonth = today.startOf('month').format('YYYY-MM')
      const endOfMonth = today.endOf('month').format('YYYY-MM')
      return {
        dateRange: [],
        monthRange: [startOfMonth, endOfMonth],
        weekRange: []
      }
    case 'week':
      // 默认本周（周一到周日）
      const startOfWeek = today.startOf('week').add(1, 'day').format('YYYY-MM-DD') // 周一
      const endOfWeek = today.endOf('week').add(1, 'day').format('YYYY-MM-DD') // 周日
      return {
        dateRange: [],
        monthRange: [],
        weekRange: [startOfWeek, endOfWeek]
      }
    default:
      return {
        dateRange: [],
        monthRange: [],
        weekRange: []
      }
  }
}

/** 时间类型切换 */
const handleTimeTypeChange = (timeType: 'day' | 'month' | 'week') => {
  currentTimeType.value = timeType

  // 设置默认值
  const defaultRanges = getDefaultDateRange(timeType)

  if (timeType === 'day') {
    times.value = defaultRanges.dateRange as [string, string]
    emits('update:modelValue', times.value)
  } else if (timeType === 'month') {
    monthTimes.value = defaultRanges.monthRange as [string, string]
    emits('update:monthValue', monthTimes.value)
  } else if (timeType === 'week') {
    weekTimes.value = defaultRanges.weekRange as [string, string]
    emits('update:weekValue', weekTimes.value)
  }

  emits('time-type-change', timeType)
}

/** 日期选择器变化事件 */
const handleDateChange = (value: [string, string]) => {
  times.value = value
  emits('update:modelValue', value)
  emits('change', value)
}

/** 月份选择器变化事件 */
const handleMonthChange = (value: [string, string]) => {
  monthTimes.value = value
  emits('update:monthValue', value)
  emits('change', value)
}

/** 周选择器变化事件 */
const handleWeekChange = (value: [string, string]) => {
  weekTimes.value = value
  emits('update:weekValue', value)
  emits('change', value)
}

/** 重置到默认值 */
const resetToDefaults = () => {
  const defaultRanges = getDefaultDateRange(currentTimeType.value)

  if (currentTimeType.value === 'day') {
    times.value = defaultRanges.dateRange as [string, string]
    emits('update:modelValue', times.value)
  } else if (currentTimeType.value === 'month') {
    monthTimes.value = defaultRanges.monthRange as [string, string]
    emits('update:monthValue', monthTimes.value)
  } else if (currentTimeType.value === 'week') {
    weekTimes.value = defaultRanges.weekRange as [string, string]
    emits('update:weekValue', weekTimes.value)
  }
}

// 暴露方法和数据
defineExpose({ times, monthTimes, weekTimes, currentTimeType, resetToDefaults })

/** 监听 modelValue 变化 */
watch(
  () => props.modelValue,
  (newValue) => {
    times.value = newValue
  },
  { immediate: true }
)

/** 监听 monthValue 变化 */
watch(
  () => props.monthValue,
  (newValue) => {
    monthTimes.value = newValue
  },
  { immediate: true }
)

/** 监听 weekValue 变化 */
watch(
  () => props.weekValue,
  (newValue) => {
    weekTimes.value = newValue
  },
  { immediate: true }
)

/** 初始化 */
onMounted(() => {
  // 检查是否有初始值，如果没有则设置默认值
  const hasInitialValue =
    (currentTimeType.value === 'day' &&
      props.modelValue &&
      props.modelValue.length > 0 &&
      props.modelValue.some((v) => v)) ||
    (currentTimeType.value === 'month' &&
      props.monthValue &&
      props.monthValue.length > 0 &&
      props.monthValue.some((v) => v)) ||
    (currentTimeType.value === 'week' &&
      props.weekValue &&
      props.weekValue.length > 0 &&
      props.weekValue.some((v) => v))

  if (!hasInitialValue) {
    const defaultRanges = getDefaultDateRange(currentTimeType.value)

    if (currentTimeType.value === 'day') {
      times.value = defaultRanges.dateRange as [string, string]
      emits('update:modelValue', times.value)
    } else if (currentTimeType.value === 'month') {
      monthTimes.value = defaultRanges.monthRange as [string, string]
      emits('update:monthValue', monthTimes.value)
    } else if (currentTimeType.value === 'week') {
      weekTimes.value = defaultRanges.weekRange as [string, string]
      emits('update:weekValue', weekTimes.value)
    }
  }
})
</script>

<style scoped>
.time-type-tabs {
  display: flex;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.time-type-tab {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  background-color: #fff;
  border-right: 1px solid #dcdfe6;
  transition: all 0.3s;
  user-select: none;
}

.time-type-tab:last-child {
  border-right: none;
}

.time-type-tab:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.time-type-tab.active {
  color: #fff;
  background-color: #409eff;
}
</style>
