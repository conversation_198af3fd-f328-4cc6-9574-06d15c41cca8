<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      style="display: flex; flex-wrap: wrap"
    >
      <AccountShopSelector
        v-model:account-values="queryParams.uids"
        v-model:shop-values="queryParams.sids"
        v-model:country-values="queryParams.countrysList"
        v-model:country-names="queryParams.countrys"
        @account-change="queryAccountChange"
        @shop-change="queryShopChange"
        @country-change="queryCountryChange"
      />
      <el-form-item label="" prop="skuType">
        <el-select v-model="queryParams.skuType" class="!w-90px" clearable placeholder="请选择">
          <el-option label="ASIN" value="ASIN" />
          <el-option label="SKU" value="SKU" />
          <el-option label="MSKU" value="MSKU" />
          <el-option label="FNSKU" value="FNSKU" />
          <el-option label="品名" value="品名" />
        </el-select>
        <TipInput
          v-model="queryParams.skuValue"
          inputClass="!w-150px"
          placeholder=""
          tooltipIcon="fa-solid:bars"
          @enter="handleTipInputEnter"
        />
      </el-form-item>
      <el-form-item label="负责人" prop="principalUids">
        <RcInputSelect
          v-model="queryParams.principalUids"
          style="min-width: 150px"
          :options="principalOptions"
        />
      </el-form-item>
      <el-form-item label="是否申请补货" prop="markedRestock">
        <el-select
          v-model="queryParams.markedRestock"
          class="!w-120px"
          clearable
          placeholder="全部"
        >
          <el-option label="全部" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否建议补货" prop="resuggestRestock">
        <el-select
          v-model="queryParams.resuggestRestock"
          class="!w-120px"
          clearable
          placeholder="全部"
        >
          <el-option label="全部" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="globalTagIds">
        <RcInputSelect
          v-model="queryParams.globalTagIds"
          style="min-width: 150px"
          :options="tagOptions"
        />
      </el-form-item>
      <el-form-item label="分析时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.analysisTime"
          :default-time="defaultTimeRange"
          class="!w-340px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          :shortcuts="defaultShortcuts"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          type="primary"
          @click="handleQuery"
          style="margin-left: 10px"
        >
          查询
        </el-button>
        <el-button @click="resetQuery"> 重置 </el-button>
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          plain
          type="primary"
          @click="handleGenerates"
        >
          智能补货分析
        </el-button>
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          plain
          type="primary"
          @click="handleGenerates"
        >
          默认规则设置
        </el-button>
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          plain
          type="primary"
          @click="handleGenerates"
        >
          自定义设置规则
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 使用封装的Table组件 -->
    <Table
      ref="copywritingTableRef"
      :loading="loading"
      :data="list"
      :columns="tableColumns"
      selection
      border
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @selection-change="handleSelectionChange"
      @update:page-size="
        (val) => {
          queryParams.pageSize = val
          getList()
        }
      "
      @update:current-page="
        (val) => {
          queryParams.pageNo = val
          getList()
        }
      "
    >
      <!-- 操作按钮区域 -->
      <template #top-btn>
        <el-button
          type=""
          plain
          @click="handleExp('select')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          申请补货
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('select')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出选中
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('all')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出全部
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('markedRestock')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出已申请补货
        </el-button>
      </template>

      <!-- 操作列模板 -->
      <template #action="scope">
        <!-- 如果是待生成状态就不展示 -->
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          link
          type="primary"
          @click="openLogForm(scope.row.asin, scope.row.id)"
        >
          历史诊断
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleExp('select')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          申请补货
        </el-button>
      </template>

      <!-- 备注列模板 -->
      <template #remark="scope">
        <el-input v-model="scope.row.remarkEdit" @blur="handleInput(scope.row)" />
      </template>

      <!-- 标签 -->
      <template #tagsResp="scope">
        <div
          v-if="
            scope.row.restockSuggestGlobalTagRespVoList &&
            scope.row.restockSuggestGlobalTagRespVoList.length > 0
          "
        >
          <el-tag
            :style="{
              backgroundColor: scope.row.restockSuggestGlobalTagRespVoList[0].color || '#409EFF',
              color: '#fff'
            }"
            class="mr-5px"
          >
            {{ scope.row.restockSuggestGlobalTagRespVoList[0].tagName }}
          </el-tag>
          <el-popover
            v-if="scope.row.restockSuggestGlobalTagRespVoList.length > 1"
            placement="top-start"
            trigger="hover"
            width="max-content-box"
          >
            <template #reference>
              <el-link type="primary" :underline="false" class="ml-5px"
                >共{{ scope.row.restockSuggestGlobalTagRespVoList.length }}个</el-link
              >
            </template>
            <div style="display: flex">
              <el-tag
                v-for="item in scope.row.restockSuggestGlobalTagRespVoList"
                :key="item.globalTagId"
                :style="{
                  backgroundColor: item.color || '#409EFF',
                  color: '#fff',
                  marginRight: '5px',
                  marginBottom: '5px'
                }"
              >
                {{ item.tagName }}
              </el-tag>
            </div>
          </el-popover>
        </div>
        <span v-else>-</span>
      </template>

      <!-- Listing负责人 -->
      <template #restockSuggestPrincipalInfoRespVos="scope">
        <span>
          {{
            scope.row.restockSuggestPrincipalInfoRespVos
              ?.map((item) => item.principalName)
              .join(',') || '-'
          }}
        </span>
      </template>

      <!-- 单日预测列模板 -->
      <template #predictedDailySales="scope">
        <span style="color: #f56c6c">{{ scope.row.predictedDailySales || '-' }}</span>
      </template>

      <!-- 预测总库存列模板 -->
      <template #predictedInventoryTarget="scope">
        <span style="color: #f56c6c">{{ scope.row.predictedInventoryTarget || '-' }}</span>
      </template>

      <!-- 建议补货列模板 -->
      <template #suggestedRestockQuantity="scope">
        <span style="font-weight: bold; color: #67c23a">{{
          scope.row.suggestedRestockQuantity || '-'
        }}</span>
      </template>
    </Table>

    <div class="flex items-center justify-between mt-10px">
      <el-text type="primary">已申请补货数 {{ isMarked }}</el-text>
    </div>
  </ContentWrap>

  <!-- 表单弹窗：诊断历史 -->
  <logListForm ref="logFormRef" @success="getList" />

  <!-- 表单弹窗：权重列表 -->
  <weightListForm ref="weightFormRef" @apply="applyWeight" />
</template>

<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { onMounted } from 'vue'
// import logListForm from './logListForm.vue'
// import weightListForm from './weightListForm.vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import type { TableColumn } from '@/types/table'
import download from '@/utils/download'
import { dateFormatter, defaultShortcuts, formatDate } from '@/utils/formatTime'

// 默认时间范围：今天 00:00:00 - 23:59:59
const defaultTimeRange = computed(() => {
  const now = new Date()
  const todayStart = new Date(now)
  todayStart.setHours(0, 0, 0, 0)

  const todayEnd = new Date(now)
  todayEnd.setHours(23, 59, 59, 999)
  return [formatDate(todayStart), formatDate(todayEnd)]
})

defineOptions({ name: 'ReplenishmentProposal' })

const accountShopSelectorRef = ref(null)

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 表格数据
const markedCount = ref(0) // 已标记补货数量

// 主查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  countrys: [], // 国家
  countrysList: [], // 国家
  sids: [], // 店铺
  shopNames: [], // 店铺名称
  uids: [],
  unames: [],
  inventoryCycle: 30, // 周期库存
  skuType: 'SKU', // 默认选择SKU类型
  skuValue: '', // SKU值
  principalName: '', // 负责人
  globalTagIds: [], //标签
  principalUids: [], // 负责人
  asinList: [], // ASIN
  mskuList: [], // MSKU
  fnskuList: [], // FNSKU
  localSkuList: [], // 本地SKU
  localNameList: [], // 品名
  markedRestock: '', // 是否标记补货 0:否 1:是
  resuggestRestock: '', // 是否建议补货 0:否 1:是
  analysisTime: defaultTimeRange.value //分析时间
})

const skuQueryList = ref([]) // 查询列表的，多个SKU查询列表

const totalWeight = ref(0)

const principalOptions = ref([])
const tagOptions = ref([])

// 获取负责人列表
const getPrincipalListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getPrincipalList()
    if (res) {
      // 将接口返回的负责人数据转换为下拉框选项格式
      principalOptions.value = res.map((item) => ({
        label: item.principalName,
        value: item.principalUid
      }))
    }
  } catch (error) {
    console.error('获取负责人列表失败', error)
  }
}

// 获取标签列表
const getTagList = async () => {
  try {
    const res = await ReplenishmentProposalApi.getGlobalTagList()
    if (res) {
      tagOptions.value = res.map((item) => ({
        label: item.tagName,
        value: item.globalTagId
      }))
    }
  } catch (error) {
    console.error('获取标签列表失败', error)
  }
}

const queryFormRef = ref() // 搜索的表单
const multipleSelection = ref<{ id: number }[]>([]) // 选中的列表项

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    const skuTypeMap = {
      ASIN: 'asinList',
      MSKU: 'mskuList',
      FNSKU: 'fnskuList',
      SKU: 'localSkuList',
      品名: 'localNameList'
    }

    // 清空所有列表
    const resetAllLists = () => {
      queryParams.asinList = []
      queryParams.mskuList = []
      queryParams.fnskuList = []
      queryParams.localSkuList = []
      queryParams.localNameList = []
    }

    // 根据skuType设置对应的查询参数
    if (queryParams.skuType && skuTypeMap[queryParams.skuType]) {
      resetAllLists()
      if (queryParams.skuValue !== '') {
        queryParams[skuTypeMap[queryParams.skuType]] = skuQueryList.value
      }
    }
    // 如果是数组，则使用第一个值或者逗号分隔的字符串
    if (Array.isArray(skuQueryList.value) && skuQueryList.value.length > 0) {
      queryParams.skuValue = skuQueryList.value.join(',')
    }

    let qdata = JSON.parse(JSON.stringify(queryParams))

    // 如果qdata.uids有all，删除all
    if (qdata.uids.includes('all')) {
      qdata.uids = qdata.uids.filter((item) => item !== 'all')
    }
    // 如果qdata.sids有all，删除all
    if (qdata.sids.includes('all')) {
      qdata.sids = qdata.sids.filter((item) => item !== 'all')
    }
    console.log(qdata)
    const data = await ReplenishmentProposalApi.getTableList(qdata)
    list.value =
      (data.list &&
        data.list.map((item) => {
          item.remarkEdit = item.remark
          return item
        })) ||
      []
    total.value = data.total
    // 计算已标记补货数量 - 这里可能需要根据实际业务逻辑调整
    markedCount.value = data.list && data.list.filter((item) => item.remark).length
    await getIsMarked()
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 打开诊断历史
const logFormRef = ref()
const openLogForm = (asin: any, id: number) => {
  logFormRef.value.open(asin, id)
}

const isMarked = ref(0)
const getIsMarked = async () => {
  // const data = await ReplenishmentProposalApi.getIsMarked()
  // if (data) {
  //   isMarked.value = data.markedRestockQty
  // }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.asinList = []
  queryParams.skuValue = ''
  queryParams.localSkuList = []
  queryParams.mskuList = []
  queryParams.fnskuList = []
  queryParams.localNameList = []
  queryParams.uids = []
  queryParams.sids = []
  queryParams.countrys = []
  queryParams.countrysList = []
  queryParams.shopNames = []
  queryParams.unames = []
  queryParams.analysisTime = []
  queryParams.globalTagIds = []
  skuQueryList.value = []
  handleQuery()
}

/** 导出选中 */
const handleExp = async (type) => {
  try {
    let ids = []
    if (type == 'select') {
      if (multipleSelection.value.length === 0) {
        message.warning('请至少选择一项')
        return
      }
      // 获取选中项的ID
      ids = multipleSelection.value.map((item) => item.id)
    }

    const data = await ReplenishmentProposalApi.exportFc(
      queryParams,
      ids,
      type == 'markedRestock' ? 1 : ''
    )
    download.excel(data, '补货分析.xls')
    message.success('导出成功')
  } catch (error) {
    console.error(error)
  }
}

/** 智能补货分析 */
const handleGenerates = async () => {
  try {
    // 检查必填参数
    if (queryParams.uids.length == 0) {
      message.error('请选择领星账号')
      return
    }

    await message.confirm('确定要进行智能补货分析吗？这可能需要一些时间。')
    loading.value = true
    let qdata = JSON.parse(JSON.stringify(queryParams))

    // 如果qdata.uids有all，删除all
    if (qdata.uids.includes('all')) {
      qdata.uids = qdata.uids.filter((item) => item !== 'all')
    }
    // 如果qdata.sids有all，删除all
    if (qdata.sids.includes('all')) {
      qdata.sids = qdata.sids.filter((item) => item !== 'all')
    }

    await ReplenishmentProposalApi.analyzeRestock(qdata)
    queryParams.uids = qdata.uids
    message.success('分析完成')
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 查询领星账号切换
const queryAccountChange = (values, names) => {
  console.log(values, names)
  queryParams.uids = values //选全部的时候传出所有账号的ids
}

// 查询店铺切换
const queryShopChange = (values, names) => {
  queryParams.sids = values
}

// 国家变更
const queryCountryChange = (value) => {
  queryParams.countrys = value
}

// 修改补货建议列表备注
const handleInput = async (row) => {
  await ElMessageBox.confirm('确定修改备注吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await ReplenishmentProposalApi.batchEditRemark({ ids: [row.id], remark: row.remarkEdit })
      try {
        message.success('修改成功')
      } catch (error) {
        row.remarkEdit = row.remark
        message.error('修改失败')
      }
    })
    .catch(() => {
      row.remarkEdit = row.remark
      console.info('操作取消')
    })
}

// 处理TipInput组件的回车事件
const handleTipInputEnter = (values) => {
  // 定义SKU类型与参数名的映射关系
  skuQueryList.value = values
}

// 打开权重列表弹窗
const weightFormRef = ref()
const openWeightList = () => {
  weightFormRef.value?.openDialog()
}

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  {
    field: 'predictedDailySales',
    label: '单日预测',
    width: 90,
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'predictedInventoryTarget',
    label: '预测总库存',
    width: 100,
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'suggestedRestockQuantity',
    label: '建议补货',
    width: 90,
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'sellerItem',
    label: '店铺',
    width: 150,
    align: 'left',
    fixed: 'left',
    showOverflowTooltip: true
  },
  {
    field: 'asin',
    label: 'ASIN',
    width: 130,
    align: 'left',
    fixed: 'left',
    isCopy: true
  },
  {
    field: 'msku',
    label: 'MSKU',
    width: 320,
    align: 'left',
    fixed: 'left',
    showOverflowTooltip: true,
    isCopy: true
  },
  {
    field: 'analyzeTime',
    label: '分析时间',
    width: 160,
    align: 'left',
    formatter: (row) => dateFormatter(row.analyzeTime)
  },
  {
    field: 'tagsResp',
    label: '标签',
    width: 100,
    align: 'left',
    showOverflowTooltip: false
  },
  {
    field: 'country',
    label: '国家',
    width: 80,
    align: 'left'
  },
  {
    field: 'restockSuggestPrincipalInfoRespVos',
    label: 'Listing负责人',
    width: 130,
    align: 'left'
  },
  {
    field: 'localName',
    label: '品名',
    width: 200,
    align: 'left'
  },
  {
    field: 'imgUrl',
    label: '图片',
    type: 'image'
  },
  {
    field: 'localSku',
    label: 'SKU',
    width: 320,
    align: 'left',
    showOverflowTooltip: true,
    isCopy: true
  },
  {
    field: 'fnsku',
    label: 'FNSKU',
    width: 150,
    align: 'center',
    showOverflowTooltip: true,
    isCopy: true
  },
  {
    field: 'action',
    label: '操作',
    width: 170,
    fixed: 'right',
    slots: {
      default: 'action'
    }
  },
  {
    field: 'salesAvg3',
    label: '3天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'salesAvg7',
    label: '7天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'salesAvg14',
    label: '14天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'salesAvg30',
    label: '30天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'salesAvg60',
    label: '60天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'salesAvg90',
    label: '90天日均',
    width: 100,
    align: 'center'
  },
  {
    field: 'totalInventory',
    label: '总库存',
    width: 90,
    align: 'center',
    icon: 'ep:warning',
    tips: '总库存=本地可用 + 预计发货量 + 待检待上架量 + 待交付 + 本地仓在途 + 采购计划 + 海外仓可用 + 海外仓在途 + FBA库存 + FBA在途'
  },
  {
    field: 'fbaInventory',
    label: 'FBA库存',
    width: 100,
    align: 'center'
  },
  {
    field: 'amazonQuantityShipping',
    label: 'FBA在途',
    width: 100,
    align: 'center'
  },
  {
    field: 'amazonQuantityShippingPlan',
    label: '预计发货量',
    width: 100,
    align: 'center'
  },
  {
    field: 'scQuantityOverseaValid',
    label: '海外仓可用',
    width: 100,
    align: 'center'
  },
  {
    field: 'scQuantityOverseaShipping',
    label: '海外仓在途',
    width: 100,
    align: 'center'
  },
  {
    field: 'scQuantityLocalValid',
    label: '本地可用',
    width: 100,
    align: 'center'
  },
  {
    field: 'scQuantityLocalQc',
    label: '待检待上架量',
    width: 120,
    align: 'center'
  },
  {
    field: 'scQuantityPurchaseShipping',
    label: '待交付',
    width: 100,
    align: 'center'
  },
  {
    field: 'scQuantityLocalShipping',
    label: '本地仓在途',
    width: 100,
    align: 'center'
  },
  {
    field: 'remark',
    label: '备注',
    width: 100,
    align: 'center',
    slots: {
      default: 'remark'
    }
  },
  {
    field: 'weightFactor',
    label: '权重',
    width: 100,
    align: 'center'
  },
  {
    field: 'inventoryCycle',
    label: '库存周期',
    width: 100,
    align: 'center'
  }
])

onMounted(() => {
  getPrincipalListFc()
  getTagList()
  setTimeout(() => {
    getList()
  }, 300)
})
</script>
