<template>
  <el-dialog
    v-model="dialogVisible"
    :title="detailTitle"
    width="90vw"
    @close="closeDialog"
    class="advertising_detail_drawer"
  >
    <div class="advertising-detail-container">

      <!-- 广告规则表格 -->
      <div class="ad-rules-section">
        <el-table
          :data="detailData.adRules"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="storeName"
            label="店铺名称"
            width="120"
          />
          <el-table-column
            prop="adType"
            label="广告类型"
            width="90"
            align="center"
          />
          <el-table-column
            label="商品图片"
            width="90"
            align="center"
          >
            <template #default="scope">
              <div>
                <el-image
                  v-if="scope.row.imgUrl"
                  :src="scope.row.imgUrl"
                  alt=""
                  style="width: 50px; height: 50px;"
                  :initial-index="0"
                  fit="cover"
                />
                <!-- :preview-src-list="[scope.row.imgUrl]" -->
                <!-- preview-teleported -->
                <span v-else>无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="sku"
            label="SKU"
            width="230"
          >
            <template #default="scope">
              <div
                v-clipboard="scope.row.sku"
                class="copy-text"
              >
                {{ scope.row.sku }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="asin"
            label="ASIN"
            width="140"
          >
            <template #default="scope">
              <div
                v-clipboard="scope.row.asin"
                class="copy-text"
              >
                {{ scope.row.asin }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="campaignName"
            label="广告活动"
            min-width="220"
          />
          <el-table-column label="预算与竞价策略">
            <template #default="scope">
              <div>
                {{ getCurrencySymbol(scope.row.site) }}{{ scope.row.budget }}--{{ getDictLabel(DICT_TYPE.OPERATION_CAMPAIGN_BIDDING_STRATEGY, scope.row.campaignBiddingStrategy) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="TOP位"
            width="80"
            align="center"
          >
            <template #default="scope">
              <div>{{ scope.row.bidTopSearchResults }}%</div>
            </template>
          </el-table-column>
          <el-table-column
            label="搜索结果其余位置"
            width="145"
            align="center"
          >
            <template #default="scope">
              <div>{{ scope.row.bidOtherSearchResults }}%</div>
            </template>
          </el-table-column>
          <el-table-column
            label="商品页面"
            width="90"
            align="center"
          >
            <template #default="scope">
              <div>{{ scope.row.bidProductPage }}%</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="groupName"
            label="广告组"
            min-width="220"
          >
            <template #default="scope">
              <div>{{ scope.row.groupName }}</div>
            </template>
          </el-table-column>

          <el-table-column
            label="默认竞价"
            width="90"
            align="center"
          >
            <template #default="scope">
              <div>
                {{ getCurrencySymbol(scope.row.site) }} {{ scope.row.defaultBid }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="targetCount"
            label="投放量"
            width="70"
            align="center"
          >
            <template #default="scope">
              <el-link
                type="primary"
                @click="showTargetingDetails(scope.row)"
              >
                {{ scope.row.targetCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            width="100"
            align="center"
          >
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getDictLabel(DICT_TYPE.OPERATION_CREATION_STATUS, scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="失败原因"
            prop="failureReason"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="100"
          >
            <template #default="scope">
              <el-button
                type="text"
                v-if="scope.row.status == 2"
                @click="resetBtn(scope.row)"
              >
                重试
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 关键词详情弹窗 -->
    <el-dialog
      v-model="keywordDialogVisible"
      title="关键词详情"
      width="600px"
      append-to-body
    >
      <el-table
        :data="keywordList"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="keywords"
          label="关键词"
        />
        <el-table-column
          prop="matchType"
          label="匹配方式"
          align="center"
        >
          <template #default="scope">
            <div class="flex flex-col gap-5px items-center justify-center">
              <div v-if="scope.row.exact == 1">{{ scope.row.exact == 1 ? '精准匹配' : '' }}</div>
              <div v-if="scope.row.phrase == 1"> {{ scope.row.phrase == 1 ? '词组匹配' : '' }}</div>
              <div v-if="scope.row.broad == 1">{{ scope.row.broad == 1 ? '广泛匹配' : '' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="竞价"
          align="center"
        >
          <template #default="scope">
            <div class="flex flex-col gap-5px items-center justify-center">
              <div v-if="scope.row.exact == 1">{{ scope.row.exact == 1 ?  scope.row.exactBid : '' }}</div>
              <div v-if="scope.row.phrase == 1"> {{ scope.row.phrase == 1 ?  scope.row.phraseBid : '' }}</div>
              <div v-if="scope.row.broad == 1">{{ scope.row.broad == 1 ?  scope.row.broadBid : '' }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 自动定位组详情弹窗 -->
    <el-dialog
      v-model="autoTargetingDialogVisible"
      title="管理自动定位组"
      width="600px"
      append-to-body
    >
      <el-table
        :data="autoTargetingList"
        border
        style="width: 100%"
      >
        <el-table-column
          label="状态"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'info'">
              {{ scope.row.enabled ? '开' : '关' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="targetingType"
          label="自动定位组"
          min-width="180"
          align="center"
        />
        <el-table-column
          label="竞价"
          width="120"
          align="center"
        >
          <template #default="scope">
            <div>
              {{ getCurrencySymbol(scope.row.site) }} {{ scope.row.bidAmount }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <template #footer>
      <div style="text-align: center;">
        <el-button @click="closeDialog">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
import { AdvertisingCreationApi, AdvertisingTemplateApi } from '@/api/operate/advertisingManage'

defineOptions({ name: 'AdvertisingDetail' })
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const detailTitle = ref('详情') // 详情标题
const creationId = ref(null) // 详情数据id
// 关键词详情弹窗
const keywordDialogVisible = ref(false)
const keywordList = ref([])

// 自动定位组详情弹窗
const autoTargetingDialogVisible = ref(false)
const autoTargetingList = ref([])

// 详情数据
const detailData = ref({ currency: 'USD', adRules: [] })

/** 获取货币符号 */
const getCurrencySymbol = (currency: string) => {
  return getDictLabel(DICT_TYPE.OPERATION_CURRENCY, currency)
}

// 显示定位详情
const showTargetingDetails = async (row) => {
  keywordList.value = []
  if (row.placementType == '1') {
    // 手动广告 - 显示关键词详情
    formLoading.value = true
    try {
      // 调用关键词接口
      const res = await AdvertisingCreationApi.getAdvertisingKeywordsPage({
        creationSkuId: row.id,
        creationId: creationId.value
      })
      if (res?.list) {
        // 转换关键词数据格式
        keywordList.value = res.list
      } else {
        keywordList.value = []
      }
      keywordDialogVisible.value = true
    } catch (error) {
      console.error('获取关键词数据失败', error)
    } finally {
      formLoading.value = false
    }
  } else if (row.placementType == '0') {
    // 自动广告 - 显示自动定位组详情
    // 自动广告的数据已经在getAdvertisingSkuPage接口中获取
    const autoData = [
      {
        enabled: row.broadMatchFlag == 1 ? true : false,
        targetingType: '宽泛匹配',
        bidAmount: row.broadMatch || 0
      },
      {
        enabled: row.exactMatchFlag == 1 ? true : false,
        targetingType: '精准匹配',
        bidAmount: row.exactMatch || 0
      },
      {
        enabled: row.similarProductsFlag == 1 ? true : false,
        targetingType: '同类商品',
        bidAmount: row.similarProducts || 0
      },
      {
        enabled: row.relatedProductsFlag == 1 ? true : false,
        targetingType: '关联商品',
        bidAmount: row.relatedProducts || 0
      }
    ]
    autoTargetingList.value = autoData
    autoTargetingDialogVisible.value = true
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    1: 'success',
    2: 'danger',
    0: 'info'
  }
  return statusMap[status] || 'info'
}
const rowInfo = ref({})

/** 打开弹窗 */
const open = async (id?: number, templateName?: string, status?: string) => {
  rowInfo.value = {
    id: id,
    templateName: templateName,
    status: status
  }
  dialogVisible.value = true
  creationId.value = id
  // 设置标题
  if (templateName) {
    detailTitle.value = `${templateName}-详情`
    if (status) {
      const statusMap = {
        success: '成功',
        fail: '失败',
        creating: '创建中'
      }
      detailTitle.value += ` (${statusMap[status] || '全部'})`
    }
  } else {
    detailTitle.value = '详情'
  }

  // 获取详情数据
  getDetails(id, status)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 获取详情
const getDetails = async (id, status) => {
  formLoading.value = true
  try {
    // 调用API获取广告SKU分页数据
    const res = await AdvertisingCreationApi.getAdvertisingSkuPage({
      creationId: id,
      status: status
    })

    if (res?.list) {
      // 将接口返回的数据转换为表格所需格式
      detailData.value = {
        currency: 'USD', // 默认货币类型
        adRules: res.list.map((item) => ({
          ...item,
          adType: item.placementType == '1' ? '手动' : '自动',
          placementType: item.placementType,
          targetCount: item.placementType == '0' ? item.zdDeliveryCount : item.sdDeliveryCount // 根据广告类型设置默认投放量
        }))
      }
    } else {
      detailData.value = {
        currency: 'USD',
        adRules: []
      }
    }
  } catch (error) {
    console.error('加载广告详情失败:', error)
  } finally {
    formLoading.value = false
  }
}
// 重试按钮
const resetBtn = async (row) => {
  await ElMessageBox.confirm('确定重试吗？', '提示')
  let res = await AdvertisingCreationApi.retryApi({
    creationId: row.creationId,
    creationSkuId: row.id
  })
  if (!res) ElMessage.success('重试成功')
  console.log(rowInfo.value)
  getDetails(rowInfo.value.id, rowInfo.value.status)
}

// 弹窗关闭
const closeDialog = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.copy-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
    text-decoration: underline;
  }
}

.advertising-detail-container {
  padding: 20px;

  h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .header-section {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;

    .shop-select {
      width: 200px;
    }

    .ad-type-toggles {
      display: flex;
      align-items: center;
      gap: 20px;

      .toggle-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
  }

  .selected-products-section {
    margin-bottom: 30px;

    .product-list {
      display: flex;
      width: 100%;
      padding-bottom: 10px;
      overflow-x: auto;
      gap: 20px;

      .product-item {
        display: flex;
        min-width: 350px;
        padding: 10px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        .product-image {
          width: 60px;
          height: 60px;
          margin-right: 10px;

          .el-image {
            width: 100%;
            height: 100%;
          }

          .image-placeholder {
            display: flex;
            width: 100%;
            height: 100%;
            color: #909399;
            background-color: #f5f7fa;
            align-items: center;
            justify-content: center;
          }
        }

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          div {
            margin-bottom: 5px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .ad-rules-section {
    margin-bottom: 30px;
  }
}
</style>
