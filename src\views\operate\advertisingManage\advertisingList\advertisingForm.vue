<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="65vw"
    style="min-height: 600px"
    @close="closeDialog"
    class="advertising_drawer"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :fullscreen="isFullscreen"
  >
    <!-- 第一步：选择模板和输入SKU -->
    <div v-if="currentStep === 1" class="advertising-form-container">
      <!-- 模板选择 -->
      <div class="template-select-container">
        <el-select
          v-model="selectedTemplate"
          placeholder="请选择模板"
          style="width: 300px"
          value-key="id"
          clearable
          filterable
        >
          <el-option
            v-for="item in templateOptions"
            :key="item.id"
            :label="item.templateName"
            :value="item"
          />
        </el-select>
      </div>

      <!-- SKU输入区域 - 列表形式 -->
      <div class="sku-input-area">
        <el-table :data="skuStoreList" border style="width: 100%; min-height: 300px">
          <el-table-column align="center" label="店铺" width="430">
            <template #header>
              <div class="flex items-center justify-center pl-2">
                <span class="pr-2">店铺</span>
                <el-tooltip content="此店铺数据只查询自身的店铺" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <el-select
                v-model="scope.row.storeInfo"
                placeholder="请选择店铺"
                style="width: 400px"
                :disabled="disabledShop"
                value-key="sid"
                @change="handleStoreChange(scope.row)"
              >
                <el-option
                  v-for="item in storeOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item"
                />
              </el-select>
            </template>
          </el-table-column>

          <!-- SKU输入列 -->
          <el-table-column align="center" label="SKU">
            <template #default="scope">
              <DynamicInput
                v-model="scope.row.skuValue"
                placeholder="请输入SKU，一行一个回车换行，最多50个"
                :rows="6"
                :maxLines="50"
                :disabled="!!scope.row.asinValue"
                @change="handleSkuValueChange(scope.row)"
                style="z-index: 90"
              />
              <div v-if="scope.row.asinValue" class="mutex-tip">
                <el-icon>
                  <Warning />
                </el-icon>
                <span>输入ASIN后不可编辑SKU</span>
              </div>
            </template>
          </el-table-column>
          <!-- ASIN输入列 -->
          <el-table-column align="center" label="ASIN">
            <template #default="scope">
              <DynamicInput
                v-model="scope.row.asinValue"
                placeholder="请输入ASIN，一行一个回车换行，最多50个"
                :rows="6"
                :maxLines="50"
                :disabled="!!scope.row.skuValue"
                @change="handleAsinValueChange(scope.row)"
                style="z-index: 90"
              />
              <div v-if="scope.row.skuValue" class="mutex-tip">
                <el-icon>
                  <Warning />
                </el-icon>
                <span>输入SKU后不可编辑ASIN</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" width="120" label="操作">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                @click="removeSkuStore(scope.$index)"
                v-if="skuStoreList.length > 1"
              >
                <Icon icon="ep:delete" />
              </el-button>
              <el-button type="primary" size="small" @click="addSkuStore">
                <Icon icon="ep:plus" />
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="sku-input-tip">
          <span class="tip-text">注意: 一个SKU创建一个广告活动，一个广告组</span>
        </div>
      </div>
    </div>

    <!-- 第二步：关键词设置 -->
    <div v-if="currentStep === 2" class="keyword-setting-container" v-loading="formLoading">
      <!-- 添加搜索和过滤功能 -->
      <div class="table-toolbar">
        <el-input
          v-model="keywordFilter"
          placeholder="搜索关键词"
          clearable
          style="width: 300px"
          @input="handleFilterChange"
        >
          <template #prefix>
            <Icon icon="ep:search" />
          </template>
        </el-input>
        <div class="table-info">
          <span
            >共{{ totalStores }}个店铺，{{ totalSkus }}个SKU/ASIN，{{ totalKeywords }}个关键词</span
          >
        </div>
      </div>

      <el-table
        :data="filteredTableData"
        border
        style="width: 100%; max-height: 700px; overflow: auto"
        :span-method="objectSpanMethod"
        :scrollbar-always-on="true"
        v-loading="tableLoading"
      >
        <el-table-column align="center" prop="storeId" label="店铺" width="150">
          <template #default="scope">
            <span>{{ getStoreName(scope.row.storeId) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sku" label="SKU/ASIN" width="280">
          <template #default="scope">
            {{ scope.row.sku || scope.row.asin || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="keyword" label="关键词" width="180">
          <template #default="scope">
            {{ scope.row.keyword }}
            <el-tooltip
              class="box-item"
              effect="dark"
              content="添加更多关键词"
              placement="top-start"
            >
              <el-button
                type="primary"
                size="small"
                @click="addKeyword(scope.row.sku ? scope.row.sku : scope.row.asin, scope.row)"
                v-if="
                  canAddMoreKeywords(
                    scope.row.sku ? scope.row.sku : scope.row.asin,
                    scope.$index,
                    scope.row
                  ) && scope.row.keyword == ''
                "
              >
                <Icon icon="ep:plus" />
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="匹配方式" width="120">
          <template #default="scope">
            <div class="match-type-checkboxes">
              <el-checkbox
                v-model="scope.row.broad"
                label="广泛匹配"
                true-label="1"
                false-label="0"
              />
              <el-checkbox
                v-model="scope.row.exact"
                label="精准匹配"
                true-label="1"
                false-label="0"
              />
              <el-checkbox
                v-model="scope.row.phrase"
                label="词组匹配"
                true-label="1"
                false-label="0"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="建议竞价" align="center">
          <template #header>
            建议竞价
            <div class="flex gap-10 bid-global-actions">
              <el-radio-group
                v-model="globalBidSelection"
                @change="applyGlobalBid"
                :disabled="!hasAnyKeywords"
                size="small"
              >
                <el-radio-button label="Low">应用低竞价</el-radio-button>
                <el-radio-button label="Mid">应用建议竞价</el-radio-button>
                <el-radio-button label="High">应用高竞价</el-radio-button>
              </el-radio-group>
            </div>
            <div v-if="!hasAnyKeywords" class="empty-keyword-tip">请点击“＋”添加关键词</div>
          </template>
          <template #default="scope">
            <div class="bid-suggestion">
              <!-- 广泛 -->
              <div class="flex items-center" v-if="scope.row.broad == 1">
                广泛匹配：
                <el-radio-group
                  v-model="scope.row.broadMatch"
                  @change="handleBidChange(scope.row, 'broad')"
                  :disabled="!scope.row.keyword"
                  size="small"
                >
                  <el-radio-button label="Low" :disabled="scope.row.broadSuggestBidLow == '-'">
                    <div style="min-width: 70px"
                      >- 低: {{ siteIcon }} {{ scope.row.broadSuggestBidLow }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="Mid" :disabled="scope.row.broadSuggestBidMid == '-'">
                    <div style="min-width: 70px"
                      >- 建议: {{ siteIcon }} {{ scope.row.broadSuggestBidMid }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="High" :disabled="scope.row.broadSuggestBidHigh == '-'">
                    <div style="min-width: 70px"
                      >- 高: {{ siteIcon }} {{ scope.row.broadSuggestBidHigh }}</div
                    >
                  </el-radio-button>
                </el-radio-group>
              </div>
              <!-- 精准 -->
              <div class="flex items-center" v-if="scope.row.exact == 1">
                精准匹配：
                <el-radio-group
                  v-model="scope.row.exactMatch"
                  @change="handleBidChange(scope.row, 'exact')"
                  :disabled="!scope.row.keyword"
                  size="small"
                >
                  <el-radio-button label="Low" :disabled="scope.row.exactSuggestBidLow == '-'">
                    <div style="min-width: 70px"
                      >- 低: {{ siteIcon }} {{ scope.row.exactSuggestBidLow }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="Mid" :disabled="scope.row.exactSuggestBidMid == '-'">
                    <div style="min-width: 70px"
                      >- 建议: {{ siteIcon }} {{ scope.row.exactSuggestBidMid }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="High" :disabled="scope.row.exactSuggestBidHigh == '-'">
                    <div style="min-width: 70px"
                      >- 高: {{ siteIcon }} {{ scope.row.exactSuggestBidHigh }}</div
                    >
                  </el-radio-button>
                </el-radio-group>
              </div>

              <!-- 词组 -->
              <div class="flex items-center" v-if="scope.row.phrase == 1">
                词组匹配：
                <el-radio-group
                  v-model="scope.row.phraseMatch"
                  @change="handleBidChange(scope.row, 'phrase')"
                  :disabled="!scope.row.keyword"
                  size="small"
                >
                  <el-radio-button label="Low" :disabled="scope.row.phraseSuggestBidLow == '-'">
                    <div style="min-width: 70px"
                      >- 低: {{ siteIcon }} {{ scope.row.phraseSuggestBidLow }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="Mid" :disabled="scope.row.phraseSuggestBidMid == '-'">
                    <div style="min-width: 70px"
                      >- 建议: {{ siteIcon }} {{ scope.row.phraseSuggestBidMid }}</div
                    >
                  </el-radio-button>
                  <el-radio-button label="High" :disabled="scope.row.phraseSuggestBidHigh == '-'">
                    <div style="min-width: 70px"
                      >- 高: {{ siteIcon }} {{ scope.row.phraseSuggestBidHigh }}</div
                    >
                  </el-radio-button>
                </el-radio-group>
              </div>
              <div v-if="!scope.row.keyword" class="empty-keyword-tip">请点击“＋”添加关键词</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="竞价" align="center" width="220">
          <template #default="scope">
            <div class="flex flex-col">
              <div v-if="scope.row.broad == 1">
                广泛匹配：{{ siteIcon }}
                <el-input-number
                  v-model="scope.row.broadBid"
                  :min="0"
                  :precision="2"
                  :step="1"
                  controls-position="right"
                  style="width: 100px"
                />
              </div>
              <div v-if="scope.row.exact == 1">
                精准匹配：{{ siteIcon }}
                <el-input-number
                  v-model="scope.row.exactBid"
                  :min="0"
                  :precision="2"
                  :step="1"
                  controls-position="right"
                  style="width: 100px"
                />
              </div>
              <div v-if="scope.row.phrase == 1">
                词组匹配：{{ siteIcon }}
                <el-input-number
                  v-model="scope.row.phraseBid"
                  :min="0"
                  :precision="2"
                  :step="1"
                  controls-position="right"
                  style="width: 100px"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="添加更多关键词"
              placement="top-start"
            >
              <el-button
                type="primary"
                size="small"
                @click="addKeyword(scope.row.sku ? scope.row.sku : scope.row.asin, scope.row)"
                v-if="
                  canAddMoreKeywords(
                    scope.row.sku ? scope.row.sku : scope.row.asin,
                    scope.$index,
                    scope.row
                  )
                "
              >
                <Icon icon="ep:plus" />
              </el-button>
            </el-tooltip>
            <el-button
              type="danger"
              size="small"
              @click="removeKeyword(scope.row)"
              v-if="scope.row.keyword"
            >
              <Icon icon="ep:delete" />
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <!-- 按钮区域 -->
      <div class="form-actions flex justify-center">
        <template v-if="currentStep === 1">
          <el-button @click="closeDialog">取消</el-button>
          <template v-if="isAutoTemplate">
            <el-button type="primary" @click="submitForm()">创建</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="goToNextStep">下一步</el-button>
          </template>
        </template>
        <template v-else>
          <el-button @click="closeDialog" v-if="isFullscreen">取消</el-button>
          <el-button @click="goToPrevStep">上一步</el-button>
          <el-button type="primary" @click="submitForm()">创建</el-button>
        </template>
      </div>
    </template>
  </el-dialog>

  <!-- 添加关键词弹窗 -->
  <el-dialog
    v-model="keywordDialogVisible"
    title="添加关键词"
    width="600px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div
      class="keyword-dialog-content"
      v-loading="keywordLoading"
      element-loading-text="关键词新增中..."
    >
      <div class="keyword-input-section">
        <h4>当前编辑SKU: {{ currentEditingSku }}</h4>
        <RowTextInput
          v-model="keywordInput"
          placeholder="请输入关键词，一行一个回车换行，最多50个"
          :maxCount="50"
          :rows="6"
          ref="keywordInputRef"
          @change="handleKeywordInputChange"
          :disabled="keywordLoading"
        />
        <div class="keyword-tips">
          <el-alert
            title="提示：每行输入一个关键词，系统会自动去重"
            type="info"
            :closable="false"
            show-icon
          />
          <div class="format-tips">
            <p>· 关键词格式要求：每行一个关键词，不要包含特殊字符</p>
            <p>· 已有关键词数量：{{ existingKeywordsCount }}/50</p>
          </div>
        </div>
      </div>

      <div class="keyword-preview-section" v-if="tempKeywords.length > 0">
        <div class="preview-header">
          <h4>预览 ({{ tempKeywords.length }}个关键词)</h4>
          <el-button type="text" size="small" @click="clearAllKeywords" :disabled="keywordLoading">
            <Icon icon="ep:delete" /> 清空
          </el-button>
        </div>
        <div class="keyword-tags">
          <el-tag
            v-for="(keyword, index) in tempKeywords"
            :key="index"
            class="keyword-tag"
            effect="plain"
            closable
            @close="removeKeywordFromTemp(index)"
            :disable-transitions="false"
          >
            {{ keyword }}
          </el-tag>
        </div>
      </div>

      <!-- 错误提示区域 -->
      <div class="keyword-error-section" v-if="keywordError">
        <el-alert
          :title="keywordError"
          type="error"
          :closable="true"
          show-icon
          @close="keywordError = ''"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeKeywordDialog" :disabled="keywordLoading">取消</el-button>
        <el-button
          type="primary"
          @click="confirmAddKeywords"
          :disabled="tempKeywords.length === 0 || keywordLoading"
          :loading="keywordLoading"
          >确认添加 ({{ tempKeywords.length }})</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { AdvertisingCreationApi, AdvertisingTemplateApi } from '@/api/operate/advertisingManage'
import { ThirdPartyAccountApi } from '@/api/system/third'
import DynamicInput from '@/components/DynamicInput/index.vue'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { QuestionFilled } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
import { ElIcon } from 'element-plus'
import { debounce } from 'lodash-es'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'AdvertisingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const isFullscreen = ref(false) // 是否全屏,屏幕分辨率小就全屏
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const tableLoading = ref(false) // 表格加载状态
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const adType = ref('') // 广告类型：auto - 自动；manual - 手动
const currentStep = ref(1) // 当前步骤：1 - 第一步；2 - 第二步
const globalBidSelection = ref('') // 全局竞价选择
const keywordFilter = ref('') // 关键词搜索过滤器

// 模板选项
const templateOptions = ref([])

// 店铺选项
const storeOptions = ref([])

// SKU-店铺列表
const skuStoreList = ref([])

const selectedTemplate = ref(null)

const siteIcon = computed(() => {
  return getDictLabel(DICT_TYPE.OPERATION_CURRENCY, selectedTemplate.value?.site)
})

// 判断是否为自动模板
const isAutoTemplate = computed(() => {
  return selectedTemplate.value?.placementType == 0
})

const disabledShop = computed(() => {
  // 店铺列表id和skuStoreList中的storeId是否匹配
  storeOptions.value.some((item) => {
    skuStoreList.value.some((item2) => {
      return item.id == item2.storeId
    })
  })

  // return skuStoreList.value.some((item) => item.storeId)
})

// 关键词数据直接存储在skuStoreList中
const flattenData = (originalData) => {
  return originalData.flatMap((item) => {
    const storeId = item.storeId
    if (item.skuList.length > 0) {
      return item.skuList.flatMap((sku) => {
        const keywords = item.keywords?.[sku] || ['']
        return keywords.map((keyword) => ({
          storeId,
          sku,
          keyword,
          skuValue: item.skuValue,
          // 保留其他必要字段
          ...item,
          // 初始化匹配方式默认值
          broad: '1',
          exact: '1',
          phrase: '0',
          // 初始化竞价相关字段
          broadBid: 0,
          exactBid: 0,
          phraseBid: 0,
          // 初始化建议竞价字段（使用默认值而不是保留旧值）
          broadMatch: '',
          exactMatch: '',
          phraseMatch: '',
          // 建议竞价字段
          exactSuggestBidLow: '0',
          exactSuggestBidMid: '0',
          exactSuggestBidHigh: '0',
          broadSuggestBidLow: '0',
          broadSuggestBidMid: '0',
          broadSuggestBidHigh: '0',
          phraseSuggestBidLow: '0',
          phraseSuggestBidMid: '0',
          phraseSuggestBidHigh: '0'
        }))
      })
    }
    if (item.asinList.length > 0) {
      return item.asinList.flatMap((asin) => {
        const keywords = item.keywords?.[asin] || ['']
        return keywords.map((keyword) => ({
          storeId,
          asin,
          keyword,
          asinValue: item.asinValue,
          // 保留其他必要字段
          ...item,
          // 初始化匹配方式默认值
          broad: '1',
          exact: '1',
          phrase: '0',
          // 初始化竞价相关字段
          broadBid: 0,
          exactBid: 0,
          phraseBid: 0,
          // 初始化建议竞价字段（使用默认值而不是保留旧值）
          broadMatch: '',
          exactMatch: '',
          phraseMatch: '',
          // 建议竞价字段
          exactSuggestBidLow: '0',
          exactSuggestBidMid: '0',
          exactSuggestBidHigh: '0',
          broadSuggestBidLow: '0',
          broadSuggestBidMid: '0',
          broadSuggestBidHigh: '0',
          phraseSuggestBidLow: '0',
          phraseSuggestBidMid: '0',
          phraseSuggestBidHigh: '0'
        }))
      })
    }
  })
}

/** 上一步 */
const goToPrevStep = () => {
  currentStep.value = 1
  keywordFilter.value = ''
}

// 扁平化处理后的表格数据
const tableData = ref([])

// 过滤后的表格数据 - 修复搜索功能
const filteredTableData = computed(() => {
  if (!keywordFilter.value) return tableData.value

  const searchTerm = keywordFilter.value.trim()
  return tableData.value.filter((row) => {
    return row.keyword && row.keyword.includes(searchTerm)
  })
})

// 总关键词数量
const totalKeywords = computed(() => {
  return filteredTableData.value.filter((row) => row.keyword && row.keyword.trim() !== '').length
})

// 总店铺数量
const totalStores = computed(() => {
  return Array.from(new Set(filteredTableData.value.map((row) => row.storeId))).length
})

// 总SKU数量
const totalSkus = computed(() => {
  return Array.from(new Set(filteredTableData.value.map((row) => row.sku || row.asin))).length
})

// 处理过滤变化
const handleFilterChange = () => {
  tableLoading.value = true
  try {
    // 不直接修改tableData，而是通过el-table的:data绑定来显示过滤后的结果
  } finally {
    nextTick(() => {
      tableLoading.value = false
    })
  }
}

// 单元格合并方法 - 适应新的表格结构，按店铺和SKU合并
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 使用当前显示的数据（filteredTableData）
  const currentTableData = filteredTableData.value

  // 店铺列合并（按 storeId 合并）
  if (columnIndex === 0) {
    let spanCount = 0
    for (let i = rowIndex; i < currentTableData.length; i++) {
      if (currentTableData[i]?.storeId === row?.storeId) {
        spanCount++
      } else {
        break
      }
    }

    if (rowIndex === 0 || currentTableData[rowIndex - 1].storeId !== row.storeId) {
      return {
        rowspan: spanCount,
        colspan: 1
      }
    } else {
      return { rowspan: 0, colspan: 0 }
    }
  }

  // SKU/ASIN列合并（按 storeId + sku||storeId + asin 合并）
  if (columnIndex === 1) {
    let spanCount = 0
    for (let i = rowIndex; i < currentTableData.length; i++) {
      if (row.sku) {
        if (
          currentTableData[i]?.storeId === row?.storeId &&
          currentTableData[i]?.sku === row?.sku
        ) {
          spanCount++
        } else {
          break
        }
      }
      if (row.asin) {
        if (
          currentTableData[i]?.storeId === row?.storeId &&
          currentTableData[i]?.asin === row?.asin
        ) {
          spanCount++
        } else {
          break
        }
      }
    }

    if (row.sku) {
      if (
        rowIndex === 0 ||
        !(
          currentTableData[rowIndex - 1].storeId === row.storeId &&
          currentTableData[rowIndex - 1].sku === row.sku
        )
      ) {
        return {
          rowspan: spanCount,
          colspan: 1
        }
      } else {
        return { rowspan: 0, colspan: 0 }
      }
    }
    if (row.asin) {
      if (
        rowIndex === 0 ||
        !(
          currentTableData[rowIndex - 1].storeId === row.storeId &&
          currentTableData[rowIndex - 1].asin === row.asin
        )
      ) {
        return {
          rowspan: spanCount,
          colspan: 1
        }
      } else {
        return { rowspan: 0, colspan: 0 }
      }
    }
  }

  // 关键词列合并（按 storeId + sku + keyword 合并）
  if (columnIndex === 2) {
    // 如果关键词为空，不进行合并
    if (!row.keyword || row.keyword.trim() === '') {
      return {
        rowspan: 1,
        colspan: 1
      }
    }

    let spanCount = 0
    for (let i = rowIndex; i < currentTableData.length; i++) {
      const currentItem = currentTableData[i]
      if (row.sku) {
        if (
          currentItem?.storeId === row?.storeId &&
          currentItem?.sku === row?.sku &&
          currentItem?.keyword === row?.keyword
        ) {
          spanCount++
        } else {
          break
        }
      }
      if (row.asin) {
        if (
          currentItem?.storeId === row?.storeId &&
          currentItem?.asin === row?.asin &&
          currentItem?.keyword === row?.keyword
        ) {
          spanCount++
        } else {
          break
        }
      }
    }
    if (row.sku) {
      if (
        rowIndex === 0 ||
        !(
          currentTableData[rowIndex - 1].storeId === row.storeId &&
          currentTableData[rowIndex - 1].sku === row.sku &&
          currentTableData[rowIndex - 1].keyword === row.keyword
        )
      ) {
        return {
          rowspan: spanCount,
          colspan: 1
        }
      } else {
        return { rowspan: 0, colspan: 0 }
      }
    }
    if (row.asin) {
      if (
        rowIndex === 0 ||
        !(
          currentTableData[rowIndex - 1].storeId === row.storeId &&
          currentTableData[rowIndex - 1].asin === row.asin &&
          currentTableData[rowIndex - 1].keyword === row.keyword
        )
      ) {
        return {
          rowspan: spanCount,
          colspan: 1
        }
      } else {
        return { rowspan: 0, colspan: 0 }
      }
    }
  }
}

// 添加SKU-店铺行
const addSkuStore = () => {
  // 获取默认竞价值，如果模板中没有defaultBid则使用0
  const defaultBid = selectedTemplate.value?.defaultBid || 0

  skuStoreList.value.push({
    storeId: null,
    skuValue: '',
    skuList: [],
    asinList: [],
    // 设置默认竞价值
    exactBid: defaultBid,
    broadBid: defaultBid,
    phraseBid: defaultBid
  })
}

// 删除SKU-店铺行
const removeSkuStore = (index) => {
  skuStoreList.value.splice(index, 1)
}

// 处理SKU值变化
const handleSkuValueChange = (row) => {
  if (row.asinValue) {
    message.error('请先清空ASIN后再输入SKU')
    row.skuValue = ''
    return
  }
  if (!row.skuValue) {
    row.skuList = []
    return
  }
  // 分行处理并去重（单店铺内去重）
  const skuLines = row.skuValue
    .split('\n')
    .map((sku) => sku.trim())
    .filter((sku) => sku !== '')

  // 使用Set进行单行内去重
  row.skuList = [...new Set(skuLines)]
  row.asinValue = '' // 清空ASIN
  row.asinList = []
  // 获取默认竞价值，如果模板中没有defaultBid则使用0
  const defaultBid = selectedTemplate.value?.defaultBid || 0

  // 设置默认竞价值
  if (!row.exactBid && row.exactBid !== 0) row.exactBid = defaultBid
  if (!row.broadBid && row.broadBid !== 0) row.broadBid = defaultBid
  if (!row.phraseBid && row.phraseBid !== 0) row.phraseBid = defaultBid
}

// 新增ASIN处理方法
const handleAsinValueChange = (row) => {
  if (row.skuValue) {
    message.error('请先清空SKU后再输入ASIN')
    row.asinValue = ''
    return
  }
  if (!row.asinValue) {
    row.asinList = []
    return
  }
  // ASIN处理逻辑
  const asinLines = row.asinValue
    .split('\n')
    .map((asin) => asin.trim())
    .filter((asin) => asin !== '')

  row.asinList = [...new Set(asinLines)]
  row.skuValue = '' // 清空SKU
  row.skuList = []
  // 获取默认竞价值，如果模板中没有defaultBid则使用0
  const defaultBid = selectedTemplate.value?.defaultBid || 0

  // 设置默认竞价值
  if (!row.exactBid && row.exactBid !== 0) row.exactBid = defaultBid
  if (!row.broadBid && row.broadBid !== 0) row.broadBid = defaultBid
  if (!row.phraseBid && row.phraseBid !== 0) row.phraseBid = defaultBid
}

// 获取店铺名称
const getStoreName = (storeId) => {
  if (!storeId) return ''
  const store = storeOptions.value.find((store) => store.value == storeId)
  return store ? store.label : ''
}

// 添加关键词相关
const keywordDialogVisible = ref(false)
const keywordInput = ref('')
const keywordInputRef = ref(null)
const currentEditingSku = ref('')
const currentEditingStoreId = ref(null) // 添加当前编辑的店铺ID
const tempKeywords = ref([])
const keywordLoading = ref(false) // 关键词加载状态
const keywordError = ref('') // 关键词错误信息
const keywordSuccess = ref('') // 关键词成功信息

// 计算当前SKU已有的关键词数量
const existingKeywordsCount = computed(() => {
  if (!currentEditingSku.value || !currentEditingStoreId.value) return 0

  const skuStoreItem = skuStoreList.value.find(
    (item) =>
      item.storeId === currentEditingStoreId.value &&
      item.skuList &&
      item.skuList.includes(currentEditingSku.value)
  )

  if (!skuStoreItem || !skuStoreItem.keywords) return 0

  return skuStoreItem.keywords[currentEditingSku.value]?.length || 0
})

// 处理关键词输入变化
const handleKeywordInputChange = (data) => {
  // 清除之前的错误和成功信息
  keywordError.value = ''
  keywordSuccess.value = ''

  // 过滤掉空白关键词
  tempKeywords.value = data.list.filter((keyword) => keyword && keyword.trim() !== '')
  tempKeywords.value = [...new Set(tempKeywords.value)]
}

// 从临时关键词列表中移除关键词
const removeKeywordFromTemp = (index) => {
  tempKeywords.value.splice(index, 1)
}

// 清空所有临时关键词
const clearAllKeywords = () => {
  tempKeywords.value = []
  keywordInput.value = ''
  if (keywordInputRef.value) {
    keywordInputRef.value.clear()
  }
}

// 关闭关键词对话框
const closeKeywordDialog = () => {
  // 如果正在加载，不允许关闭
  if (keywordLoading.value) return

  keywordDialogVisible.value = false
  keywordInput.value = ''
  tempKeywords.value = []
  keywordError.value = ''
  keywordSuccess.value = ''
}

// 确认添加关键词
const confirmAddKeywords = async () => {
  // 清除之前的错误和成功信息
  keywordError.value = ''
  keywordSuccess.value = ''

  // 设置加载状态
  keywordLoading.value = true
  formLoading.value = true

  try {
    // 去重tempKeywords.value
    const uniqueKeywords = [...new Set(tempKeywords.value)]

    // 过滤掉空白关键词
    const filteredKeywords = uniqueKeywords.filter((keyword) => keyword && keyword.trim() !== '')

    if (filteredKeywords.length === 0) {
      keywordError.value = '请至少输入一个有效关键词'
      return
    }

    const sku = currentEditingSku.value
    const currentStoreId = currentEditingStoreId.value // 使用当前正在编辑的店铺ID

    // 在skuStoreList中查找对应店铺和SKU项
    const skuStoreItem = skuStoreList.value.find((item) =>
      item.storeId === currentStoreId && item.skuList.length > 0
        ? item.skuList.includes(sku)
        : item.asinList.includes(sku)
    )

    if (!skuStoreItem) {
      keywordError.value = '未找到对应的店铺SKU配置项'
      return
    }

    // 初始化关键词存储结构
    if (!skuStoreItem.keywords) {
      skuStoreItem.keywords = {}
    }
    if (!skuStoreItem.keywords[sku]) {
      skuStoreItem.keywords[sku] = []
    }
    // 检查关键词数量限制
    const existingKeywords = skuStoreItem.keywords[sku] || []
    if (existingKeywords.length + filteredKeywords.length > 50) {
      keywordError.value = `每个SKU最多添加50个关键词，当前已有${
        existingKeywords.length
      }个，最多还可添加${50 - existingKeywords.length}个`
      return
    }

    // 添加新关键词（规范化处理及精确去重）
    const normalizedKeywords = filteredKeywords.map((k) => k.trim()).filter((k) => k)

    const existingSet = new Set(skuStoreItem.keywords[sku] || [])
    const newKeywords = normalizedKeywords.filter((k) => !existingSet.has(k))

    if (newKeywords.length === 0) {
      keywordError.value = '所有关键词已存在，无需重复添加'
      return
    }

    // 更新skuStoreItem中的关键词
    skuStoreItem.keywords[sku] = [...(skuStoreItem.keywords[sku] || []), ...newKeywords]

    // 找到当前店铺和SKU的空关键词行（如果存在）
    const emptyKeywordRowIndex = tableData.value.findIndex(
      (row) =>
        row.storeId === currentStoreId &&
        (row.sku ? row.sku === sku : row.asin === sku) &&
        (!row.keyword || row.keyword === '')
    )

    // 找到当前店铺和SKU的最后一个关键词行
    const skuRows = tableData.value.filter(
      (row) => row.storeId === currentStoreId && (row.sku ? row.sku === sku : row.asin === sku)
    )
    const lastSkuRowIndex =
      skuRows.length > 0 ? tableData.value.indexOf(skuRows[skuRows.length - 1]) : -1

    // 确定插入位置（优先使用空行，其次在最后一行后插入）
    const insertIndex =
      emptyKeywordRowIndex !== -1
        ? emptyKeywordRowIndex
        : lastSkuRowIndex !== -1
          ? lastSkuRowIndex + 1
          : tableData.value.length

    // 如果有空行，先移除它
    if (emptyKeywordRowIndex !== -1) {
      tableData.value.splice(emptyKeywordRowIndex, 1)
    }

    keywordSuccess.value = '正在查询关键词数据，请稍候...'
    console.log(tableData.value)
    // 创建新行 - 应用当前全局竞价设置
    const newRows =
      newKeywords.map((keyword) => {
        // 获取默认竞价值，如果模板中没有defaultBid则使用0
        const defaultBid = selectedTemplate.value?.defaultBid || 0
        const newRow = {
          storeId: currentStoreId, // 确保使用正确的店铺ID
          [skuStoreItem.asinList.length > 0 ? 'asin' : 'sku']: sku,
          keyword,
          skuValue: skuStoreItem.skuValue,
          // 保留其他必要字段
          ...skuStoreItem,
          // 初始化匹配方式默认值
          broad: '1',
          exact: '1',
          phrase: '0',
          // 初始化竞价相关字段，使用模板中的默认竞价
          broadBid: defaultBid,
          exactBid: defaultBid,
          phraseBid: defaultBid,
          // 初始化建议竞价选择
          broadMatch: '',
          exactMatch: '',
          phraseMatch: '',
          // 建议竞价字段
          exactSuggestBidLow: '0',
          exactSuggestBidMid: '0',
          exactSuggestBidHigh: '0',
          broadSuggestBidLow: '0',
          broadSuggestBidMid: '0',
          broadSuggestBidHigh: '0',
          phraseSuggestBidLow: '0',
          phraseSuggestBidMid: '0',
          phraseSuggestBidHigh: '0'
        }

        // 如果已经应用了全局竞价，则新增的关键词也应用相同的竞价设置
        if (globalBidSelection.value) {
          // 应用全局竞价设置到每种匹配方式
          ;['broad', 'exact', 'phrase'].forEach((matchType) => {
            if (newRow[matchType] == '1') {
              const suggestBid = newRow[`${matchType}SuggestBid${globalBidSelection.value}`]
              if (suggestBid != undefined && suggestBid !== '-') {
                // 更新匹配类型选择和竞价
                newRow[`${matchType}Match`] = globalBidSelection.value
                newRow[`${matchType}Bid`] = suggestBid
              }
            }
          })
        }

        return newRow
      }) || []
    try {
      const cpcKeyRes =
        (await ThirdPartyAccountApi.cpcQueryAd({
          sid: currentStoreId,
          matchTypes: [
            tableData.value[insertIndex]?.broad == '1' ? 0 : '',
            tableData.value[insertIndex]?.exact == '1' ? 1 : '',
            tableData.value[insertIndex]?.phrase == '1' ? 2 : ''
          ].filter((item) => item !== ''),
          keywords: newKeywords,
          appid: storeOptions.value.find((item) => item.value === currentStoreId)?.appid
        })) || []
      // 创建匹配类型映射表（中文 -> 英文前缀）
      const matchTypeMap = {
        精确匹配: 'exact',
        广泛匹配: 'broad',
        词组匹配: 'phrase'
      }
      // 处理接口返回结果，更新建议竞价字段
      if (Array.isArray(cpcKeyRes)) {
        cpcKeyRes.forEach((item) => {
          const targetRow = newRows.find((row) => row.keyword === item.keyword)
          if (!targetRow) return
          // 获取匹配类型英文前缀
          const prefix = matchTypeMap[item.matchType]
          if (!prefix) return
          // 更新建议竞价字段
          targetRow[`${prefix}SuggestBidLow`] = item.rangeEnd.toString() // rangeEnd -> Low
          targetRow[`${prefix}SuggestBidMid`] = '0' // Mid 固定为0
          targetRow[`${prefix}SuggestBidHigh`] = item.rangeStart.toString() // rangeStart -> High
        })
      } else {
        console.warn('CPC查询结果格式不符合预期:', cpcKeyRes)
      }

      // 在指定位置插入新行，保持表格位置稳定
      tableData.value.splice(insertIndex, 0, ...newRows)

      // 重置过滤
      if (keywordFilter.value) {
        keywordFilter.value = ''
      }

      // 设置成功信息
      keywordSuccess.value = `成功添加${newKeywords.length}个关键词（去重后）`

      // 延迟关闭对话框，让用户看到成功信息
      setTimeout(() => {
        if (keywordSuccess.value) {
          ElMessage.success(keywordSuccess.value)
          // 确保用户没有手动关闭成功提示
          closeKeywordDialog()
        }
      }, 500)
    } catch (error) {
      console.error('CPC查询失败:', error)
      keywordError.value = `关键词建议竞价查询失败: ${error.msg || '未知错误'}`
      ElMessageBox.confirm('获取建议竞价失败，是否仍要添加这些关键词？', '提示', {
        confirmButtonText: '确认添加',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 在指定位置插入新行，保持表格位置稳定
          tableData.value.splice(insertIndex, 0, ...newRows)
          keywordSuccess.value = `成功添加${newKeywords.length}个关键词（无建议竞价）`
          setTimeout(() => {
            if (keywordSuccess.value) {
              ElMessage.success(keywordSuccess.value)
              closeKeywordDialog()
            }
          }, 500)
        })
        .catch(() => {
          ElMessage.info('已取消添加关键词')
        })
    }
  } catch (error) {
    console.error('添加关键词失败:', error)
    keywordError.value = `添加关键词失败: ${error.msg || '未知错误'}`
  } finally {
    // 重置加载状态
    formLoading.value = false
    keywordLoading.value = false
  }
}

// 检查是否可以添加更多关键词
const canAddMoreKeywords = (skuOrasin, keywordIndex, row) => {
  // 查找包含该SKU的店铺项
  const storeItem = skuStoreList.value.find((item) =>
    item.skuList && item.skuList.length > 0
      ? item.skuList && item.skuList && item.skuList.includes(skuOrasin)
      : item.asinList && item.asinList && item.asinList.includes(skuOrasin)
  )
  if (!storeItem || !storeItem.keywords) return true

  // 获取当前SKU的关键词数量
  const keywordsCount = storeItem.keywords[skuOrasin]?.length || 0

  // 如果关键词数量已达到50个，则不允许添加更多
  if (keywordsCount >= 50) return false

  // 如果是空关键词行，或者是该SKU的最后一个关键词行，则显示添加按钮
  const isEmptyKeyword = !row.keyword || row.keyword.trim() === ''
  const isLastKeywordRow =
    tableData.value
      .filter((item) =>
        item.storeId === row.storeId && item.sku ? item.sku === skuOrasin : item.asin === skuOrasin
      )
      .findIndex((item) => item === row) ===
    tableData.value.filter((item) =>
      item.storeId === row.storeId && item.sku ? item.sku === skuOrasin : item.asin === skuOrasin
    ).length -
      1

  return isEmptyKeyword || isLastKeywordRow
}

// 添加关键词
const addKeyword = (skuOrasin, row) => {
  // 获取当前行的店铺ID
  if (row) {
    currentEditingStoreId.value = row.storeId
  }

  // 重置状态
  currentEditingSku.value = skuOrasin
  tempKeywords.value = []
  keywordInput.value = ''
  keywordError.value = ''
  keywordSuccess.value = ''
  keywordDialogVisible.value = true

  // 延迟聚焦输入框，提高用户体验
  nextTick(() => {
    if (keywordInputRef.value) {
      keywordInputRef.value.$el.querySelector('textarea').focus()
    }
  })
}

// 删除关键词
const removeKeyword = (row) => {
  // 查找包含该SKU的店铺项
  const storeItem = skuStoreList.value.find(
    (item) => item.skuList && item.skuList.includes(row.sku)
  )

  if (!storeItem || !storeItem.keywords) return

  // 检查当前SKU是否只剩下最后一个关键词
  const skuKeywords = tableData.value.filter(
    (item) =>
      item.storeId === row.storeId &&
      item.sku === row.sku &&
      item.keyword &&
      item.keyword.trim() !== ''
  )

  const isLastKeyword = skuKeywords.length === 1

  // 从skuStoreItem.keywords中删除关键词
  if (storeItem.keywords[row.sku]) {
    const keywordIndex = storeItem.keywords[row.sku].findIndex((k) => k === row.keyword)
    if (keywordIndex !== -1) {
      storeItem.keywords[row.sku].splice(keywordIndex, 1)
    }
  }

  // 查找当前行在tableData中的索引
  const rowIndex = tableData.value.findIndex(
    (item) => item.storeId === row.storeId && item.sku === row.sku && item.keyword === row.keyword
  )

  if (rowIndex !== -1) {
    if (isLastKeyword) {
      // 如果是最后一个关键词，将其替换为空白行而不是删除
      const emptyRow = { ...row, keyword: '' }
      // 重置匹配方式的选择
      emptyRow.broadMatch = ''
      emptyRow.exactMatch = ''
      emptyRow.phraseMatch = ''
      // 保留匹配方式的复选框状态
      tableData.value.splice(rowIndex, 1, emptyRow)
      message.info('已保留一个空白行，您可以继续添加关键词')
    } else {
      // 如果不是最后一个关键词，直接删除
      tableData.value.splice(rowIndex, 1)
      message.success('关键词删除成功')
    }
  }

  // 检查是否还有关键词，如果没有，重置全局竞价选择
  const hasKeywordsAfterRemove = tableData.value.some(
    (item) => item.keyword && item.keyword.trim() !== ''
  )
  if (!hasKeywordsAfterRemove) {
    globalBidSelection.value = ''
  }
}

// 处理竞价选择变化
const handleBidChange = (row: any, type: any, all: any) => {
  if (row[`${type}Match`] === 'Low' && !all) {
    row[`${type}Bid`] = row[`${type}SuggestBidLow`]
  } else if (row[`${type}Match`] === 'Mid') {
    row[`${type}Bid`] = row[`${type}SuggestBidMid`]
  } else if (row[`${type}Match`] === 'High') {
    row[`${type}Bid`] = row[`${type}SuggestBidHigh`]
  }
}

// 判断是否有任何关键词
const hasAnyKeywords = computed(() => {
  // 检查tableData中是否有任何行包含非空关键词
  return tableData.value.some((row) => row.keyword && row.keyword.trim() !== '')
})

// 修改应用全局竞价函数
const applyGlobalBid = (value) => {
  // 直接在tableData上操作，应用全局竞价设置到所有有效行
  tableData.value.forEach((row) => {
    // 跳过没有关键词的行
    if (!row.keyword || row.keyword.trim() === '') {
      return
    }

    // 应用全局竞价设置到每种匹配方式
    ;['broad', 'exact', 'phrase'].forEach((matchType) => {
      // 只处理已启用的匹配方式
      if (row[matchType] == '1') {
        const suggestBid = row[`${matchType}SuggestBid${value}`]
        if (suggestBid != undefined && suggestBid !== '-') {
          // 更新匹配类型选择和竞价
          row[`${matchType}Match`] = value
          row[`${matchType}Bid`] = suggestBid
        }
      }
    })
  })
}

/** 打开弹窗 */
const openDialog = async (type: string, adTypeValue: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  adType.value = adTypeValue
  currentStep.value = 1
  keywordFilter.value = ''
  selectedTemplate.value = null
  // 初始化SKU-店铺列表
  skuStoreList.value = [
    { storeId: null, skuValue: '', skuList: [] }
    // {
    //   storeId: 3362,
    //   skuValue: '3',
    //   skuList: ['3'],
    //   storeInfo: {
    //     id: null,
    //     uid: null,
    //     sellerItem: '梁家波-DE-IT',
    //     sid: 3362,
    //     transMap: {},
    //     value: 3362,
    //     label: '梁家波-DE-IT'
    //   },
    //   storeName: '梁家波-DE-IT'
    // },
    // {
    //   storeId: 2625,
    //   skuValue: '4\n5',
    //   skuList: ['4', '5'],
    //   storeInfo: {
    //     id: null,
    //     uid: null,
    //     sellerItem: '宿超-日本-JP',
    //     sid: 2625,
    //     transMap: {},
    //     value: 2625,
    //     label: '宿超-日本-JP'
    //   },
    //   storeName: '宿超-日本-JP'
    // }
  ]
  getShopListFc()
  // 获取模板选项
  const tempREs = await AdvertisingTemplateApi.getAdvertisingTemplatePage({ size: 1000, page: 1 })
  templateOptions.value = tempREs.list.map((item) => ({
    ...item,
    templateName:
      item.templateName +
      `(${getDictLabel(DICT_TYPE.OPERATION_PLACEMENT_TYPE, item.placementType)})`
  }))
}

defineExpose({ openDialog }) // 提供 openDialog 方法，用于打开弹窗

const getShopListFc = async () => {
  const res = await ThirdPartyAccountApi.getShopList()
  storeOptions.value = res.map((item) => ({
    ...item,
    value: item.sid,
    label: item.sellerItem || item.name || item.shopName
  }))

  const resFromSelf = await ThirdPartyAccountApi.getSellerBySelf()
}

// 弹窗关闭
const closeDialog = () => {
  dialogVisible.value = false
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

// 实际的提交表单逻辑
const doSubmitForm = async (type?: string) => {
  if (currentStep.value === 1 && isAutoTemplate.value) {
    // 自动模板提交
    // 校验SKU列表不能为空
    const hasSkus = skuStoreList.value.some(
      (item) =>
        (item.skuList && item.skuList.length > 0) || (item.asinList && item.asinList.length > 0)
    )
    if (!hasSkus) {
      message.error('请至少输入一个SKU或ASIN')
      return
    }
  } else if (currentStep.value === 2) {
    // 手动模板提交
    // 校验关键词设置
    let hasKeywords = false
    filteredTableData.value.filter((storeItem) => {
      // 判断已经添加的关键词和skuList长度是否一样，一样则说明已经设置完关键词
      if (
        storeItem.keywords &&
        (storeItem.skuList.length > 0
          ? Object.keys(storeItem.keywords).length == storeItem.skuList.length
          : Object.keys(storeItem.keywords).length == storeItem.asinList.length)
      ) {
        hasKeywords = true
      } else {
        hasKeywords = false
      }
    })
    if (!hasKeywords) {
      message.error('请给所有SKU或ASIN设置关键词')
      return
    }
  }

  // 提交请求
  formLoading.value = true
  try {
    // 设置模板ID和广告类型
    const templateId = selectedTemplate.value.id
    const adType = isAutoTemplate.value ? 'auto' : 'manual'

    // 准备提交数据
    let submitData = []

    if (isAutoTemplate.value) {
      // 自动模板：为每个SKU创建一个对象
      skuStoreList.value.forEach((storeItem) => {
        // 跳过没有店铺ID或SKU的行
        if (!storeItem.storeId) {
          return
        }
        // 填sku
        if (storeItem.skuList && storeItem.skuList.length > 0) {
          // 为每个SKU创建一个对象
          storeItem.skuList.forEach((sku) => {
            submitData.push({
              ...selectedTemplate.value,
              templateId: templateId,
              placementType: selectedTemplate.value.placementType.toString(),
              adType: adType,
              sku: sku,
              storeId: storeItem.storeId, // 添加店铺ID
              storeName: storeItem.storeName, // 添加店铺名称
              // 其他必要字段
              userId: selectedTemplate.value.userId || undefined,
              deptId: selectedTemplate.value.deptId || undefined,
              jobUuid: selectedTemplate.value.jobUuid || undefined
            })
          })
        }
        // 填asin
        if (storeItem.asinList && storeItem.asinList.length > 0) {
          // 为每个asin创建一个对象
          storeItem.asinList.forEach((asin) => {
            submitData.push({
              ...selectedTemplate.value,
              templateId: templateId,
              placementType: selectedTemplate.value.placementType.toString(),
              adType: adType,
              asin: asin,
              storeId: storeItem.storeId, // 添加店铺ID
              storeName: storeItem.storeName, // 添加店铺名称
              // 其他必要字段
              userId: selectedTemplate.value.userId || undefined,
              deptId: selectedTemplate.value.deptId || undefined,
              jobUuid: selectedTemplate.value.jobUuid || undefined
            })
          })
        }
      })
    } else {
      // 手动模板：直接使用tableData中的数据
      tableData.value.forEach((row) => {
        // 跳过没有关键词的行
        if (!row.keyword || row.keyword.trim() === '') {
          return
        }

        // 为每个匹配方式创建一个对象
        const matchTypes = ['broad', 'exact', 'phrase']
        matchTypes.forEach((matchType) => {
          // 只处理选中的匹配方式
          if (row[matchType] == '1') {
            // 创建基础对象
            const keywordObj = {
              ...selectedTemplate.value,
              templateId: templateId,
              placementType: selectedTemplate.value.placementType.toString(),
              adType: adType,
              sku: row.sku,
              asin: row.asin,
              storeId: row.storeId,
              storeName: row.storeName,
              keywords: row.keyword,
              // 匹配方式字段 - 只设置当前匹配方式为1，其他为0
              exact: matchType == 'exact' ? '1' : '0',
              broad: matchType == 'broad' ? '1' : '0',
              phrase: matchType == 'phrase' ? '1' : '0',
              // 竞价字段 - 只设置当前匹配方式的竞价
              exactBid: matchType == 'exact' ? row.exactBid || 0 : 0,
              broadBid: matchType == 'broad' ? row.broadBid || 0 : 0,
              phraseBid: matchType == 'phrase' ? row.phraseBid || 0 : 0,
              // 建议竞价字段
              exactSuggestBidLow: matchType == 'exact' ? row.exactSuggestBidLow || 0 : 0,
              exactSuggestBidMid: matchType == 'exact' ? row.exactSuggestBidMid || 0 : 0,
              exactSuggestBidHigh: matchType == 'exact' ? row.exactSuggestBidHigh || 0 : 0,
              broadSuggestBidLow: matchType == 'broad' ? row.broadSuggestBidLow || 0 : 0,
              broadSuggestBidMid: matchType == 'broad' ? row.broadSuggestBidMid || 0 : 0,
              broadSuggestBidHigh: matchType == 'broad' ? row.broadSuggestBidHigh || 0 : 0,
              phraseSuggestBidLow: matchType == 'phrase' ? row.phraseSuggestBidLow || 0 : 0,
              phraseSuggestBidMid: matchType == 'phrase' ? row.phraseSuggestBidMid || 0 : 0,
              phraseSuggestBidHigh: matchType == 'phrase' ? row.phraseSuggestBidHigh || 0 : 0,
              // 其他必要字段
              userId: selectedTemplate.value.userId || undefined,
              deptId: selectedTemplate.value.deptId || undefined,
              jobUuid: selectedTemplate.value.jobUuid || undefined
            }

            submitData.push(keywordObj)
          }
        })
      })
    }

    // 检查是否有数据要提交
    if (submitData.length === 0) {
      message.error(isAutoTemplate.value ? '请至少输入一个SKU' : '请至少添加一个关键词')
      return
    }

    let data = {
      templateId: '',
      skus: []
    }

    if (isAutoTemplate.value) {
      submitData.forEach((item) => {
        ;(data.templateId = item.templateId),
          data.skus.push({
            sid: item.storeId,
            storeName: item.storeName,
            sku: item.sku,
            asin: item.asin,
            ...item,
            keywords: []
          })
      })
    } else {
      data = transformData(submitData)
    }

    await AdvertisingCreationApi.createAdvertising(data)

    message.success('创建成功')

    // 发送操作成功的事件
    emit('success')
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

// 使用debounce包装提交函数，防止重复点击
const submitForm = debounce(
  (type?: string) => {
    // 立即显示加载状态，提升用户体验
    formLoading.value = true
    doSubmitForm(type)
  },
  500,
  { leading: true, trailing: false }
)

function transformData(originalData) {
  // 1. 初始化结果对象
  const result = {
    templateId: originalData[0]?.templateId || 0, // 取第一个元素的templateId
    skus: [] as Array<{
      sid: number
      storeName: string
      sku: string
      asin: string
      keywords: Array<{
        keywords: string
        exact: string
        broad: string
        phrase: string
        exactBid: number
        exactSuggestBidLow: number | string
        exactSuggestBidMid: number | string
        exactSuggestBidHigh: number | string
        broadBid: number
        broadSuggestBidLow: number | string
        broadSuggestBidMid: number | string
        broadSuggestBidHigh: number | string
        phraseBid: number
        phraseSuggestBidLow: number | string
        phraseSuggestBidMid: number | string
        phraseSuggestBidHigh: number | string
        // 其他字段
        id?: number
        creationSkuId?: number
        keywordType?: string
        userId?: number
        deptId?: number
        creationId?: number
      }>
    }>
  }

  // 2. 使用Map按storeId和sku分组
  const skuMap = new Map<string, any>()

  originalData.forEach((item) => {
    const key = `${item.storeId}_${item.sku || item.asin}_${item.keyword}`
    if (!skuMap.has(key)) {
      skuMap.set(key, {
        sid: item.storeId,
        storeName: item.storeName,
        sku: item.sku,
        asin: item.asin, // 新增ASIN字段
        keywords: []
      })
    }

    // 3. 合并相同关键词的不同匹配方式
    const existingKeyword = skuMap.get(key).keywords.find((k) => k.keywords === item.keywords)

    if (existingKeyword) {
      // 更新现有关键词的匹配方式
      existingKeyword.exact = item.exact === '1' ? '1' : existingKeyword.exact
      existingKeyword.broad = item.broad === '1' ? '1' : existingKeyword.broad
      existingKeyword.phrase = item.phrase === '1' ? '1' : existingKeyword.phrase

      // 更新竞价数据
      if (item.exact === '1') {
        existingKeyword.exactBid = item.exactBid
        existingKeyword.exactSuggestBidLow = item.exactSuggestBidLow
        existingKeyword.exactSuggestBidMid = item.exactSuggestBidMid
        existingKeyword.exactSuggestBidHigh = item.exactSuggestBidHigh
      }

      if (item.broad === '1') {
        existingKeyword.broadBid = item.broadBid
        existingKeyword.broadSuggestBidLow = item.broadSuggestBidLow
        existingKeyword.broadSuggestBidMid = item.broadSuggestBidMid
        existingKeyword.broadSuggestBidHigh = item.broadSuggestBidHigh
      }

      if (item.phrase === '1') {
        existingKeyword.phraseBid = item.phraseBid
        existingKeyword.phraseSuggestBidLow = item.phraseSuggestBidLow
        existingKeyword.phraseSuggestBidMid = item.phraseSuggestBidMid
        existingKeyword.phraseSuggestBidHigh = item.phraseSuggestBidHigh
      }
    } else {
      // 添加新关键词
      skuMap.get(key).keywords.push({
        keywords: item.keywords,
        exact: item.exact,
        broad: item.broad,
        phrase: item.phrase,
        exactBid: item.exactBid,
        exactSuggestBidLow: item.exactSuggestBidLow,
        exactSuggestBidMid: item.exactSuggestBidMid,
        exactSuggestBidHigh: item.exactSuggestBidHigh,
        broadBid: item.broadBid,
        broadSuggestBidLow: item.broadSuggestBidLow,
        broadSuggestBidMid: item.broadSuggestBidMid,
        broadSuggestBidHigh: item.broadSuggestBidHigh,
        phraseBid: item.phraseBid,
        phraseSuggestBidLow: item.phraseSuggestBidLow,
        phraseSuggestBidMid: item.phraseSuggestBidMid,
        phraseSuggestBidHigh: item.phraseSuggestBidHigh,
        // 其他字段（根据需求添加）
        id: item.id,
        keywordType: '1', // 默认值
        userId: item.userId,
        deptId: item.deptId
      })
    }
  })

  // 4. 将Map转换为数组
  result.skus = Array.from(skuMap.values())

  return result
}

// 第一步-校验
function validateStoreAndSKU(data) {
  const errors = []

  // 校验规则1：店铺storeId不能为空
  data.forEach((item, index) => {
    if (!item.storeId) {
      errors.push({
        type: 'empty_store',
        message: `第${index + 1}行店铺ID不能为空`,
        position: index
      })
    }
  })

  data.forEach((item, index) => {
    // 检查必须至少有一个标识符
    if (!item.skuList?.length && !item.asinList?.length) {
      errors.push({
        type: 'empty_sku_asin_list',
        message: `第${index + 1}行必须输入SKU或ASIN`,
        position: index
      })
    }
    // 检查不能同时存在
    if (item.skuList?.length != 0 && item.asinList?.length != 0) {
      errors.push({
        type: 'empty_sku_asin_on_list',
        message: `第${index + 1}行不能同时输入SKU和ASIN`,
        position: index
      })
    }
  })

  // 校验规则1：店铺storeId不能相同
  const storeIds = new Set()
  const duplicateStores = new Set()

  data.forEach((item) => {
    if (storeIds.has(item.storeId)) {
      duplicateStores.add(item.storeId)
    } else {
      storeIds.add(item.storeId)
    }
  })

  if (duplicateStores.size > 0) {
    errors.push({
      type: 'duplicate_store',
      message: `店铺ID重复：${Array.from(duplicateStores).join(', ')}`,
      storeIds: Array.from(duplicateStores)
    })
  }

  // 校验规则2：每个skuList内数据不能相同
  data.forEach((item, index) => {
    const skuSet = new Set()
    const duplicateSkus = new Set()

    item.skuList.forEach((sku) => {
      if (skuSet.has(sku)) {
        duplicateSkus.add(sku)
      } else {
        skuSet.add(sku)
      }
    })

    if (duplicateSkus.size > 0) {
      errors.push({
        type: 'duplicate_sku',
        message: `店铺 ${item.storeId} 的SKU重复：${Array.from(duplicateSkus).join(', ')}`,
        storeId: item.storeId,
        duplicateSkus: Array.from(duplicateSkus),
        position: index
      })
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/** 下一步 */
const goToNextStep = () => {
  // 验证模板是否选择
  if (!selectedTemplate.value) {
    message.error('请选择广告模板')
    return
  }

  const validation = validateStoreAndSKU(skuStoreList.value)

  if (!validation.isValid) {
    // 显示第一个错误（可根据需求调整显示策略）
    const firstError = validation.errors[0]
    switch (firstError.type) {
      case 'empty_store':
        message.error(`请为第${firstError.position + 1}行选择店铺`)
        break
      case 'empty_sku_asin_list':
        message.error(`第${firstError.position + 1}行必须输入SKU或ASIN`)
        break
      case 'empty_sku_asin_on_list':
        message.error(`第${firstError.position + 1}行不能同时输入SKU和ASIN`)
        break
      case 'duplicate_store':
        message.error(`店铺ID重复：${firstError.storeIds.join(', ')}`)
        break
      case 'duplicate_sku':
        message.error(
          `店铺 ${firstError.storeId} 存在重复SKU：${firstError.duplicateSkus.join(', ')}`
        )
        break
    }
    return
  }

  // 保存当前tableData中的数据状态
  const currentDataMap = new Map()
  if (tableData.value.length > 0) {
    tableData.value.forEach((row) => {
      // 使用storeId+sku+keyword作为唯一键
      const key = `${row.storeId}_${row.sku || row.asin}_${row.keyword}`
      currentDataMap.set(key, { ...row })
    })
  }

  // 生成新的tableData
  const newTableData = flattenData(skuStoreList.value)
  // 合并已有数据，保留用户已经设置的值
  newTableData.forEach((row) => {
    const key = `${row.storeId}_${row.sku || row.asin}_${row.keyword}`
    if (currentDataMap.has(key)) {
      // 合并已有数据，保留匹配方式和竞价设置
      const existingRow = currentDataMap.get(key)
      // 保留匹配方式选择
      row.broad = existingRow.broad
      row.exact = existingRow.exact
      row.phrase = existingRow.phrase
      // 保留竞价选择
      row.broadMatch = existingRow.broadMatch
      row.exactMatch = existingRow.exactMatch
      row.phraseMatch = existingRow.phraseMatch
      // 保留竞价值
      row.broadBid = existingRow.broadBid
      row.exactBid = existingRow.exactBid
      row.phraseBid = existingRow.phraseBid
    }
  })

  tableData.value = newTableData
  currentStep.value = 2
}

// 店铺选择
const handleStoreChange = (row) => {
  row.storeId = row.storeInfo.sid
  row.storeName = row.storeInfo.label
}

// 监听关键词数据变化
watch(
  () => hasAnyKeywords.value,
  (newValue) => {
    if (!newValue) {
      globalBidSelection.value = ''
    }
  }
)

// 监听tableData变化缓存
watch(
  () => tableData.value,
  (newValue) => {
    if (currentStep.value === 2) {
      localStorage.setItem('tableData', JSON.stringify(newValue))
    }
  },
  { deep: true }
)
// 判断窗口宽度并更新 isFullscreen
function checkScreenSize() {
  isFullscreen.value = window.innerWidth < 1757
}
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style lang="scss" scoped>
.dynamic-input-container {
  position: relative;
}

.advertising-form-container {
  padding: 20px;

  .template-select-container {
    margin-bottom: 20px;
  }

  .sku-input-area {
    margin-bottom: 20px;

    .sku-input-tip {
      margin-top: 10px;

      .tip-text {
        font-size: 14px;
        color: #f56c6c;
      }
    }
  }
}

.keyword-setting-container {
  padding: 20px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.match-type-checkboxes {
  display: flex;
  flex-direction: column;
}

.empty-keyword-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #f56c6c;
}

.bid-suggestion {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 2px;

  :deep(.el-radio-button__inner) {
    padding: 8px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background-color: #409eff;
    border-color: #409eff;
    box-shadow: -1px 0 0 0 #409eff;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .el-radio-button {
      margin-right: 0;
    }
  }
}

.bid-global-actions {
  margin: 10px 0;
  justify-content: center;

  :deep(.el-radio-button__inner) {
    padding: 8px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background-color: #409eff;
    border-color: #409eff;
    box-shadow: -1px 0 0 0 #409eff;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .el-radio-button {
      margin-right: 0;
    }
  }
}

/* 关键词对话框样式 */
.keyword-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .keyword-input-section {
    h4 {
      margin-top: 0;
      margin-bottom: 10px;
      font-weight: 500;
      color: #606266;
    }

    .keyword-tips {
      margin-top: 10px;

      .format-tips {
        padding: 8px 12px;
        margin-top: 8px;
        font-size: 13px;
        color: #606266;
        background-color: #f8f8f8;
        border-radius: 4px;

        p {
          margin: 4px 0;
          line-height: 1.5;
        }
      }
    }
  }

  .keyword-preview-section {
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h4 {
        margin: 0;
        font-weight: 500;
        color: #606266;
      }
    }

    .keyword-tags {
      display: flex;
      max-height: 150px;
      padding: 10px;
      overflow-y: auto;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        margin-right: 0;
        transition: all 0.2s;

        &:hover {
          background-color: #ecf5ff;
        }
      }
    }
  }

  .keyword-error-section,
  .keyword-success-section {
    margin-top: 10px;
  }
}

/* 表格滚动优化 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }
}

/* 竞价设置区域优化 */
// .bid-suggestion {
//   transition: all 0.3s ease;

//   &:hover {
//     padding: 5px 0;
//     background-color: #f5f7fa;
//     border-radius: 4px;
//   }
// }

/* 表格工具栏样式 */
.table-toolbar {
  display: flex;
  padding: 10px;
  margin-bottom: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  justify-content: space-between;
  align-items: center;

  .table-info {
    font-size: 14px;
    color: #606266;
  }
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  padding: 10px 0;
  margin-top: 20px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  justify-content: center;
}

:deep(.el-drawer__body) {
  position: relative;
  height: calc(100% - 60px); // 减去头部和底部的高度
  overflow-y: auto;
}

:deep(.el-backtop) {
  right: 40px;
  bottom: 40px;
}
</style>
