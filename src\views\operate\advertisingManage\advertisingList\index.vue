<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="模板" prop="templateId">
        <el-select v-model="queryParams.templateId" placeholder="全部" clearable class="!w-120px">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in templateOptions"
            :key="item.id"
            :label="item.templateName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="广告类型" prop="placementType">
        <el-select
          v-model="queryParams.placementType"
          placeholder="全部"
          clearable
          class="!w-120px"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_PLACEMENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="全部" clearable class="!w-120px">
          <el-option label="全部" value="" />
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTime">
        <shortcut-date-range-picker isTimes v-model="queryParams.createTime" />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
          v-hasPermi="['operation:advertising-creation:query']"
        >
          <Icon icon="ep:search" />查询
        </el-button>
        <el-button @click="resetQuery" v-hasPermi="['operation:advertising-creation:query']">
          <Icon icon="ep:refresh" />重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <div class="mb-4 flex gap-2">
      <el-button
        type="primary"
        @click="openForm('create')"
        v-hasPermi="['operation:advertising-creation:create']"
      >
        <Icon icon="ep:plus" />创建广告
      </el-button>
      <el-button
        type="primary"
        @click="openTemplateForm('create', 0)"
        v-hasPermi="['operation:advertising-creation:create']"
      >
        <Icon icon="ep:plus" />创建广告模板
      </el-button>
    </div>

    <el-table
      border
      ref="adTableRef"
      v-loading="loading"
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="广告类型" prop="placementType" width="100">
        <template #default="scope">
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="viewAdDetail(scope.row.id, scope.row.templateName)"
          >
          </el-link> -->
          {{ scope.row.placementType == '0' ? '自动' : '手动' }}
        </template>
      </el-table-column>

      <el-table-column label="模板" prop="templateName" />

      <el-table-column align="center" label="状态" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status">
            {{ getDictLabel(DICT_TYPE.OPERATION_TASK_STATUS, scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="SKU状态（可点击查看）" prop="status" width="300">
        <template #default="scope">
          <div class="sku-status-container">
            <el-tag
              type="success"
              class="status-tag"
              @click="viewAdDetail(scope.row.id, scope.row.templateName, 1)"
              :style="{ cursor: 'pointer' }"
            >
              成功： {{ scope.row.successCount }}
            </el-tag>
            <el-tag
              type="danger"
              class="status-tag"
              @click="viewAdDetail(scope.row.id, scope.row.templateName, 2)"
              :style="{ cursor: 'pointer' }"
            >
              失败： {{ scope.row.failCount }}
            </el-tag>
            <el-tag
              type="info"
              class="status-tag"
              @click="viewAdDetail(scope.row.id, scope.row.templateName, 0)"
              :style="{ cursor: 'pointer' }"
            >
              创建中： {{ scope.row.totalCount - scope.row.successCount - scope.row.failCount }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="失败原因" prop="failureReason" width="300" show-overflow-tooltip />
      <el-table-column label="创建时间" prop="createTime" :formatter="dateFormatter" width="160" />
      <el-table-column label="操作" width="160">
        <template #default="scope">
          <el-button
            type="text"
            v-hasPermi="['operation:advertising-creation:query']"
            @click="viewAdDetail(scope.row.id, scope.row.templateName)"
          >
            详情
          </el-button>
          <el-button
            type="text"
            v-hasPermi="['operation:advertising-creation:create']"
            v-if="scope.row.status == 2"
            @click="resetBtn(scope.row)"
          >
            重试
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!--新增/编辑-->
  <AdvertisingForm ref="formRef" @success="getList" />

  <!--模板表单-->
  <TemplateForm ref="templateFormRef" @success="getList" />

  <!-- 详情弹窗 -->
  <Details ref="detailsRef" />
</template>

<script lang="ts" setup>
import { AdvertisingCreationApi, AdvertisingTemplateApi } from '@/api/operate/advertisingManage'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import TemplateForm from '../advertisingTemlMg/components/TemplateForm.vue'
import Details from './advertisingDetails.vue'
import AdvertisingForm from './advertisingForm.vue'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  templateId: '',
  placementType: '',
  status: '',
  createTime: []
})

// 模板选项
const templateOptions = ref([])

const queryFormRef = ref() // 搜索的表单
const multipleSelection = ref<{ id: number }[]>([]) // 选中的列表项

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    // 调用实际API
    const params = {
      ...queryParams
    }

    const res = await AdvertisingCreationApi.getAdvertisingPage(params)
    if (res) {
      list.value = res.list || []
      total.value = res.total || 0
    }
    const tempREs = await AdvertisingTemplateApi.getAdvertisingTemplatePage({ size: 1000, page: 1 })
    templateOptions.value = tempREs.list.map((item) => ({
      ...item,
      templateName:
        item.templateName +
        `(${getDictLabel(DICT_TYPE.OPERATION_PLACEMENT_TYPE, item.placementType)})`
    }))
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.openDialog(type, id)
}

/** 添加/修改模板操作 */
const templateFormRef = ref()
const openTemplateForm = (type: string, placementType: string, id?: number) => {
  templateFormRef.value.openDialog(type, placementType, id)
}

const detailsRef = ref()
/** 查看广告详情 */
const viewAdDetail = (id: number, templateName: string, status?: string) => {
  detailsRef.value.open(id, templateName, status)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 重试按钮
const resetBtn = async (row) => {
  let res = await AdvertisingCreationApi.retryApi({
    creationId: row.id
  })
  if (!res) ElMessage.success('重试成功')
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.budget-info {
  display: flex;
  flex-direction: column;
  align-items: center;

  .budget-amount {
    display: flex;
    align-items: center;
    gap: 4px;

    .currency-symbol {
      font-weight: bold;
      color: #409eff;
    }

    .budget-type {
      margin-left: 4px;
      font-size: 12px;
      color: #606266;
    }
  }
}

.sku-status-container {
  display: flex;
  justify-content: center;
  gap: 8px;

  .status-tag {
    transition: all 0.3s;
  }
}
</style>
