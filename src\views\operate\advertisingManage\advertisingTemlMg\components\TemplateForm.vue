<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    size="90vw"
    style="min-width:1000px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
    >
      <el-form-item
        label="广告类型"
        prop="placementType"
      >
        <el-radio-group
          v-model="formData.placementType"
          :disabled="!!formData.id"
        >
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_PLACEMENT_TYPE)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="站点"
        prop="siteId"
        style="width: 30%;"
      >
        <el-select
          v-model="formData.siteId"
          placeholder="请选择站点"
          class="w-full"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_SITE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        label="模板名称"
        prop="templateName"
        style="width: 30%;"
      >
        <el-input
          v-model="formData.templateName"
          placeholder="请输入模板名称"
        />
      </el-form-item>

      <div class="rule-setting-section">
        <h3>规则设置</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="预算"
              prop="budget"
              required
            >
              <div class="input-with-currency">
                <el-input
                  v-model="formData.budget"
                  placeholder="请输入预算"
                  style="width: 300px"
                >
                  <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                  </template>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="时间"
              required
            >
              <div class="date-range-picker">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="开始日期"
                  style="width: 50%"
                />
                <span class="date-separator">-</span>
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="结束日期"
                  :disabled-date="disabledDate"
                  :disabled="!formData.startDate"
                  style="width: 50%"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="广告组合">
              <el-select
                v-model="formData.adGroup"
                placeholder="请选择广告组合"
                style="width: 300px"
                filterable
                :filter-method="filterAdGroupOptions"
                clearable
              >
                <el-option
                  v-for="item in filteredAdGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>

          </el-col>
          <el-col :span="12">
            <el-form-item
              label="竞价策略"
              required
            >
              <el-select
                v-model="formData.bidStrategy"
                placeholder="请选择竞价策略"
                style="width: 300px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_CAMPAIGN_BIDDING_STRATEGY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="广告组默认竞价"
              required
            >
              <div class="input-with-currency">
                <el-input
                  v-model="formData.defaultBid"
                  placeholder="请输入默认竞价"
                  style="width: 300px"
                  @input="defaultBidChange"
                >
                  <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                  </template>
                </el-input>
              </div>
            </el-form-item>
            <el-form-item v-if="formData.placementType == 0">
              <el-checkbox
                v-model="formData.bidByTargetingGroup"
                true-label="1"
                false-label="0"
              >按定向组设置竞价</el-checkbox>
            </el-form-item>

            <!-- 定向组竞价设置 -->
            <div
              v-if="formData.placementType == 0 &&formData.bidByTargetingGroup"
              class="bid-settings"
            >
              <div class="bid-setting-header">
                <span class="bid-type">定向组</span>
                <span class="bid-value">竞价</span>
              </div>

              <div class="bid-setting-item">
                <div class="bid-type-switch">
                  <el-switch v-model="formData.exactMatchEnabled" />
                  <span style="width:100px">精准匹配</span>
                </div>
                <div class="bid-value-input">
                  <el-input
                    v-model="formData.exactMatchBid"
                    placeholder="0.3"
                  >
                    <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                    </template>
                  </el-input>
                </div>
              </div>

              <div class="bid-setting-item">
                <div class="bid-type-switch">
                  <el-switch v-model="formData.phraseMatchEnabled" />
                  <span style="width:100px">宽泛匹配</span>
                </div>
                <div class="bid-value-input">
                  <el-input
                    v-model="formData.phraseMatchBid"
                    placeholder="0.3"
                  >
                    <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                    </template>
                  </el-input>
                </div>
              </div>

              <div class="bid-setting-item">
                <div class="bid-type-switch">
                  <el-switch v-model="formData.substitutesEnabled" />
                  <span style="width:100px">同类商品</span>
                </div>
                <div class="bid-value-input">
                  <el-input
                    v-model="formData.substitutesBid"
                    placeholder="0.3"
                  >
                    <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                    </template>
                  </el-input>
                </div>
              </div>

              <div class="bid-setting-item">
                <div class="bid-type-switch">
                  <el-switch v-model="formData.complementsEnabled" />
                  <span style="width:100px">关联商品</span>
                </div>
                <div class="bid-value-input">
                  <el-input
                    v-model="formData.complementsBid"
                    placeholder="0.3"
                  >
                    <template #prefix> <span class="currency-symbol">{{ getCurrencySymbol(formData.siteId) }}</span>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label-width="250px"
              label-position="top"
              label="根据广告位调整竞价  (替换 Bid+)："
            >
              <div class="percentage-box">
                <el-form-item
                  label-width="300px"
                  label-position="right"
                  label="搜索结果顶部（首页）："
                >
                  <div class="percentage-input">
                    <el-input
                      v-model.number="formData.topPlacementAdjustment"
                      type="number"
                      max="900"
                      min="0"
                      placeholder="0"
                      style="width: 120px"
                      @input="handleInput"
                    >
                      <template #append>%</template>
                    </el-input>
                  </div>
                </el-form-item>

                <el-form-item
                  label-width="300px"
                  label-position="right"
                  label="（测试版）搜索结果其余位置："
                >
                  <div class="percentage-input">
                    <el-input
                      v-model.number="formData.sidebarAdjustment"
                      type="number"
                      max="900"
                      min="0"
                      placeholder="0"
                      style="width: 120px"
                      @input="handleInput"
                    >
                      <template #append>%</template>
                    </el-input>

                  </div>
                </el-form-item>
                <el-form-item
                  label-width="300px"
                  label-position="right"
                  label="商品页面："
                >
                  <div class="percentage-input">
                    <el-input
                      v-model.number="formData.productPageAdjustment"
                      type="number"
                      max="900"
                      min="0"
                      placeholder="0"
                      style="width: 120px"
                      @input="handleInput"
                    >
                      <template #append>%</template>
                    </el-input>
                  </div>
                </el-form-item>
                <el-form-item
                  label-width="300px"
                  label-position="right"
                  label="亚马逊企业购  亚马逊企业购广告位："
                >
                  <div class="percentage-input">
                    <el-input
                      v-model.number="formData.bidAmazonEnterprisePurchase"
                      type="number"
                      max="900"
                      min="0"
                      placeholder="0"
                      style="width: 120px"
                      @input="handleInput"
                    >
                      <template #append>%</template>
                    </el-input>
                  </div>
                </el-form-item>
              </div>
            </el-form-item>

          </el-col>
        </el-row>
      </div>

      <div class="rule-setting-section">
        <h3>命名规则</h3>
        <div class="naming-rule-container">
          <div class="naming-rule-item">
            <span class="naming-rule-label">广告活动:</span>
            <el-input
              v-model="formData.campaignPrefix"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.campaignMiddle"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.campaignSuffix"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.campaignCustom"
              placeholder="自定义"
              class="naming-rule-input"
              maxlength="64"
              @input="updateNamingExample"
            />
            <div class="naming-rule-example">
              示例: {{ namingExample.campaign }}
            </div>
          </div>

          <div class="naming-rule-item">
            <span class="naming-rule-label">广告组:</span>
            <el-input
              v-model="formData.adGroupPrefix"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.adGroupMiddle"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.adGroupSuffix"
              :disabled="true"
              class="naming-rule-input"
            />
            <span class="naming-rule-separator">-</span>
            <el-input
              v-model="formData.adGroupCustom"
              placeholder="自定义"
              maxlength="64"
              class="naming-rule-input"
              @input="updateNamingExample"
            />
            <div class="naming-rule-example">
              示例: {{ namingExample.adGroup }}
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="flex w100% justify-center">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { AdvertisingTemplateApi } from '@/api/operate/advertisingManage'
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formType = ref('')
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: undefined,
  siteId: '',
  templateName: '',
  placementType: 0, // 默认为自动广告
  budget: '10',
  // 默认为当前日期yyyy-MM-dd
  startDate: new Date().toISOString().split('T')[0],
  endDate: '',
  adGroup: '',
  bidStrategy: 1,
  defaultBid: '0.3',
  bidByTargetingGroup: '1',
  exactMatchEnabled: true,
  exactMatchBid: '0.3',
  phraseMatchEnabled: false,
  phraseMatchBid: '0.3',
  substitutesEnabled: false,
  substitutesBid: '0.3',
  complementsEnabled: false,
  complementsBid: '0.3',
  bidAdjustment: '0',
  topPlacementAdjustment: '0',
  sidebarAdjustment: '0',
  productPageAdjustment: '0',
  bidAmazonEnterprisePurchase: '0',
  // 命名规则字段
  campaignPrefix: 'SKU',
  campaignMiddle: 'ZD',
  campaignSuffix: '创建日期',
  campaignCustom: '',
  adGroupPrefix: 'SKU',
  adGroupMiddle: 'ZD',
  adGroupSuffix: '创建日期',
  adGroupCustom: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  siteId: [{ required: true, message: '请选择站点', trigger: 'change' }],
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  placementType: [{ required: true, message: '请选择广告类型', trigger: 'change' }],
  budget: [{ required: true, message: '请输入投放预算', trigger: 'blur' }],
  bidStrategy: [{ required: true, message: '请选择竞价策略', trigger: 'change' }],
  adGroup: [{ required: true, message: '请选择广告组合', trigger: 'change' }],
  defaultBid: [{ required: true, message: '请输入广告组默认竞价', trigger: 'blur' }]
})

// 广告组合选项
const adGroupOptions = ref([])
const filteredAdGroupOptions = ref([])

// 获取货币符号
const getCurrencySymbol = (siteId: string) => {
  const site = getIntDictOptions(DICT_TYPE.OPERATION_SITE).find((item) => item.value === siteId)
  if (!site) return '€'

  const currencyMatch = site.label.match(/\(([^)]+)\)/)
  return currencyMatch ? currencyMatch[1] : '€'
}
// 广告组默认竞价变动后
const defaultBidChange = (value) => {
  formData.exactMatchBid = value
  formData.phraseMatchBid = value
  formData.substitutesBid = value
  formData.complementsBid = value
}

const formRef = ref<FormInstance>()

// 打开对话框
const openDialog = async (type: string, placementType: any, id?: any) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()

  // 设置广告类型
  formData.placementType = placementType || 0

  // 设置标题
  dialogTitle.value =
    type === 'create' ? `创建${placementType == 0 ? '自动' : '手动'}广告模板` : '编辑广告模板'

  // 如果是编辑，则获取数据
  if (type === 'update' && id) {
    formData.id = id
    // 调用API获取详情
    loading.value = true
    try {
      const res = await AdvertisingTemplateApi.getAdvertisingTemplateDetail(id)
      console.log(typeof +res.placementType)
      if (res) {
        console.log(res)
        // console.log(res.beginDate.join('-'))

        // 将API返回的数据映射到表单
        Object.assign(formData, {
          siteId: +res.site,
          templateName: res.templateName,
          placementType: +res.placementType, // 根据API参数，'0'为自动广告，'1'为手动广告
          budget: res.budget?.toString(),
          bidStrategy: +res.campaignBiddingStrategy,
          adGroup: res.portfolio,
          defaultBid: res.defaultBid?.toString(),
          // 竞价设置
          exactMatchEnabled: !!res.exactMatch,
          exactMatchBid: res.exactMatch?.toString() || '0.3',
          phraseMatchEnabled: !!res.broadMatch,
          phraseMatchBid: res.broadMatch?.toString() || '0.3',
          substitutesEnabled: !!res.similarProducts,
          substitutesBid: res.similarProducts?.toString() || '0.3',
          complementsEnabled: !!res.relatedProducts,
          complementsBid: res.relatedProducts?.toString() || '0.3',
          // 广告位调整
          bidAmazonEnterprisePurchase: res.bidAmazonEnterprisePurchase?.toString() || '0',
          topPlacementAdjustment: res.bidTopSearchResults?.toString() || '0',
          sidebarAdjustment: res.bidOtherSearchResults?.toString() || '0',
          productPageAdjustment: res.bidProductPage?.toString() || '0',
          // 日期
          startDate: res.beginDate,
          endDate: res.endDate,
          // 命名规则
          namingRuleCampaign: res.namingRuleCampaign,
          namingRuleGroup: res.namingRuleGroup
        })
        // 解析命名规则
        if (res.namingRuleCampaign) {
          const parts = res.namingRuleCampaign.split('-')
          if (parts.length >= 4) {
            formData.campaignPrefix = parts[0]
            formData.campaignMiddle = parts[1]
            formData.campaignSuffix = '创建日期'
            formData.campaignCustom = parts[3]
          }
        }

        if (res.namingRuleGroup) {
          const parts = res.namingRuleGroup.split('-')
          if (parts.length >= 4) {
            formData.adGroupPrefix = parts[0]
            formData.adGroupMiddle = parts[1]
            formData.adGroupSuffix = '创建日期'
            formData.adGroupCustom = parts[3]
          }
        }

        // 更新命名示例
        updateNamingExample()
      }
    } catch (error) {
      console.error('获取模板详情失败', error)
    } finally {
      loading.value = false
    }
  }
  getAdPortfolioList()
}

// 重置表单
const resetForm = () => {
  formData.id = undefined
  formData.siteId = ''
  formData.templateName = ''
  formData.placementType = 0
  formData.budget = '10'
  formData.startDate = new Date().toISOString().split('T')[0]
  formData.adGroup = ''
  formData.bidStrategy = 1
  formData.defaultBid = '0.3'
  formData.bidByTargetingGroup = '1'
  formData.exactMatchEnabled = true
  formData.exactMatchBid = '0.3'
  formData.phraseMatchEnabled = false
  formData.phraseMatchBid = '0.3'
  formData.substitutesEnabled = false
  formData.substitutesBid = '0.3'
  formData.complementsEnabled = false
  formData.complementsBid = '0.3'
  formData.bidAdjustment = '0'
  formData.topPlacementAdjustment = '0'
  formData.sidebarAdjustment = '0'
  formData.productPageAdjustment = '0'
  // 重置命名规则字段
  formData.campaignPrefix = 'SKU'
  formData.campaignMiddle = formData.placementType == 0 ? 'ZD' : 'SD'
  formData.campaignSuffix = '创建日期'
  formData.campaignCustom = ''

  formData.adGroupPrefix = 'SKU'
  formData.adGroupMiddle = formData.placementType == 0 ? 'ZD' : 'SD'
  formData.adGroupSuffix = '创建日期'
  formData.adGroupCustom = ''
}
// 获取广告组合列表
const getAdPortfolioList = async (query?: any) => {
  try {
    const res = await AutomationAdvertisementAnalysisApi.getAdPortfolio({
      adPortfolio: query
    })
    adGroupOptions.value = res
    filteredAdGroupOptions.value = res
  } catch (error) {
    console.log(error)
  }
}

// 自定义筛选方法
const filterAdGroupOptions = (query: string) => {
  if (!query) {
    filteredAdGroupOptions.value = adGroupOptions.value
    return
  }
  
  const searchQuery = query.toLowerCase()
  
  // 分别获取以查询字符串开头的和包含查询字符串的选项
  const startsWithMatches = adGroupOptions.value.filter(item => {
    return item.name.toLowerCase().startsWith(searchQuery)
  })
  
  const containsMatches = adGroupOptions.value.filter(item => {
    const name = item.name.toLowerCase()
    return name.includes(searchQuery) && !name.startsWith(searchQuery)
  })
  
  // 优先显示以查询字符串开头的选项，然后是包含查询字符串的选项
  filteredAdGroupOptions.value = [...startsWithMatches, ...containsMatches]
}



// 提交表单
const submitForm = async () => {
  console.log(formData)
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    try {
      loading.value = true
      // 调用API保存数据
      // 构建API所需的数据格式
      const apiData = {
        id: formData.id,
        templateName: formData.templateName,
        site: formData.siteId,
        placementType: formData.placementType, // 根据API参数要求，自动广告为'0'，手动广告为'1'
        budget: Number(formData.budget),
        defaultBid: Number(formData.defaultBid),
        portfolio: formData.adGroup,
        campaignBiddingStrategy: formData.bidStrategy,
        broadMatch: formData.phraseMatchEnabled ? Number(formData.phraseMatchBid) : 0,
        exactMatch: formData.exactMatchEnabled ? Number(formData.exactMatchBid) : 0,
        relatedProducts: formData.complementsEnabled ? Number(formData.complementsBid) : 0,
        similarProducts: formData.substitutesEnabled ? Number(formData.substitutesBid) : 0,
        bidAmazonEnterprisePurchase: Number(formData.bidAmazonEnterprisePurchase) || 0,
        bidTopSearchResults: Number(formData.topPlacementAdjustment) || 0,
        bidOtherSearchResults: Number(formData.sidebarAdjustment) || 0,
        bidProductPage: Number(formData.productPageAdjustment) || 0,
        beginDate: formData.startDate,
        endDate: formData.endDate,
        bidByTargetingGroup: formData.bidByTargetingGroup,
        exactMatchFlag: formData.exactMatchEnabled ? 1 : 0,
        broadMatchFlag: formData.phraseMatchEnabled ? 1 : 0,
        relatedProductsFlag: formData.complementsEnabled ? 1 : 0,
        similarProductsFlag: formData.substitutesEnabled ? 1 : 0,
        namingRuleCampaign: `${formData.campaignPrefix}-${formData.campaignMiddle}-CREATE_DATE${
          formData.campaignCustom ? '-' + formData.campaignCustom : ''
        }`,
        namingRuleGroup: `${formData.adGroupPrefix}-${formData.adGroupMiddle}-CREATE_DATE${
          formData.adGroupCustom ? '-' + formData.adGroupCustom : ''
        }`
        // 用户ID和部门ID由后端处理，不需要前端传递
      }

      if (formType.value === 'create') {
        await AdvertisingTemplateApi.createAdvertisingTemplate(apiData)
      } else {
        await AdvertisingTemplateApi.updateAdvertisingTemplate(apiData)
      }

      ElMessage.success(formType.value === 'create' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      emit('success')
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  })
}

// 命名规则示例
const namingExample = reactive({
  campaign: '',
  adGroup: ''
})

// 更新命名规则示例
const updateNamingExample = () => {
  const today = new Date()
  const year = today.getFullYear().toString().slice(2) // 获取年份后两位
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  const dateStr = `${year}${month}${day}`

  // 不在这里设置默认值，避免覆盖已有值
  // 只在初始化时设置默认值，这里只更新示例

  // 生成示例
  namingExample.campaign = `${dateStr}SND03YZKJDFK-${formData.campaignMiddle}-${dateStr}-${
    formData.campaignCustom || '自定义'
  }`
  namingExample.adGroup = `${dateStr}SND03YZKJDFK-${formData.adGroupMiddle}-${dateStr}-${
    formData.adGroupCustom || '自定义'
  }`
}

// 搜索结果顶部（首页）：、（测试版）搜索结果其余位置：、商品页面：、亚马逊企业购 亚马逊企业购广告位：校验，必须是0-900的正整数
const handleInput = (val) => {
  val = Number(val)
  if (val < 0 || val > 900 || !Number.isInteger(val)) {
    ElMessage.error('请输入0-900的正整数')
  }
}
import dayjs from 'dayjs'
const disabledDate = (time: Date) => {
  if (!formData.startDate) return true
  return dayjs(time).isBefore(dayjs(formData.startDate))
}

// 监听广告类型变化，更新广告组命名规则中间部分
watch(
  () => formData.placementType,
  (newType) => {
    formData.campaignMiddle = newType == 0 ? 'ZD' : 'SD'
    formData.adGroupMiddle = newType == 0 ? 'ZD' : 'SD'
    updateNamingExample()
  }
)

// 初始化命名规则示例
onMounted(() => {
  updateNamingExample()
})

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.rule-setting-section {
  margin-top: 20px;

  h3 {
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
  }

  .naming-rule-container {
    margin-top: 20px;

    .naming-rule-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .naming-rule-label {
        width: 80px;
        margin-right: 10px;
        font-size: 12px;
        text-align: right;
      }

      .naming-rule-input {
        width: 180px;
      }

      .naming-rule-separator {
        margin: 0 5px;
        font-weight: bold;
      }
    }

    .naming-rule-example {
      margin-left: 20px;
      font-size: 12px;
      color: #666;
    }
  }
}

.input-with-currency {
  display: flex;
  align-items: center;

  .currency-symbol {
    margin-right: 5px;
    font-weight: bold;
    color: black;
  }
}

.date-range-picker {
  display: flex;
  align-items: center;

  .date-separator {
    margin: 0 10px;
  }
}

.bid-settings {
  max-width: 53%;
  min-width: 40%;
  padding: 10px;
  margin-bottom: 20px;
  margin-left: 100px;
  border: 1px solid #eee;
  border-radius: 4px;

  .bid-setting-header {
    display: flex;
    margin-bottom: 10px;
    font-size: 12px;
    font-weight: bold;

    .bid-type {
      flex: 1;
    }

    .bid-value {
      width: 120px;
      text-align: center;
    }
  }

  .bid-setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .bid-type-switch {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 12px;
    }

    .bid-value-input {
      display: flex;
      width: 120px;
      align-items: center;

      .currency-symbol {
        margin-right: 5px;
        font-weight: bold;
        color: black;
      }
    }
  }
}

.percentage-box {
  display: flex;
  flex-direction: column;

  ::v-deep(.el-form-item__label) {
    text-align: right !important;
  }

  :deep(.el-form-item__label) {
    text-align: right !important;
  }
}

.percentage-input {
  display: flex;
  align-items: center;
}
</style>
