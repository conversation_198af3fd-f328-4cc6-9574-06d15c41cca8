<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="站点" prop="siteId">
        <el-select v-model="queryParams.siteId" placeholder="请选择站点" clearable class="!w-200px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_SITE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称查询"
          clearable
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="广告类型" prop="placementType">
        <el-select
          v-model="queryParams.placementType"
          placeholder="全部"
          clearable
          class="!w-120px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_PLACEMENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTime">
        <shortcut-date-range-picker isTimes v-model="queryParams.createTime" />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
          v-hasPermi="['operation:advertising-template:query']"
        >
          <Icon icon="ep:search" />查询
        </el-button>
        <el-button @click="resetQuery" v-hasPermi="['operation:advertising-template:query']">
          <Icon icon="ep:refresh" />重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <div class="mb-4 flex gap-2">
      <el-button
        type="primary"
        @click="openForm('create', 0)"
        v-hasPermi="['operation:advertising-template:create']"
      >
        <Icon icon="ep:plus" />创建广告模板
      </el-button>
    </div>

    <el-table
      border
      ref="templateTableRef"
      v-loading="loading"
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="站点" prop="siteName" width="200">
        <template #default="scope">
          {{ getSiteName(scope.row.site) }}
        </template>
      </el-table-column>

      <el-table-column label="模板名称" prop="templateName" min-width="180" />

      <el-table-column label="广告类型" prop="placementType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.placementType === '0' ? 'success' : 'primary'">
            {{ scope.row.placementType === '0' ? '自动' : '手动' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" prop="createTime" :formatter="dateFormatter" width="160" />

      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.placementType, scope.row.id)"
            v-hasPermi="['operation:advertising-template:create']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            v-hasPermi="['operation:advertising-template:delete']"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!--模板表单-->
  <TemplateForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { AdvertisingTemplateApi } from '@/api/operate/advertisingManage'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import TemplateForm from './components/TemplateForm.vue'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  siteId: '',
  templateName: '',
  placementType: '',
  createTime: []
})

const queryFormRef = ref() // 搜索的表单
const multipleSelection = ref<{ id: number }[]>([]) // 选中的列表项

// 获取站点名称
const getSiteName = (siteId: string) => {
  const site = getIntDictOptions(DICT_TYPE.OPERATION_SITE).find((item) => item.value == siteId)
  return site ? site.label : siteId
}

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    // 调用实际API
    const params = {
      ...queryParams,
      site: queryParams.siteId // 将siteId映射到接口需要的site参数
    }

    const res = await AdvertisingTemplateApi.getAdvertisingTemplatePage(params)
    if (res) {
      list.value = res.list || []
      total.value = res.total || 0
    }
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, placementType: string, id?: number) => {
  formRef.value.openDialog(type, placementType, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AdvertisingTemplateApi.deleteAdvertisingTemplate(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.site-info {
  display: flex;
  align-items: center;

  .site-currency {
    margin-left: 4px;
    font-size: 12px;
    color: #606266;
  }
}
</style>
