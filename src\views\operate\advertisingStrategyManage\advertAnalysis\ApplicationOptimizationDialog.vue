<template>
  <el-dialog
    v-model="dialogVisible"
    title="应用优化建议"
    width="50%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-if="loading" class="text-center py-4">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span class="ml-2">加载中...</span>
    </div>

    <div v-else>
      <div class="flex gap-10px mb-10px">
        <!-- 预算调整 -->
        <div class="flex items-center gap-10px">
          <span class="w-80px text-right">预算调整:</span>
          <el-select
            v-model="batchEditValues.budgetType"
            placeholder="请选择调整方式"
            style="width: 200px"
          >
            <el-option label="将预算调整到" value="set_to" />
            <el-option label="提高预算-按百分比" value="increase_percent" />
            <el-option label="提高预算-按固定" value="increase_fixed" />
            <el-option label="降低预算-按百分比" value="decrease_percent" />
            <el-option label="降低预算-按固定值" value="decrease_fixed" />
          </el-select>
          <el-input
            v-model="batchEditValues.budgetValue"
            placeholder="请输入值"
            size="small"
            type="number"
            :min="0"
            step="0.1"
            style="width: 150px"
          >
            <template #append>
              <span>{{ batchEditValues.budgetType?.includes('percent') ? '%' : '' }}</span>
            </template>
          </el-input>
        </div>
        <!-- 竞价调整 -->
        <div class="flex items-center gap-10px">
          <span class="w-80px text-right">竞价调整:</span>
          <el-select
            v-model="batchEditValues.bidType"
            placeholder="请选择调整方式"
            style="width: 200px"
          >
            <el-option label="将竞价调整到" value="set_to" />
            <el-option label="提高竞价-按百分比" value="increase_percent" />
            <el-option label="提高竞价-按固定" value="increase_fixed" />
            <el-option label="降低竞价-按百分比" value="decrease_percent" />
            <el-option label="降低竞价-按固定值" value="decrease_fixed" />
          </el-select>
          <el-input
            v-model="batchEditValues.bidValue"
            placeholder="请输入值"
            size="small"
            type="number"
            :min="0"
            step="0.1"
            style="width: 150px"
          >
            <template #append>
              <span>{{ batchEditValues.bidType?.includes('percent') ? '%' : '' }}</span>
            </template>
          </el-input>
        </div>
        <!-- 预览按钮 -->
        <div class="flex justify-center">
          <el-button type="primary" size="small" @click="previewBatchAdjust" style="height: 30px"
            >预览</el-button
          >
        </div>
      </div>
      <!-- 广告活动列表 -->
      <div v-for="(campaign, index) in applicationData" :key="index" class="campaign-section">
        <div class="flex items-center w-100% gap-10px">
          <div class="font-semibold size-15px w-auto">广告活动：{{ campaign.adCampaign }}</div>
          <div class="flex items-center gap-10px">
            <div class="flex items-center gap-10px">
              <div> 当前预算：</div>
              <span class="text-blue-600 font-medium"
                >{{ campaign.currency || 'JP ¥' }}{{ campaign.budget }}</span
              >
              <div>调整后预算：</div>
              <el-tooltip
                placement="right"
                :disabled="!shouldShowBudgetTooltip(campaign)"
                :show-after="100"
                :hide-after="50"
              >
                <template #content>
                  <div v-html="getBudgetTooltipContent(campaign)"></div>
                </template>
                <div
                  @mouseenter="handleBudgetMouseEnter(campaign, 'CAMPAIGN_UPDATE_BUDGET')"
                  @mouseleave="handleBudgetMouseLeave(campaign)"
                >
                  <el-input
                    v-model="campaign.adjustBudget"
                    class="!w-100px"
                    size="small"
                    type="number"
                    :min="0"
                  />
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 关键词表格 -->
        <el-table :data="campaign.keywords" border stripe>
          <el-table-column label="关键词" prop="keyword" />
          <el-table-column label="广告组" prop="adGroup" />
          <el-table-column label="当前竞价" width="120">
            <template #default="{ row }">
              <span class="text-blue-600">{{ row.currency || 'JP ¥' }} {{ row.bid }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优化建议" prop="suggest" width="150" />
          <el-table-column label="调整后竞价" width="120">
            <template #default="{ row }">
              <el-tooltip
                placement="right"
                :disabled="!shouldShowBidTooltip(row)"
                :show-after="100"
                :hide-after="50"
              >
                <template #content>
                  <div v-html="getBidTooltipContent(row)"></div>
                </template>
                <div
                  @mouseenter="handleBidMouseEnter(row, 'KEYWORD_UPDATE_BID')"
                  @mouseleave="handleBidMouseLeave(row)"
                >
                  <el-input
                    v-model="row.adjustBid"
                    class="!w-100px"
                    size="small"
                    type="number"
                    :min="0"
                  />
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <el-button type="primary" size="small" @click="openBatchBidDialog(index, $index)">
                批量
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeOptimization" :loading="executing">
          立即执行
        </el-button>
      </div>
    </template>

    <!-- 批量修改预算弹窗 -->
    <el-popover v-model:visible="batchBudgetVisible" placement="top" :width="200" trigger="manual">
      <template #reference>
        <div ref="batchBudgetRef"></div>
      </template>
      <div>
        <el-select
          v-model="batchBudgetType"
          placeholder="请选择调整方式"
          style="width: 100%; margin-bottom: 10px"
        >
          <el-option label="提高预算_按百分比" value="increase_percent" />
          <el-option label="提高预算_按定值" value="increase_fixed" />
          <el-option label="降低预算_按百分比" value="decrease_percent" />
          <el-option label="降低预算_按定值" value="decrease_fixed" />
        </el-select>
        <el-input
          v-model="batchBudgetValue"
          placeholder="请输入值"
          type="number"
          style="width: 100%; margin-bottom: 10px"
        >
          <template #append>
            <span>{{ batchBudgetType?.includes('percent') ? '%' : '' }}</span>
          </template>
        </el-input>
        <div style="text-align: right">
          <el-button size="small" @click="batchBudgetVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="applyBatchBudget">确认</el-button>
        </div>
      </div>
    </el-popover>

    <!-- 批量修改竞价弹窗 -->
    <el-popover v-model:visible="batchBidVisible" placement="top" :width="200" trigger="manual">
      <template #reference>
        <div ref="batchBidRef"></div>
      </template>
      <div>
        <el-select
          v-model="batchBidType"
          placeholder="请选择调整方式"
          style="width: 100%; margin-bottom: 10px"
        >
          <el-option label="提高竞价_按百分比" value="increase_percent" />
          <el-option label="提高竞价_按定值" value="increase_fixed" />
          <el-option label="降低竞价_按百分比" value="decrease_percent" />
          <el-option label="降低竞价_按定值" value="decrease_fixed" />
        </el-select>
        <el-input
          v-model="batchBidValue"
          placeholder="请输入值"
          type="number"
          style="width: 100%; margin-bottom: 10px"
        >
          <template #append>
            <span>{{ batchBidType?.includes('percent') ? '%' : '' }}</span>
          </template>
        </el-input>
        <div style="text-align: right">
          <el-button size="small" @click="batchBidVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="applyBatchBid">确认</el-button>
        </div>
      </div>
    </el-popover>

    <!-- 执行结果弹窗 -->
    <ExecutionResultDialog
      v-model="resultDialogVisible"
      :result="executionResult"
      @closed="executionResult = {}"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import { useMessage } from '@/hooks/web/useMessage'
import { Loading } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'

const message = useMessage()
const emit = defineEmits(['optimization-complete'])

const dialogVisible = ref(false)
const loading = ref(false)
const executing = ref(false)
const applicationData = ref([])
const selectedIds = ref([])
const visibleShop = ref(false)

// 批量修改预算相关
const batchBudgetVisible = ref(false)
const batchBudgetRef = ref()
const batchBudgetType = ref('')
const batchBudgetValue = ref('')
const currentCampaignIndex = ref(-1)

// 批量修改竞价相关
const batchBidVisible = ref(false)
const batchBidRef = ref()
const batchBidType = ref('')
const batchBidValue = ref('')
const currentKeywordIndex = ref(-1)

const batchEditValues = reactive({
  budgetType: '',
  budgetValue: '',
  bidType: '',
  bidValue: ''
})

// 执行结果弹窗相关
const resultDialogVisible = ref(false)
const executionResult = ref({})

// tooltip相关状态
const budgetTooltipCache = ref(new Map())
const bidTooltipCache = ref(new Map())
const budgetTooltipLoading = ref(new Map())
const bidTooltipLoading = ref(new Map())
const budgetRequestingSet = ref(new Set())
const bidRequestingSet = ref(new Set())
const budgetTooltipDelayMap = ref(new Map())
const bidTooltipDelayMap = ref(new Map())

// 打开弹窗
const open = async (data: number[]) => {
  selectedIds.value = data.map((item) => item.id) || []
  dialogVisible.value = true
  loading.value = true
  // 清空input
  batchEditValues.budgetType = ''
  batchEditValues.budgetValue = ''
  batchEditValues.bidType = ''
  batchEditValues.bidValue = ''

  try {
    const response = await AutomationAdvertisementAnalysisApi.getApplication(selectedIds.value)
    applicationData.value = response || []
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取应用优化建议失败:', error)
    message.error('获取应用优化建议失败')
  }
}

// 打开批量修改预算弹窗
const openBatchBudgetDialog = (campaignIndex: number) => {
  currentCampaignIndex.value = campaignIndex
  batchBudgetType.value = ''
  batchBudgetValue.value = ''
  batchBudgetVisible.value = true
}

// 打开批量修改竞价弹窗
const openBatchBidDialog = (campaignIndex: number, keywordIndex: number) => {
  currentCampaignIndex.value = campaignIndex
  currentKeywordIndex.value = keywordIndex
  batchBidType.value = ''
  batchBidValue.value = ''
  batchBidVisible.value = true
}

// 应用批量预算修改
const applyBatchBudget = () => {
  if (!batchBudgetType.value || !batchBudgetValue.value) {
    message.warning('请选择调整方式并输入值')
    return
  }

  const campaign = applicationData.value[currentCampaignIndex.value]
  const currentBudget = parseFloat(campaign.budget)
  const value = parseFloat(batchBudgetValue.value)

  let newBudget = currentBudget

  switch (batchBudgetType.value) {
    case 'increase_percent':
      newBudget = currentBudget * (1 + value / 100)
      break
    case 'increase_fixed':
      newBudget = currentBudget + value
      break
    case 'decrease_percent':
      newBudget = currentBudget * (1 - value / 100)
      break
    case 'decrease_fixed':
      newBudget = currentBudget - value
      break
  }

  campaign.adjustBudget = Math.max(0, newBudget).toFixed(2)
  batchBudgetVisible.value = false
}

// 应用批量竞价修改
const applyBatchBid = () => {
  if (!batchBidType.value || !batchBidValue.value) {
    message.warning('请选择调整方式并输入值')
    return
  }

  const campaign = applicationData.value[currentCampaignIndex.value]
  const value = parseFloat(batchBidValue.value)

  // 如果是针对特定关键词
  if (currentKeywordIndex.value >= 0) {
    const keyword = campaign.keywords[currentKeywordIndex.value]
    const currentBid = parseFloat(keyword.bid)
    let newBid = currentBid

    switch (batchBidType.value) {
      case 'increase_percent':
        newBid = currentBid * (1 + value / 100)
        break
      case 'increase_fixed':
        newBid = currentBid + value
        break
      case 'decrease_percent':
        newBid = currentBid * (1 - value / 100)
        break
      case 'decrease_fixed':
        newBid = currentBid - value
        break
    }

    keyword.adjustBid = Math.max(0, newBid).toFixed(2)
  } else {
    // 批量应用到所有关键词
    campaign.keywords.forEach((keyword) => {
      const currentBid = parseFloat(keyword.bid)
      let newBid = currentBid

      switch (batchBidType.value) {
        case 'increase_percent':
          newBid = currentBid * (1 + value / 100)
          break
        case 'increase_fixed':
          newBid = currentBid + value
          break
        case 'decrease_percent':
          newBid = currentBid * (1 - value / 100)
          break
        case 'decrease_fixed':
          newBid = currentBid - value
          break
      }

      keyword.adjustBid = Math.max(0, newBid).toFixed(2)
    })
  }

  batchBidVisible.value = false
}

// 预览批量调整（预算和竞价）
const previewBatchAdjust = () => {
  let hasValidInput = false

  // 处理预算调整
  if (batchEditValues.budgetType && batchEditValues.budgetValue) {
    const budgetValue = parseFloat(batchEditValues.budgetValue)
    if (isNaN(budgetValue) || budgetValue < 0) {
      message.warning('请输入有效的预算数值')
      return
    }

    applicationData.value.forEach((campaign) => {
      const currentBudget = parseFloat(campaign.budget || 0)
      let newBudget = currentBudget

      switch (batchEditValues.budgetType) {
        case 'set_to':
          newBudget = budgetValue
          break
        case 'increase_percent':
          newBudget = currentBudget * (1 + budgetValue / 100)
          break
        case 'increase_fixed':
          newBudget = currentBudget + budgetValue
          break
        case 'decrease_percent':
          newBudget = currentBudget * (1 - budgetValue / 100)
          break
        case 'decrease_fixed':
          newBudget = currentBudget - budgetValue
          break
      }

      campaign.adjustBudget = Math.max(0, newBudget).toFixed(2)
    })

    hasValidInput = true
  }

  // 处理竞价调整
  if (batchEditValues.bidType && batchEditValues.bidValue) {
    const bidValue = parseFloat(batchEditValues.bidValue)
    if (isNaN(bidValue) || bidValue < 0) {
      message.warning('请输入有效的竞价数值')
      return
    }

    applicationData.value.forEach((campaign) => {
      if (campaign.keywords && campaign.keywords.length > 0) {
        campaign.keywords.forEach((keyword) => {
          const currentBid = parseFloat(keyword.adjustBid || keyword.bid || 0)
          let newBid = currentBid

          switch (batchEditValues.bidType) {
            case 'set_to':
              newBid = bidValue
              break
            case 'increase_percent':
              newBid = currentBid * (1 + bidValue / 100)
              break
            case 'increase_fixed':
              newBid = currentBid + bidValue
              break
            case 'decrease_percent':
              newBid = currentBid * (1 - bidValue / 100)
              break
            case 'decrease_fixed':
              newBid = currentBid - bidValue
              break
          }

          keyword.adjustBid = Math.max(0, newBid).toFixed(2)
        })
      }
    })

    hasValidInput = true
  }

  if (!hasValidInput) {
    message.warning('请至少选择一种调整方式并输入值')
    return
  }

  message.success('批量调整预览完成')
}

// 处理预算鼠标进入事件
const handleBudgetMouseEnter = (campaign, bizType) => {
  const cacheKey = `${campaign.campaignId}_budget`

  // 如果正在请求中，则不重复请求
  if (budgetRequestingSet.value.has(cacheKey)) {
    return
  }

  // 清除之前的延迟定时器
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(budgetTooltipDelayMap.value.get(cacheKey))
  }

  // 设置0.5秒延迟后再调用接口
  const delayTimer = setTimeout(() => {
    // 标记正在请求
    budgetRequestingSet.value.add(cacheKey)

    // 设置loading状态
    budgetTooltipLoading.value.set(cacheKey, true)

    // 生成预算范围信息（仅在有最小值或最大值时）
    let budgetRangeInfo = ''
    if (campaign.minBudget || campaign.maxBudget) {
      budgetRangeInfo = `预算范围: ${campaign.minBudget ? '最小' + (campaign.currency || '$') + campaign.minBudget + ' - ' : ''}${campaign.maxBudget ? '最大' + (campaign.currency || '$') + campaign.maxBudget : ''}`
      // 设置默认内容
      budgetTooltipCache.value.set(cacheKey, budgetRangeInfo)
    } else {
      // 如果没有范围信息，设置loading内容
      budgetTooltipCache.value.set(cacheKey, 'Loading...')
    }

    // 异步获取最新日志
    AutomationAdvertisementAnalysisApi.getSingleLogSelf({
      appId: campaign.appId,
      bizType: bizType,
      selfId: campaign.campaignId
    })
      .then((logData) => {
        if (logData) {
          const content = `最近一次${logData.type || ''}是在${logData.source || ''}中完成</br>时间：${logData.operationTime || ''}</br>操作人：${logData.userName || ''}</br>从${logData.before || ''}修改成${logData.after || ''}${budgetRangeInfo ? '</br>' + budgetRangeInfo : ''}`
          budgetTooltipCache.value.set(cacheKey, content)
        } else if (budgetRangeInfo) {
          // 如果没有日志数据但有预算范围，显示预算范围
          budgetTooltipCache.value.set(cacheKey, budgetRangeInfo)
        }
      })
      .catch(() => {
        // 保持默认内容
      })
      .finally(() => {
        budgetTooltipLoading.value.set(cacheKey, false)
        budgetRequestingSet.value.delete(cacheKey)
        budgetTooltipDelayMap.value.delete(cacheKey)
      })
  }, 500) // 0.5秒延迟

  budgetTooltipDelayMap.value.set(cacheKey, delayTimer)
}

// 处理预算鼠标离开事件
const handleBudgetMouseLeave = (campaign) => {
  // 清除延迟定时器，避免不必要的API调用
  const cacheKey = `${campaign.campaignId}_budget`
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(budgetTooltipDelayMap.value.get(cacheKey))
    budgetTooltipDelayMap.value.delete(cacheKey)
  }
}

// 判断是否应该显示预算tooltip
const shouldShowBudgetTooltip = (campaign) => {
  // 有最小值或最大值配置，或者有缓存的日志内容，或者正在加载中，或者有延迟定时器运行中
  const hasMinMax = campaign.minBudget || campaign.maxBudget
  const cacheKey = `${campaign.campaignId}_budget`
  const cachedContent = budgetTooltipCache.value.get(cacheKey)
  const hasLogContent = cachedContent && cachedContent.includes('最近一次')
  const isLoading = budgetTooltipLoading.value.get(cacheKey)
  const hasDelayTimer = budgetTooltipDelayMap.value.has(cacheKey)
  return hasMinMax || hasLogContent || isLoading || hasDelayTimer
}

// 获取预算tooltip内容
const getBudgetTooltipContent = (campaign) => {
  const cacheKey = `${campaign.campaignId}_budget`

  // 如果正在加载，显示loading
  if (budgetTooltipLoading.value.get(cacheKey)) {
    return 'Loading...'
  }

  // 如果有延迟定时器运行中，显示准备加载状态
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    return 'Loading...'
  }

  // 返回缓存内容或默认内容
  const cachedContent = budgetTooltipCache.value.get(cacheKey)

  // 如果没有最小值和最大值，且没有日志内容，则不显示范围信息
  if (
    !campaign.minBudget &&
    !campaign.maxBudget &&
    (!cachedContent || !cachedContent.includes('最近一次'))
  ) {
    return cachedContent || ''
  }

  // 只有在有最小值或最大值时才生成范围信息
  const budgetRangeInfo =
    campaign.minBudget || campaign.maxBudget
      ? `预算范围: ${campaign.minBudget ? '最小' + (campaign.currency || '$') + campaign.minBudget + ' - ' : ''}${campaign.maxBudget ? '最大' + (campaign.currency || '$') + campaign.maxBudget : ''}`
      : ''
  return cachedContent || budgetRangeInfo
}

// 处理竞价鼠标进入事件
const handleBidMouseEnter = (keyword, bizType) => {
  const cacheKey = `${keyword.keywordId}_bid`

  // 如果正在请求中，则不重复请求
  if (bidRequestingSet.value.has(cacheKey)) {
    return
  }

  // 清除之前的延迟定时器
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(bidTooltipDelayMap.value.get(cacheKey))
  }

  // 设置0.5秒延迟后再调用接口
  const delayTimer = setTimeout(() => {
    // 标记正在请求
    bidRequestingSet.value.add(cacheKey)

    // 设置loading状态
    bidTooltipLoading.value.set(cacheKey, true)

    // 生成竞价范围信息（仅在有最小值或最大值时）
    let bidRangeInfo = ''
    if (keyword.minBidding || keyword.maxBidding) {
      bidRangeInfo = `竞价范围: ${keyword.minBidding ? '最小' + (keyword.currency || '$') + keyword.minBidding + ' - ' : ''}${keyword.maxBidding ? '最大' + (keyword.currency || '$') + keyword.maxBidding : ''}`
      // 设置默认内容
      bidTooltipCache.value.set(cacheKey, bidRangeInfo)
    } else {
      // 如果没有范围信息，设置loading内容
      bidTooltipCache.value.set(cacheKey, 'Loading...')
    }

    // 异步获取最新日志
    AutomationAdvertisementAnalysisApi.getSingleLogSelf({
      appId: keyword.appId,
      bizType: bizType,
      selfId: keyword.keywordId
    })
      .then((logData) => {
        if (logData) {
          const content = `最近一次${logData.type || ''}是在${logData.source || ''}中完成</br>时间：${logData.operationTime || ''}</br>操作人：${logData.userName || ''}</br>从${logData.before || ''}修改成${logData.after || ''}${bidRangeInfo ? '</br>' + bidRangeInfo : ''}`
          bidTooltipCache.value.set(cacheKey, content)
        } else if (bidRangeInfo) {
          // 如果没有日志数据但有竞价范围，显示竞价范围
          bidTooltipCache.value.set(cacheKey, bidRangeInfo)
        }
      })
      .catch(() => {
        // 保持默认内容
      })
      .finally(() => {
        bidTooltipLoading.value.set(cacheKey, false)
        bidRequestingSet.value.delete(cacheKey)
        bidTooltipDelayMap.value.delete(cacheKey)
      })
  }, 500) // 0.5秒延迟

  bidTooltipDelayMap.value.set(cacheKey, delayTimer)
}

// 处理竞价鼠标离开事件
const handleBidMouseLeave = (keyword) => {
  // 清除延迟定时器，避免不必要的API调用
  const cacheKey = `${keyword.keywordId}_bid`
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(bidTooltipDelayMap.value.get(cacheKey))
    bidTooltipDelayMap.value.delete(cacheKey)
  }
}

// 判断是否应该显示竞价tooltip
const shouldShowBidTooltip = (keyword) => {
  // 有最小值或最大值配置，或者有缓存的日志内容，或者正在加载中，或者有延迟定时器运行中
  const hasMinMax = keyword.minBidding || keyword.maxBidding
  const cacheKey = `${keyword.keywordId}_bid`
  const cachedContent = bidTooltipCache.value.get(cacheKey)
  const hasLogContent = cachedContent && cachedContent.includes('最近一次')
  const isLoading = bidTooltipLoading.value.get(cacheKey)
  const hasDelayTimer = bidTooltipDelayMap.value.has(cacheKey)
  return hasMinMax || hasLogContent || isLoading || hasDelayTimer
}

// 获取竞价tooltip内容
const getBidTooltipContent = (keyword) => {
  const cacheKey = `${keyword.keywordId}_bid`

  // 如果正在加载，显示loading
  if (bidTooltipLoading.value.get(cacheKey)) {
    return 'Loading...'
  }

  // 如果有延迟定时器运行中，显示准备加载状态
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    return 'Loading...'
  }

  // 返回缓存内容或默认内容
  const cachedContent = bidTooltipCache.value.get(cacheKey)

  // 如果没有最小值和最大值，且没有日志内容，则不显示范围信息
  if (
    !keyword.minBidding &&
    !keyword.maxBidding &&
    (!cachedContent || !cachedContent.includes('最近一次'))
  ) {
    return cachedContent || ''
  }

  // 只有在有最小值或最大值时才生成范围信息
  const bidRangeInfo =
    keyword.minBidding || keyword.maxBidding
      ? `竞价范围: ${keyword.minBidding ? '最小' + (keyword.currency || '$') + keyword.minBidding + ' - ' : ''}${keyword.maxBidding ? '最大' + (keyword.currency || '$') + keyword.maxBidding : ''}`
      : ''
  return cachedContent || bidRangeInfo
}

// 执行优化建议
const executeOptimization = async () => {
  try {
    executing.value = true

    // 构建请求数据
    const requestData = applicationData.value.map((campaign) => ({
      ...campaign,
      appId: campaign.appId,
      sid: campaign.sid,
      campaignId: campaign.campaignId,
      adCampaign: campaign.adCampaign,
      budget: campaign.budget,
      adjustBudget: campaign.adjustBudget,
      campaignStatus: campaign.state,
      adjustCampaignStatus: campaign.adjustState,
      log: campaign.log,
      country: campaign.country || '',
      keywords: campaign.keywords.map((keyword) => ({
        ...keyword,
        appId: keyword.appId,
        sid: keyword.sid,
        keywordId: keyword.keywordId,
        adCampaign: keyword.adCampaign,
        keyword: keyword.keyword,
        adGroup: keyword.adGroup,
        bid: keyword.bid,
        adjustBid: keyword.adjustBid,
        suggest: keyword.suggest,
        state: keyword.state,
        adjustState: keyword.adjustState,
        log: keyword.log,
        country: keyword.country || ''
      }))
    }))

    const result = await AutomationAdvertisementAnalysisApi.updateApplication(requestData)

    // 保存执行结果并显示结果弹窗
    executionResult.value = result || {}
    dialogVisible.value = false
    resultDialogVisible.value = true

    // 发送事件通知父组件刷新列表
    emit('optimization-complete')
  } catch (error) {
    console.error('执行优化建议失败:', error)
    message.error('执行优化建议失败')
  } finally {
    executing.value = false
  }
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.campaign-section {
  margin-bottom: 6px;
  border-radius: 8px;

  .campaign-header {
    border-bottom: 1px solid #e4e7ed;
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  .el-input {
    width: 100px;
  }
}

:deep(.el-input__inner) {
  height: 30px;
}
</style>
