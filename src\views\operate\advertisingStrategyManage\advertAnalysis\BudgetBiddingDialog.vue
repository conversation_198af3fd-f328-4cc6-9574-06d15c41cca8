<template>
  <el-dialog
    v-model="dialogVisible"
    title="预算与竞价区间管理"
    width="50%"
    :before-close="handleClose"
  >
    <div class="budget-bidding-container">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="country" label="国家" width="120" align="center" />
        <el-table-column label="预算限制" align="center">
          <template #default="scope">
            <div class="budget-inputs">
              <el-input
                v-model="scope.row.minBudget"
                placeholder="最小值"
                size="small"
                style="width: 80px"
                @input="validatePositiveNumber(scope.row, 'minBudget')"
                @blur="saveBudgetBidding(scope.row)"
              />
              <span class="mx-2">-</span>
              <el-input
                v-model="scope.row.maxBudget"
                placeholder="最大值"
                size="small"
                style="width: 80px"
                @input="validatePositiveNumber(scope.row, 'maxBudget')"
                @blur="saveBudgetBidding(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="竞价限制" align="center">
          <template #default="scope">
            <div class="bidding-inputs">
              <el-input
                v-model="scope.row.minBidding"
                placeholder="最小值"
                size="small"
                style="width: 80px"
                @input="validatePositiveNumber(scope.row, 'minBidding')"
                @blur="saveBudgetBidding(scope.row)"
              />
              <span class="mx-2">-</span>
              <el-input
                v-model="scope.row.maxBidding"
                placeholder="最大值"
                size="small"
                style="width: 80px"
                @input="validatePositiveNumber(scope.row, 'maxBidding')"
                @blur="saveBudgetBidding(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-switch
              v-loading="saveLoading"
              :disabled="saveLoading"
              v-model="scope.row.status"
              active-text="开启"
              inactive-text="关闭"
              inline-prompt
              @change="saveBudgetBidding(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import { useMessage } from '@/hooks/web/useMessage'
import { ref } from 'vue'

const message = useMessage()

const dialogVisible = ref(false)
const loading = ref(false)
const saveLoading = ref(false)
// 表格数据
const tableData = ref([])

// 打开对话框
const open = () => {
  dialogVisible.value = true
  getBudgetBiddingList()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 获取预算竞价列表
const getBudgetBiddingList = async () => {
  try {
    loading.value = true
    const response = await AutomationAdvertisementAnalysisApi.getBudgetBiddingList()
    if (response) {
      tableData.value = response
    }
  } catch (error) {
    console.error('获取预算竞价列表失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 验证正数输入
const validatePositiveNumber = (row, field) => {
  if (typeof row[field] === 'undefined') {
    row[field] = ''
    return
  }

  // 转换为字符串
  if (typeof row[field] === 'number') {
    row[field] = row[field].toString()
  }

  // 过滤非数字和小数点的字符
  row[field] = row[field].replace(/[^\d.]/g, '')

  // 处理空字符串情况
  if (row[field] === '') {
    return
  }

  // 确保只有一个小数点
  const parts = row[field].split('.')
  if (parts.length > 2) {
    row[field] = parts[0] + '.' + parts.slice(1).join('')
  }

  // 确保小数点后不超过两位
  if (parts[1] && parts[1].length > 2) {
    row[field] = parts[0] + '.' + parts[1].substring(0, 2)
  }

  // 验证是否为正数
  const value = Number(row[field])
  if (!isNaN(value) && value < 0) {
    row[field] = ''
    message.warning('请输入正数')
  }
}

// 保存预算竞价设置
const saveBudgetBidding = async (row) => {
  saveLoading.value = true
  try {
    const params = row

    await AutomationAdvertisementAnalysisApi.saveBudgetBidding(params)
    getBudgetBiddingList()
    message.success('保存成功')
    saveLoading.value = false
  } catch (error) {
    console.error('保存预算竞价设置失败:', error)
    message.error('保存失败')
    saveLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.budget-bidding-container {
  padding: 20px 0;
}

.budget-inputs,
.bidding-inputs {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mx-2 {
  margin: 0 8px;
}
</style>
