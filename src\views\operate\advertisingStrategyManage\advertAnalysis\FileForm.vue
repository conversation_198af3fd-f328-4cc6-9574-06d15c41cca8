<template>
  <Dialog v-model="dialogVisible" title="上传文件" width="500px">
    <div class="file_name_box">
      文件名称：
      <el-input
        v-model="data.fileName"
        style="width: 200px"
        placeholder="请输入文件名称"
        clearable
      />
    </div>
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="importUrl + '?fileName=' + data.fileName"
      :auto-upload="false"
      :disabled="uploadDisable"
      :headers="uploadHeaders"
      :limit="1"
      :on-change="handleFileChange"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-success="submitFormSuccess"
      accept=".xlsx, .xls"
      drag
    >
      <Icon icon="ep:upload-filled" class="text-3xl text-gray-400 mb-2" />
      <div class="el-upload__text"> 填写"文件名称"后将文件拖到此处，或 <em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip mt-2">
          <div class="text-red-500">提示：仅允许导入 xls、xlsx 格式文件！</div>
          <div class="text-gray-500 mt-1">上传前请确保文件格式正确，数据完整</div>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { getAccessToken, getTenantId } from '@/utils/auth'
import type { UploadProps } from 'element-plus'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

defineOptions({ name: 'InfraFileForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const uploadDisable = ref(true) // 上传按钮的是否禁用
const formLoading = ref(false) // 表单的加载中
const fileList = ref([]) // 文件列表
const data = ref({ path: '', fileName: '' })
const uploadRef = ref()
const importUrl =
  import.meta.env.VITE_BASE_URL +
  import.meta.env.VITE_API_URL +
  '/operation/automation-advertising-strategy/import'
const { uploadUrl, httpRequest } = useUpload()
const uploadHeaders = ref() // 上传 Header 头
/** 打开弹窗 */
const open = (id?: number, callback?: () => void) => {
  dialogVisible.value = true
  resetForm()

  // 在上传成功后调用回调
  if (callback) {
    successCallback.value = callback
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  file.name = data.value.fileName
  data.value.path = file.name
}

/** 提交表单 */
const submitFileForm = () => {
  if (fileList.value.length === 0) {
    message.error(' 请上传文件')
    return
  }
  // 提交请求
  uploadHeaders.value = {
    Authorization: 'Bearer ' + getAccessToken(),
    'tenant-id': getTenantId()
  }
  uploadRef.value!.submit()
}

/** 文件上传成功处理 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitFormSuccess = (response, file, fileList) => {
  if (response.code !== 0) {
    message.error(response.msg)
    formLoading.value = false
    resetForm()
    return
  }
  const index = fileList.findIndex((f) => f.uid === file.uid)
  if (index !== -1) {
    fileList[index].name = data.value.fileName // 更新文件名称
  }
  // 清理
  dialogVisible.value = false
  formLoading.value = false
  unref(uploadRef)?.clearFiles()
  // 提示成功，并刷新
  emit('success')
  // 清空data
  data.value.fileName = ''
  data.value.path = ''
  message.success(t('common.createSuccess'))
}

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error(' 上传失败，请您重新上传！')
  formLoading.value = false
  // 重置上传组件状态，允许再次上传
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  // 重置上传状态和文件
  formLoading.value = false
  uploadRef.value?.clearFiles()
  fileList.value = []
  data.value.fileName = ''
  data.value.path = ''
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error(' 最多只能上传一个文件！请删除之前的文件后再上传！')
}

// 文件上传前校验
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  // 校验文件名是否填写
  if (!data.value.fileName) {
    message.error(' 请填写文件名称')
    return false
  }

  // 校验文件类型
  const validExtensions = ['xls', 'xlsx']
  const fileExtension = rawFile.name.slice(rawFile.name.lastIndexOf('.') + 1).toLowerCase()
  if (!validExtensions.includes(fileExtension)) {
    message.error(' 仅允许上传 xls、xlsx 格式的文件！')
    return false
  }

  const customFile = new File([rawFile], data.value.fileName + '.' + fileExtension, {
    type: rawFile.type
  })
  // 将新文件附加到FormData中
  const formData = new FormData()
  formData.append('file', customFile)
  // 其他参数（如data.value.path ）也需附加到formData
  formData.append('path', data.value.path)
  // 替换原始文件
  rawFile = customFile
  // 可选：校验文件大小（示例为10MB限制）
  // if (rawFile.size  / 1024 / 1024 > 10) {
  //   message.error(' 文件大小不能超过10MB！')
  //   return false
  // }
  return true
}

watch(
  () => data.value.fileName,
  (newVal) => {
    if (!newVal) {
      uploadDisable.value = true
    } else {
      uploadDisable.value = false
    }
  },
  { immediate: true }
)
// 添加回调函数引用
const successCallback = ref<(() => void) | null>(null)
</script>

<style lang="scss" scoped>
.file_name_box {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}
</style>
