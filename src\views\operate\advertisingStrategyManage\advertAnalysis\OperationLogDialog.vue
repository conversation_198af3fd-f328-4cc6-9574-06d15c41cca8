<template>
  <Dialog v-model="dialogVisible" title="操作日志" width="70%">
    <div class="mb-4">
      <el-tabs v-model="queryParams.field" @tab-change="handleQuery">
        <el-tab-pane label="关键词" name="SP关键词" />
        <el-tab-pane label="广告活动" name="SP广告活动" />
      </el-tabs>

      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="操作时间" prop="operationTime">
          <el-date-picker
            v-model="queryParams.operationTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-1" />查询
          </el-button>
          <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-1" />重置 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="list" border style="width: 100%">
      <el-table-column prop="operationTime" label="操作时间" width="150" />
      <el-table-column prop="userName" label="操作人" width="100" />
      <el-table-column prop="fieldDesc" label="对象详情" width="240" show-overflow-tooltip />
      <el-table-column label="广告活动" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.extra?.adCampaign || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="广告组" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.extra?.adGroup || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="操作类型" width="80" />
      <el-table-column prop="before" label="操作前的数据" show-overflow-tooltip width="100" />
      <el-table-column prop="after" label="操作后的数据" show-overflow-tooltip width="100" />
      <el-table-column prop="source" label="操作来源" width="80" />
      <el-table-column prop="bizType" label="操作日志类型" width="120" />
      <el-table-column label="是否成功" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.errorMsg ? 'danger' : 'success'">
            {{ scope.row.errorMsg ? '失败' : '成功' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-show="total > 0"
      v-model:current-page="queryParams.pageNo"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="getList"
      @current-change="getList"
      class="mt-4"
    />
  </Dialog>
</template>

<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import { useMessage } from '@/hooks/web/useMessage'
import { reactive, ref } from 'vue'

defineOptions({ name: 'OperationLogDialog' })

const message = useMessage()
const dialogVisible = ref(false)
const loading = ref(false)
const list = ref([])
const total = ref(0)
const currentId = ref<number>()

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  field: 'SP关键词',
  operationTime: [
    new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000)
      .toISOString()
      .slice(0, 19)
      .replace('T', ' '),
    new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
      .toISOString()
      .slice(0, 19)
      .replace('T', ' ')
  ] //默认15天+明天，年-月-日-时-分-秒
})

// 打开弹窗
const open = (id: number) => {
  console.log(id)
  currentId.value = id
  dialogVisible.value = true
  resetQuery()
  getList()
}

// 查询列表
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      id: currentId.value
    }
    const data = await AutomationAdvertisementAnalysisApi.getPageLog(params)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    message.error('获取操作日志失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.field = 'SP关键词'
  queryParams.operationTime = [
    new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000)
      .toISOString()
      .slice(0, 19)
      .replace('T', ' '),
    new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
      .toISOString()
      .slice(0, 19)
      .replace('T', ' ')
  ]
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.el-pagination {
  text-align: center;
}
</style>
