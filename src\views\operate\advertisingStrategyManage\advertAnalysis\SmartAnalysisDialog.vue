<template>
  <el-dialog
    v-model="dialogVisible"
    title="智能广告分析"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" inline label-width="80px">
      <!-- 账号店铺选择器 -->
      <AccountShopSelector
        ref="createAccountRef"
        v-model:account-values="form.uids"
        v-model:shop-values="form.sids"
        v-model:country-values="form.countrysList"
        v-model:country-names="form.countrys"
        @account-change="handleAccountChange"
        @shop-change="handleShopChange"
        @country-change="handleCountryChange"
      />
      <!-- 广告策略 -->
      <el-form-item label="广告策略选择" prop="strategyId" label-width="100px">
        <el-select v-model="form.strategyId" class="!w-150px" clearable placeholder="请选择类型">
          <el-option
            v-for="type in adTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>

      <!-- 优化项 -->
      <!-- <el-form-item label="优化项" prop="optimizationItems">
        <el-select
          v-model="form.optimizationItems"
          class="!w-150px"
          clearable
          placeholder="请选择优化项"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_ITEM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <!-- 广告组合 -->
      <el-form-item label="广告组合" prop="portfolioIds" label-width="80px">
        <template #label>
          <span class="flex items-center">
            广告组合
            <ElTooltip
              content="请输入需要显示得广告组合，不填默认取账号下所有广告组合"
              placement="bottom"
            >
              <Icon icon="ep:warning" :size="12" />
            </ElTooltip>
          </span>
        </template>
        <RcInputSelect
          style="min-width: 170px"
          v-model="form.portfolioIds"
          :options="adPortfolioOptions"
          @change="handlePortfolioChange"
        />
      </el-form-item>

      <!-- 广告状态 -->
      <el-form-item label="广告状态" prop="state" label-width="80px">
        <el-select v-model="form.state" class="!w-150px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_ADVERTING_STATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 分析时间的维度 -->
      <!-- <el-form-item label="分析时间的维度" label-width="110px">
        <el-radio-group v-model="form.analysisTimeDimension">
          <el-radio label="1天">1天</el-radio>
          <el-radio label="7天">7天</el-radio>
          <el-radio label="自定义">自定义</el-radio>
        </el-radio-group>
      </el-form-item> -->

      <el-form-item label="分析时间跨度" prop="analysisTimeRange" label-width="110px">
        <el-radio-group
          v-model="form.analysisTimeRange"
          @change="form.customDays = ''"
          style="width: 390px"
        >
          <template
            v-for="(item, index) in getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY)"
            :key="index"
          >
            <!-- 这里默认自定义不超过30天，最后一项就是30，要改自定义天数限制范围的话去字典改最后一项 -->
            <el-radio
              v-if="index != getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY).length - 1"
              :label="item.label"
              >近{{ item.label }}天</el-radio
            >
          </template>
          <el-radio :label="0"
            >自定义近
            <el-input
              :disabled="form.analysisTimeRange != 0"
              v-model.number="form.customDays"
              class="!w-130px"
              clearable
              min="1"
              :max="
                getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY)[
                  getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY).length - 1
                ].label
              "
              @input="dayInput"
              placeholder="天数"
              type="number"
            >
              <template #append>
                <span>天</span>
              </template>
            </el-input>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 上传策略文件 -->
      <el-form-item label=" " label-width="auto">
        <el-button
          v-hasPermi="['operation:advertisement-analysis:create']"
          type="primary"
          plain
          @click="openForm"
        >
          <Icon icon="ep:upload" class="mr-5px" /> 上传策略文件
        </el-button>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="startAnalysis" :loading="analyzing"> 开始分析 </el-button>
      </div>
    </template>
    <!-- 表单弹窗：上传文件 -->
    <FileForm ref="formRef" @success="getAdStrategyList" />
  </el-dialog>
</template>

<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import AccountShopSelector from '@/components/AccountShopSelector/index.vue'
import RcInputSelect from '@/components/RcInputSelect/index.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ElTooltip } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'

import FileForm from './FileForm.vue'
const message = useMessage()
const emit = defineEmits(['analysis-complete'])

const dialogVisible = ref(false)
const analyzing = ref(false)
const createAccountRef = ref()

// 表单数据 - 与ddd.vue中的createForm保持一致
const form = reactive({
  uids: [],
  sids: [],
  countrysList: [],
  countrys: [],
  analysisTimeRange: '1',
  customDays: '',
  strategyId: '',
  optimizationItems: '',
  portfolioIds: [],
  state: '0'
})
const formRef = ref()
// 选项数据
const adTypes = ref([])
const adPortfolioOptions = ref([])

// 监听账号变化
const handleAccountChange = (uids) => {
  // 重置相关数据
  form.portfolioIds = []
  // 可以在这里获取广告组合选项
  getAdPortfolioOptions()
}

// 监听店铺变化
const handleShopChange = (sids) => {
  // 获取广告组合列表
  getAdPortfolioOptions()
}

// 监听国家变化
const handleCountryChange = (countries) => {
  // 处理国家变化
}

// 监听广告组合变化
const handlePortfolioChange = () => {
  // 处理广告组合变化
}

// 获取广告策略列表
const getAdStrategyList = async () => {
  try {
    const res = await AutomationAdvertisementAnalysisApi.getAutomationAdvertisementStrategy()
    // 倒序，方便选中上传后的文件
    res.reverse()
    adTypes.value = res.map((item) => ({
      value: item.id,
      label: item.name || item.strategyName
    }))

    // 则默认选中第一个
    if (adTypes.value && adTypes.value.length > 0) {
      form.strategyId = adTypes.value[0].value
    }
  } catch (error) {
    console.error('获取广告策略列表失败', error)
    // message.error('获取广告策略列表失败')
  }
}

// 获取广告组合选项
const getAdPortfolioOptions = async () => {
  try {
    // 这里应该调用实际的API获取广告组合
    // const response = await getAdPortfolioOptionsApi()
    // adPortfolioOptions.value = response.data
  } catch (error) {
    console.error('获取广告组合选项失败:', error)
  }
}

// 修改 openForm 方法，添加回调
const openForm = () => {
  formRef.value.open(null, () => {
    // 上传完成后刷新广告策略列表
    getAdStrategyList()
  })
}
/**
 * 自定义天数输入处理函数
 * @param value - 输入的天数值
 * @returns 处理后的天数值
 */
const dayInput = (value) => {
  // 如果输入值小于等于0,设为1
  if (value <= 0) {
    form.customDays = 1
  }

  // 获取字典中允许的最大天数
  const maxDays = getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY)[
    getIntDictOptions(DICT_TYPE.AIM_SEARRCH_DAY).length - 1
  ].label

  // 如果超过最大天数限制,设为最大值
  if (form.customDays > maxDays) {
    form.customDays = maxDays
  }

  return form.customDays
}

// 开始分析 - 与ddd.vue中的handleAnalysis保持一致
const startAnalysis = async () => {
  try {
    // 表单验证
    if (form.uids.length == 0) {
      message.warning('请选择领星账号')
      return
    }
    if (!form.strategyId) {
      message.warning('请选择广告策略')
      return
    }
    if (form.state == '') {
      message.warning('请选择广告状态')
      return
    }

    let data = JSON.parse(JSON.stringify(form))
    if (form.customDays != '') {
      data.analysisTimeRange = form.customDays
    }

    await message.confirm('确定要进行智能广告分析吗？这可能需要一些时间。')
    analyzing.value = true

    // 调用分析API
    await AutomationAdvertisementAnalysisApi.intelligentAnalysis(data)

    message.success('分析已开始，请稍候查看结果')
    dialogVisible.value = false

    // 通知父组件刷新数据
    emit('analysis-complete', data)
  } catch (error) {
    console.error('分析失败', error)
  } finally {
    analyzing.value = false
  }
}

// 打开弹窗
const open = (queryParams = {}) => {
  // Reset form to default state before applying new queryParams
  resetForm()

  // Use nextTick to ensure the component is ready before updating
  nextTick(() => {
    // Synchronize query parameters to the form
    if (queryParams.uids) form.uids = [...queryParams.uids]
    if (queryParams.sids) form.sids = [...queryParams.sids]
    if (queryParams.countrysList) form.countrysList = [...queryParams.countrysList]
    if (queryParams.countrys) form.countrys = [...queryParams.countrys]
    if (queryParams.strategyId) form.strategyId = queryParams.strategyId
    if (queryParams.optimizationItems) form.optimizationItems = queryParams.optimizationItems
    if (queryParams.portfolioIds) form.portfolioIds = [...queryParams.portfolioIds]
    if (queryParams.state) form.state = queryParams.state
    if (queryParams.analysisTimeRange) form.analysisTimeRange = queryParams.analysisTimeRange
    if (queryParams.customDays) form.customDays = queryParams.customDays

    // Get ad strategy list
    getAdStrategyList()

    dialogVisible.value = true
  })
}

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.assign(form, {
    uids: [],
    sids: [],
    countrysList: [],
    countrys: [],
    analysisTimeRange: '1',
    customDays: '',
    strategyId: '',
    optimizationItems: '',
    portfolioIds: [],
    state: '0'
  })
}

// 监听弹窗关闭，重置表单
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-upload__tip) {
  margin-top: 5px;
  font-size: 12px;
  color: #999;
}
</style>
