<template>
  <!-- 列表 -->
  <ContentWrap title="待优化广告列表" message="列表数据只显示当前账号的分析结果">
    <el-tabs v-model="tabsName" type="card" class="demo-tabs" @tab-click="clickTabs">
      <el-tab-pane label="关键词" name="first" />
    </el-tabs>
    <div class="optimization-options mb-4 flex items-center">
      <span class="mr-2 font-size-15px">快速操作：</span>
      <el-button
        v-hasPermi="['operation:advertisement-analysis:query']"
        v-for="option in optimizationOptions"
        :key="option.name"
        :type="tabsNames === option.name ? 'primary' : ''"
        size="small"
        @click="toggleOption(option.name)"
      >
        {{ option.label }}({{ option.count }}个)
      </el-button>
    </div>

    <el-form
      class="-mb-15px -mr-25px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <AccountShopSelector
        ref="queryAccountRef"
        :isDefault="false"
        style="display: inline-flex; vertical-align: middle"
        v-model:account-values="queryParams.uids"
        v-model:shop-values="queryParams.sids"
        v-model:country-values="queryParams.countrysList"
        v-model:country-names="queryParams.countrys"
        @account-change="handleAccountChange"
        @shop-change="
          () => {
            getAdPortfolioList()
            handleQuery()
          }
        "
      />
      <el-form-item label="广告组合" prop="adPortfolio" label-width="80px">
        <el-select
          v-model="queryParams.adPortfolio"
          placeholder="请选择广告组合"
          style="width: 150px"
          clearable
          multiple
          @change="handleQuery"
        >
          <el-option
            v-for="item in adPortfolioOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="广告活动" prop="adCampaign" label-width="80px">
        <TipInput
          v-model="queryParams.adCampaign"
          inputClass="!w-150px"
          placeholder="广告活动"
          tooltipIcon="fa-solid:bars"
          @enter="(val) => (queryParams.adCampaignList = val)"
        />
      </el-form-item>

      <!-- 第二行筛选条件 -->
      <el-form-item label="广告组" prop="adGroup">
        <TipInput
          v-model="queryParams.adGroup"
          inputClass="!w-150px"
          placeholder="广告组"
          tooltipIcon="fa-solid:bars"
          @enter="(val) => (queryParams.adGroupList = val)"
        />
      </el-form-item>

      <el-form-item label="">
        <el-select
          v-model="queryParams.searchType"
          class="!w-[100px] mr-1"
          placeholder="查询方式"
          @change="handleQuery"
        >
          <el-option label="模糊" value="fuzzy" />
          <el-option label="精准" value="exact" />
        </el-select>
        <el-input
          v-model="queryParams.searchKey"
          class="flex-1"
          clearable
          style="width: 120px"
          placeholder="关键词"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="匹配方式" prop="matchingMethod">
        <el-select
          v-model="queryParams.matchingMethod"
          class="!w-110px"
          clearable
          placeholder="全部"
          @change="handleQuery"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_MATCH_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          />
        </el-select>
      </el-form-item>

      <!-- 第三行筛选条件 -->
      <el-form-item label="操作状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-110px"
          clearable
          placeholder="全部"
          @change="handleQuery"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分析时间" prop="createTime">
        <shortcut-date-range-picker v-model="queryParams.analysisTime" />
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          type="primary"
          @click="handleQuery"
        >
          <Icon icon="ep:search" class="mr-1" />查询
        </el-button>
        <el-button @click="resetQuery" v-hasPermi="['operation:advertisement-analysis:query']">
          <Icon icon="ep:refresh" class="mr-1" />重置
        </el-button>
        <QueryConditionTemplate
          :type="'advertAnalysis'"
          :form-data="queryParams"
          @update:form-data="onUpdateFormData"
        />
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          type="primary"
          @click="openBudgetBiddingDialog"
        >
          <Icon icon="ep:money" class="mr-1" />预算与竞价区间管理
        </el-button>
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          type="primary"
          @click="openSmartAnalysisDialog"
        >
          <Icon icon="ep:data-analysis" class="mr-1" />智能广告分析
        </el-button>
      </el-form-item>
    </el-form>
    <el-progress
      v-if="getIsOpen"
      :percentage="percentage"
      :stroke-width="10"
      :indeterminate="true"
    />
    <!-- 表格 -->
    <Table
      style="margin-top: 20px"
      ref="tableRef"
      :loading="loading"
      :data="list"
      :columns="tableColumns"
      selection
      border
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @selection-change="handleSelectionChange"
      @update:page-size="
        (val) => {
          queryParams.pageSize = val
          getList()
        }
      "
      @update:current-page="
        (val) => {
          queryParams.pageNo = val
          getList()
        }
      "
      @sort-change="handleSortChange"
      isSort
    >
      <template #top-btn>
        <el-button
          type="primary"
          @click="openApplicationOptimizationDialog"
          :disabled="multipleSelection.length === 0"
        >
          <Icon icon="ep:setting" class="mr-1" />
          执行优化建议
        </el-button>
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          type="success"
          plain
          @click="handleExp"
        >
          <Icon icon="ep:download" />导出选中
        </el-button>
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          type="success"
          plain
          @click="handleExps"
        >
          <Icon icon="ep:download" />导出全部
        </el-button>
      </template>
      <!-- 说明文字 -->
      <template #top-btn-right>
        <el-popover class="box-item" title="" placement="left-start" width="500px">
          <template #reference>
            <Icon icon="ep:warning" />
          </template>
          <div
            v-html="
              `
<b>关于库存数据更新说明</b></br>
• 更新频次：每日3次定时更新（07:00/12:00/18:00）</br>
• 数据延迟：每次更新后30分钟内完成全量数据同步</br>
</br>
<b>关于查询数据说明</b></br>
• 本列表显示最新分析结果</br>
• 选择时间范围后，将展示该时段内最后一次分析数据</br>
• 无选择时默认显示最近一次分析结果</br>
`
            "
          >
          </div>
        </el-popover>
      </template>
      <!-- 满足规则 -->
      <template #satisfactionRules="scope">
        <div v-if="scope.row.satisfactionRules" class="rule-container">
          <!-- effect="light" -->
          <el-tooltip
            placement="top"
            :content="formatRulesTooltip(scope.row.satisfactionRules)"
            :show-after="200"
            raw-content
          >
            <div class="rule-grid">
              <template
                v-for="(value, key, index) in parseRules(scope.row.satisfactionRules)"
                :key="key"
              >
                <div class="rule-item" v-if="index < 2">
                  <span class="rule-key">{{ key }}:</span>
                  <span class="text-blue-500 rule-value">{{ value }}</span>
                </div>
                <!-- <div
                    class="flex"
                    v-if="index == 2"
                  >查看更多……</div> -->
              </template>
            </div>
          </el-tooltip>
        </div>
      </template>
      <!-- 优化建议 -->
      <template #optimizationSuggestion="scope">
        <div v-if="scope.row.budgetOperation" style="font-weight: bold">
          <span
            :class="{
              'text-green-500': scope.row.budgetOperation.includes('加'),
              'text-red-500': scope.row.budgetOperation.includes('减')
            }"
          >
            {{ scope.row.budgetOperation }}
          </span>
        </div>
        <div v-if="scope.row.cpcOperation" style="font-weight: bold">
          <span
            :class="{
              'text-green-500': scope.row.cpcOperation.includes('加'),
              'text-red-500': scope.row.cpcOperation.includes('减')
            }"
          >
            {{ scope.row.cpcOperation }}
          </span>
        </div>
      </template>
      <!-- 总库存 -->
      <template #totalInventory="scope">
        <div class="flex items-center gap-4px w-100%">
          <div>{{ scope.row.totalInventory }}</div>
          <el-popover
            placement="right"
            :width="240"
            trigger="hover"
            v-if="scope.row.totalInventory"
          >
            <template #reference>
              <Icon :size="12" icon="ep:arrow-down" class="cursor-pointer" />
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>采购计划</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购计划单待采购量<br />
                        统计数据：采购计划（待采购）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchasePlan }}
              </el-descriptions-item>
              <el-descriptions-item class="relative">
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA库存</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 待调仓 + 调仓中 + 可售 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                <div class="flex items-center gap-4px">
                  <span>{{ scope.row.inventorys[0]?.fbaInventory }}</span>
                  <el-popover
                    placement="right"
                    :width="240"
                    trigger="hover"
                    v-if="scope.row.inventorys[0]?.fba"
                  >
                    <template #reference>
                      <Icon :size="12" icon="ep:arrow-right" class="cursor-pointer" />
                    </template>
                    <el-descriptions :column="1" border>
                      <el-descriptions-item label="FBA可售-可售">
                        {{ scope.row.inventorys[0]?.fba[0]?.afnFulfillableQuantity }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-待调仓">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcTransfers }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-调仓中">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcProcessing }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-popover>
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        shipments申报量-签收量，仅统计6个月内创建且系统货件状态为"进行中"的货件
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>预计发货量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        统计以下单据：<br />
                        ①发货计划：未关联货件且未关联发货单，且状态为待审批、待处理状态的发货计划<br />
                        ②发货单：未关联货件，且状态为待审批、待配货、待发货状态的发货单
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShippingPlan }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        海外仓备货单待收货量+目的仓为海外仓的调拨单待收货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓可用/本地可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待检待上架量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的待检待上架量（汇总SKU无绑定FNSKU数量与SKU+FNSKU数量）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalQc }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待交付</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购单的待交付量，SKU为组合产品则额外包含期望待到货量<br />
                        统计数据：采购单(待提交) + 采购单(待审批) + 采购单(待下单) + 采购单(待到货)
                        + 采购单(已驳回) + 委外订单(待提交) + 委外订单(待审批) + 委外订单(待下单) +
                        委外订单(待到货) + 期望待到货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchaseShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 目的仓为本地仓的调拨单待收货量 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalShipping }}
              </el-descriptions-item>
            </el-descriptions>
          </el-popover>
        </div>
      </template>
      <!-- 当前预算 -->
      <template #dailyBudget="scope">
        <div class="flex items-center gap-4px w-100%">
          <el-tooltip
            placement="right"
            :disabled="!shouldShowBudgetTooltip(scope.row)"
            :show-after="100"
            :hide-after="50"
          >
            <template #content>
              <div v-html="getBudgetTooltipContent(scope.row)"></div>
            </template>
            <div
              class="editable-input-container"
              @mouseenter="handleBudgetMouseEnter(scope.row)"
              @mouseleave="handleBudgetMouseLeave(scope.row)"
            >
              <el-input
                v-model="scope.row.adjustBudget"
                size="small"
                :min="scope.row.minBudget"
                :max="scope.row.maxBudget"
                style="width: 100px"
                @keyup.enter="updateBudget(scope.row)"
              >
                <template #prefix>
                  <span class="input-prefix">{{ scope.row.currency || '$' }}</span>
                </template>
                <template #suffix>
                  <Icon
                    v-if="showBudgetIcon == scope.row.id"
                    :icon="budgetUpdateLoading.get(scope.row.id) ? 'ep:loading' : 'ep:check'"
                    :class="
                      budgetUpdateLoading.get(scope.row.id)
                        ? 'cursor-not-allowed text-blue-500 animate-spin'
                        : 'cursor-pointer text-green-500 hover:text-green-600'
                    "
                    @click="!budgetUpdateLoading.get(scope.row.id) && updateBudget(scope.row)"
                  />
                </template>
              </el-input>
            </div>
          </el-tooltip>
        </div>
      </template>
      <!-- 当前竞价 -->
      <template #bid="scope">
        <div class="flex items-center gap-4px w-100%">
          <el-tooltip
            placement="right"
            :disabled="!shouldShowBidTooltip(scope.row)"
            :show-after="100"
            :hide-after="50"
          >
            <template #content>
              <div v-html="getBidTooltipContent(scope.row)"></div>
            </template>
            <div
              class="editable-input-container"
              @mouseenter="handleBidMouseEnter(scope.row)"
              @mouseleave="handleBidMouseLeave(scope.row)"
            >
              <el-input
                v-model="scope.row.adjustBid"
                size="small"
                :min="scope.row.minBidding"
                :max="scope.row.maxBidding"
                style="width: 100px"
                @keyup.enter="updateBid(scope.row)"
              >
                <template #prefix>
                  <span class="input-prefix">{{ scope.row.currency || '$' }}</span>
                </template>
                <template #suffix>
                  <Icon
                    v-if="showBidIcon == scope.row.id"
                    :icon="bidUpdateLoading.get(scope.row.id) ? 'ep:loading' : 'ep:check'"
                    :class="
                      bidUpdateLoading.get(scope.row.id)
                        ? 'cursor-not-allowed text-blue-500 animate-spin'
                        : 'cursor-pointer text-green-500 hover:text-green-600'
                    "
                    @click="!bidUpdateLoading.get(scope.row.id) && updateBid(scope.row)"
                  />
                </template>
              </el-input>
            </div>
          </el-tooltip>
        </div>
      </template>
      // 国家/店铺
      <template #countryShop="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.country" class="w-100%" truncated>{{ row.country }}</el-text>
          <el-text v-clipboard="row.shopName" class="w-100%" truncated>{{ row.shopName }}</el-text>
        </div>
      </template>
      <!-- 操作 -->
      <template #action="scope">
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          link
          type="primary"
          @click="openLogForm(scope.row.keyword, scope.row.id)"
        >
          历史诊断
        </el-button>
        <el-button
          v-hasPermi="['operation:advertisement-analysis:query']"
          link
          type="primary"
          @click="openOperationLogDialog(scope.row)"
        >
          操作日志
        </el-button>
      </template>
    </Table>
    <!-- 分页已集成到Table组件中 -->
  </ContentWrap>

  <!-- 表单弹窗：诊断历史 -->
  <logListForm ref="logFormRef" />

  <!-- 智能广告分析弹窗 -->
  <SmartAnalysisDialog ref="smartAnalysisDialogRef" @analysis-complete="handleAnalysisComplete" />

  <!-- 应用优化建议弹窗 -->
  <ApplicationOptimizationDialog
    ref="applicationOptimizationDialogRef"
    @optimization-complete="handleOptimizationComplete"
  />

  <!-- 预算与竞价区间管理弹窗 -->
  <BudgetBiddingDialog ref="budgetBiddingDialogRef" />

  <!-- 执行结果弹窗 -->
  <ExecutionResultDialog
    v-model="resultDialogVisible"
    :result="executionResult"
    @closed="executionResult = {}"
  />

  <!-- 操作日志弹窗 -->
  <OperationLogDialog ref="operationLogDialogRef" />
</template>

<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import AccountShopSelector from '@/components/AccountShopSelector/index.vue'
import QueryConditionTemplate from '@/components/QueryConditionTemplate/index.vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import type { TableColumn } from '@/types/table'
import { getRefreshToken } from '@/utils/auth'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import { dateFormatter, formatDate } from '@/utils/formatTime'
import { useWebSocket } from '@vueuse/core'
import { ElButton, ElTooltip, TabsPaneContext } from 'element-plus'
import { computed, onMounted, reactive, ref, watchEffect } from 'vue'

import ApplicationOptimizationDialog from './ApplicationOptimizationDialog.vue'
import BudgetBiddingDialog from './BudgetBiddingDialog.vue'
import logListForm from './logListForm.vue'
import OperationLogDialog from './OperationLogDialog.vue'
import SmartAnalysisDialog from './SmartAnalysisDialog.vue'

defineOptions({ name: 'AdvertAnalysis' })

const onUpdateFormData = (newVal) => {
  // 使用 Object.assign 来更新 queryParams 的属性
  // 同时保留响应性
  Object.keys(queryParams).forEach((key) => {
    // 如果是数组，则清空
    if (Array.isArray(queryParams[key])) {
      queryParams[key] = []
    } else {
      // 否则，设置为 undefined 或 null
      queryParams[key] = undefined
    }
  })
  Object.assign(queryParams, newVal)
  handleQuery()
}

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  {
    field: 'imgUrl',
    type: 'image',
    label: '图片',
    fixed: 'left',
    width: 80
  },
  {
    field: 'sku',
    label: 'SKU',
    fixed: 'left',
    width: 280
  },
  {
    field: 'keyword',
    label: '关键词',
    fixed: 'left',
    width: 150
  },
  {
    field: 'adGroup',
    label: '广告组',
    fixed: 'left',
    isCopy: true,
    width: 220
  },
  {
    field: 'analysisTime',
    label: '分析时间',
    width: 150,
    formatter: (row) => dateFormatter(row.analysisTime)
  },
  {
    field: 'satisfactionRules',
    label: '满足规则',
    showOverflowTooltip: true,
    width: 230
  },
  {
    field: 'optimizationSuggestion',
    label: '优化建议',
    width: 90
  },
  {
    field: 'totalInventory',
    label: '总库存',
    align: 'center',
    width: 90,
    icon: 'ep:warning',
    tips: '总库存 = 本地可用 + 待检待上架量 + 待交付 + 本地仓在途 + 采购计划 + 海外仓可用 + 海外仓在途 + FBA可售 + FBA在途 + 预计发货量'
  },
  {
    field: 'dailyBudget',
    label: '当前预算',
    align: 'center',
    width: 120,
    slotName: 'dailyBudget',
    icon: 'ep:warning',
    tips: '当前预算（每日预算）：领星每天设置的支出上限，控制当天实际花费不超过该金额。'
  },
  {
    field: 'bid',
    label: '当前竞价',
    align: 'center',
    width: 120,
    slotName: 'bid',
    icon: 'ep:warning',
    tips: '当前竞价：关键词的实时出价，即你为每次点击设置的最高费用（实际扣费可能≤此价格）。'
  },
  {
    field: 'countryShop',
    label: '国家/店铺',
    align: 'left',
    fixed: 'left',
    slotName: 'countryShop',
    width: 150,
    showOverflowTooltip: true
  },
  {
    field: 'adType',
    label: '类型',
    width: 80
  },
  {
    field: 'adPortfolio',
    label: '广告组合',
    isCopy: true,
    width: 100
  },
  {
    field: 'adCampaign',
    label: '广告活动',
    align: 'left',
    isCopy: true,
    width: 265
  },
  {
    field: 'analysisTimeRange',
    label: '分析时间跨度',
    width: 160
  },
  {
    field: 'matchingMethod',
    label: '匹配方式',
    width: 90
  },
  {
    field: 'orders',
    label: '订单量',
    width: 70
  },
  {
    field: 'impressions',
    label: '曝光量',
    width: 70
  },
  {
    field: 'clicks',
    label: '点击',
    width: 70
  },
  {
    field: 'ctr',
    label: 'CTR',
    width: 70
  },
  {
    field: 'cpcLocalCurrency',
    label: 'CPC',
    width: 100
  },
  {
    field: 'spendLocalCurrency',
    label: '花费',
    width: 100
  },
  {
    field: 'acos',
    label: 'ACoS',
    width: 130
  },
  {
    field: 'cvr',
    label: 'CVR',
    width: 100
  },
  {
    field: 'budget',
    label: '预算',
    width: 70,
    icon: 'ep:warning',
    tips: '预算（有效天数总预算）：在选定分析时间跨度时间段内，仅累加有数据记录的每日预算（如7天中4天有数据，则总预算=这4天的预算之和）。'
  },
  {
    field: 'budgetUtilizationRate',
    label: '预算使用率',
    width: 100
  },
  {
    field: 'uname',
    label: '领星账号',
    width: 100
  },
  {
    field: 'operator',
    label: '操作人',
    width: 100
  },
  {
    field: 'action',
    label: '操作',
    width: 200,
    fixed: 'right'
  }
])

// 默认时间范围：今天 00:00:00 - 23:59:59
const defaultTimeRange = computed(() => {
  const now = new Date()
  const todayStart = new Date(now)
  todayStart.setHours(0, 0, 0, 0)

  const todayEnd = new Date(now)
  todayEnd.setHours(23, 59, 59, 999)
  return [formatDate(todayStart), formatDate(todayEnd)]
})

const getIsOpen = ref(false) // WebSocket 连接是否打开
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 字典表格数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  uids: [],
  sids: [],
  countrys: [],
  countrysList: [],
  keyword: '',
  keywords: '',
  shopName: '',
  lingxingAccount: '',
  adType: '',
  adPortfolio: '',
  adPortfolioList: [],
  adCampaign: '',
  adCampaignList: [],
  adGroup: '',
  adGroupList: [],
  customDays: '',
  matchingMethod: '',
  optimizationSuggestion: '',
  searchType: 'fuzzy',
  searchKey: '',
  budgetOperation: '',
  cpcOperation: '',
  analysisTime: defaultTimeRange.value,
  sortingFields: [],
  status: '0' //操作状态参数
})

const isCollapse = ref(true) // 控制搜索区域的折叠状态
const percentage = ref(0) // 分析的进度条
const showBudgetIcon = ref(null) // 控制预算编辑图标显示
const showBidIcon = ref(null) // 控制竞价编辑图标显示

// 按钮loading状态
const budgetUpdateLoading = ref(new Map()) // 预算更新loading状态
const bidUpdateLoading = ref(new Map()) // 竞价更新loading状态

// 防抖相关
const budgetDebounceMap = ref(new Map())
const bidDebounceMap = ref(new Map())

// tooltip内容缓存
const budgetTooltipCache = ref(new Map())
const bidTooltipCache = ref(new Map())

// tooltip loading状态
const budgetTooltipLoading = ref(new Map())
const bidTooltipLoading = ref(new Map())

// tooltip请求状态标记，防止重复请求
const budgetRequestingSet = ref(new Set())
const bidRequestingSet = ref(new Set())

// tooltip延迟定时器
const budgetTooltipDelayMap = ref(new Map())
const bidTooltipDelayMap = ref(new Map())

// 执行结果弹窗相关
const resultDialogVisible = ref(false)
const executionResult = ref({})

const optimizationOptions = ref([
  { name: 'all', label: '全部建议', count: 0 },
  { name: 'ysadd', label: '预算加', count: 0 },
  { name: 'ysadds', label: '预算减', count: 0 },
  { name: 'cpcadd', label: 'CPC加', count: 0 },
  { name: 'cpcadds', label: 'CPC减', count: 0 }
])

const queryFormRef = ref() // 搜索的表单
const smartAnalysisDialogRef = ref() // 智能广告分析弹窗
const applicationOptimizationDialogRef = ref() // 应用优化建议弹窗
const budgetBiddingDialogRef = ref() // 预算与竞价区间管理弹窗
const operationLogDialogRef = ref() // 操作日志弹窗
const multipleSelection = ref<{ id: number }[]>([]) // 选中的列表项

// 广告组合选项数据
const adPortfolioOptions = ref([])

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    // 清除所有tooltip缓存和请求状态，确保重新获取最新数据
    budgetTooltipCache.value.clear()
    bidTooltipCache.value.clear()
    budgetTooltipLoading.value.clear()
    bidTooltipLoading.value.clear()
    budgetRequestingSet.value.clear()
    bidRequestingSet.value.clear()

    // 清除所有延迟定时器
    budgetTooltipDelayMap.value.forEach((timer) => clearTimeout(timer))
    bidTooltipDelayMap.value.forEach((timer) => clearTimeout(timer))
    budgetTooltipDelayMap.value.clear()
    bidTooltipDelayMap.value.clear()

    let qData = JSON.parse(JSON.stringify(queryParams))
    const data =
      await AutomationAdvertisementAnalysisApi.getAutomationAdvertisementAnalysisPage(qData)
    // 获取统计值
    const statistics = await AutomationAdvertisementAnalysisApi.getStatistical(qData)

    // 处理统计数据
    optimizationOptions.value.forEach((option) => {
      switch (option.name) {
        case 'ysadd':
          option.count = statistics.budgetOperationAdd || 0
          break
        case 'ysadds':
          option.count = statistics.budgetOperationMinus || 0
          break
        case 'cpcadd':
          option.count = statistics.cpcOperationAdd || 0
          break
        case 'cpcadds':
          option.count = statistics.cpcOperationMinus || 0
          break
        case 'all':
          option.count = statistics.all || 0
          break
      }
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.keyword = ''
  queryParams.keywords = ''
  // 判断模糊还是精准，模糊的话传keyword，精准传keywords
  if (queryParams.searchType === 'fuzzy') {
    queryParams.keywords = queryParams.searchKey
  }
  if (queryParams.searchType === 'exact') {
    queryParams.keyword = queryParams.searchKey
  }
  queryParams.pageNo = 1
  getList()
}

// 打开诊断历史
const logFormRef = ref()
const openLogForm = (keyword: string, id: number) => {
  logFormRef.value.open(keyword, id)
}

// 打开智能广告分析弹窗
const openSmartAnalysisDialog = () => {
  smartAnalysisDialogRef.value.open(queryParams)
}

// 处理分析完成
const handleAnalysisComplete = (analysisData) => {
  // 同步分析时选择的查询条件到外层查询参数
  if (analysisData) {
    if (analysisData.uids && analysisData.uids.length > 0) {
      queryParams.uids = [...analysisData.uids]
    }
    if (analysisData.sids && analysisData.sids.length > 0) {
      queryParams.sids = [...analysisData.sids]
    }
    if (analysisData.countrysList && analysisData.countrysList.length > 0) {
      queryParams.countrysList = [...analysisData.countrysList]
    }
    if (analysisData.countrys && analysisData.countrys.length > 0) {
      queryParams.countrys = [...analysisData.countrys]
    }
    if (analysisData.portfolioIds && analysisData.portfolioIds.length > 0) {
      queryParams.adPortfolio = [...analysisData.portfolioIds]
    }
    // 获取广告组合列表以确保选项可用
    if (queryParams.sids.length > 0) {
      getAdPortfolioList()
    }
  }
  getIsOpen.value = true
  percentage.value = 0
  getList()
}

// 打开应用优化建议弹窗
const openApplicationOptimizationDialog = () => {
  if (multipleSelection.value.length === 0) {
    message.warning('请至少选择一项')
    return
  }
  applicationOptimizationDialogRef.value.open(multipleSelection.value)
}

// 处理优化完成事件
const handleOptimizationComplete = () => {
  // 刷新列表数据
  getList()
  // 清空选中项
  multipleSelection.value = []
}

// 打开预算与竞价区间管理弹窗
const openBudgetBiddingDialog = () => {
  budgetBiddingDialogRef.value.open()
}

// 获取广告组合列表
const getAdPortfolioList = async () => {
  try {
    if (queryParams.sids.length === 0) {
      adPortfolioOptions.value = []
      return
    }
    const params = {
      sids: queryParams.sids.join(',')
    }
    const response = await AutomationAdvertisementAnalysisApi.getAdPortfolio(params)
    adPortfolioOptions.value = response.data || []
  } catch (error) {
    console.error('获取广告组合列表失败:', error)
    adPortfolioOptions.value = []
  }
}

// 处理账号变化
const handleAccountChange = () => {
  // 清空广告组合选择
  queryParams.adPortfolio = []
  adPortfolioOptions.value = []
  // 获取新的广告组合列表
  if (queryParams.sids.length > 0) {
    getAdPortfolioList()
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    if (!id) {
      message.warning('请选择要忽略建议的项')
      return
    }
    // 忽略建议的二次确认
    await message.confirm('确定要忽略建议吗？')
    // 忽略建议
    await AutomationAdvertisementAnalysisApi.ignore(id)
    message.success('忽略建议成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.searchKey = ''
  queryParams.searchType = 'fuzzy'
  queryParams.keyword = ''
  queryParams.keywords = ''
  queryParams.uids = []
  queryParams.sids = []
  queryParams.countrysList = []
  queryParams.countrys = []
  queryParams.analysisTime = []
  queryParams.adCampaignList = []
  adPortfolioOptions.value = []
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.uids = []
  queryParams.sids = []
  queryParams.countrys = []
  queryParams.countrysList = []
  queryParams.keyword = ''
  queryParams.keywords = ''
  queryParams.shopName = ''
  queryParams.lingxingAccount = ''
  queryParams.adType = ''
  queryParams.adPortfolio = ''
  queryParams.adPortfolioList = []
  queryParams.adCampaign = ''
  queryParams.adCampaignList = []
  queryParams.adGroup = ''
  queryParams.adGroupList = []
  queryParams.customDays = ''
  queryParams.matchingMethod = ''
  queryParams.optimizationSuggestion = ''
  queryParams.searchType = 'fuzzy'
  queryParams.searchKey = ''
  queryParams.budgetOperation = ''
  queryParams.cpcOperation = ''
  queryParams.sortingFields = []
  queryParams.status = '0' //操作状态参数
  handleQuery()
}

/** 导出选中 */
const handleExp = async () => {
  try {
    if (multipleSelection.value.length === 0) {
      message.warning('请至少选择一项')
      return
    }
    await message.confirm('确定要导出选中数据吗？')
    // 获取选中项的 ID
    const ids: number[] = multipleSelection.value.map((item) => item.id)
    // 调用导出接口
    const data = await AutomationAdvertisementAnalysisApi.exportSelected(ids)
    download.excel(data, '广告策略分析.xls')
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败', error)
  }
}
/** 导出全部 */
const handleExps = async () => {
  try {
    await message.confirm('确定要导出全部数据吗？')
    // 调用导出接口
    let qData = JSON.parse(JSON.stringify(queryParams))
    delete qData.page
    delete qData.pageSize
    const data = await AutomationAdvertisementAnalysisApi.exportAll(qData)
    download.excel(data, '广告策略分析.xls')
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败', error)
  }
}

const tabsName = ref('first')
const tabsNames = ref('') // tabs选项

const clickTabs = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
// 字典
const OPTION_MAP = {
  ysadd: { budget: '预算加', CPC: '' },
  ysadds: { budget: '预算减', CPC: '' },
  cpcadd: { budget: '', CPC: 'CPC加' },
  cpcadds: { budget: '', CPC: 'CPC减' },
  all: { budgetAdd: '预算加', budgetSub: '预算减', cpcAdd: 'CPC加', cpcSub: 'CPC减' }
}
// 快速操作
const toggleOption = (name: string) => {
  tabsNames.value = tabsNames.value === name ? '' : name
  const option = OPTION_MAP[tabsNames.value] ?? { budget: '', CPC: '' }
  queryParams.budgetOperation = option.budget
  queryParams.cpcOperation = option.CPC
  if (tabsNames.value === 'all') {
    queryParams.budgetAdd = option.budgetAdd
    queryParams.budgetSub = option.budgetSub
    queryParams.cpcAdd = option.cpcAdd
    queryParams.cpcSub = option.cpcSub
  } else {
    queryParams.budgetAdd = ''
    queryParams.budgetSub = ''
    queryParams.cpcAdd = ''
    queryParams.cpcSub = ''
  }
  getList()
}

onMounted(() => {
  // 获取广告分析进度
  getProgressFc()
  setTimeout(() => {
    getList()
  }, 300)
})

// 解析规则数据
const parseRules = (rules) => {
  if (!rules) return {}
  try {
    return typeof rules === 'string' ? JSON.parse(rules) : rules
  } catch (e) {
    console.error('解析规则数据失败', e)
    return {}
  }
}

// 格式化规则为tooltip内容
const formatRulesTooltip = (rulesStr) => {
  try {
    const rules = parseRules(rulesStr)
    return Object.entries(rules)
      .map(
        ([key, value]) =>
          `<div class="rule-item"><span class="rule-key">${key}:</span><span class="rule-value" style="font-weight: bold;color: #d1d1d1">${value}</span></div>`
      )
      .join('\n')
  } catch (e) {
    return '无法解析规则'
  }
}

// 美化显示ai建议
const formatText = (text) => {
  return text
    .replace(/•/g, '  • ') // 美化项目符号
    .replace(/✅/g, '  ✅ ') // 美化勾选符号
}

// webscoket ---------------------------------------------------------

const server = ref(
  (import.meta.env.VITE_BASE_URL + '/operation/ws').replace('http', 'ws') +
    '?token=' +
    getRefreshToken() // 使用 getRefreshToken() 方法，而不使用 getAccessToken() 方法的原因：WebSocket 无法方便的刷新访问令牌
) // WebSocket 服务地址

/** 发起 WebSocket 连接 */
const { status, data, send, close, open } = useWebSocket(server.value, {
  autoReconnect: true,
  heartbeat: true
})

// 查询广告分析进度
const getProgressFc = async () => {
  const res = await AutomationAdvertisementAnalysisApi.getProgress()
  if (res.display) {
    getIsOpen.value = true
    percentage.value = res.content
  } else {
    getIsOpen.value = false
    percentage.value = 0
  }
}

/** 监听接收到的数据 */
watchEffect(() => {
  if (!data.value) {
    return
  }
  try {
    // 1. 收到心跳
    if (data.value === 'pong') {
      // console.log('心跳', data.value)
      console.log('心悄咪咪跳了一下')
      return
    }

    // 2.1 解析 type 消息类型
    const jsonMessage = JSON.parse(data.value)
    const type = jsonMessage.type
    if (!type) {
      console.warn('未知的消息类型：' + data.value)
      return
    }
    // 2.2 ad-analysis
    if (type === 'ad-analysis') {
      // console.log(jsonMessage)
      if (jsonMessage.content != 0 && jsonMessage.content != 100) {
        getIsOpen.value = true
      }
      percentage.value = jsonMessage.content
      if (jsonMessage.content == 100) {
        setTimeout(() => {
          getIsOpen.value = false
        }, 3000)
        message.success('分析完成')
      }
      return
    }
    console.warn('未处理消息：' + data.value)
  } catch (error) {
    console.error(error)
  }
})

// 验证预算范围
const validateBudget = (row) => {
  // 确保adjustBudget存在，如果是数字则转为字符串
  if (typeof row.adjustBudget === 'undefined') {
    row.adjustBudget = '0'
  } else if (typeof row.adjustBudget === 'number') {
    row.adjustBudget = row.adjustBudget.toString()
  }

  // 过滤非数字和小数点的字符，包括负号
  row.adjustBudget = row.adjustBudget.replace(/[^\d.]/g, '')

  // 处理空字符串情况
  if (row.adjustBudget === '') {
    row.adjustBudget = '0'
  }

  // 确保只有一个小数点
  const parts = row.adjustBudget.split('.')
  if (parts.length > 2) {
    row.adjustBudget = parts[0] + '.' + parts.slice(1).join('')
  }

  // 确保小数点后不超过两位
  if (parts[1] && parts[1].length > 2) {
    row.adjustBudget = parts[0] + '.' + parts[1].substring(0, 2)
  }

  // 转换最终值为数字类型
  const budget = Number(row.adjustBudget)
  const minBudget = Number(row.minBudget) || 0
  const maxBudget = Number(row.maxBudget) || 999999

  // 验证范围（确保为正数）
  if (isNaN(budget) || budget < 0) {
    row.adjustBudget = minBudget
    message.warning('请输入有效的正数预算金额')
    return false
  } else if (budget < minBudget) {
    row.adjustBudget = minBudget
    message.warning(`预算不能小于最小值 ${row.currency || '$'}${minBudget}`)
    return false
  } else if (budget > maxBudget) {
    row.adjustBudget = maxBudget
    message.warning(`预算不能大于最大值 ${row.currency || '$'}${maxBudget}`)
    return false
  }

  // 验证通过，将值存回row对象
  row.adjustBudget = budget
  return true
}

// 验证竞价范围
const validateBid = (row) => {
  // 确保adjustBid存在并转换为字符串
  if (typeof row.adjustBid === 'undefined') {
    row.adjustBid = '0'
  } else if (typeof row.adjustBid === 'number') {
    row.adjustBid = row.adjustBid.toString()
  }

  // 过滤非数字和小数点的字符，包括负号
  row.adjustBid = row.adjustBid.replace(/[^\d.]/g, '')

  // 处理空字符串情况
  if (row.adjustBid === '') {
    row.adjustBid = '0'
  }

  // 确保只有一个小数点
  const parts = row.adjustBid.split('.')
  if (parts.length > 2) {
    row.adjustBid = parts[0] + '.' + parts.slice(1).join('')
  }

  // 确保小数点后不超过两位
  if (parts[1] && parts[1].length > 2) {
    row.adjustBid = parts[0] + '.' + parts[1].substring(0, 2)
  }

  // 转换最终值为数字类型
  const adjustBid = Number(row.adjustBid)
  const minBidding = Number(row.minBidding) || 0
  const maxBidding = Number(row.maxBidding) || 999999

  // 验证范围（确保为正数）
  if (isNaN(adjustBid) || adjustBid < 0) {
    row.adjustBid = minBidding
    message.warning('请输入有效的正数竞价金额')
    return false
  } else if (adjustBid < minBidding) {
    row.adjustBid = minBidding
    message.warning(`竞价不能小于最小值 ${row.currency || '$'}${minBidding}`)
    return false
  } else if (adjustBid > maxBidding) {
    row.adjustBid = maxBidding
    message.warning(`竞价不能大于最大值 ${row.currency || '$'}${maxBidding}`)
    return false
  }

  // 验证通过，将值存回row对象
  row.adjustBid = adjustBid
  return true
}

// 防抖函数
const debounce = (func, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

// 处理预算鼠标进入事件
const handleBudgetMouseEnter = (row) => {
  showBudgetIcon.value = row.id

  const cacheKey = `${row.id}_budget`

  // 如果正在请求中，则不重复请求
  if (budgetRequestingSet.value.has(cacheKey)) {
    return
  }

  // 清除之前的延迟定时器
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(budgetTooltipDelayMap.value.get(cacheKey))
  }

  // 设置0.5秒延迟后再调用接口
  const delayTimer = setTimeout(() => {
    // 标记正在请求
    budgetRequestingSet.value.add(cacheKey)

    // 设置loading状态
    budgetTooltipLoading.value.set(cacheKey, true)

    // 生成预算范围信息（仅在有最小值或最大值时）
    let budgetRangeInfo = ''
    if (row.minBudget || row.maxBudget) {
      budgetRangeInfo = `预算范围: ${row.minBudget ? '最小' + (row.currency || '$') + row.minBudget + ' - ' : ''}${row.maxBudget ? '最大' + (row.currency || '$') + row.maxBudget : ''}`
      // 设置默认内容
      budgetTooltipCache.value.set(cacheKey, budgetRangeInfo)
    } else {
      // 如果没有范围信息，设置loading内容
      budgetTooltipCache.value.set(cacheKey, 'Loading...')
    }

    // 异步获取最新日志
    AutomationAdvertisementAnalysisApi.getSingleLogSelf({
      appId: row.appId,
      bizType: 'CAMPAIGN_UPDATE_BUDGET',
      selfId: row.campaignId
    })
      .then((logData) => {
        if (logData) {
          const content = `最近一次${logData.type || ''}是在${logData.source || ''}中完成</br>时间：${logData.operationTime || ''}</br>操作人：${logData.userName || ''}</br>从${logData.before || ''}修改成${logData.after || ''}${budgetRangeInfo ? '</br>' + budgetRangeInfo : ''}`
          budgetTooltipCache.value.set(cacheKey, content)
        } else if (budgetRangeInfo) {
          // 如果没有日志数据但有预算范围，显示预算范围
          budgetTooltipCache.value.set(cacheKey, budgetRangeInfo)
        }
      })
      .catch(() => {
        // 保持默认内容
      })
      .finally(() => {
        budgetTooltipLoading.value.set(cacheKey, false)
        budgetRequestingSet.value.delete(cacheKey)
        budgetTooltipDelayMap.value.delete(cacheKey)
      })
  }, 500) // 0.5秒延迟

  budgetTooltipDelayMap.value.set(cacheKey, delayTimer)
}

// 处理预算鼠标离开事件
const handleBudgetMouseLeave = (row) => {
  showBudgetIcon.value = null

  // 清除延迟定时器，避免不必要的API调用
  const cacheKey = `${row.id}_budget`
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(budgetTooltipDelayMap.value.get(cacheKey))
    budgetTooltipDelayMap.value.delete(cacheKey)
  }
}

// 判断是否应该显示预算tooltip
const shouldShowBudgetTooltip = (row) => {
  // 有最小值或最大值配置，或者有缓存的日志内容，或者正在加载中，或者有延迟定时器运行中
  const hasMinMax = row.minBudget || row.maxBudget
  const cacheKey = `${row.id}_budget`
  const cachedContent = budgetTooltipCache.value.get(cacheKey)
  const hasLogContent = cachedContent && cachedContent.includes('最近一次')
  const isLoading = budgetTooltipLoading.value.get(cacheKey)
  const hasDelayTimer = budgetTooltipDelayMap.value.has(cacheKey)

  return hasMinMax || hasLogContent || isLoading || hasDelayTimer
}

// 获取预算tooltip内容
const getBudgetTooltipContent = (row) => {
  const cacheKey = `${row.id}_budget`

  // 如果正在加载，显示loading
  if (budgetTooltipLoading.value.get(cacheKey)) {
    return 'Loading...'
  }

  // 如果有延迟定时器运行中，显示准备加载状态
  if (budgetTooltipDelayMap.value.has(cacheKey)) {
    return 'Loading...'
  }

  // 返回缓存内容或默认内容
  const cachedContent = budgetTooltipCache.value.get(cacheKey)

  // 如果没有最小值和最大值，且没有日志内容，则不显示范围信息
  if (!row.minBudget && !row.maxBudget && (!cachedContent || !cachedContent.includes('最近一次'))) {
    return cachedContent || ''
  }

  // 只有在有最小值或最大值时才生成范围信息
  const budgetRangeInfo =
    row.minBudget || row.maxBudget
      ? `预算范围: ${row.minBudget ? '最小' + (row.currency || '$') + row.minBudget + ' - ' : ''}${row.maxBudget ? '最大' + (row.currency || '$') + row.maxBudget : ''}`
      : ''
  return cachedContent || budgetRangeInfo
}

// 处理竞价鼠标进入事件
const handleBidMouseEnter = (row) => {
  showBidIcon.value = row.id

  const cacheKey = `${row.id}_bid`

  // 如果正在请求中，则不重复请求
  if (bidRequestingSet.value.has(cacheKey)) {
    return
  }

  // 清除之前的延迟定时器
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(bidTooltipDelayMap.value.get(cacheKey))
  }

  // 设置0.5秒延迟后再调用接口
  const delayTimer = setTimeout(() => {
    // 标记正在请求
    bidRequestingSet.value.add(cacheKey)

    // 设置loading状态
    bidTooltipLoading.value.set(cacheKey, true)

    // 生成竞价范围信息（仅在有最小值或最大值时）
    let bidRangeInfo = ''
    if (row.minBidding || row.maxBidding) {
      bidRangeInfo = `竞价范围: ${row.minBidding ? '最小' + (row.currency || '$') + row.minBidding + ' - ' : ''}${row.maxBidding ? '最大' + (row.currency || '$') + row.maxBidding : ''}`
      // 设置默认内容
      bidTooltipCache.value.set(cacheKey, bidRangeInfo)
    } else {
      // 如果没有范围信息，设置loading内容
      bidTooltipCache.value.set(cacheKey, 'Loading...')
    }

    // 异步获取最新日志
    AutomationAdvertisementAnalysisApi.getSingleLogSelf({
      appId: row.appId,
      bizType: 'KEYWORD_UPDATE_BID',
      selfId: row.keywordId
    })
      .then((logData) => {
        if (logData) {
          const content = `最近一次${logData.type || ''}是在${logData.source || ''}中完成</br>时间：${logData.operationTime || ''}</br>操作人：${logData.userName || ''}</br>从${logData.before || ''}修改成${logData.after || ''}${bidRangeInfo ? '</br>' + bidRangeInfo : ''}`
          bidTooltipCache.value.set(cacheKey, content)
        } else if (bidRangeInfo) {
          // 如果没有日志数据但有竞价范围，显示竞价范围
          bidTooltipCache.value.set(cacheKey, bidRangeInfo)
        }
      })
      .catch(() => {
        // 保持默认内容
      })
      .finally(() => {
        bidTooltipLoading.value.set(cacheKey, false)
        bidRequestingSet.value.delete(cacheKey)
        bidTooltipDelayMap.value.delete(cacheKey)
      })
  }, 500) // 0.5秒延迟

  bidTooltipDelayMap.value.set(cacheKey, delayTimer)
}

// 处理竞价鼠标离开事件
const handleBidMouseLeave = (row) => {
  showBidIcon.value = null

  // 清除延迟定时器，避免不必要的API调用
  const cacheKey = `${row.id}_bid`
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    clearTimeout(bidTooltipDelayMap.value.get(cacheKey))
    bidTooltipDelayMap.value.delete(cacheKey)
  }
}

// 判断是否应该显示竞价tooltip
const shouldShowBidTooltip = (row) => {
  // 有最小值或最大值配置，或者有缓存的日志内容，或者正在加载中，或者有延迟定时器运行中
  const hasMinMax = row.minBidding || row.maxBidding
  const cacheKey = `${row.id}_bid`
  const cachedContent = bidTooltipCache.value.get(cacheKey)
  const hasLogContent = cachedContent && cachedContent.includes('最近一次')
  const isLoading = bidTooltipLoading.value.get(cacheKey)
  const hasDelayTimer = bidTooltipDelayMap.value.has(cacheKey)

  return hasMinMax || hasLogContent || isLoading || hasDelayTimer
}

// 获取竞价tooltip内容
const getBidTooltipContent = (row) => {
  const cacheKey = `${row.id}_bid`

  // 如果正在加载，显示loading
  if (bidTooltipLoading.value.get(cacheKey)) {
    return 'Loading...'
  }

  // 如果有延迟定时器运行中，显示准备加载状态
  if (bidTooltipDelayMap.value.has(cacheKey)) {
    return 'Loading...'
  }

  // 返回缓存内容或默认内容
  const cachedContent = bidTooltipCache.value.get(cacheKey)

  // 如果没有最小值和最大值，且没有日志内容，则不显示范围信息
  if (
    !row.minBidding &&
    !row.maxBidding &&
    (!cachedContent || !cachedContent.includes('最近一次'))
  ) {
    return cachedContent || ''
  }

  // 只有在有最小值或最大值时才生成范围信息
  const bidRangeInfo =
    row.minBidding || row.maxBidding
      ? `竞价范围: ${row.minBidding ? '最小' + (row.currency || '$') + row.minBidding + ' - ' : ''}${row.maxBidding ? '最大' + (row.currency || '$') + row.maxBidding : ''}`
      : ''
  return cachedContent || bidRangeInfo
}

// 更新预算（带防抖）
const updateBudget = (row) => {
  const rowId = row.id

  // 清除之前的防抖定时器
  if (budgetDebounceMap.value.has(rowId)) {
    clearTimeout(budgetDebounceMap.value.get(rowId))
  }

  // 设置新的防抖定时器
  const timeoutId = setTimeout(async () => {
    if (!validateBudget(row)) {
      return
    }

    // 设置loading状态
    budgetUpdateLoading.value.set(row.id, true)

    try {
      const params = {
        appId: row.appId || 0,
        adGroup: row.adGroup || 0,
        sid: row.sid || 0,
        campaignId: row.campaignId || 0,
        adCampaign: row.adCampaign || '',
        budget: Number(row.dailyBudget),
        adjustBudget: Number(row.adjustBudget),
        campaignStatus: row.campaignStatus || 'enabled',
        adjustCampaignStatus: row.campaignStatus || 'enabled',
        country: row.country || ''
      }

      const result = await AutomationAdvertisementAnalysisApi.updateCampaign(params)
      showBudgetIcon.value = null

      // 清除tooltip缓存和请求状态
      budgetTooltipCache.value.delete(`${row.id}_budget`)
      budgetRequestingSet.value.delete(`${row.id}_budget`)

      // 显示执行结果弹窗
      if (result) {
        executionResult.value = result
        resultDialogVisible.value = true
      }
    } catch (error) {
      message.error('预算更新失败：' + (error.message || '未知错误'))
    } finally {
      // 清除loading状态
      budgetUpdateLoading.value.delete(row.id)
      budgetDebounceMap.value.delete(rowId)
    }
  }, 1000)

  budgetDebounceMap.value.set(rowId, timeoutId)
}

// 更新竞价（带防抖）
const updateBid = (row) => {
  const rowId = row.id

  // 清除之前的防抖定时器
  if (bidDebounceMap.value.has(rowId)) {
    clearTimeout(bidDebounceMap.value.get(rowId))
  }

  // 设置新的防抖定时器
  const timeoutId = setTimeout(async () => {
    if (!validateBid(row)) {
      return
    }

    // 设置loading状态
    bidUpdateLoading.value.set(row.id, true)

    try {
      const params = {
        appId: row.appId || 0,
        adCampaign: row.adCampaign || 0,
        sid: row.sid || 0,
        keywordId: row.keywordId || 0,
        keyword: row.keyword || '',
        adGroup: row.adGroup || '',
        bid: Number(row.bid),
        adjustBid: Number(row.adjustBid),
        suggest: row.suggest || '',
        state: row.state || 'enabled',
        adjustState: row.state || 'enabled',
        log: `竞价更新为 ${row.currency || '$'}${row.bid}`,
        country: row.country || ''
      }

      const result = await AutomationAdvertisementAnalysisApi.updateBudget(params)
      showBidIcon.value = null

      // 清除tooltip缓存和请求状态
      bidTooltipCache.value.delete(`${row.id}_bid`)
      bidRequestingSet.value.delete(`${row.id}_bid`)

      // 显示执行结果弹窗
      if (result) {
        executionResult.value = result
        resultDialogVisible.value = true
      }
    } catch (error) {
      message.error('竞价更新失败：' + (error.message || '未知错误'))
    } finally {
      // 清除loading状态
      bidUpdateLoading.value.delete(row.id)
      bidDebounceMap.value.delete(rowId)
    }
  }, 1000)

  bidDebounceMap.value.set(rowId, timeoutId)
}

// 打开操作日志弹窗
const openOperationLogDialog = (row) => {
  console.log(row)
  operationLogDialogRef.value.open(row.id)
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}
</script>

<style lang="scss" scoped>
.top_tipe {
  margin-bottom: 10px;
}

.optimization-options {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.rule-container {
  max-height: 200px;
  padding: 0 5px;
  overflow-y: auto;
  text-align: left;
}

.rule-value-e {
  padding-right: 20px; /* 为省略号留出空间 */
  margin-bottom: 8px;
  overflow: hidden;
  color: #606266;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-red-500 {
  color: #f56c6c;
}

.text-green-500 {
  color: #67c23a;
}

.text-blue-500 {
  color: #409eff;
}

.json-display {
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

.section {
  padding: 16px;
  margin-bottom: 24px;
  background: #f8f9fa;
  border-radius: 8px;
}

h3 {
  padding-bottom: 8px;
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 1px solid #eee;
}

pre {
  margin: 0;
  font-family: inherit;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 可编辑输入框样式 */
.editable-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.editable-input-container .el-input {
  transition: all 0.3s ease;
}

.editable-input-container:hover .el-input {
  box-shadow: 0 0 0 1px #409eff;
}

.editable-input-container:hover .el-input .el-input__wrapper {
  box-shadow: 0 0 0 1px #409eff inset;
}

.input-prefix {
  font-size: 12px;
  color: #909399;
}

.editable-input-container .el-input__suffix {
  display: flex;
  align-items: center;
  padding-right: 8px;
}

.editable-input-container .cursor-pointer {
  padding: 2px;
  font-size: 14px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.editable-input-container .cursor-pointer:hover {
  background-color: #f0f9ff;
  transform: scale(1.1);
}
</style>
