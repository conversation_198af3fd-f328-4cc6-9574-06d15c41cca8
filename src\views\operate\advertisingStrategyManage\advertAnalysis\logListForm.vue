<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    size="90vw"
    @close="closeDialog"
    class="copywriting_drawer"
  >
    <!-- 列表 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="分析时间" prop="analysisTime">
        <el-date-picker
          v-model="queryParams.analysisTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-340px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      border
      ref="copywritingTableRef"
      v-loading="formLoading"
      :data="list"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column
        align="center"
        label="分析时间"
        width="160"
        :formatter="dateFormatter"
        prop="analysisTime"
      />
      <el-table-column align="left" label="关键词" width="150" prop="keyword" fixed="left" />
      <el-table-column align="left" label="广告组" width="220" prop="adGroup" fixed="left" />
      <el-table-column align="center" label="满足规则" width="200">
        <template #default="scope">
          <div v-if="scope.row.satisfactionRules" class="rule-container">
            <template v-for="(value, key) in parseRules(scope.row.satisfactionRules)" :key="key">
              <div class="rule-item">
                <span class="rule-key">{{ key }}:</span>
                <span class="text-blue-500">{{ value }}</span>
                <!-- :class="{
                    'text-red-500': value.includes('<') && !value.includes('>='),
                    'text-green-500': value.includes('>=') && !value.includes('<'),
                    'text-blue-500': value.includes('>=') && value.includes('<')
                  }" -->
              </div>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="优化建议" width="90" prop="optimizationSuggestion">
        <template #default="scope">
          <div v-if="scope.row.budgetOperation">
            <span
              :class="{
                'text-green-500': scope.row.budgetOperation.includes('加'),
                'text-red-500': scope.row.budgetOperation.includes('减')
              }"
            >
              {{ scope.row.budgetOperation }}
            </span>
          </div>
          <div v-if="scope.row.cpcOperation">
            <span
              :class="{
                'text-green-500': scope.row.cpcOperation.includes('加'),
                'text-red-500': scope.row.cpcOperation.includes('减')
              }"
            >
              {{ scope.row.cpcOperation }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="店铺名称" width="120" prop="shopName" />
      <el-table-column align="center" label="类型" width="80" prop="adType" />
      <el-table-column align="center" label="广告组合" width="90" prop="adPortfolio" />
      <el-table-column align="center" label="广告活动" width="265" prop="adCampaign" />
      <el-table-column align="left" label="分析时间跨度" width="160" prop="analysisTimeRange" />
      <el-table-column align="center" label="匹配方式" width="90" prop="matchingMethod" />
      <el-table-column align="center" label="订单量" width="70" prop="orders" />
      <el-table-column align="center" label="曝光量" width="70" prop="impressions" />
      <el-table-column align="center" label="点击" width="70" prop="clicks" />
      <el-table-column align="center" label="CTR" width="70" prop="ctr" />
      <el-table-column align="center" label="CPC-本币" width="100" prop="cpcLocalCurrency" />
      <el-table-column align="center" label="花费-本币" width="100" prop="spendLocalCurrency" />
      <el-table-column align="center" label="ACoS" width="130" prop="acos" />
      <el-table-column align="center" label="CVR" width="100" prop="cvr" />
      <el-table-column align="center" label="预算" width="70" prop="budget" />
      <el-table-column align="center" label="预算使用率" width="100" prop="budgetUtilizationRate" />
      <el-table-column align="center" label="领星账号" width="100" prop="uname" />
      <el-table-column align="center" label="操作人" width="100" prop="operator" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <template #footer>
      <div style="text-align: center">
        <el-button @click="closeDialog()">关 闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { AutomationAdvertisementAnalysisApi } from '@/api/operate/advertAnalysis'
import { dateFormatter } from '@/utils/formatTime'
import { useClipboard } from '@vueuse/core'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'SystemDictTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('分析历史') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const total = ref(0) // 列表的总页数
const list = ref([]) // 字典表格数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  keyword: '',
  shopName: '',
  adType: undefined,
  adPortfolio: undefined,
  adCampaign: undefined,
  adGroup: undefined,
  matchingMethod: undefined,
  lingxingAccount: undefined,
  operator: undefined,
  analysisId: undefined, // 广告分析表id
  analysisTime: []
})

const formRef = ref() // 表单 Ref
const queryFormRef = ref() // 查询表单 Ref

/** 打开弹窗 */
const open = async (keyword: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('诊断历史：' + keyword)
  queryFormRef.value?.resetFields()
  // 修改时，设置数据
  queryParams.analysisId = id
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.pageNo = 1
  queryParams.pageSize = 12
  handleQuery()
}

// 列表接口
const getList = async () => {
  formLoading.value = true
  try {
    const data = await AutomationAdvertisementAnalysisApi.getPageHistory(queryParams)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    message.error('获取诊断历史列表失败')
  } finally {
    formLoading.value = false
  }
}

// 解析规则数据
const parseRules = (rules) => {
  if (!rules) return {}
  try {
    return typeof rules === 'string' ? JSON.parse(rules) : rules
  } catch (e) {
    console.error('解析规则数据失败', e)
    return {}
  }
}

/** 关闭弹窗 */
const closeDialog = () => {
  dialogVisible.value = false
  queryFormRef.value?.resetFields()
  queryParams.analysisId = undefined
}
</script>

<style lang="scss" scoped>
.copywriting_drawer {
  :deep(.el-drawer__body) {
    padding: 20px;
    overflow: auto;
  }
}

.rule-container {
  max-height: 200px;
  padding: 0 5px;
  overflow-y: auto;
  text-align: left;
}

.rule-item {
  display: flex;
  padding: 2px 0;
  font-size: 12px;
  line-height: 18px;
  border-bottom: 1px dashed #eee;
  justify-content: space-between;
}

.rule-key {
  margin-right: 5px;
  font-weight: bold;
}

.rule-value {
  color: #606266;
}

.text-red-500 {
  color: #f56c6c;
}

.text-green-500 {
  color: #67c23a;
}

.text-blue-500 {
  color: #409eff;
}
</style>
