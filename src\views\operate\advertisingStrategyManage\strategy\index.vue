<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form :inline="true" :model="queryParams" class="-mb-15px" label-width="68px">
      <el-form-item>
        <el-input
          v-model="queryParams.strategyName"
          placeholder="请输入策略名称查询"
          clearable
          class="!w-250px"
        />
      </el-form-item>
      <el-form-item>
        <shortcut-date-range-picker v-model="queryParams.createTime" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
          v-hasPermi="['operation:advertising-strategy:query']"
          >查询</el-button
        >
        <el-button @click="handleAddStrategy" v-hasPermi="['operation:advertising-strategy:create']"
          >新增策略</el-button
        >
        <el-button
          type="danger"
          @click="handleBatchDelete"
          v-hasPermi="['operation:advertising-strategy:delete']"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      border
      ref="strategyTableRef"
      v-loading="loading"
      :data="list"
      style="width: 100%; margin-top: 20px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="strategyName" label="策略名称" width="380" />
      <el-table-column prop="rule" label="区间划分规则" min-width="950">
        <template #default="scope">
          <div v-if="scope.row.rule" class="rule-container">
            <div class="rule-grid rules-container">
              <template v-for="(value, key) in parseRules(scope.row.rule)" :key="key">
                <!-- <div class="rule-item">
                  <span class="rule-key">{{ key }}:</span>
                  <span class="text-blue-500 rule-value">{{ value.map(item=> item).join(',') }}</span>
                </div> -->
                <div class="rule-item">
                  <span class="rule-key">{{ key }}:</span>
                  <span class="text-blue-500 rule-value">{{
                    value.map((item) => item).join(',')
                  }}</span>
                </div>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" :formatter="dateFormatter" />
      <el-table-column prop="rule" label="分享范围" width="150" align="left">
        <template #default="scope">
          <el-select
            v-model="scope.row.shareScope"
            placeholder=""
            :disabled="!scope.row.originalOwner && scope.row.originalOwner != 1"
            :readonly="!scope.row.originalOwner && scope.row.originalOwner != 1"
          >
            <el-option label="私有" value="0" @click="shareFc(scope.row, 0)" />
            <el-option label="团队共享" value="1" @click="shareFc(scope.row, 1)" />
            <el-option label="全平台共享" value="2" @click="shareFc(scope.row, 2)" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleCopyTemplate(scope.row)"
            v-hasPermi="['operation:advertising-strategy:create']"
            >复制模板</el-button
          >
          <el-button
            size="small"
            v-if="scope.row.flag != 0 && scope.row.originalOwner && scope.row.originalOwner != 0"
            @click="handleEditStrategy(scope.row)"
            v-hasPermi="['operation:advertising-strategy:create']"
            >编 辑</el-button
          >

          <el-button
            size="small"
            type="danger"
            v-if="scope.row.flag != 0"
            @click="handleDeleteStrategy(scope.row.id)"
            v-hasPermi="['operation:advertising-strategy:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 新增策略弹窗 -->
  <el-dialog
    title="新增策略"
    v-model="dialogVisible"
    width="30%"
    :close-on-click-modal="false"
    @close="handleCloseDialog"
  >
    <div class="strategy-form">
      <!-- 步骤1：选择模板 -->
      <div class="step-item">
        <div class="step-title">1、请选择策略模板：</div>
        <div class="template-list">
          <div
            v-for="(item, index) in templateOptions"
            :key="item.id + '_' + index"
            class="template-item"
            :class="{ active: item.id == addStrategyForm.templateId }"
            @click="handleTemplateSelect(item)"
          >
            {{ item.strategyName }}
          </div>
        </div>
      </div>

      <!-- 步骤2：新建模板名称 -->
      <div class="step-item">
        <div class="step-title">2、新建模板名称：</div>
        <el-input v-model="addStrategyForm.newTemplateName" placeholder="请输入模板名称" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" v-loading="nextLoaidng" @click="handleAddStrategyConfirm"
          >下一步</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  AutomationAdvertisementStrategyApi,
  AutomationAdvertisementStrategyPageReqVO
} from '@/api/operate/advertisingStrategyManage/index.ts'
import { dateFormatter } from '@/utils/formatTime'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref } from 'vue'

const router = useRouter() // 路由

const loading = ref(false)
const list = ref([])
const total = ref(0)
const queryParams = reactive<AutomationAdvertisementStrategyPageReqVO>({
  pageNo: 1,
  pageSize: 50,
  strategyName: '',
  createTime: []
})
const dialogVisible = ref(false)
const addStrategyForm = reactive({
  templateId: '',
  newTemplateName: ''
})
const selectedRows = ref(null)
// 增加5条测试数据
const templateOptions = ref([
  { id: 1, name: '本土英国-7维度区间策略基础版-*********' },
  { id: 2, name: '印度-7维度区间策略基础版-*********' },
  { id: 3, name: '中企欧洲-7维度区间策略基础版-*********' },
  { id: 4, name: '日本-7维度区间策略基础版-*********' },
  { id: 5, name: '美国-7维度区间策略基础版-*********' }
])

// 选择模板
const handleTemplateSelect = (item) => {
  addStrategyForm.templateId = item.id
  // 加copy和时间戳
  addStrategyForm.newTemplateName = `${item.strategyName}-copy` + '-' + Date.now()
}

// 查询
const handleQuery = async () => {
  loading.value = true
  try {
    getList()
  } finally {
    loading.value = false
  }
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const res =
      await AutomationAdvertisementStrategyApi.getAutomationAdvertisementStrategyPage(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleAddStrategy = async () => {
  let res = await AutomationAdvertisementStrategyApi.getAutomationAdvertisementStrategyPage({
    page: 1,
    pageSize: 100
  })
  templateOptions.value = res.list
  dialogVisible.value = true
}
const nextLoaidng = ref(false)
// 新增模板成功，下一步
const handleAddStrategyConfirm = async () => {
  if (!addStrategyForm.templateId || !addStrategyForm.newTemplateName) {
    ElMessage.warning('请选择模板并填写新建模板名称')
    return
  }
  nextLoaidng.value = true
  // ElMessage.success('正在创建策略模板，请稍后……')
  try {
    let res = await AutomationAdvertisementStrategyApi.createAutomationAdvertisementStrategy({
      id: addStrategyForm.templateId,
      strategyName: addStrategyForm.newTemplateName
    })
    console.log(res)
    ElMessage.success('策略创建成功，数据以保存，请设置区间划分规则，页面实时保存，请放心操作')
    // 跳转到settingForm
    router.push({
      name: 'SettingForm',
      query: { id: res, name: addStrategyForm.newTemplateName }
    })
    dialogVisible.value = false
  } catch (error) {
    console.log(error)
  } finally {
    nextLoaidng.value = false
  }
}

// 关闭模板
const handleCloseDialog = () => {
  addStrategyForm.newTemplateName = ''
  addStrategyForm.templateId = ''
  dialogVisible.value = false
}
// 编辑策略
const handleEditStrategy = (row) => {
  console.log('编辑策略', row)
  // 跳转到settingForm
  router.push({
    name: 'SettingForm',
    query: { id: row.id, name: row.strategyName }
  })
}
// 复制模板
const handleCopyTemplate = async (row) => {
  console.log('复制模板', row)
  let res = await AutomationAdvertisementStrategyApi.getAutomationAdvertisementStrategyPage({
    page: 1,
    pageSize: 100
  })
  // 添加置顶逻辑
  const index = res.list.findIndex((item) => item.id === row.id)
  if (index !== -1) {
    const [selectedItem] = res.list.splice(index, 1)
    res.list.unshift(selectedItem)
  }
  templateOptions.value = res.list
  addStrategyForm.templateId = row.id
  addStrategyForm.newTemplateName = row.strategyName + '-copy' + '-' + Date.now()
  dialogVisible.value = true
}

const handleSelectionChange = (val) => {
  console.log('选中项', val)
  selectedRows.value = val
}
// 单条删除
const handleDeleteStrategy = async (ids) => {
  try {
    await ElMessageBox.confirm('确定删除该策略吗？')
    await AutomationAdvertisementStrategyApi.deleteStrategyBatch([ids])
    ElMessage.success('删除成功')
    getList() // 刷新列表
  } catch (e) {
    console.error('删除失败', e)
  }
}

// 分享
const shareFc = async (row, shareScope) => {
  console.log('分享', row)
  await AutomationAdvertisementStrategyApi.shareStrategy({
    id: row.id,
    shareScope: shareScope
  })
  ElMessage.success('分享成功')
  getList() // 刷新列表
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedRows.value?.length) {
    ElMessage.warning('请选择要删除的策略')
    return
  }
  try {
    await ElMessageBox.confirm(`确定删除选中的${selectedRows.value.length}条策略吗？`)
    const ids: number[] = selectedRows.value.map((item) => item.id)
    await AutomationAdvertisementStrategyApi.deleteStrategyBatch(ids)
    ElMessage.success('批量删除成功')
    getList()
    selectedRows.value = [] // 清空选中
  } catch (e) {
    console.error('批量删除失败', e)
  }
}
// 解析规则数据
const parseRules = (rules) => {
  if (!rules) return {}
  try {
    return typeof rules === 'string' ? JSON.parse(rules) : rules
  } catch (e) {
    console.error('解析规则数据失败', e)
    return {}
  }
}

// 格式化规则为tooltip内容
const formatRulesTooltip = (rulesStr) => {
  try {
    const rules = parseRules(rulesStr)
    return Object.entries(rules)
      .map(
        ([key, value]) =>
          `<div class="rule-item"><span class="rule-key">${key}:</span><span class="rule-value" style="font-weight: bold;color: #d1d1d1">${value}</span></div>`
      )
      .join('\n')
  } catch (e) {
    return '无法解析规则'
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.rules-container {
  /* 默认单列显示 */
  column-count: 1;
}

/* 当规则项超过3个时切换为两列布局 */
.rules-container:has(.rule-item:nth-child(4)) {
  column-count: 2;
  column-gap: 20px;
}

.dialog-footer {
  text-align: right;
}

.strategy-form {
  padding: 20px;
}

.step-item {
  margin-bottom: 20px;
}

.step-title {
  margin-bottom: 10px;
  font-weight: bold;
}

.template-list {
  max-height: 150px; /* 控制默认显示3条 */
  padding: 8px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  .template-item {
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .template-item:hover,
  .active {
    background-color: #fff1cb;
  }
}
</style>
