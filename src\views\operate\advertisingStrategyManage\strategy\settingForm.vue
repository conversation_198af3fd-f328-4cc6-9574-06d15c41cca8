<template>
  <ContentWrap title="设置区间划分规则">
    <div
      class="flex items-end flex-col top-table"
      style="width: max-content"
    >
      <el-table
        v-loading="topTabLoading"
        element-loading-text="正在保存规则，请稍后"
        :data="form.rules"
        style="width: max-content"
        border
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          prop="transferName"
          label="维度名称"
          width="100"
        />
        <el-table-column
          label="区间划分规则"
          width="400"
        >
          <template #default="scope">
            <div class="flex gap-10px">
              <!-- 匹配方式特殊处理 -->
              <template v-if="scope.row.transferName === '匹配方式'">
                <div class="flex itmes-center">
                  <el-radio-group
                    v-model="scope.row.matchType"
                    @change="handleMatchTypeChange(scope.$index)"
                  >
                    <el-tag label="精准匹配">精准匹配</el-tag>
                    <el-tag label="广泛匹配">广泛匹配</el-tag>
                  </el-radio-group>
                </div>
              </template>

              <!-- 预算使用率特殊处理 -->
              <template v-else-if="scope.row.transferName === '预算使用率'">
                <div>
                  <el-input
                    size="small"
                    v-model.number="scope.row.value1"
                    maxlength="7"
                    placeholder="请输入"
                    style="width: 90px"
                    @focus="handleFocus(scope.row, 'value1')"
                    @blur="handleBlur(scope.row, 'value1')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '<' }}</span>
                    </template>
                    <template #append>
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                </div>
                <div>
                  <el-input
                    size="small"
                    v-model.number="scope.row.value2"
                    maxlength="7"
                    placeholder="请输入"
                    style="width: 90px"
                    @focus="handleFocus(scope.row, 'value2')"
                    @blur="handleBlur(scope.row, 'value2')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '≥' }}</span>
                    </template>
                    <template #append>
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                </div>
              </template>
              <!-- 普通字段处理 -->
              <template v-else>
                <div>
                  <el-input
                    size="small"
                    v-model.number="scope.row.value1"
                    maxlength="7"
                    placeholder="请输入"
                    :style="scope.$index > 1 ? 'width: 90px;' : 'width: 70px;'"
                    @focus="handleFocus(scope.row, 'value1')"
                    @blur="handleBlur(scope.row, 'value1')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '<' }}</span>
                    </template>
                    <template
                      #append
                      v-if="needPercentage(scope.row.transferName)"
                    >
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                </div>
                <div class="flex itmes-center">
                  <el-input
                    size="small"
                    v-model.number="scope.row.value2"
                    maxlength="7"
                    placeholder="请输入"
                    :style="scope.$index > 1 ? 'width: 90px;' : 'width: 70px;'"
                    @focus="handleFocus(scope.row, 'value2')"
                    @blur="handleBlur(scope.row, 'value2')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '≥' }}</span>
                    </template>
                    <template
                      #append
                      v-if="needPercentage(scope.row.transferName)"
                    >
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                  <el-input
                    size="small"
                    v-model.number="scope.row.value3"
                    maxlength="7"
                    placeholder="请输入"
                    :style="scope.$index > 1 ? 'width: 90px;' : 'width: 70px;'"
                    @focus="handleFocus(scope.row, 'value3')"
                    @blur="handleBlur(scope.row, 'value3')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '<' }}</span>
                    </template>
                    <template
                      #append
                      v-if="needPercentage(scope.row.transferName)"
                    >
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                </div>
                <div>
                  <el-input
                    size="small"
                    v-model.number="scope.row.value4"
                    maxlength="7"
                    placeholder="请输入"
                    :style="scope.$index > 1 ? 'width: 90px;' : 'width: 70px;'"
                    @focus="handleFocus(scope.row, 'value4')"
                    @blur="handleBlur(scope.row, 'value4')"
                    @input="handleInput(scope.$index)"
                  >
                    <template #prepend>
                      <span style="font-size: 14px; font-weight: bold">{{ '≥' }}</span>
                    </template>
                    <template
                      #append
                      v-if="needPercentage(scope.row.transferName)"
                    >
                      <span style="font-size: 14px">%</span>
                    </template>
                  </el-input>
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="80"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.transferName !== '匹配方式'"
              type="text"
              @click="handleSaver(scope.$index)"
              :class="{ 'highlight-btn': scope.row.isModified }"
            >保存</el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="提示信息"
          width="220"
        >
          <template #default="scope">
            <div
              v-if="scope.row.error"
              class="error-message"
            >
              <el-alert
                :title="scope.row.errorMsg"
                type="error"
                :closable="false"
                show-icon
                size="small"
              />
            </div>
          </template>
        </el-table-column> -->
      </el-table>
      <el-button
        class="mt-10px w-100px"
        :type="hasModified ? 'warning' : 'primary'"
        @click="generateRules"
        v-loading="topTabLoading"
      >统一保存</el-button>
    </div>
  </ContentWrap>

  <!-- 筛选条件 -->
  <ContentWrap
    title="可能性列表"
    class="mt-10px query-box"
  >
    <!-- 筛选条件 -->
    <el-affix
      target=".query-box"
      :offset="85"
      @change="(value) => (affixStatus = value)"
    >
      <div
        class="filter-container"
        :style="affixStatus ? 'background: #f5f7fa;' : 'background: #fff'"
      >
        <div class="filter-trigger">
          <el-popover
            placement="bottom-start"
            width="auto"
            trigger="click"
            popper-class="filter-popover"
          >
            <template #reference>
              <el-button
                type="primary"
                size="small"
              >
                <el-icon>
                  <Filter />
                </el-icon>
                <span class="ml-5px">筛选条件</span>
              </el-button>
            </template>
            <div class="filter-options">
              <div
                class="filter-option-group"
                v-for="(item, index) in originalRules"
                :key="index"
              >
                <div class="filter-option-title">{{ item.transferName }}：</div>
                <template
                  v-for="(qitem, qindex) in item.detailVOS"
                  :key="qindex"
                >
                  <el-radio-group v-model="filterParams[qitem.filedName]">
                    <el-radio
                      border
                      :label="qitem.condition"
                    >{{ qitem.condition }}</el-radio>
                  </el-radio-group>
                </template>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="filter-tags flex items-center">
          <el-tag
            v-for="(value, key) in activeFilters"
            :key="key"
            closable
            @close="removeFilter(key)"
            class="mr-10px"
            type="info"
            effect="plain"
          >
            {{ getFilterLabel(key, value) }}
          </el-tag>
          <el-button
            v-if="Object.keys(activeFilters).length != 0"
            type="danger"
            @click="((filterParams = {}), getStrategyAllList)"
          >重置</el-button>
          <el-button
            v-if="Object.keys(activeFilters).length != 0"
            type="primary"
            @click="getStrategyAllList"
          >查询（{{ filterCount }}）</el-button>
        </div>
        <el-button
          style="width: 80px; margin-top: 10px"
          type=""
          @click="handleBatchEdit"
        >批量编辑</el-button>
      </div>
    </el-affix>
    <!-- 列表 -->
    <el-table-v2
      ref="tableRef"
      v-loading="combinationLoading"
      :columns="columns"
      :data="combinationList"
      :width="1200"
      :height="600"
      :row-height="null"
      border
      :estimated-row-height="150"
      scrollToTop="0"
      scrollbar-always-on
      cache="10"
    />
  </ContentWrap>

  <!-- 批量编辑弹窗 -->
  <el-dialog
    v-model="batchEditVisible"
    title="批量编辑操作"
    width="500px"
  >
    <div class="batch-edit-container">
      <div class="edit-section">
        <div class="section-title">预算操作</div>
        <div class="operation-container">
          <el-select
            v-model="batchEditData.budgetOperation.type"
            size="small"
            class="mr-5px"
            style="width: 80px !important"
          >
            <el-option
              label="增加"
              value="increase"
            />
            <el-option
              label="减少"
              value="decrease"
            />
            <el-option
              label="不变"
              value="keep"
            />
          </el-select>
          <template v-if="batchEditData.budgetOperation.type !== 'keep'">
            <el-input
              type="number"
              v-model="batchEditData.budgetOperation.value"
              size="small"
              class="w-80px mr-5px"
              style="width: 80px !important"
              placeholder="请输入"
              min="0"
              step="1"
              @input="
                batchEditData.budgetOperation.value =
                  batchEditData.budgetOperation.value < 0 ? 0 : batchEditData.budgetOperation.value
              "
            />
            <el-select
              v-model="batchEditData.budgetOperation.unit"
              size="small"
              class="mr-5px"
              style="width: 80px !important"
            >
              <el-option
                label="数值"
                value="value"
              />
              <el-option
                label="百分比"
                value="percent"
              />
            </el-select>
          </template>
        </div>
      </div>

      <div class="edit-section mt-20px">
        <div class="section-title">CPC操作</div>
        <div class="operation-container">
          <el-select
            v-model="batchEditData.cpcOperation.type"
            size="small"
            class="mr-5px"
            style="width: 80px !important"
          >
            <el-option
              label="增加"
              value="increase"
            />
            <el-option
              label="减少"
              value="decrease"
            />
            <el-option
              label="不变"
              value="keep"
            />
          </el-select>
          <template v-if="batchEditData.cpcOperation.type !== 'keep'">
            <el-input
              type="number"
              v-model="batchEditData.cpcOperation.value"
              size="small"
              class="w-80px mr-5px"
              style="width: 80px !important"
              placeholder="请输入"
              min="0"
              step="1"
              @input="
                batchEditData.cpcOperation.value =
                  batchEditData.cpcOperation.value < 0 ? 0 : batchEditData.cpcOperation.value
              "
            />
            <el-select
              v-model="batchEditData.cpcOperation.unit"
              size="small"
              class="mr-5px"
              style="width: 80px !important"
            >
              <el-option
                label="数值"
                value="value"
              />
              <el-option
                label="百分比"
                value="percent"
              />
            </el-select>
          </template>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          v-loading="combinationLoading"
          @click="batchEditVisible = false"
        >取消</el-button>
        <el-button
          v-loading="combinationLoading"
          type="primary"
          @click="confirmBatchEdit"
        >确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="jsx">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Filter } from '@element-plus/icons-vue'
import { AutomationAdvertisementStrategyApi } from '@/api/operate/advertisingStrategyManage/index'
import { ElCheckbox } from 'element-plus'

const route = useRoute()

defineOptions({ name: 'SettingForm' })

const topTabLoading = ref(false)
const combinationLoading = ref(false)
const form = ref({
  dimensionName: '',
  rules: []
})
const originalRules = ref([]) // 筛选条件
const affixStatus = ref(false)

// 筛选条件相关数据
const filterParams = ref({})

// 筛选出来的条数
const filterCount = ref(0)

// 当前激活的筛选条件
const activeFilters = computed(() => {
  const result = {}
  Object.entries(filterParams.value).forEach(([key, value]) => {
    if (
      value &&
      originalRules.value.some((rule) => rule.detailVOS.some((i) => i.filedName === key))
    ) {
      result[key] = value
    }
  })
  return result
})

// 获取筛选条件标签文本
const getFilterLabel = (key, value) => {
  const labelMap = {
    clicks: '点击',
    cpcLocalCurrency: 'CPC-本币',
    ctr: 'CTR',
    cvr: 'CVR',
    acos: 'ACoS',
    matchType: '匹配方式',
    budgetUsageRate: '预算使用率'
  }
  return `${labelMap[key]}: ${value}`
}

// 移除筛选条件
const removeFilter = (key) => {
  filterParams.value[key] = ''
}

// 处理预算操作焦点
const handleBudgetFocus = (row) => {
  row.budgetOperation.isEditing = true
}

// 处理预算操作失焦
const handleBudgetBlur = (row) => {
  // 使用setTimeout确保点击其他元素时不会立即失焦
  setTimeout(() => {
    row.budgetOperation.isEditing = false
    // 自动保存
    handleSaveOperation(row)
  }, 200)
}

// input变化
const handleBudgetChange = (row) => {
  console.log(row)
}

// 处理CPC操作焦点
const handleCpcFocus = (row) => {
  row.cpcOperation.isEditing = true
}

// 处理CPC操作失焦
const handleCpcBlur = (row) => {
  // 使用setTimeout确保点击其他元素时不会立即失焦
  setTimeout(() => {
    row.cpcOperation.isEditing = false
    // 自动保存
    handleSaveOperation(row)
  }, 200)
}

// 获取预算操作显示文本
const getBudgetDisplayText = (operation) => {
  if (operation.type === 'keep') {
    return '- 保持不变'
  }
  if (!operation.value) {
    return operation.type === 'increase' ? '↑  预算加  ' : '↓  预算减  '
  }

  const prefix = operation.type === 'increase' ? '↑  预算加  ' : '↓  预算减  '
  const suffix = operation.unit === 'percent' ? '%' : ''
  return `${prefix}${operation.value}${suffix}`
}

// 获取CPC操作显示文本
const getCpcDisplayText = (operation) => {
  if (operation.type === 'keep') {
    return '- 保持不变'
  }

  if (!operation.value) {
    return operation.type === 'increase' ? '↑  CPC加  ' : '↓  CPC减  '
  }

  const prefix = operation.type === 'increase' ? '↑  CPC加  ' : '↓  CPC减  '
  const suffix = operation.unit === 'percent' ? '%' : ''
  return `${prefix}${operation.value}${suffix}`
}

// 组合列表数据
const combinationList = ref([])

const formRef = ref(null)
const hasModified = computed(() => form.value.rules.some((rule) => rule.isModified))

// 处理输入框焦点
const handleFocus = (row, field) => {
  // 当输入框获得焦点时，添加黄色高亮
  const inputEl = event.target.closest('.el-input__wrapper')
  if (inputEl) {
    inputEl.style.boxShadow = '0 0 0 1px #E6A23C inset'
  }
}

// 处理输入框失去焦点
const handleBlur = (row, field) => {
  // 当输入框失去焦点时，恢复正常颜色
  const inputEl = event.target.closest('.el-input__wrapper')
  if (inputEl) {
    inputEl.style.boxShadow =
      '0 1px 0 0 var(--el-border-color) inset, 0 -1px 0 0 var(--el-border-color) inset, -1px 0 0 0 var(--el-border-color) inset, -1px 0 0 0 var(--el-border-color) inset'
  }

  // 验证输入值
  validateRow(row)
}

// 处理输入变化
const handleInput = (index) => {
  const row = form.value.rules[index]
  row.isModified = true
  validateRow(row)
}

// 验证行数据
const validateRow = (row) => {
  row.error = false
  row.errorMsg = ''
  row.change = true

  // 匹配方式不需要验证数值
  if (row.transferName === '匹配方式') {
    return true
  }

  // 预算使用率只需要验证两个值
  if (row.transferName === '预算使用率') {
    // 验证值大于0
    if (
      (row.value1 && parseFloat(row.value1) <= 0) ||
      (row.value2 && parseFloat(row.value2) <= 0)
    ) {
      row.error = true
      row.errorMsg = `${row.transferName}：数值必须大于0`
      return false
    }
    // 验证 value1 < value2
    if (row.value1 && row.value2 && parseFloat(row.value1) !== parseFloat(row.value2)) {
      row.error = true
      row.errorMsg = `${row.transferName}：< 和 ≥ 的值必须相等`
      return false
    }
    return true
  }

  // 根据不同维度提供具体错误信息
  const getDimensionErrorMsg = (transferName, errorType) => {
    const errorMsgs = {
      点击: {
        valueEqual: '< 和 ≥ 的值必须相等',
        rangeError: '第一个区间的值必须小于第二个区间的值'
      },
      'CPC-本币': {
        valueEqual: '< 和 ≥ 的值必须相等',
        rangeError: '第一个区间的值必须小于第二个区间的值'
      },
      CTR: {
        valueEqual: '< 和 ≥ 的值必须相等',
        rangeError: '第一个区间的值必须小于第二个区间的值'
      },
      CVR: {
        valueEqual: '< 和 ≥ 的值必须相等',
        rangeError: '第一个区间的值必须小于第二个区间的值'
      },
      ACoS: {
        valueEqual: '< 和 ≥ 的值必须相等',
        rangeError: '第一个区间的值必须小于第二个区间的值'
      }
    }

    return `${transferName}：${errorMsgs[transferName]?.[errorType] || '输入值有误'}`
  }

  // 验证 value1 = value2
  if (row.value1 && row.value2 && parseFloat(row.value1) !== parseFloat(row.value2)) {
    row.error = true
    row.errorMsg = getDimensionErrorMsg(row.transferName, 'valueEqual')
    return false
  }

  // 验证 value3 = value4
  if (row.value3 && row.value4 && parseFloat(row.value3) !== parseFloat(row.value4)) {
    row.error = true
    row.errorMsg = getDimensionErrorMsg(row.transferName, 'valueEqual')
    return false
  }

  // 验证 value1/value2 < value3/value4
  if (row.value1 && row.value3 && parseFloat(row.value1) >= parseFloat(row.value3)) {
    row.error = true
    row.errorMsg = getDimensionErrorMsg(row.transferName, 'rangeError')
    return false
  }

  return true
}

// 表格行样式
const tableRowClassName = ({ row }) => {
  return row.error ? 'error-row' : '' || row.change ? 'change-row' : ''
}

// 判断是否需要显示百分比
const needPercentage = (transferName) => {
  return ['CTR', 'CVR', 'ACoS', '预算使用率'].includes(transferName)
}

// 保存单行数据
const handleSaver = async (index) => {
  const row = form.value.rules[index]
  await generateRules()
  if (validateRow(row)) {
    row.isModified = false
    row.change = false
    ElMessage.success(`第 ${index + 1} 行保存成功下方数据已更新`)
  } else {
    ElMessage.error(row.errorMsg || `第 ${index + 1} 行数据有误，请检查`)
  }
}

// 保存
const generateRules = async () => {
  let hasError = false
  let errorMessages = []
  for (let i = 0; i < form.value.rules.length; i++) {
    const rule = form.value.rules[i]
    // 跳过匹配方式的验证
    if (rule.transferName === '匹配方式') {
      continue
    }
    if (!validateRow(rule)) {
      hasError = true
      if (rule.errorMsg) {
        errorMessages.push(`第 ${i + 1} 行: ${rule.errorMsg}`)
      }
    }
  }

  if (hasError) {
    if (errorMessages.length > 0) {
      ElMessage({
        message: errorMessages.join('\n'),
        type: 'error',
        duration: 5000,
        showClose: true,
        dangerouslyUseHTMLString: false
      })
    } else {
      ElMessage.error('存在错误数据，请检查')
    }
    return
  }

  // 没有错误，继续处理
  // 构建API需要的数据格式
  const apiData = []
  form.value.rules.forEach((rule) => {
    const fieldData = {
      filedName: rule.filedName,
      transferName: rule.transferName,
      detailVOS: []
    }
    // 跳过匹配方式
    if (rule.transferName === '匹配方式') {
      return
    } else if (rule.transferName === '预算使用率') {
      if (rule.value1) {
        fieldData.detailVOS.push({
          ...rule,
          ...rule.detailVOS[0],
          condition: `<${rule.value1}%`,
          min: '',
          max: rule.value1,
          id: rule.detailVOS[0].id,
          strategyId: rule.detailVOS[0].strategyId
        })
      }
      if (rule.value2) {
        fieldData.detailVOS.push({
          ...rule,
          ...rule.detailVOS[1],
          condition: `>=${rule.value2}%`,
          min: rule.value2,
          max: '',
          id: rule.detailVOS[1].id,
          strategyId: rule.detailVOS[1].strategyId
        })
      }
    }
    // 其他字段处理
    else {
      // 第一个区间 <value1
      if (rule.value1) {
        const condition = needPercentage(rule.transferName) ? `<${rule.value1}%` : `<${rule.value1}`
        fieldData.detailVOS.push({
          ...rule,
          ...rule.detailVOS[0],
          condition,
          min: '',
          max: rule.value1,
          id: rule.detailVOS[0].id,
          strategyId: rule.detailVOS[0].strategyId
        })
      }

      // 中间区间 >=value2<value3
      if (rule.value2 && rule.value3) {
        const condition = needPercentage(rule.transferName)
          ? `>=${rule.value2}%<${rule.value3}%`
          : `>=${rule.value2}<${rule.value3}`
        fieldData.detailVOS.push({
          ...rule,
          ...rule.detailVOS[1],
          condition,
          min: rule.value2,
          max: rule.value3,
          id: rule.detailVOS[1].id,
          strategyId: rule.detailVOS[1].strategyId
        })
      }

      // 最后区间 >=value4
      if (rule.value4) {
        const condition = needPercentage(rule.transferName)
          ? `>=${rule.value4}%`
          : `>=${rule.value4}`
        fieldData.detailVOS.push({
          ...rule,
          ...rule.detailVOS[2],
          condition,
          min: rule.value4,
          max: '',
          id: rule.detailVOS[2].id,
          strategyId: rule.detailVOS[2].strategyId
        })
      }
    }
    apiData.push(fieldData)
  })

  // 这里可以调用API保存数据
  let detailVOS = apiData.map((item) => item.detailVOS.flat())
  topTabLoading.value = true
  try {
    let res = await AutomationAdvertisementStrategyApi.updateStrategyBatch(detailVOS.flat())
    // 重置所有修改标记
    form.value.rules.forEach((rule) => {
      rule.isModified = false
      rule.change = false
    })
    ElMessage.success('组合规则生成成功,正在获取组合列表数据...')
    getData()
    getStrategyAllList()
  } catch (e) {
    console.log(e)
  } finally {
    topTabLoading.value = false
  }
}

// 批量编辑相关数据
const batchEditVisible = ref(false)
const batchEditData = ref({
  budgetOperation: {
    type: 'increase',
    value: '',
    unit: 'value'
  },
  cpcOperation: {
    type: 'increase',
    value: '',
    unit: 'value'
  }
})

// 处理批量编辑按钮点击
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项进行批量编辑')
    return
  }

  // 重置批量编辑数据
  batchEditData.value = {
    budgetOperation: {
      type: 'increase',
      value: '',
      unit: 'value'
    },
    cpcOperation: {
      type: 'increase',
      value: '',
      unit: 'value'
    }
  }

  batchEditVisible.value = true
}

// 确认批量编辑
const confirmBatchEdit = async () => {
  // 校验预算操作参数
  if (
    batchEditData.value.budgetOperation.type !== 'keep' &&
    !batchEditData.value.budgetOperation.value
  ) {
    ElMessage.warning('请填写完整的预算操作参数')
    return
  }

  // 校验CPC操作参数
  if (batchEditData.value.cpcOperation.type !== 'keep' && !batchEditData.value.cpcOperation.value) {
    ElMessage.warning('请填写完整的CPC操作参数')
    return
  }

  try {
    await ElMessageBox.confirm('确认要执行批量编辑操作吗？', '操作确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    saveBatchOperations()
  } catch (error) {
    // 用户取消操作
  }
}

route.meta.title = route.query.name

// 解析API返回的条件字符串
const parseCondition = (condition, fieldName) => {
  // 处理匹配方式的特殊情况
  if (condition === '精准匹配' || condition === '广泛匹配') {
    return { matchType: condition }
  }

  // 处理其他条件
  const result = {}

  // 处理 <X 格式
  if (condition.startsWith('<')) {
    result.value1 = condition.replace(/[<%]/g, '')
  }
  // 处理 >=X 格式
  else if (condition.startsWith('>=')) {
    result.value2 = condition.replace(/[>=%]/g, '')
  }
  // 处理 >=X<Y 格式
  else if (condition.includes('<') && condition.includes('>=')) {
    const parts = condition.split('<')
    result.value2 = parts[0].replace(/[>=%]/g, '')
    result.value3 = parts[1].replace(/[%]/g, '')
  }

  return result
}

// 从API获取数据并处理
const getData = async () => {
  originalRules.value = []
  try {
    const res = await AutomationAdvertisementStrategyApi.getStrategyDetail(route.query.id)
    // 处理返回的数据
    res.forEach((field) => {
      field.detailVOS.forEach((detail) => {
        detail.isModified = false
        detail.error = false
        const parsedValues = parseCondition(detail.condition, field.filedName)

        // 匹配方式特殊处理
        if (field.filedName === 'matchType') {
          detail.matchType = parsedValues.matchType || '精准匹配'
        }
        // 预算使用率特殊处理
        else if (field.filedName === 'budgetUsageRate') {
          if (detail.condition.startsWith('<')) {
            detail.value1 = detail.max
            field.value1 = detail.max
          } else if (detail.condition.startsWith('>=')) {
            detail.value2 = detail.min
            field.value2 = detail.min
          }
        } else {
          // 根据min和max设置值
          if (detail.min === '') {
            detail.value1 = detail.max
            field.value1 = detail.max
          } else if (detail.max === '') {
            detail.value4 = detail.min
            field.value4 = detail.min
          } else {
            // 中间区间
            detail.value2 = detail.min
            detail.value3 = detail.max
            field.value2 = detail.min
            field.value3 = detail.max
          }
        }
      })
      originalRules.value.push(field)
    })
    form.value.rules = res
    console.log(form.value.rules)
  } catch (error) {
    console.error('获取策略详情失败:', error)
  }
}

// 获取规则组合列表
const getStrategyAllList = async () => {
  combinationLoading.value = true
  let res = await AutomationAdvertisementStrategyApi.getStrategyAll({
    pageNo: 1,
    pageSize: 1000,
    strategyId: route.query.id,
    ...filterParams.value
  })
  // 处理返回的数据
  combinationList.value = res.list.map((item) => {
    // 解析预算操作字符串
    const budgetOperation = parseBudgetOperation(item.budgetOperation)
    // 解析CPC操作字符串
    const cpcOperation = parseCpcOperation(item.cpcOperation)

    // 构建条件摘要
    const conditions = []
    if (item.clicks) conditions.push({ name: '点击', value: item.clicks })
    if (item.cpcLocalCurrency) conditions.push({ name: 'CPC-本币', value: item.cpcLocalCurrency })
    if (item.ctr) conditions.push({ name: 'CTR', value: item.ctr })
    if (item.cvr) conditions.push({ name: 'CVR', value: item.cvr })
    if (item.acos) conditions.push({ name: 'ACoS', value: item.acos })
    if (item.matchType) conditions.push({ name: '匹配方式', value: item.matchType })
    if (item.budgetUsageRate) conditions.push({ name: '预算使用率', value: item.budgetUsageRate })
    return {
      id: item.id,
      conditions,
      budgetOperation,
      cpcOperation,
      // 其他需要的字段
      rule: item.rule,
      eigenvalue: item.eigenvalue,
      strategyId: item.strategyId
    }
  })
  filterCount.value = res.total
  combinationLoading.value = false

  // try {
  // } finally {
  //   combinationLoading.value = false
  // }
}

// 将正则表达式提取为常量
const OPERATION_REGEX = {
  VALUE: /(\d+\.?\d*)/,
  ACTION: /(加|减)/,
  UNIT: /(%)/
}

// 解析预算操作字符串，例如："预算加 20 %"
const parseBudgetOperation = (operationStr) => {
  if (!operationStr || operationStr.includes('不变')) {
    return { type: 'keep', value: 0, unit: 'percent', isEditing: false }
  }

  const action = OPERATION_REGEX.ACTION.test(operationStr)
    ? OPERATION_REGEX.ACTION.exec(operationStr)[1]
    : 'keep'

  const valueMatch = OPERATION_REGEX.VALUE.exec(operationStr)
  const value = valueMatch ? parseFloat(valueMatch[1]) : 0

  const unit = OPERATION_REGEX.UNIT.test(operationStr) ? 'percent' : 'value'

  return {
    type: action === '加' ? 'increase' : 'decrease',
    value,
    unit,
    isEditing: false
  }
}

// 解析CPC操作字符串
const parseCpcOperation = (operationStr) => {
  // 与预算操作解析逻辑类似
  if (!operationStr) {
    return { type: 'keep', value: 0, unit: 'percent', isEditing: false }
  }

  const result = { type: 'keep', value: 0, unit: 'percent', isEditing: false }

  if (operationStr.includes('加')) {
    result.type = 'increase'
  } else if (operationStr.includes('减')) {
    result.type = 'decrease'
  } else {
    return result
  }

  // 提取数值
  const valueMatch = operationStr.match(/\d+(\.\d+)?/)
  if (valueMatch) {
    result.value = parseFloat(valueMatch[0])
  }

  // 判断单位
  if (operationStr.includes('%')) {
    result.unit = 'percent'
  } else {
    result.unit = 'value'
  }

  return result
}

// 将操作对象转换为字符串，用于提交到后端
const formatBudgetOperation = (operation) => {
  if (!operation || operation.type === 'keep') {
    return '预算不变'
  }

  const action = operation.type === 'increase' ? '加' : '减'
  const unit = operation.unit === 'percent' ? '%' : ''

  return `预算${action} ${operation.value} ${unit}`.trim()
}

// 将CPC操作对象转换为字符串
const formatCpcOperation = (operation) => {
  if (!operation || operation.type === 'keep') {
    return 'CPC不变'
  }

  const action = operation.type === 'increase' ? '加' : '减'
  const unit = operation.unit === 'percent' ? '%' : ''

  return `CPC${action} ${operation.value} ${unit}`.trim()
}

// 保存单个组合的修改
const handleSaveOperation = async (row) => {
  console.log(row)
  try {
    // 构建提交的数据
    const params = [
      {
        id: row.id,
        strategyId: row.strategyId,
        budgetOperation: formatBudgetOperation(row.budgetOperation),
        cpcOperation: formatCpcOperation(row.cpcOperation)
      }
    ]

    // 调用保存接口
    const res = await AutomationAdvertisementStrategyApi.updateAllBatch(params)
    try {
      ElMessage.success('保存成功')
      // 重置编辑状态
      row.budgetOperation.isEditing = false
      row.cpcOperation.isEditing = false
    } catch (error) {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败', error)
  }
}

// 批量保存
const saveBatchOperations = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要编辑的行')
    return
  }

  try {
    combinationLoading.value = true
    // 获取选中的行
    const params = selectedRows.value.map((item) => ({
      id: item.id,
      budgetOperation: formatBudgetOperation(batchEditData.value.budgetOperation),
      cpcOperation: formatCpcOperation(batchEditData.value.cpcOperation),
      strategyId: item.strategyId
    }))

    // 调用批量保存接口
    const res = await AutomationAdvertisementStrategyApi.updateAllBatch(params)
    try {
      ElMessage.success('批量保存成功')
      batchEditVisible.value = false

      // 更新选中行的数据
      selectedRows.value.forEach((row) => {
        const item = combinationList.value.find((i) => i.id === row.id)
        if (item) {
          item.budgetOperation = { ...batchEditData.value.budgetOperation, isEditing: false }
          item.cpcOperation = { ...batchEditData.value.cpcOperation, isEditing: false }
        }
      })
      getData()
      getStrategyAllList()
    } catch (error) {
      console.error('批量保存失败', error)
    }
  } catch (error) {
    console.error('批量保存失败', error)
  } finally {
    combinationLoading.value = false
  }
}

const columns = [
  {
    key: 'selection',
    title: '',
    width: 55,
    cellRenderer: ({ rowData }) => (
      <ElCheckbox
        modelValue={selectedRows.value.some((r) => r.id === rowData.id)}
        onChange={() => handleSelectionChange(rowData)}
      />
    ),
    headerCellRenderer: () => (
      <ElCheckbox
        modelValue={
          combinationList.value.length > 0 &&
          selectedRows.value.length === combinationList.value.length
        }
        indeterminate={
          selectedRows.value.length > 0 && selectedRows.value.length < combinationList.value.length
        }
        onChange={(val) => handleSelectAll(val)}
      />
    )
  },
  {
    key: 'id',
    title: '组合ID',
    width: 80,
    dataKey: 'id'
  },
  {
    key: 'conditions',
    title: '条件摘要',
    width: 200,
    cellRenderer: ({ rowData }) => (
      <div class="condition-summary">
        {rowData.conditions.map((item, index) => (
          <div key={index} class="condition-item">
            <span class="condition-name">{item.name}:</span>
            <span class="condition-value">{item.value}</span>
          </div>
        ))}
      </div>
    )
  },
  // 预算操作列
  {
    key: 'budgetOperation',
    title: '预算操作',
    width: 300,
    cellRenderer: ({ rowData }) => (
      <div class="operation-container">
        {rowData.budgetOperation.isEditing ? (
          <div class="operation-input">
            <el-input
              type="number"
              modelValue={rowData.budgetOperation.value}
              onUpdate:modelValue={(val) => (rowData.budgetOperation.value = val)}
              size="small"
              class="w-80px mr-5px"
              min="0"
              setp="1"
              onChange={() => handleBudgetChange}
              onInput={() =>
                (rowData.budgetOperation.value =
                  rowData.budgetOperation.value < 0 ? 0 : rowData.budgetOperation.value)
              }
            >
              {{
                prepend: () => (
                  <el-select
                    modelValue={rowData.budgetOperation.type}
                    onUpdate:modelValue={(val) => (rowData.budgetOperation.type = val)}
                    size="small"
                    class="mr-5px"
                    style={{ width: '70px' }}
                  >
                    <el-option label="增加" value="increase" />
                    <el-option label="减少" value="decrease" />
                    <el-option label="不变" value="keep" />
                  </el-select>
                ),
                append: () => (
                  <el-select
                    modelValue={rowData.budgetOperation.unit}
                    onUpdate:modelValue={(val) => (rowData.budgetOperation.unit = val)}
                    size="small"
                    class="mr-5px"
                    style={{ width: '70px' }}
                  >
                    <el-option label="数值" value="value" />
                    <el-option label="百分比" value="percent" />
                  </el-select>
                )
              }}
            </el-input>
            <el-button type="primary" size="small" onClick={() => handleBudgetBlur(rowData)}>
              确认
            </el-button>
          </div>
        ) : (
          <div
            class={[
              'budget-display',
              {
                increase: rowData.budgetOperation.type === 'increase',
                decrease: rowData.budgetOperation.type === 'decrease',
                keep: rowData.budgetOperation.type === 'keep'
              }
            ]}
            onClick={() => handleBudgetFocus(rowData)}
          >
            {getBudgetDisplayText(rowData.budgetOperation)}
          </div>
        )}
      </div>
    )
  },

  // CPC操作列
  {
    key: 'cpcOperation',
    title: 'CPC操作',
    width: 300,
    cellRenderer: ({ rowData }) => (
      <div class="operation-container">
        {rowData.cpcOperation.isEditing ? (
          <div class="operation-input">
            <el-input
              type="number"
              modelValue={rowData.cpcOperation.value}
              onUpdate:modelValue={(val) => (rowData.cpcOperation.value = val)}
              size="small"
              min="0"
              setp="1"
              class="w-80px mr-5px"
              onInput={() =>
                (rowData.cpcOperation.value =
                  rowData.cpcOperation.value < 0 ? 0 : rowData.cpcOperation.value)
              }
            >
              {{
                prepend: () => (
                  <el-select
                    modelValue={rowData.cpcOperation.type}
                    onUpdate:modelValue={(val) => (rowData.cpcOperation.type = val)}
                    size="small"
                    class="mr-5px"
                    style={{ width: '70px' }}
                  >
                    <el-option label="增加" value="increase" />
                    <el-option label="减少" value="decrease" />
                    <el-option label="不变" value="keep" />
                  </el-select>
                ),
                append: () => (
                  <el-select
                    modelValue={rowData.cpcOperation.unit}
                    onUpdate:modelValue={(val) => (rowData.cpcOperation.unit = val)}
                    size="small"
                    class="mr-5px"
                    style={{ width: '70px' }}
                  >
                    <el-option label="数值" value="value" />
                    <el-option label="百分比" value="percent" />
                  </el-select>
                )
              }}
            </el-input>
            <el-button type="primary" size="small" onClick={() => handleCpcBlur(rowData)}>
              确认
            </el-button>
          </div>
        ) : (
          <div
            class={[
              'cpc-display',
              {
                increase: rowData.cpcOperation.type === 'increase',
                decrease: rowData.cpcOperation.type === 'decrease',
                keep: rowData.cpcOperation.type === 'keep'
              }
            ]}
            onClick={() => handleCpcFocus(rowData)}
          >
            {getCpcDisplayText(rowData.cpcOperation)}
          </div>
        )}
      </div>
    )
  }
]

// 选中的行数据，初始为空数组，确保不会默认全选
const selectedRows = ref([])

// 处理全选/取消全选
const handleSelectAll = (checked) => {
  // 根据checked状态更新所有行的选中状态
  if (checked) {
    // 全选：将所有行添加到selectedRows
    selectedRows.value = [...combinationList.value]
  } else {
    // 取消全选：清空selectedRows
    selectedRows.value = []
  }
}

// 处理单行选择变化
const handleSelectionChange = (rowData) => {
  const index = selectedRows.value.findIndex((r) => r.id === rowData.id)
  if (index === -1) {
    // 如果行不在选中数组中，添加它
    selectedRows.value.push(rowData)
  } else {
    // 如果行已在选中数组中，移除它
    selectedRows.value.splice(index, 1)
  }
}

onMounted(() => {
  getData()
  getStrategyAllList()
})
</script>

<style lang="scss" scoped>
.filter-options {
  padding: 10px;
}

::v-deep(.filter-option-group) {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
  gap: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  .filter-option-title {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
}

// 筛选条件样式
::v-deep(.filter-container) {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 0;
  background: #fff;
}

.filter-trigger {
  display: flex;
  align-items: center;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.error-message {
  margin: 5px 0;

  .el-alert {
    padding: 5px 8px;
    font-size: 12px;
  }
}

.batch-edit-container {
  padding: 10px;

  .section-title {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
  }

  .edit-section {
    margin-bottom: 15px;
  }
}

// 状态标签样式
::v-deep(.increase),
::v-deep(.decrease),
::v-deep(.keep) {
  display: inline-block;
  min-width: 80px;
  padding: 5px 10px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;

  &.increase {
    color: #67c23a;
    background-color: rgb(103 194 58 / 10%);
    border: 1px solid rgb(103 194 58 / 20%);
  }

  &.decrease {
    color: #f56c6c;
    background-color: rgb(245 108 108 / 10%);
    border: 1px solid rgb(245 108 108 / 20%);
  }

  &.keep {
    color: #909399;
    background-color: rgb(144 147 153 / 10%);
    border: 1px solid rgb(144 147 153 / 20%);
  }
}

::v-deep(.batch-edit-container) ::v-deep(.operation-container) {
  display: flex;
  align-items: center;
  gap: 5px; // 添加间距提升可读性

  ::v-deep(.el-select),
  ::v-deep(.el-input) {
    width: 80px !important;
    height: 30px;
    padding: 0 6px;
    margin-right: 5px;
    line-height: 30px;

    &:last-child {
      margin-right: 0;
    }
  }

  ::v-deep(.el-input__inner) {
    height: 30px !important;
    padding: 0 6px;
    line-height: 30px !important;
  }
}

// 表格行状态
::v-deep(.error-row),
::v-deep(.change-row) {
  --el-table-tr-bg-color: var(--el-color-error-light-9);
  --el-table-tr-bg-hover: var(--el-color-error-light-7);

  &:hover {
    --el-table-tr-bg-color: var(--el-color-error-light-7);
  }
}

::v-deep(.change-row) {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
  --el-table-tr-bg-hover: var(--el-color-warning-light-7);
}

.top-table {
  // 输入框样式重置
  ::v-deep(.el-input-group__prepend),
  ::v-deep(.el-input-group__append),
  ::v-deep(.el-input__wrapper) {
    padding: 0 6px;
    margin: 0;
    background: #fff;
    box-shadow: none !important;
  }

  ::v-deep(.el-input-group__prepend) {
    border: 1px var(--el-border-color) solid;
    border-right: 0;
  }

  ::v-deep(.el-input-group__append) {
    border: 1px var(--el-border-color) solid;
    border-left: 0;
  }

  ::v-deep(.el-input__wrapper) {
    border: 1px var(--el-border-color) solid;
    border-right: 0;
    border-left: 0;
  }

  // 高亮按钮
  ::v-deep(.highlight-btn) {
    font-weight: bold;
    color: #e6a23c !important;
  }
}

// 条件摘要样式
::v-deep(.condition-summary) {
  display: flex;
  flex-wrap: wrap;
  width: 150px;
  justify-content: space-between;
  padding: 4px 0;

  // 输入框样式重置
  ::v-deep(.el-input-group__prepend),
  ::v-deep(.el-input-group__append),
  ::v-deep(.el-input__wrapper) {
    padding: 0 6px;
    margin: 0;
    background: #fff;
  }

  .condition-item {
    display: flex;
    width: 100%;
    padding: 2px 6px;
    font-size: 12px;
    white-space: nowrap;
    background-color: #f5f7fa;
    border-radius: 4px;
    align-items: center;
    justify-content: space-between;

    .condition-name {
      margin-right: 4px;
      font-weight: bold;
    }

    .condition-value {
      color: var(--el-color-primary);
    }
  }
}
</style>
