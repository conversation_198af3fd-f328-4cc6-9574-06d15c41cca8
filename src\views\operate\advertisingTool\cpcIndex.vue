<template>
  <ContentWrap class="keyword-bidding-container">
    <el-form :model="formData">
      <el-form-item required label="店铺" prop="sid" class="flex items-center">
        <template #label>
          <div class="flex items-center pl-2">
            <span class="pr-2">店铺</span>
            <el-tooltip content="此店铺数据只查询自身的店铺" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-select
          v-model="formData.sid"
          placeholder="请选择店铺"
          clearable
          filterable
          class="!w-200px"
        >
          <el-option
            v-for="item in shopList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span>{{ item.label }} - {{ item.country }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配方式：" required prop="matchTypes" class="flex items-center mt-10px">
        <el-checkbox-group v-model="formData.matchTypes">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_MATCH_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="" required prop="matchTypes" class="flex items-center mt-10px">
        <RowTextInput
          v-model="formData.inputText"
          placeholder="请输入关键词，一行一个，回车换行，最多1000个关键词"
          :maxCount="1000"
          :rows="10"
          ref="keywordInputRef"
          @change="handleInput"
        />
      </el-form-item>
    </el-form>

    <div class="action-buttons">
      <el-button type="primary" @click="queryKeywords">查询</el-button>
      <el-button @click="exportToExcel">导出Excel</el-button>
    </div>

    <el-table
      :data="tableData"
      border
      v-loading="tableLoading"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="keyword" label="关键词" />
      <el-table-column prop="matchType" label="匹配类型" />
      <el-table-column prop="bid" label="建议竞价">
        <template #default="{ row }"> {{ row.currencySymbol }} {{ row.bid }} </template>
      </el-table-column>
      <el-table-column prop="rangeEnd" label="上限">
        <template #default="{ row }"> {{ row.currencySymbol }} {{ row.rangeEnd }} </template>
      </el-table-column>
      <el-table-column prop="rangeStart" label="下限">
        <template #default="{ row }"> {{ row.currencySymbol }} {{ row.rangeStart }} </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ThirdPartyAccountApi } from '@/api/system/third'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { QuestionFilled } from '@element-plus/icons-vue'
import { ElIcon, ElMessage, ElTooltip } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import * as XLSX from 'xlsx'

// 店铺选项
const shopList = ref([])

// 查询参数
const formData = reactive({
  sid: '',
  matchTypes: ['0', 1, 2],
  inputText: '',
  keywords: [],
  appid: ''
})

const tableData = ref([])
const tableLoading = ref(false)
const handleInput = () => {
  // 这里可以处理输入的关键词
}

// 查询
const queryKeywords = async () => {
  if (!formData.sid) {
    ElMessage.warning('请选择店铺')
    return
  }
  if (!formData.matchTypes.length) {
    ElMessage.warning('请选择匹配类型')
    return
  }
  if (!formData.inputText.trim()) {
    ElMessage.warning('请输入关键词')
    return
  }
  // 这里应该是调用API查询关键词的逻辑
  const keywords = formData.inputText.split('\n').filter((k) => k.trim())

  // 使用Map进行去重，保留没有空格的版本
  const tableMap = new Map()
  keywords.forEach((item) => {
    // 如果是新的SKU或者当前SKU比已存在的更干净（没有空格），则更新
    const normalizedSku = item.trim()
    if (normalizedSku && !tableMap.has(normalizedSku)) {
      tableMap.set(normalizedSku, item)
    }
  })
  formData.appid = shopList.value.find((item) => item.value === formData.sid)?.appid
  tableLoading.value = true
  try {
    // 转换回数组并更新表单数据
    let data = JSON.parse(JSON.stringify(formData))
    data.keywords = Array.from(tableMap.keys())
    console.log(data)
    const res = await ThirdPartyAccountApi.cpcQuery(data)
    tableData.value = res
  } finally {
    tableLoading.value = false
  }
}

// 导出表格
const exportToExcel = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(
    tableData.value.map((item) => ({
      关键词: item.keyword,
      匹配类型: item.matchType,
      建议竞价: item.currencySymbol + item.bid,
      上限: item.currencySymbol + item.rangeEnd,
      下限: item.currencySymbol + item.rangeStart
    }))
  )

  // 设置列宽
  ws['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 15 }, { wch: 15 }]

  XLSX.utils.book_append_sheet(wb, ws, '关键词数据')

  // 生成文件并触发下载
  XLSX.writeFile(wb, `关键词竞价数据_${new Date().toLocaleDateString().replace(/g/, '-')}.xlsx`)
  ElMessage.success('导出成功')
}

// 获取店铺list
const getShopListFc = async () => {
  const res = await ThirdPartyAccountApi.getSellerBySelf()
  shopList.value = res.map((item) => ({
    ...item,
    value: item.sid,
    label: item.sellerItem
  }))
}

onMounted(() => {
  getShopListFc()
})
</script>

<style scoped>
.keyword-bidding-container {
  padding: 20px;
  margin: 0 auto;
}

.input-section {
  margin: 20px 0;
}

.action-buttons {
  margin-bottom: 20px;
}
</style>
