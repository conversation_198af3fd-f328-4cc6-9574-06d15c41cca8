<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- 新增多选，新增逻辑 -->
      <el-form-item
        label="国家"
        prop="countries"
        v-if="!formData.id"
        required
        error="请选择国家"
      >
        <el-select
          v-model="formData.countries"
          clearable
          filterable
          multiple
          placeholder="全部"
        >
          <el-option
            v-for="item in regionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 修改，单选，原有逻辑 -->
      <el-form-item
        label="国家"
        prop="country"
        v-if="formData.id"
        required
        error="请选择国家"
      >
        <el-select
          v-model="formData.country"
          clearable
          filterable
          placeholder="全部"
        >
          <el-option
            v-for="item in regionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="语言"
        prop="language"
      >
        <el-select
          filterable
          v-model="formData.language"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.AIM_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="分类"
        prop="categoryCode"
      >
        <el-select
          filterable
          v-model="formData.categoryCode"
          placeholder="请选择"
        >
          <el-tooltip
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_FORBIDDEN_WORDS_CATEGORY)"
            :key="dict.value"
            class="box-item"
            effect="dark"
            :content="typeContent(dict)"
            raw-content
            placement="right-end"
          >
            <el-option
              :label="dict.label"
              :value="dict.value"
            />
          </el-tooltip>

        </el-select>
      </el-form-item>
      <el-form-item
        label="违禁词"
        prop="forbiddenWord"
      >
        <el-input
          v-model="formData.forbiddenWord"
          placeholder="请输入违禁词"
          type="text"
        />
      </el-form-item>
      <el-form-item
        label="中文翻译"
        prop="chineseTranslation"
      >
        <el-input
          v-model="formData.chineseTranslation"
          placeholder="请输入中文翻译"
          type="text"
        />
      </el-form-item>
      <el-form-item
        label="备注"
        prop="remark"
      >
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button
        type="primary"
        @click="submitForm"
        :loading="formLoading"
      >确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { DICT_TYPE, getDictObj, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import {
  updateForbiddenWordApi,
  createForbiddenWordApi,
  getForbiddenWordDetailApi
} from '@/api/operate/forbiddenWords'

// 国家选项
const regionOptions = ref([])

// 获取国家列表
const getCountryListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getCountryList()
    if (res) {
      // 将接口返回的国家数据转换为下拉框选项格式
      regionOptions.value = res.map((item) => ({
        label: item.country,
        value: item.country
      }))
    }
  } catch (error) {
    console.error('获取国家列表失败', error)
  }
}
const props = defineProps({
  id: {
    type: Number,
    default: undefined
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.id ? '编辑违禁词' : '新增违禁词'
})

const formRef = ref<FormInstance>()
const formLoading = ref(false)
const formData = reactive({
  id: undefined,
  countries: [],
  country: [],
  language: '',
  categoryCode: '',
  forbiddenWord: '',
  chineseTranslation: '',
  remark: ''
})

const formRules = reactive<FormRules>({
  language: [{ required: true, message: '请选择语言', trigger: 'change' }],
  categoryCode: [{ required: true, message: '请选择分类', trigger: 'change' }],
  forbiddenWord: [{ required: true, message: '请输入违禁词', trigger: 'blur' }]
  // chineseTranslation: [{ required: true, message: '请输入中文翻译', trigger: 'blur' }]
})

// 处理类型提示文案
const typeContent = (dict) => {
  let dictItem = getDictObj(DICT_TYPE.OPERATION_FORBIDDEN_WORDS_CATEGORY, dict.value)
  return dictItem && dictItem.remark
    ? `<div>${dictItem?.remark.replace(/\n/g, '<br>')}</div>`
    : '暂无说明'
}

/** 表单校验 */
const validate = async () => {
  return await formRef.value?.validate()
}

/** 表单提交 */
const submitForm = async () => {
  try {
    if (formData.id && !formData.country) {
      return ElMessage.error('请选择国家')
    } else if (!formData.id && formData.countries.length === 0) {
      return ElMessage.error('请选择国家')
    }
    // 校验表单
    await validate()
    formLoading.value = true
    formData.categoryName = getDictLabel(
      DICT_TYPE.OPERATION_FORBIDDEN_WORDS_CATEGORY,
      formData.categoryCode
    )
    // 提交表单
    if (props.id) {
      // 编辑模式
      await updateForbiddenWordApi(formData)
      ElMessage.success('修改成功')
    } else {
      // 新增模式
      await createForbiddenWordApi(formData)
      ElMessage.success('新增成功')
    }

    // 关闭弹窗，刷新列表
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error(error)
    ElMessage.error(props.id ? '修改失败' : '新增失败')
  } finally {
    formLoading.value = false
  }
}

/** 获取详情 */
const getDetail = async (id: number) => {
  try {
    // 调用获取详情API
    const res = await getForbiddenWordDetailApi(id)
    res.categoryCode = +res.categoryCode
    Object.assign(formData, res)
  } catch (error) {
    console.error(error)
    ElMessage.error('获取违禁词详情失败')
    // 关闭弹窗
    dialogVisible.value = false
  }
}

/** 监听ID变化，加载详情 */
watch(
  () => props.id,
  (val) => {
    getCountryListFc()
    if (val) {
      // 编辑模式，获取详情
      getDetail(val)
    } else {
      // 新增模式，重置表单
      formRef.value?.resetFields()
      Object.assign(formData, {
        id: undefined,
        countries: [],
        country: '',
        language: '',
        categoryCode: '',
        forbiddenWord: '',
        chineseTranslation: '',
        remark: ''
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.add-forbidden-words {
  padding: 10px;

  .section-title {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
  }

  .operation-container {
    margin-bottom: 15px;
  }
}
</style>
