<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="团队" prop="tenantIds">
        <el-select
          v-model="queryParams.tenantIds"
          placeholder="请选择团队"
          clearable
          filterable
          class="!w-240px"
        >
          <!-- <el-option
            label="全部"
            value=""
          /> -->
          <el-option
            v-for="item in tenantList"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-select
          v-model="queryParams.country"
          class="!w-240px"
          clearable
          filterable
          placeholder="全部"
        >
          <!-- <el-option
            label="全部"
            value=""
          /> -->
          <el-option
            v-for="item in regionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="语言" prop="language">
        <el-select
          filterable
          class="!w-240px"
          v-model="queryParams.language"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.AIM_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="categoryCode">
        <el-select
          filterable
          class="!w-240px"
          v-model="queryParams.categoryCode"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_FORBIDDEN_WORDS_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="违禁词" prop="forbiddenWord">
        <el-input
          v-model="queryParams.forbiddenWord"
          placeholder="请输入违禁词"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="approvalStatusCodeList">
        <el-select
          v-model="queryParams.approvalStatusCodeList"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <!-- <el-option
            label="全部"
            value=""
          /> -->
          <el-option label="已审批" value="1" />
          <el-option label="未审批" value="10" />
        </el-select>
      </el-form-item>
      <el-form-item label="添加人" prop="creatorName">
        <el-input
          v-model="queryParams.creatorName"
          placeholder="请输入添加人"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批人" prop="approvalName">
        <el-input
          v-model="queryParams.approvalName"
          placeholder="请输入审批人"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="添加时间" prop="createTime">
        <shortcut-date-range-picker isTimes v-model="queryParams.createTime" />
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <shortcut-date-range-picker isTimes v-model="queryParams.updateTime" />
      </el-form-item>
      <el-form-item label="审批时间" prop="approvalTime">
        <shortcut-date-range-picker isTimes v-model="queryParams.approvalTime" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="flex mt-20px mb-20px">
      <el-button
        type="primary"
        @click="openForm()"
        v-hasPermi="['operation:forbidden-words:create']"
      >
        添加违禁词
      </el-button>
      <!-- <el-button
        type=""
        @click="handleBatchApprove"
        v-hasPermi="['operation:forbidden-words:approval-batch']"
      >
        批量审批
      </el-button> -->
      <el-button type="" @click="handleImport" v-hasPermi="['operation:forbidden-words:import']">
        导入
      </el-button>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column label="团队" align="center" prop="tenantName" width="120" />
      <el-table-column label="国家" align="left" prop="country" width="120" />
      <el-table-column label="语言" align="left" prop="language" width="120" />
      <el-table-column label="分类" align="left" prop="categoryName" width="150" />
      <el-table-column label="违禁词" align="left" prop="forbiddenWord">
        <template #default="{ row }">
          <div class="flex flex-col">
            <div>{{ row.forbiddenWord ? row.forbiddenWord : '-' }}</div>
            <div>({{ row.chineseTranslation ? row.chineseTranslation : '-' }})</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="approvalStatusName" width="100" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="添加人" align="center" prop="creatorName" width="120" />
      <el-table-column label="审批人" align="center" prop="approvalName" width="120" />
      <el-table-column label="添加时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ formatDate(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template #default="{ row }">
          <span>{{ formatDate(row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批时间" align="center" prop="approvalTime" width="180">
        <template #default="{ row }">
          <span>{{ formatDate(row.approvalTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="180">
        <template #default="{ row }">
          <!-- v-if="row.approvalStatusCode != 1" -->
          <el-button
            type="primary"
            link
            @click="openForm(row.id)"
            v-hasPermi="['operation:forbidden-words:update']"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.approvalStatusCode != 1"
            type="danger"
            link
            @click="handleDelete(row.id)"
            v-hasPermi="['operation:forbidden-words:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-if="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单弹窗：添加/修改 -->
    <ForbiddenWordsForm v-if="formVisible" :id="formId" v-model="formVisible" @success="getList" />

    <!-- 导入 -->
    <ForbiddenWordsImportForm ref="importFormRef" @success="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

import {
  batchApproveForbiddenWordApi,
  deleteForbiddenWordApi,
  getAllTenant,
  getForbiddenWordPageApi
} from '@/api/operate/forbiddenWords'
import Pagination from '@/components/Pagination/index.vue'
import { formatDate } from '@/utils/formatTime'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import ForbiddenWordsForm from './form.vue'
import ForbiddenWordsImportForm from './importForm.vue'

const importFormRef = ref(null)
defineOptions({ name: 'OperateForbiddenWords' })

const loading = ref(false)
const total = ref(0)
const list = ref([])
const queryFormRef = ref()
const exportLoading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  tenantIds: '',
  country: '',
  language: '',
  categoryCode: '',
  forbiddenWord: '',
  approvalStatusCodeList: '',
  creatorName: '',
  approvalName: '',
  createTime: [],
  updateTime: [],
  approvalTime: []
})

// 团队列表
const tenantList = ref([])

const selectedRows = ref([])

// 表单参数
const formVisible = ref(false)
const formId = ref<number>()

// 国家选项
const regionOptions = ref([])

// 获取国家列表
const getCountryListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getCountryList()
    if (res) {
      // 将接口返回的国家数据转换为下拉框选项格式
      regionOptions.value = res.map((item) => ({
        label: item.country,
        value: item.country
      }))
    }
  } catch (error) {
    console.error('获取国家列表失败', error)
  }
}

// 列表选择数据
const handleSelectionChange = (val: any) => {
  selectedRows.value = val
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 处理日期范围参数
    const params = { ...queryParams }

    // 调用API获取数据
    const res = await getForbiddenWordPageApi(params)
    list.value = res.list
    total.value = res.total
    const red = await getAllTenant()
    tenantList.value = red.map((item) => {
      return {
        ...item,
        label: item.name,
        value: item.id
      }
    })
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = (id?: number) => {
  formId.value = id
  formVisible.value = true
}

/** 删除按钮操作 */
const handleDelete = (id: number) => {
  ElMessageBox.confirm('确认要删除该违禁词吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteForbiddenWordApi(id)
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      console.error(error)
    }
  })
}

/** 导入按钮操作 */
const handleImport = () => {
  importFormRef.value?.open()
}

/** 上传失败处理 */
const handleUploadError = () => {}

/** 批量审批操作 */
const handleBatchApprove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  const ids = selectedRows.value.map((item: any) => item.id)
  ElMessageBox.confirm('确认要审批选中的违禁词吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用批量审批API
      await batchApproveForbiddenWordApi(ids)
      ElMessage.success('批量审批成功')
      await getList()
    } catch (error) {
      console.error(error)
    }
  })
}
onMounted(() => {
  getCountryListFc()
  getList()
})
</script>
