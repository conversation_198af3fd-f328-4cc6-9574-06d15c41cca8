<template>
  <Dialog
    v-model="dialogVisible"
    title="分配参与人"
    width="400px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="80px"
    >
      <el-form-item
        label="参与人"
        prop="participantIds"
        :rules="[{ required: true, message: '请选择参与人', trigger: 'change' }]"
      >
        <el-select
          v-model="formData.participantIds"
          placeholder="请选择参与人 (可多选)"
          multiple
          filterable
          collapse-tags
          collapse-tags-tooltip
          :loading="userLoading"
          class="w-full"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname"
            :value="user.nickname"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >分配</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { getUserListNoPermission } from '@/api/system/user'
import { ElMessage } from 'element-plus'
import { assignParticipants } from '@/api/tool/tableData'

defineOptions({ name: 'AssignDialog' })

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const userLoading = ref(false)
const userOptions = ref([])
const selectedRows = ref([])

const formData = reactive({
  participantIds: []
})

// 获取运营人员列表
const getOperatorList = async () => {
  userLoading.value = true
  try {
    const res = await getUserListNoPermission()
    if (res && Array.isArray(res)) {
      userOptions.value = res.map((item) => ({
        id: item.id,
        nickname: item.nickname
      }))
    }
  } catch (error) {
    console.error('获取运营人员列表失败', error)
  } finally {
    userLoading.value = false
  }
}
const templateId = ref(null)
// 打开弹窗
const open = (rows, tmpid, isBatch = false) => {
  templateId.value = tmpid
  selectedRows.value = rows

  // 如果是单行操作且有参与人数据，则回显
  if (!isBatch && rows.length === 1) {
    const row = rows[0]
    // 从行数据中获取参与人字段，需要根据实际字段名调整
    const participantsField = Object.entries(row.resultNameMap).find(
      ([k, v]) => v === '参与人'
    )?.[0]
    if (participantsField && row[participantsField]) {
      // 如果参与人数据是字符串（多个参与人用逗号分隔），则分割成数组
      const participants =
        typeof row[participantsField] === 'string'
          ? row[participantsField]
              .split(',')
              .map((p) => p.trim())
              .filter((p) => p)
          : Array.isArray(row[participantsField])
          ? row[participantsField]
          : []

      formData.participantIds = participants
    } else {
      formData.participantIds = []
    }
  } else {
    // 批量操作不回显
    formData.participantIds = []
  }

  dialogVisible.value = true
  getOperatorList()
}

// 提交分配
const handleSubmit = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  if (selectedRows.value.length === 0) {
    ElMessage.error('没有选中的数据')
    return
  }

  submitLoading.value = true
  try {
    // 调用分配参与人接口
    const requestData = {
      participants: formData.participantIds,
      rowIds: selectedRows.value.map((row) => row.id),
      templateId: templateId.value
    }

    await assignParticipants(requestData)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('分配失败', error)
    ElMessage.error('分配失败')
  } finally {
    submitLoading.value = false
  }
}

defineExpose({
  open
})
</script>
