"header":[
    {
        "fieldId": "1933136397201563648",
        "fieldName": "图片",
        "paramTypeCode": "file",
        "paramTypeName": "文件 ",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563648",
        "align": "left",
        "title": "图片",
        "label": "图片",
        "slotsName": "image"
    },
    {
        "fieldId": "1933136397201563649",
        "fieldName": "产品名称",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563649",
        "align": "left",
        "title": "产品名称",
        "label": "产品名称",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563650",
        "fieldName": "核心词",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563650",
        "align": "left",
        "title": "核心词",
        "label": "核心词",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563651",
        "fieldName": "关键词",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563651",
        "align": "left",
        "title": "关键词",
        "label": "关键词",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563652",
        "fieldName": "产品参数",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563652",
        "align": "left",
        "title": "产品参数",
        "label": "产品参数",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563653",
        "fieldName": "特殊说明",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563653",
        "align": "left",
        "title": "特殊说明",
        "label": "特殊说明",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563654",
        "fieldName": "SKU",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563654",
        "align": "left",
        "title": "SKU",
        "label": "SKU",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563655",
        "fieldName": "核心关键词",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563655",
        "align": "left",
        "title": "核心关键词",
        "label": "核心关键词",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563656",
        "fieldName": "仓库库存",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 1,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563656",
        "align": "left",
        "title": "仓库库存",
        "label": "仓库库存",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563657",
        "fieldName": "店铺",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "0",
        "categoryName": "基本信息",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563657",
        "align": "left",
        "title": "店铺",
        "label": "店铺",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563658",
        "fieldName": "竞品链接",
        "paramTypeCode": "href",
        "paramTypeName": "链接",
        "categoryCode": "1",
        "categoryName": "采购管理",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563658",
        "align": "left",
        "title": "竞品链接",
        "label": "竞品链接",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563659",
        "fieldName": "竞品链接1",
        "paramTypeCode": "href",
        "paramTypeName": "链接",
        "categoryCode": "1",
        "categoryName": "采购管理",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563659",
        "align": "left",
        "title": "竞品链接1",
        "label": "竞品链接1",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563660",
        "fieldName": "竞品链接2",
        "paramTypeCode": "href",
        "paramTypeName": "链接",
        "categoryCode": "1",
        "categoryName": "采购管理",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563660",
        "align": "left",
        "title": "竞品链接2",
        "label": "竞品链接2",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563661",
        "fieldName": "汇率",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "1",
        "categoryName": "采购管理",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563661",
        "align": "left",
        "title": "汇率",
        "label": "汇率",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563662",
        "fieldName": "创建人",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563662",
        "align": "left",
        "title": "创建人",
        "label": "创建人",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563663",
        "fieldName": "创建时间",
        "paramTypeCode": "time",
        "paramTypeName": "时间 ",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563663",
        "align": "left",
        "title": "创建时间",
        "label": "创建时间",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563664",
        "fieldName": "审核人",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563664",
        "align": "left",
        "title": "审核人",
        "label": "审核人",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563665",
        "fieldName": "审核时间",
        "paramTypeCode": "time",
        "paramTypeName": "时间 ",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563665",
        "align": "left",
        "title": "审核时间",
        "label": "审核时间",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563666",
        "fieldName": "审核状态",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563666",
        "align": "left",
        "title": "审核状态",
        "label": "审核状态",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563667",
        "fieldName": "审核备注",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563667",
        "align": "left",
        "title": "审核备注",
        "label": "审核备注",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563668",
        "fieldName": "是否投产",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563668",
        "align": "left",
        "title": "是否投产",
        "label": "是否投产",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563669",
        "fieldName": "核心词",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563669",
        "align": "left",
        "title": "核心词",
        "label": "核心词",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563670",
        "fieldName": "关键词",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563670",
        "align": "left",
        "title": "关键词",
        "label": "关键词",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563671",
        "fieldName": "产品参数",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563671",
        "align": "left",
        "title": "产品参数",
        "label": "产品参数",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563672",
        "fieldName": "特殊说明",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563672",
        "align": "left",
        "title": "特殊说明",
        "label": "特殊说明",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563673",
        "fieldName": "备注",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 1,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563673",
        "align": "left",
        "title": "备注",
        "label": "备注",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563674",
        "fieldName": "负责人",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563674",
        "align": "left",
        "title": "负责人",
        "label": "负责人",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563675",
        "fieldName": "参与人",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 0,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563675",
        "align": "left",
        "title": "参与人",
        "label": "参与人",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563676",
        "fieldName": "汇率",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 1,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563676",
        "align": "left",
        "title": "汇率",
        "label": "汇率",
        "slotsName": ""
    },
    {
        "fieldId": "1933136397201563677",
        "fieldName": "listing文案状态",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 1,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563677",
        "align": "left",
        "title": "listing文案状态",
        "label": "listing文案状态",
        "slotsName": "listingCopyStatus"
    },
    {
        "fieldId": "1933136397201563678",
        "fieldName": "上架状态",
        "paramTypeCode": "input",
        "paramTypeName": "文本",
        "categoryCode": "6",
        "categoryName": "其它",
        "required": 1,
        "edit": 1,
        "needUnit": 0,
        "templateId": "1933115785909575680",
        "field": "1933136397201563678",
        "align": "left",
        "title": "上架状态",
        "label": "上架状态",
        "slotsName": ""
    },
    {
        "field": "action",
        "align": "left",
        "label": "操作",
        "type": "actions",
        "slotName": "action1",
        "fixed": "right",
        "width": 320
    }
]

  "list": [
    {
      "rowId": "1933330620765118464",
      "tableHeadId": "1933115788111585280",
      "resultMap": {
        "1933136397201563663": "2025-06-12 20:26:48",
        "1933136397201563662": "admin",
        "1933136397201563661": "",
        "1933136397201563660": "https://www.amazon.co.jp/-/zh/dp/B0DQ4X6WTT",
        "1933136397201563659": "https://www.amazon.co.jp/-/zh/dp/B09XTSCW2W",
        "1933136397201563658": "https://www.amazon.co.jp/-/zh/dp/B0DWKCCBTY",
        "1933136397201563657": "2-王冬冬-中企日本-JP",
        "1933136397201563656": "",
        "1933136397201563655": "轻、舒适",
        "1933136397201563654": "PX202506122034",
        "1933136397201563653": "数字类放在最前",
        "1933136397201563652": "100g",
        "1933136397201563651": "舒适",
        "1933136397201563650": "轻盈",
        "1933136397201563649": "跑鞋",
        "1933136397201563648": "",
        "1933136397201563678": "",
        "1933136397201563677": "{\"rowIds\":1933330620765118464,\"productInfoId\":13581,\"status\":\"成功\"}",
        "1933136397201563676": "",
        "1933136397201563675": "",
        "1933136397201563674": "admin",
        "1933136397201563673": "",
        "1933136397201563672": "数字类放在最前",
        "1933136397201563671": "100g",
        "1933136397201563670": "舒适",
        "1933136397201563669": "轻盈",
        "1933136397201563668": "是",
        "1933136397201563667": "",
        "1933136397201563666": "已通过",
        "1933136397201563665": "2025-06-13 09:05:09",
        "1933136397201563664": "admin"
      },
      "resultNameMap": {
        "1933136397201563663": "创建时间",
        "1933136397201563662": "创建人",
        "1933136397201563661": "汇率",
        "1933136397201563660": "竞品链接2",
        "1933136397201563659": "竞品链接1",
        "1933136397201563658": "竞品链接",
        "1933136397201563657": "店铺",
        "1933136397201563656": "仓库库存",
        "1933136397201563655": "核心关键词",
        "1933136397201563654": "SKU",
        "1933136397201563653": "特殊说明",
        "1933136397201563652": "产品参数",
        "1933136397201563651": "关键词",
        "1933136397201563650": "核心词",
        "1933136397201563649": "产品名称",
        "1933136397201563648": "图片",
        "1933136397201563678": "上架状态",
        "1933136397201563677": "listing文案状态",
        "1933136397201563676": "汇率",
        "1933136397201563675": "参与人",
        "1933136397201563674": "负责人",
        "1933136397201563673": "备注",
        "1933136397201563672": "特殊说明",
        "1933136397201563671": "产品参数",
        "1933136397201563670": "关键词",
        "1933136397201563669": "核心词",
        "1933136397201563668": "是否投产",
        "1933136397201563667": "审核备注",
        "1933136397201563666": "审核状态",
        "1933136397201563665": "审核时间",
        "1933136397201563664": "审核人"
      }
    },
    {
      "rowId": "1933118308414332928",
      "tableHeadId": "1933115788111585280",
      "resultMap": {
        "1933115785976684564": "100g",
        "1933115785976684565": "数字类放在最前",
        "1933115785976684566": "",
        "1933115785976684567": "admin",
        "1933115785976684560": "",
        "1933115785976684561": "是",
        "1933115785976684562": "轻盈",
        "1933115785976684563": "舒适",
        "1933115785976684568": "",
        "1933115785976684569": "",
        "1933115785976684570": "{\"rowIds\":1933118308414332928,\"productInfoId\":13571,\"status\":\"已删除\"}",
        "1933115785976684571": "",
        "1933115785976684548": "100g",
        "1933115785976684549": "数字类放在最前",
        "1933115785976684550": "PX202506121857",
        "1933115785976684551": "轻、舒适",
        "1933115785976684544": "",
        "1933115785976684545": "跑鞋",
        "1933115785976684546": "轻盈",
        "1933115785976684547": "舒适",
        "1933115785976684556": "2025-06-12 18:55:22",
        "1933115785976684557": "admin",
        "1933115785976684558": "2025-06-12 19:04:37",
        "1933115785976684559": "已通过",
        "1933115785976684552": "",
        "1933115785976684553": "林加南-跟卖ZYY-BE",
        "1933115785976684554": "",
        "1933115785976684555": "admin"
      },
      "resultNameMap": {
        "1933115785976684564": "产品参数",
        "1933115785976684565": "特殊说明",
        "1933115785976684566": "备注",
        "1933115785976684567": "负责人",
        "1933115785976684560": "审核备注",
        "1933115785976684561": "是否投产",
        "1933115785976684562": "核心词",
        "1933115785976684563": "关键词",
        "1933115785976684568": "参与人",
        "1933115785976684569": "汇率",
        "1933115785976684570": "listing文案状态",
        "1933115785976684571": "上架状态",
        "1933115785976684548": "产品参数",
        "1933115785976684549": "特殊说明",
        "1933115785976684550": "SKU",
        "1933115785976684551": "核心关键词",
        "1933115785976684544": "图片",
        "1933115785976684545": "产品名称",
        "1933115785976684546": "核心词",
        "1933115785976684547": "关键词",
        "1933115785976684556": "创建时间",
        "1933115785976684557": "审核人",
        "1933115785976684558": "审核时间",
        "1933115785976684559": "审核状态",
        "1933115785976684552": "仓库库存",
        "1933115785976684553": "店铺",
        "1933115785976684554": "汇率",
        "1933115785976684555": "创建人"
      }
    }
  ],