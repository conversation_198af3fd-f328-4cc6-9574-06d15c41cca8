<template>
  <ContentWrap style="padding-top: 0 !important" v-loading="templateOptions.length === 0">
    <!-- 投产表选择器和折叠控制 -->
    <div class="flex items-center mb-10px gap-10px">
      <el-form-item prop="developmentTable" style="margin-bottom: 0 !important">
        <el-select
          v-model="queryParams.templateId"
          placeholder="请选择投产表"
          clearable
          class="!w-240px"
          @change="handleTemplateChange"
        >
          <el-option
            v-for="item in templateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 基础操作按钮 -->
      <div class="flex items-center gap-10px">
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <QueryConditionTemplate
          :type="'prodTable'"
          :form-data="queryParams"
          @update:form-data="onUpdateFormData"
        />
        <el-button type="text" @click="searchCollapsed = !searchCollapsed" class="ml-10px">
          <Icon :icon="searchCollapsed ? 'ep:arrow-down' : 'ep:arrow-up'" class="mr-5px" />
          {{ searchCollapsed ? '展开' : '收起' }}
        </el-button>
      </div>
    </div>

    <!-- 搜索工作栏 -->
    <el-collapse-transition>
      <el-form
        v-show="!searchCollapsed"
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="80px"
      >
        <el-form-item prop="productName">
          <div class="flex gap-20px">
            <el-input
              v-model="queryParams.productName"
              placeholder="产品名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
              style="height: 32px"
            />
            <el-select
              v-model="queryParams.listingCopyStatus"
              placeholder="listing文案状态"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="(item, index) in getIntDictOptions(DICT_TYPE.INFRA_LISTING_STATUS)"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="queryParams.shelfStatus"
              placeholder="上架状态"
              clearable
              class="!w-240px"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
            <el-select
              v-model="queryParams.personsInCharge"
              placeholder="负责人（可多选）"
              clearable
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
              class="!w-240px"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="user.nickname"
                :value="user.nickname"
              />
            </el-select>
            <el-select
              v-model="queryParams.participants"
              placeholder="参与人（可多选）"
              clearable
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
              class="!w-240px"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="user.nickname"
                :value="user.nickname"
              />
            </el-select>
            <AccountShopSelector
              :showAccount="false"
              v-model:shop-values="queryParams.shopIdList"
              v-model:shop-names="queryParams.shopNameList"
              v-model:country-names="queryParams.countryList"
              v-model:country-values="queryParams.countryLists"
            />
          </div>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <shortcut-date-range-picker v-model="queryParams.createTime" />
        </el-form-item>
        <el-form-item label="审核时间" prop="approvalTime">
          <shortcut-date-range-picker v-model="queryParams.approvalTime" />
        </el-form-item>
      </el-form>
    </el-collapse-transition>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <Table
      border
      v-if="queryParams.templateId && dynamicColumns.length > 0"
      height="75vh"
      ref="tableRefs"
      :loading="loading"
      :columns="dynamicColumns.length > 0 ? dynamicColumns : columns"
      :data="list"
      selection
      :saveStatus="saveStatus"
      :type="String(queryParams.templateId)"
      :pagination="{
        total: totalCount,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      :dragField="(row, index) => (row.id ? row.id : `${index}_${row.name}`)"
      @selection-change="handleSelectChange"
      @update:page-size="handlePageSizeChange"
      @update:current-page="handlePageChange"
      @sort-change="handleSortChange"
      isSort
    >
      <!-- 顶部操作按钮 -->
      <template #top-btn>
        <el-button
          v-hasPermi="['infra:tools-table-data:assign-participants']"
          type="primary"
          @click="handleBatchAssign"
          >分配参与人</el-button
        >
        <el-button plain @click="handleBatchGenerateCopywriting">
          <Icon icon="ep:document-add" class="mr-5px" />批量生成文案
        </el-button>
        <el-button v-hasPermi="['infra:tools-table-data:export']" plain @click="handleExport">
          <Icon icon="ep:download" class="mr-5px" />导出筛选数据
        </el-button>
      </template>
      <template #listingCopyStatus="{ row, column }">
        {{ getStatus(row[column.fieldId]) }}
      </template>
      <template #action="{ row }">
        <!-- 编辑/保存按钮 -->
        <el-button
          style="margin-left: 0"
          v-if="!row.isEdit"
          v-hasPermi="['infra:tools-table-data:save']"
          type="text"
          size="small"
          @click="handleEditRow(row)"
        >
          编辑
        </el-button>
        <el-button
          style="margin-left: 0"
          v-else
          v-hasPermi="['infra:tools-table-data:save']"
          type="text"
          size="small"
          @click="saveChangedData(row)"
        >
          保存
        </el-button>
        <!-- 基础按钮 -->
        <el-button
          style="margin-left: 0"
          type="text"
          size="small"
          v-hasPermi="['infra:tools-table-data:assign-participants']"
          @click="handleLaunch(row)"
        >
          分配参与人
        </el-button>
        <el-button
          style="margin-left: 0"
          type="text"
          size="small"
          @click="handleGenerateCopywriting(row)"
        >
          生成文案
        </el-button>
        <el-button
          style="margin-left: 0"
          type="text"
          size="small"
          :disabled="handleViewCopywritingStatus(row)"
          @click="handleViewCopywriting(row)"
        >
          查看文案
        </el-button>
      </template>
    </Table>
    <el-empty v-else description="请选择要投产的表模板" />
  </ContentWrap>

  <!-- 弹窗 -->
  <AssignDialog ref="assignDialogRef" @success="handleAssignSuccess" />
  <CopywritingForm ref="copywritingFormRef" />
</template>

<script setup lang="jsx">
import { getUserListNoPermission } from '@/api/system/user'
import {
  exportExcel, // 导入导出方法
  getPage as getTableDataPage,
  update as updateTableData
} from '@/api/tool/tableData'
import {
  getTableHeadByTemplateId,
  getTemplateList as getTemplateListApi
} from '@/api/tool/tableTemplateCenter'
import request from '@/config/axios'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import CopywritingForm from '@/views/dev/copywriting/copywritingForm.vue' // 导入文案表单组件
import AssignDialog from './AssignDialog.vue'

import { useRouter } from 'vue-router' // 导入 useRouter

defineOptions({ name: 'ProdTable' })

const props = defineProps({
  drawerMode: {
    type: Boolean,
    default: false
  },
  initialTemplateId: {
    type: [String, Number],
    default: null
  },
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})

const router = useRouter() // 获取路由实例

const onUpdateFormData = (newVal) => {
  Object.assign(queryParams, newVal)
  handleQuery()
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const tableRef = ref(null)
const loading = ref(false) // 列表的加载中
const searchCollapsed = ref(false) // 搜索区域折叠状态，默认折叠
const list = ref([]) // 列表的数据
const templateOptions = ref([])
const tableHeadData = ref(null)
const dynamicColumns = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  templateId: undefined, // 模板ID
  tableHeadId: undefined, // 表头ID
  productName: undefined,
  listingCopyStatus: undefined,
  personsInCharge: [], // 审核人ID
  participants: [],
  shelfStatus: undefined,
  createTime: [],
  shopIdList: [], //店铺列表
  shopNameList: [], //店铺名称列表
  countryList: [], //国家列表
  countryLists: [], //国家列表
  sortingFields: [], // 排序字段
  approvalTime: [] // 新增审核时间
})
const queryFormRef = ref() // 搜索的表单
const selectedRows = ref([]) // 表格的选中列表
const saveStatus = ref({
  saving: false,
  lastSaved: null,
  error: null
}) // 保存状态
const previousList = ref([]) // 用于对比的前一次数据

// 分页相关状态
const totalCount = ref(0)
const allData = ref([]) // 存储所有已加载的数据

const totalPendingAudit = ref(0)
const currentPendingAudit = ref(0)

const assignDialogRef = ref()
const copywritingFormRef = ref() // 文案表单引用
const currentEditingRowId = ref(null) // 当前编辑行的ID

// 列配置
const columns = [
  {
    field: 'action',
    align: 'left',
    label: '操作',
    type: 'actions',
    slotName: 'action1',
    fixed: 'right',
    width: 320
  }
]

// 格式化表格数据
const formatTableData = (dataList) => {
  if (!dataList || !dataList.length) return []

  return dataList.map((item) => {
    // 创建行数据对象，包含行ID
    const rowData = {
      id: item.rowId
    }

    // 将 resultMap 中的数据展开到行对象中
    Object.entries(item.resultMap).forEach(([fieldId, value]) => {
      rowData[fieldId] = value
      rowData.resultNameMap = item.resultNameMap
    })

    return rowData
  })
}

/** 查询列表 */
const getList = async (isApproval, isLoadMore = false) => {
  if (!queryParams.tableHeadId) {
    ElMessage.warning('请先选择投产表')
    return
  }

  loading.value = true
  let queryData = JSON.parse(JSON.stringify(queryParams))
  delete queryData.shopIdList
  try {
    // 调用API获取表格数据
    const res = await getTableDataPage({
      ...queryData,
      tableHeadId: queryData.tableHeadId,
      createTime: queryData.createTime,
      approvalTime: queryData.approvalTime,
      templateType: 2 //开发1 投产2 审核3
    })

    if (res) {
      // 处理返回的数据，转换为表格需要的格式
      const formattedData = formatTableData(res.list || [])
      totalCount.value = res.total || 0

      if (isLoadMore) {
        // 触底加载更多：追加数据
        allData.value = [...allData.value, ...formattedData]
        list.value = allData.value

        // 检查是否还有更多数据
        if (tableRef.value) {
          const hasMore = allData.value.length < totalCount.value
          tableRef.value.setHasMore(hasMore)
        }
      } else {
        // 首次加载或刷新：重置数据
        allData.value = formattedData
        list.value = formattedData

        // 恢复编辑状态
        if (currentEditingRowId.value) {
          const editingRow = list.value.find((row) => row.id === currentEditingRowId.value)
          if (editingRow) {
            editingRow.isEdit = true
          }
          currentEditingRowId.value = null
        }

        // 重置分页状态
        if (tableRef.value) {
          tableRef.value.resetPagination()
          const hasMore = formattedData.length < totalCount.value
          tableRef.value.setHasMore(hasMore)
        }
      }
    } else {
      if (!isLoadMore) {
        allData.value = []
        list.value = []
        totalCount.value = 0
      }
    }
  } catch (error) {
    console.error('获取表格数据失败', error)
    if (!isLoadMore) {
      allData.value = []
      list.value = []
      totalCount.value = 0
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.templateId = undefined
  queryParams.productName = undefined
  queryParams.listingCopyStatus = undefined
  queryParams.shelfStatus = undefined
  queryParams.personsInCharge = []
  queryParams.participants = []
  queryParams.shopIdList = []
  queryParams.shopNameList = []
  queryParams.countryList = []
  queryParams.countryLists = []
  queryParams.tableHeadId = ''
  queryParams.pageNo = 1
  queryParams.pageSize = 50

  // 清理数据
  allData.value = []
  list.value = []
  totalCount.value = 0

  // 清理编辑状态
  currentEditingRowId.value = null

  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  handleQuery()
}

// 分页显示数量变化
function handlePageSizeChange(pageSize) {
  queryParams.pageSize = pageSize
  getList()
  console.log('分页显示数量变化', pageSize)
}

// 分页变化
function handlePageChange(page) {
  queryParams.pageNo = page
  getList()
  console.log('分页变化', page)
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}

import download from '@/utils/download'

/** 导出按钮操作 */
const handleExport = async () => {
  if (!queryParams.templateId) {
    message.warning('请先选择要投产的表模板')
    return
  }
  try {
    const selectedIds = selectedRows.value.map((row) => row.id)
    const data = await exportExcel({
      ...queryParams,
      templateType: 2
    })
    download.excel(data, '投产表导出数据.xlsx')
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  }
}

// 处理选择变化
const handleSelectChange = (selection) => {
  selectedRows.value = selection
}

const userOptions = ref([])
// 获取用户列表
const getOperatorList = async () => {
  try {
    const res = await getUserListNoPermission()
    if (res && Array.isArray(res)) {
      userOptions.value = res.map((item) => ({
        id: item.id,
        nickname: item.nickname
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
  }
}

// 处理模板变更
const handleTemplateChange = async (templateId) => {
  // 清理编辑状态
  currentEditingRowId.value = null

  // 清理自动保存状态
  saveStatus.value = {
    saving: false,
    lastSaved: null,
    error: null
  }

  // 重置分页和数据状态
  queryParams.pageNo = 1
  queryParams.pageSize = 50
  allData.value = []
  list.value = []
  totalCount.value = 0

  if (!templateId) {
    tableHeadData.value = null
    dynamicColumns.value = []
    return
  }

  loading.value = true
  try {
    // 调用API获取表头数据
    const res = await getTableHeadByTemplateId(templateId)
    if (res) {
      tableHeadData.value = res
      queryParams.tableHeadId = res.id

      // 解析表头JSON字符串
      if (res.tableHead) {
        const tableHeadObj = JSON.parse(res.tableHead)
        if (tableHeadObj && tableHeadObj.fields) {
          // 根据表头字段生成动态列配置
          dynamicColumns.value = generateDynamicColumns(tableHeadObj.fields)
          console.log(dynamicColumns.value)
          // 获取表格数据
          await getList()
        }
      }
    }
  } catch (error) {
    console.error('获取表头数据失败', error)
  } finally {
    loading.value = false
  }
}

// 判断行是否已审核通过
const isRowApproved = (row) => {
  // 尝试从不同可能的字段中获取审核状态
  return Object.values(row).some((value) => value == '已通过')
}

// 根据表头字段生成动态列配置
const generateDynamicColumns = (fields) => {
  // 查找 特殊配置listting文案状态 字段的 fieldId，仅在投产表有这个逻辑
  const listingCopyStatusField = fields.find((field) => field.fieldName === 'listing文案状态')
  const listingCopyStatusFieldId = listingCopyStatusField ? listingCopyStatusField.fieldId : null
  // 获取复选框列
  const checkboxColumn = columns.find((col) => col.field === 'checkbox')
  // 获取操作列
  const actionColumn = columns.find((col) => col.field === 'action')

  // 设置操作列固定在右侧
  if (actionColumn) {
    actionColumn.fixed = 'right'
  }

  // 根据表头字段生成动态列
  const fieldColumns = fields.map((field) => {
    // 基础列配置
    const column = {
      ...field,
      field: field.fieldId,
      align: 'left',
      title: field.fieldName,
      label: field.fieldName,
      slotsName:
        field.paramTypeCode == 'file'
          ? 'image'
          : field.fieldName == 'listing文案状态'
            ? 'listingCopyStatus'
            : ''
    }
    return column
  })

  // 复选框列在最前，字段列在中间，操作列在最后
  return checkboxColumn
    ? [checkboxColumn, ...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
    : [...fieldColumns, ...(actionColumn ? [actionColumn] : [])]
}

// 获取模板列表
const getTemplateList = async () => {
  try {
    const res = await getTemplateListApi({
      pageNo: 1,
      pageSize: 100,
      templateTypeCode: 2
    })
    if (res) {
      templateOptions.value = res.map((item) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

/** 分配参与人操作 */
const handleLaunch = (row) => {
  assignDialogRef.value.open([row], queryParams.templateId, false)
}

// 添加一个标识来暂时禁用watch监听器
const isRefreshingData = ref(false)

// 获取变化的字段
const getChangedFields = (newObj, oldObj) => {
  const changedFields = {}
  for (const key in newObj) {
    if (newObj[key] !== oldObj[key]) {
      changedFields[key] = newObj[key]
    }
  }
  return changedFields
}

// 检查是否有未保存的数据
const hasUnsavedData = (excludeRow = null) => {
  return list.value.some((row) => {
    if (excludeRow && row.id === excludeRow.id) {
      return false
    }
    return row.isEdit
  })
}

// 保存所有未保存的数据
const saveAllUnsavedData = async (excludeRow = null) => {
  const unsavedRows = list.value.filter((row) => {
    if (excludeRow && row.id === excludeRow.id) {
      return false
    }
    return row.isEdit
  })

  for (const row of unsavedRows) {
    await saveChangedData(row, true) // 传入 true 表示自动保存
  }
}

// 处理编辑行
const handleEditRow = async (row) => {
  try {
    // 检查是否有其他未保存的数据
    if (hasUnsavedData(row)) {
      // 保存所有未保存的数据
      await saveAllUnsavedData(row)
      // 设置当前编辑行ID
      currentEditingRowId.value = row.id
      // 刷新数据以获取最新状态
      await getList()
    } else {
      // 直接设置编辑状态
      row.isEdit = true
      currentEditingRowId.value = row.id
    }
  } catch (error) {
    console.error('编辑行失败:', error)
    message.error('编辑失败')
  }
}

// 保存变化的数据
const saveChangedData = async (changedRow, isAutoSave = false) => {
  if (!queryParams.templateId || saveStatus.value.saving) {
    return
  }

  saveStatus.value.saving = true
  saveStatus.value.error = null

  try {
    console.log('保存变化的行数据:', changedRow)

    // 准备保存的数据
    const saveData = {
      templateId: queryParams.templateId,
      tableHeadId: queryParams.tableHeadId,
      rowId: changedRow.id,
      resultMap: {}
    }

    // 只保存有变化的字段
    Object.keys(changedRow).forEach((key) => {
      if (key !== 'id' && key !== '_X_ROW_KEY' && key !== 'action' && key !== 'checkbox') {
        saveData.resultMap[key] = changedRow[key]
      }
    })

    delete saveData.resultMap.resultNameMap
    delete saveData.resultMap.isEdit

    // 将null值转换为空字符串
    Object.keys(saveData.resultMap).forEach((key) => {
      if (saveData.resultMap[key] === null || saveData.resultMap[key] === undefined) {
        saveData.resultMap[key] = ''
      }
    })

    // 调用保存接口
    await updateTableData(saveData)

    // 更新保存状态
    saveStatus.value.lastSaved = new Date()

    // 设置行为非编辑状态
    changedRow.isEdit = false

    // 如果是用户主动保存，清除当前编辑行ID
    if (!isAutoSave) {
      currentEditingRowId.value = null
    }

    // 设置刷新标识，暂时禁用watch监听器
    isRefreshingData.value = true

    // 重新获取数据，更新算法后的参数
    await getList()

    // 如果是自动保存且存在currentEditingRowId，清除它
    if (isAutoSave && currentEditingRowId.value) {
      currentEditingRowId.value = null
    }

    // 延迟重置标识，确保数据更新完成
    setTimeout(() => {
      isRefreshingData.value = false
    }, 200)
  } catch (error) {
    console.error('保存失败:', error)
    saveStatus.value.error = '保存失败'
    message.error('保存失败')
    isRefreshingData.value = false
  } finally {
    saveStatus.value.saving = false
  }
}

/** 初始化 */
onMounted(async () => {
  await getTemplateList()
  await getOperatorList()

  // 如果是抽屉模式，则应用初始值
  if (props.drawerMode) {
    if (props.initialTemplateId) {
      queryParams.templateId = props.initialTemplateId
      // 在调用 handleTemplateChange 之前应用其他过滤器
      if (props.initialFilters) {
        Object.assign(queryParams, props.initialFilters)
      }
      // handleTemplateChange 会获取列并调用 getList
      await handleTemplateChange(queryParams.templateId)
    } else if (props.initialFilters) {
      // 如果没有模板ID，但有过滤器，则直接调用 getList
      Object.assign(queryParams, props.initialFilters)
      await getList()
    }
  }
})

const handleAuditSuccess = async () => {
  await getList()
}

const handleLaunchSuccess = () => {
  getList()
}

/** 处理列配置变更 */
const handleCustomChange = async () => {
  try {
    // 重新获取表头配置
    const headRes = await getTableHeadByTemplateId(queryParams.templateId)
    if (headRes && headRes.tableHead) {
      tableHeadData.value = headRes
      queryParams.tableHeadId = headRes.id

      // 解析表头JSON字符串
      const parsedTableHead = JSON.parse(headRes.tableHead)

      // 生成动态列配置
      dynamicColumns.value = generateDynamicColumns(parsedTableHead.fields)

      // 重新获取列表数据
      await getList()

      ElMessage.success('列配置已更新')
    }
  } catch (error) {
    console.error('更新列配置失败', error)
    ElMessage.error('更新列配置失败')
  } finally {
    // 确保loading状态被关闭
    loading.value = false
  }
}

/** 分配参与人操作 */
const handleBatchAssign = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要分配的条目')
    return
  }
  assignDialogRef.value.open(selectedRows.value, queryParams.templateId, true)
}

const handleAssignSuccess = () => {
  getList()
}

// 批量生成文案
const handleBatchGenerateCopywriting = async () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据进行文案生成')
    return
  }
  if (!queryParams.templateId) {
    message.warning('请先选择投产表')
    return
  }
  try {
    const rowIds = selectedRows.value.map((row) => row.id)
    await request.post({
      url: '/infra/tools-table-data/batch-generate',
      data: {
        rowIds: rowIds,
        templateId: queryParams.templateId
      }
    })
    message.success('批量生成文案任务已提交')
    getList() // 刷新列表数据
  } catch (error) {
    console.error('批量生成文案失败:', error)
    message.error('批量生成文案失败')
  }
}

// 单行生成文案
const handleGenerateCopywriting = async (row) => {
  if (!queryParams.templateId) {
    message.warning('请先选择投产表')
    return
  }
  try {
    await request.post({
      url: '/infra/tools-table-data/batch-generate',
      data: {
        rowIds: [row.id],
        templateId: queryParams.templateId
      }
    })
    message.success('生成文案任务已提交')
    getList() // 刷新列表数据
  } catch (error) {
    console.error('生成文案失败:', error)
    message.error('生成文案失败')
  }
}

// 判断是否能点击查看文案
const handleViewCopywritingStatus = (row) => {
  const listingCopyStatusField =
    (row &&
      row.resultNameMap &&
      Object.entries(row.resultNameMap).find(([key, value]) => value === 'listing文案状态')?.[0]) ||
    ''
  const listingCopyStatusValue = row[listingCopyStatusField]
  const parsedValue = JSON.parse(listingCopyStatusValue)
  return parsedValue && parsedValue.status && parsedValue.status == '已删除' ? true : false
}

// 查看文案
const handleViewCopywriting = (row) => {
  const listingCopyStatusField =
    (row &&
      row.resultNameMap &&
      Object.entries(row.resultNameMap).find(([key, value]) => value === 'listing文案状态')?.[0]) ||
    ''
  if (!listingCopyStatusField) {
    message.error('未找到listing文案状态字段配置')
    return
  }
  const listingCopyStatusValue = row[listingCopyStatusField]
  if (listingCopyStatusValue) {
    try {
      const parsedValue = JSON.parse(listingCopyStatusValue)
      if (parsedValue && parsedValue.productInfoId) {
        if (copywritingFormRef.value) {
          copywritingFormRef.value.open('update', parsedValue.productInfoId, false) //编辑状态，id，是否显示新增下一条按钮
        } else {
          message.error('文案表单组件不可用，无法查看文案')
          console.error('CopywritingForm ref is not available for view copywriting')
        }
      } else {
        message.warning('未找到生成记录ID或产品信息ID')
      }
    } catch (e) {
      message.error('listing文案状态数据格式不正确，无法查看文案')
      console.error('解析listing文案状态失败:', e)
    }
  } else {
    message.warning('listing文案状态为空，无法查看文案')
  }
}

// 过滤listing文案状态返回显示
const getStatus = (data) => {
  return data && JSON.parse(data).status
}
</script>

<style scoped>
/* 自动保存状态提示的过渡动画 */
.save-status-enter-active,
.save-status-leave-active {
  transition: all 0.3s ease;
}

.save-status-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.save-status-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.save-status-tag {
  transition: all 0.2s ease;
}

.save-status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}
</style>
