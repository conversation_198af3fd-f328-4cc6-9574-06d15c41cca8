<template>
  <div class="chart-container">
    <div v-if="isDataEmpty" class="no-data-placeholder">暂无数据</div>
    <el-popover v-else placement="bottom" :width="500" trigger="hover" popper-class="chart-popover">
      <template #reference>
        <div
          ref="chartRef"
          :style="{ width: '100%', height: '50px', cursor: 'pointer' }"
          @click="openDialog"
        ></div>
      </template>
      <div
        ref="tooltipChartRef"
        :style="{ width: '500px', height: '350px' }"
        @click="openDialog"
      ></div>
    </el-popover>

    <el-dialog
      v-model="dialogVisible"
      title="毛利润趋势详情"
      width="900px"
      :before-close="handleDialogClose"
      appendToBody
    >
      <div class="dialog-content">
        <!-- 商品信息区域 -->
        <div class="product-info">
          <div class="product-image">
            <el-image
              v-if="rowData.imgUrl"
              :src="rowData.imgUrl"
              :style="{ width: '80px', height: '80px' }"
              fit="cover"
              :preview-src-list="[rowData.imgUrl]"
            />
            <div v-else class="no-image">暂无图片</div>
          </div>
          <div class="product-details">
            <div class="product-name">
              <span class="label">品名：</span>
              <span class="value">{{ rowData.localName || '-' }}</span>
            </div>
            <div class="product-asin">
              <span class="label">ASIN：</span>
              <span class="value">{{ rowData.asin || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-area">
          <div ref="dialogChartRef" :style="{ width: '100%', height: '400px' }"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  data: {
    type: Array as () => { date: string; dailyGrossProfit: number; dailyGrossMargin: number }[],
    required: true
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})

const chartRef = ref<HTMLElement | null>(null)
const tooltipChartRef = ref<HTMLElement | null>(null)
const dialogChartRef = ref<HTMLElement | null>(null)
const dialogVisible = ref(false)

const isDataEmpty = computed(() => !props.data || props.data.length === 0)

let chartInstance: echarts.ECharts | null = null
let tooltipChartInstance: echarts.ECharts | null = null
let dialogChartInstance: echarts.ECharts | null = null

const initChart = (ref, options) => {
  if (ref.value) {
    const instance = echarts.init(ref.value)
    instance.setOption(options)
    return instance
  }
  return null
}

const getChartOptions = (mode = 'simple') => {
  // mode: 'simple' - 列表中的简单图表, 'popover' - 弹出层图表, 'dialog' - 弹窗详细图表
  const isDialog = mode === 'dialog'
  const isPopover = mode === 'popover'
  const showDetailed = isDialog

  return {
    tooltip: {
      show: isPopover || isDialog,
      trigger: 'axis',
      formatter: (params) => {
        if (isPopover) {
          // 弹出层简单tooltip
          const data = params[0]
          return `${data.name}<br/>毛利润: ¥${data.value}<br/>毛利率: ${props.data[data.dataIndex]?.dailyGrossMargin || 0}`
        } else {
          // 弹窗详细tooltip
          const profitData = params.find((item) => item.seriesName === '毛利润')
          const marginData = params.find((item) => item.seriesName === '毛利率')
          return `${params[0].name}<br/>
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#409EFF;"></span>毛利润: ¥${profitData?.value || 0}<br/>
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#67C23A;"></span>毛利率: ${marginData?.value || 0}`
        }
      }
    },
    legend: {
      show: isDialog,
      data: ['毛利润', '毛利率']
    },
    dataZoom: isDialog
      ? [
          {
            type: 'inside'
          },
          {
            type: 'slider',
            bottom: 10,
            height: 20
          }
        ]
      : [],
    grid: showDetailed
      ? {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        }
      : isPopover
        ? {
            left: '10%',
            right: '10%',
            top: '10%',
            bottom: '10%',
            containLabel: true
          }
        : {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          },
    xAxis: {
      type: 'category',
      show: isPopover || isDialog,
      data: props.data.map((item) => item.date),
      axisLabel: {
        show: isPopover || isDialog,
        rotate: isDialog ? 45 : 0
      }
    },
    yAxis: isDialog
      ? [
          {
            type: 'value',
            name: '毛利润(¥)',
            position: 'left',
            axisLine: {
              lineStyle: {
                color: '#409EFF'
              }
            },
            axisLabel: {
              color: '#409EFF'
            }
          },
          {
            type: 'value',
            name: '毛利率(%)',
            position: 'right',
            axisLine: {
              lineStyle: {
                color: '#67C23A'
              }
            },
            axisLabel: {
              color: '#67C23A'
            }
          }
        ]
      : {
          type: 'value',
          show: isPopover,
          name: isPopover ? '毛利润(¥)' : ''
        },
    series: isDialog
      ? [
          {
            name: '毛利润',
            type: 'line',
            data: props.data.map((item) => item.dailyGrossProfit),
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 4
          },
          {
            name: '毛利率',
            type: 'line',
            yAxisIndex: 1,
            data: props.data.map((item) => item.dailyGrossMargin),
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#67C23A'
            },
            itemStyle: {
              color: '#67C23A'
            },
            symbol: 'circle',
            symbolSize: 4
          }
        ]
      : [
          {
            name: '毛利润',
            type: 'line',
            data: props.data.map((item) => item.dailyGrossProfit),
            smooth: true,
            lineStyle: {
              width: 1,
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF'
            },
            symbol: 'none',
            symbolSize: 0
          }
        ]
  }
}

onMounted(() => {
  if (!isDataEmpty.value) {
    chartInstance = initChart(chartRef, getChartOptions('simple'))
  }
})

// 监听tooltipChartRef的变化，当popover显示时初始化图表
watch(
  tooltipChartRef,
  (newVal) => {
    if (newVal && !tooltipChartInstance && !isDataEmpty.value) {
      nextTick(() => {
        tooltipChartInstance = initChart(tooltipChartRef, getChartOptions('popover'))
      })
    }
  },
  { immediate: true }
)

watch(dialogVisible, (val) => {
  if (val) {
    nextTick(() => {
      dialogChartInstance = initChart(dialogChartRef, getChartOptions('dialog'))
    })
  } else {
    dialogChartInstance?.dispose()
    dialogChartInstance = null
  }
})

const openDialog = () => {
  if (isDataEmpty.value) return
  dialogVisible.value = true
}

const handleDialogClose = () => {
  dialogVisible.value = false
}

// 组件卸载时清理图表实例
onUnmounted(() => {
  chartInstance?.dispose()
  tooltipChartInstance?.dispose()
  dialogChartInstance?.dispose()
  chartInstance = null
  tooltipChartInstance = null
  dialogChartInstance = null
})
</script>

<style scoped>
:deep(canvas) {
  cursor: pointer !important;
}

.chart-container {
  position: relative;
  cursor: pointer;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-info {
  display: flex;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  align-items: center;
  gap: 16px;
}

.product-image {
  flex-shrink: 0;
}

.no-image {
  display: flex;
  width: 80px;
  height: 80px;
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name,
.product-asin {
  display: flex;
  align-items: center;
}

.label {
  min-width: 60px;
  font-weight: 600;
  color: #666;
}

.value {
  font-weight: 500;
  color: #333;
}

.chart-area {
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.no-data-placeholder {
  display: flex;
  width: 100%;
  height: 50px;
  font-size: 12px;
  color: #999;
  cursor: not-allowed;

  /* background-color: #f5f5f5; */

  /* border: 1px dashed #d9d9d9; */
  border-radius: 4px;
  align-items: center;
  justify-content: center;
}
</style>

<style>
.chart-popover {
  padding: 8px !important;
}
</style>
