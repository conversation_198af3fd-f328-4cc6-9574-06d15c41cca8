<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请补货"
    width="70%"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-table
      :data="tableData"
      style="width: 100%; max-height: 900px; overflow-y: auto"
      scrollbar-always-on
      border
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column prop="imgUrl" label="图片" width="100">
        <template #default="scope">
          <el-image
            v-if="scope.row.imgUrl"
            :src="scope.row.imgUrl"
            style="width: 60px; height: 60px"
            fit="contain"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="localName" label="品名" show-overflow-tooltip />
      <el-table-column prop="localSku" label="SKU" show-overflow-tooltip />
      <el-table-column prop="sellerItem" label="店铺" show-overflow-tooltip />
      <el-table-column label="建议补货量" width="120">
        <template #header>
          <div style="display: flex; align-items: center">
            <span>建议补货量</span>
            <el-button
              type="primary"
              link
              size="small"
              class="ml-5px"
              @click="handleApplySuggestion"
              >应用</el-button
            >
          </div>
        </template>
        <template #default="scope">
          <span>{{ scope.row.suggestedRestockQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="planRestockQty" label="计划补货量" width="180">
        <template #header>
          <div style="display: flex; align-items: center">
            <span>计划补货量</span>
            <el-popover
              placement="top"
              :width="200"
              trigger="click"
              v-model:visible="batchPopoverVisible"
            >
              <template #reference>
                <el-button type="primary" link size="small" class="ml-5px">批量</el-button>
              </template>
              <el-input-number
                v-model="batchPlanRestockQty"
                :min="0"
                placeholder="输入数量"
                controls-position="right"
                style="width: 100%"
              />
              <div style="margin-top: 10px; text-align: right">
                <el-button size="small" @click="batchPopoverVisible = false">取消</el-button>
                <el-button type="primary" size="small" @click="handleBatchApply">确认</el-button>
              </div>
            </el-popover>
          </div>
        </template>
        <template #default="scope">
          <el-input-number
            v-model="scope.row.planRestockQty"
            :min="0"
            placeholder="请输入"
            controls-position="right"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button type="danger" link @click="removeItem(scope.$index)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="handleApply" :loading="loading">申请</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref, watch } from 'vue'

interface TableRowData {
  id: number
  imgUrl?: string
  productName?: string
  msku?: string
  sellerItem?: string
  suggestedRestockQuantity?: number | string
  planRestockQty?: number | null
  [key: string]: any
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success', 'item-removed'])

const dialogVisible = ref(false)
const tableData = ref<TableRowData[]>([])
const batchPlanRestockQty = ref<number | null>(null)
const batchPopoverVisible = ref(false)
const loading = ref(false)

const totalPlanRestockQty = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (Number(item.planRestockQty) || 0), 0)
})

const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (column.property === 'planRestockQty') {
      sums[index] = `计划补货量：${totalPlanRestockQty.value}`
    } else {
      sums[index] = ''
    }
  })
  return sums
}

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
  }
)

const openDialog = (items: TableRowData[]) => {
  tableData.value = JSON.parse(JSON.stringify(items)).map((item: TableRowData) => ({
    ...item,
    planRestockQty: item.planRestockQty === undefined ? null : item.planRestockQty
  }))
  dialogVisible.value = true
  emit('update:visible', true)
}

const closeDialog = () => {
  dialogVisible.value = false
  batchPopoverVisible.value = false // Ensure popover is closed
  tableData.value = [] // Clear table data
  batchPlanRestockQty.value = null // Clear batch input
  emit('update:visible', false)
}

const removeItem = (index: number) => {
  const removedItem = tableData.value.splice(index, 1)
  if (removedItem && removedItem.length > 0) {
    emit('item-removed', removedItem[0].id)
  }
}

const handleApplySuggestion = () => {
  ElMessageBox.confirm('确定要将建议补货量应用到计划补货量吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      tableData.value.forEach((item) => {
        item.planRestockQty = item.suggestedRestockQuantity
      })
      ElMessage.success('应用成功')
    })
    .catch(() => {
      // catch error
    })
}

const handleBatchApply = () => {
  if (batchPlanRestockQty.value === null || batchPlanRestockQty.value < 0) {
    ElMessage.warning('请输入有效的批量计划补货量')
    return
  }
  tableData.value.forEach((item) => {
    item.planRestockQty = batchPlanRestockQty.value
  })
  batchPopoverVisible.value = false
  batchPlanRestockQty.value = null // Reset after apply
}

const handleApply = async () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('请至少保留一项补货商品')
    return
  }

  const invalidItems = tableData.value.filter(
    (item) =>
      item.planRestockQty === null || item.planRestockQty === undefined || item.planRestockQty < 0
  )

  if (invalidItems.length > 0) {
    ElMessage.warning('所有商品的计划补货量都不能为空且必须大于等于0')
    return
  }

  const payload = tableData.value.map((item) => ({
    id: item.id,
    planRestockQty: item.planRestockQty as number
  }))

  try {
    loading.value = true
    await ReplenishmentProposalApi.batchRequestRestock(payload)
    ElMessage.success('申请补货成功')
    emit('success')
    closeDialog()
  } catch (error) {
    console.error('申请补货失败:', error)
    // ElMessage.error('申请补货失败，请重试') // Specific error handling can be added here
  } finally {
    loading.value = false
  }
}

// Expose openDialog method to parent component
defineExpose({
  openDialog
})
</script>

<style scoped>
.ml-5px {
  margin-left: 5px;
}
</style>
