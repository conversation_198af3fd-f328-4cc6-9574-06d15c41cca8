<template>
  <el-dialog v-model="dialogVisible" :title="title" width="45%" @close="closeDialog">
    <el-form :model="formData" label-width="108px">
      <el-row>
        <el-form-item label="库存周期" prop="inventoryCycle">
          <el-input
            v-model="formData.inventoryCycle"
            class="!w-150px"
            :min="1"
            :max="365"
            type="number"
            placeholder="请输入库存周期(天)"
          >
            <template #append>
              <el-select
                v-model="selectedCycle"
                placeholder="常用天数"
                style="width: 90px"
                @change="handleCycleChange"
              >
                <el-option label="20天" :value="20" />
                <el-option label="30天" :value="30" />
                <el-option label="35天" :value="35" />
                <el-option label="40天" :value="40" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
      </el-row>
      <div>
        <el-form-item style="margin-bottom: 0 !important">
          <template #label>
            <div class="flex items-center">
              补货权重占比
              <el-tooltip
                content="<p>📊 权重分配逻辑：</p><p>为6个时间段分配百分比（总和需100%），权重越高代表该时间段数据对补货建议的影响越大。各时间段特性：</p><p>① 3天均值：紧急补货（快速响应波动）</p><p>② 7天均值：短期平衡（1周趋势）</p><p>③ 14天均值：中期观察（2周稳定性）</p><p>④ 30天均值：常规规律（月度周期）</p><p>⑤ 60天/90天：长期防控（防库存积压）</p><p>🔧 配置指南：</p><p>✅ 短期优先策略（如促销季）：</p><p>65%-35%-0%-0%-0%-0% → 主看3天，辅看7天</p><p>✅ 常规快消品：</p><p>50%-40%-10%-0%-0%-0% → 3天+7天+14天组合</p><p>✅ 耐用品/季节性商品：</p><p>35%-30%-25%-10%-0%-0% → 平衡短中期数据</p><p>✅ 长尾高成本商品：</p><p>30%-25%-20%-15%-10%-0% → 全周期递减防积压</p><p>✅ 通用安全策略：</p><p>25%-25%-20%-15%-10%-5% → 阶梯覆盖各周期</p><p>💡 小贴士：</p><p>权重越靠前的时段对决策影响越大</p><p>销售波动大→加大短期权重（3天/7天）</p><p>销售稳定→加大长期权重（30天/60天）</p><p>系统默认策略：65%-35%-0%-0%-0%-0%（适合快速周转商品）</p>"
                raw-content
              >
                <Icon icon="ep:warning" :size="12" />
              </el-tooltip>
            </div>
          </template>
          <div class="flex">
            <div class="flex items-center gap-2">
              <el-select
                v-model="formData.weightRatioTemplate"
                class="!w-240px"
                placeholder="请选择预设模板"
                @change="handleWeightRatioTemplateChange"
                :disabled="resetWeight"
                filterable
              >
                <el-option
                  v-for="item in weightRatioOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div
                class="flex items-center gap-5"
                :class="{ 'text-red-500': totalWeight.toFixed(1) != 100 }"
              >
                总计: {{ totalWeight.toFixed(1) }}%
                {{ totalWeight.toFixed(1) != 100 ? '(必须等于100%)' : '' }}
                <el-button type="text" plain @click="handleResetWeightRatio">{{
                  !resetWeight ? '自定义权重规则' : '取消'
                }}</el-button>
                <el-button v-if="resetWeight" type="text" plain @click="openWeightList"
                  >查看我的权重</el-button
                >
                <el-button v-if="resetWeight" plain type="text" @click="handleSaveWeight">
                  保存为我的权重
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <div
          v-if="resetWeight"
          class="flex flex-wrap items-center gap-2 mb-15px mt-15px"
          style="font-size: 14px; color: #606266"
        >
          <div class="flex items-center">
            <span class="mr-1">3天:</span>
            <el-input-number
              v-model="formData.restockRatio3d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
          <div class="flex items-center">
            <span class="mr-1">7天:</span>
            <el-input-number
              v-model="formData.restockRatio7d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
          <div class="flex items-center">
            <span class="mr-1">14天:</span>
            <el-input-number
              v-model="formData.restockRatio14d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
          <div class="flex items-center">
            <span class="mr-1">30天:</span>
            <el-input-number
              v-model="formData.restockRatio30d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
          <div class="flex items-center">
            <span class="mr-1">60天:</span>
            <el-input-number
              v-model="formData.restockRatio60d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
          <div class="flex items-center">
            <span class="mr-1">90天:</span>
            <el-input-number
              v-model="formData.restockRatio90d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              size="small"
              class="!w-120px"
              @change="calculateTotalWeight"
            />
            <span class="ml-1">%</span>
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </template>
    <weightListForm ref="weightListFormRef" @apply="applyWeight" />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { Icon } from '@/components/Icon'
import { ElMessage } from 'element-plus'
import { computed, defineEmits, defineProps, reactive, ref } from 'vue'
import weightListForm from './weightListForm.vue' // 确保路径正确

const {
  getDefaultRuleSettings,
  createDefaultRuleSettings,
  updateDefaultRuleSettings,
  batchSetCustomRuleSettings
} = ReplenishmentProposalApi

const props = defineProps({
  // title: {
  //   type: String,
  //   default: '规则设置'
  // }
})

const title = ref('') // 用于存储弹窗标题

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formData = reactive<any>({
  id: undefined, // 用于存储默认规则的ID
  inventoryCycle: 30,
  weightRatioTemplate: '65-35-0-0-0-0',
  restockRatio3d: 65.0,
  restockRatio7d: 35.0,
  restockRatio14d: 0.0,
  restockRatio30d: 0.0,
  restockRatio60d: 0.0,
  restockRatio90d: 0.0,
  ids: [] // 用于自定义规则设置时存储选中的列表项ID
})
const selectedCycle = ref(30) // 存储常用天数选择器的值
const resetWeight = ref(false) // 是否启用自定义权重规则

const totalWeight = computed(() => {
  return (
    (parseFloat(formData.restockRatio3d) || 0) +
    (parseFloat(formData.restockRatio7d) || 0) +
    (parseFloat(formData.restockRatio14d) || 0) +
    (parseFloat(formData.restockRatio30d) || 0) +
    (parseFloat(formData.restockRatio60d) || 0) +
    (parseFloat(formData.restockRatio90d) || 0)
  )
})
const weightListFormRef = ref()

const weightRatioOptions = ref([
  { id: 'default-0', label: '100-0-0-0-0-0', value: '100-0-0-0-0-0' },
  { id: 'default-1', label: '65-35-0-0-0-0', value: '65-35-0-0-0-0' },
  { id: 'default-2', label: '50-40-10-0-0-0', value: '50-40-10-0-0-0' },
  { id: 'default-3', label: '35-30-25-10-0-0', value: '35-30-25-10-0-0' },
  { id: 'default-4', label: '30-25-20-15-10-0', value: '30-25-20-15-10-0' },
  { id: 'default-5', label: '25-25-20-15-10-5', value: '25-25-20-15-10-5' }
])

// 获取我的权重列表
const getMyWeightList = async () => {
  try {
    const res = await ReplenishmentProposalApi.listWeight()
    if (res) {
      // 重置weightRatioOptions为默认值
      weightRatioOptions.value = [
        { id: 'default-0', label: '100-0-0-0-0-0', value: '100-0-0-0-0-0' },
        { id: 'default-1', label: '65-35-0-0-0-0', value: '65-35-0-0-0-0' },
        { id: 'default-2', label: '50-40-10-0-0-0', value: '50-40-10-0-0-0' },
        { id: 'default-3', label: '35-30-25-10-0-0', value: '35-30-25-10-0-0' },
        { id: 'default-4', label: '30-25-20-15-10-0', value: '30-25-20-15-10-0' },
        { id: 'default-5', label: '25-25-20-15-10-5', value: '25-25-20-15-10-5' }
      ]

      // 将我的权重数据添加到weightRatioOptions数组后面
      res.forEach((item, index) => {
        const weightValue = `${item.weight3d}-${item.weight7d}-${item.weight14d}-${item.weight30d}-${item.weight60d}-${item.weight90d}`
        const weightLabel = `${weightValue}-(${item.name || '未命名'})`
        // 为自定义权重的value添加唯一标识，避免与默认选项重复
        const uniqueValue = `custom-${item.id || index}-${weightValue}`
        weightRatioOptions.value.push({
          id: `custom-${item.id || index}`, // 确保唯一的id
          label: weightLabel,
          value: uniqueValue, // 使用唯一的value
          originalValue: weightValue, // 保存原始权重值用于后端传递
          originalId: item.id, // 保存原始id用于其他操作
          name: item.name
        })
      })
    }
  } catch (error) {
    console.error('获取我的权重列表失败', error)
  }
}

// 打开弹窗
const openDialog = async (dialogTitle: string, selectedIds: number[] = []) => {
  title.value = dialogTitle
  dialogVisible.value = true
  resetForm()
  await getMyWeightList() // 获取我的权重列表
  if (dialogTitle === '默认规则设置') {
    try {
      const res = await getDefaultRuleSettings()
      if (res && res) {
        resetWeight.value = true
        Object.assign(formData, res)
        // 根据返回的权重值，判断是否属于预设模板，如果不是，则启用自定义权重
        const matchedTemplate = weightRatioOptions.value.find((option) => {
          // 对于自定义权重使用originalValue，对于默认选项使用value
          const weightValue = option.originalValue || option.value
          const ratios = weightValue.split('-').map(Number)
          return (
            ratios[0] === formData.restockRatio3d &&
            ratios[1] === formData.restockRatio7d &&
            ratios[2] === formData.restockRatio14d &&
            ratios[3] === formData.restockRatio30d &&
            ratios[4] === formData.restockRatio60d &&
            ratios[5] === formData.restockRatio90d
          )
        })
        if (matchedTemplate) {
          formData.weightRatioTemplate = matchedTemplate.value
          resetWeight.value = false
        } else {
          formData.weightRatioTemplate = '' // 清空预设模板选择
          resetWeight.value = true // 启用自定义权重
        }
        selectedCycle.value = formData.inventoryCycle // 同步常用天数选择器
      }
    } catch (error) {
      console.error('获取默认规则失败', error)
    }
  } else if (dialogTitle === '自定义规则设置') {
    if (!selectedIds || selectedIds.length === 0) {
      ElMessage.warning('请至少选择一条数据进行自定义规则设置')
      dialogVisible.value = false // 关闭弹窗
      return
    }
    formData.ids = selectedIds
  }
}

const closeDialog = () => {
  dialogVisible.value = false
}

const resetForm = () => {
  formData.id = undefined // 清除可能存在的id
  formData.inventoryCycle = 30
  formData.weightRatioTemplate = '65-35-0-0-0-0'
  formData.restockRatio3d = 65.0
  formData.restockRatio7d = 35.0
  formData.restockRatio14d = 0.0
  formData.restockRatio30d = 0.0
  formData.restockRatio60d = 0.0
  formData.restockRatio90d = 0.0
  selectedCycle.value = 30
  resetWeight.value = false // 默认不启用自定义权重
  formData.ids = [] // 清除可能存在的ids
}

const handleCycleChange = (value) => {
  formData.inventoryCycle = value
}

const calculateTotalWeight = () => {
  totalWeight.value = [
    formData.restockRatio3d,
    formData.restockRatio7d,
    formData.restockRatio14d,
    formData.restockRatio30d,
    formData.restockRatio60d,
    formData.restockRatio90d
  ].reduce((sum, val) => sum + (Number(val) || 0), 0)
}

const handleWeightRatioTemplateChange = (value) => {
  // 查找选中的权重选项
  const selectedOption = weightRatioOptions.value.find((option) => option.value === value)

  // 如果是自定义权重，使用originalValue；否则直接使用value
  const weightValue = selectedOption?.originalValue || value

  const ratios = weightValue.split('-').map(Number)
  formData.restockRatio3d = ratios[0]
  formData.restockRatio7d = ratios[1]
  formData.restockRatio14d = ratios[2]
  formData.restockRatio30d = ratios[3]
  formData.restockRatio60d = ratios[4]
  formData.restockRatio90d = ratios[5]
  calculateTotalWeight()
}

const handleResetWeightRatio = () => {
  resetWeight.value = !resetWeight.value
  if (!resetWeight.value) {
    // 如果取消自定义，则恢复模板选择
    handleWeightRatioTemplateChange(formData.weightRatioTemplate)
  }
}

const openWeightList = () => {
  weightListFormRef.value?.openDialog()
}

const applyWeight = (weightData) => {
  formData.weightRatioTemplate = '' // 清空模板选择，因为是自定义应用
  formData.restockRatio3d = weightData.weight3d
  formData.restockRatio7d = weightData.weight7d
  formData.restockRatio14d = weightData.weight14d
  formData.restockRatio30d = weightData.weight30d
  formData.restockRatio60d = weightData.weight60d
  formData.restockRatio90d = weightData.weight90d
  resetWeight.value = true // 强制进入自定义模式
  calculateTotalWeight()
}

// 保存为我的权重
const handleSaveWeight = async () => {
  if (totalWeight.value.toFixed(1) != 100) {
    ElMessage.error('权重总和必须等于100%')
    return
  }
  // 打开编辑弹窗，预填当前权重值
  weightListFormRef.value?.openDialog()
  // 延迟一下，确保弹窗打开后再触发新增
  setTimeout(() => {
    let data = {
      weight3d: formData.restockRatio3d,
      weight7d: formData.restockRatio7d,
      weight14d: formData.restockRatio14d,
      weight30d: formData.restockRatio30d,
      weight60d: formData.restockRatio60d,
      weight90d: formData.restockRatio90d
    }
    // 设置当前权重值
    weightListFormRef.value?.handleAdd(data)
    weightListFormRef.value.calculateEditTotalWeight(data)
  }, 300)
}

// 提交表单
const handleSubmit = async () => {
  if (totalWeight.value.toFixed(1) != 100) {
    ElMessage.error('补货权重占比总和必须等于100%')
    return
  }
  try {
    const params = {
      inventoryCycle: formData.inventoryCycle,
      restockRatio3d: formData.restockRatio3d,
      restockRatio7d: formData.restockRatio7d,
      restockRatio14d: formData.restockRatio14d,
      restockRatio30d: formData.restockRatio30d,
      restockRatio60d: formData.restockRatio60d,
      restockRatio90d: formData.restockRatio90d
    }
    if (title.value === '默认规则设置') {
      if (formData.id) {
        await updateDefaultRuleSettings({ ...params, id: formData.id })
      } else {
        await createDefaultRuleSettings(params)
      }
      ElMessage.success('默认规则保存成功')
    } else if (title.value === '自定义规则设置') {
      if (!formData.ids || formData.ids.length === 0) {
        ElMessage.error('请至少选择一条数据进行自定义规则设置')
        return
      }
      await batchSetCustomRuleSettings({ ...params, ids: formData.ids })
      emit('success')
      ElMessage.success('自定义规则批量设置成功')
    }
    emit('success', { ...formData })
    closeDialog()
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('保存规则失败', error)
  }
}

// 初始化计算一次总权重
calculateTotalWeight()

// 暴露方法给父组件
defineExpose({
  openDialog
})
</script>

<style scoped>
/* 根据需要添加样式 */
</style>
