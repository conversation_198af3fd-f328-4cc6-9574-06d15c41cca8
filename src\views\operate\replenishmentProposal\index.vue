<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      style="display: flex; flex-wrap: wrap"
    >
      <AccountShopSelector
        v-model:account-values="queryParams.uids"
        v-model:shop-values="queryParams.sids"
        v-model:country-values="queryParams.countrysList"
        v-model:country-names="queryParams.countrys"
        @account-change="queryAccountChange"
        @shop-change="queryShopChange"
        @country-change="queryCountryChange"
      />
      <el-form-item label="" prop="skuType">
        <el-select v-model="queryParams.skuType" class="!w-90px" clearable placeholder="请选择">
          <el-option label="ASIN" value="ASIN" />
          <el-option label="SKU" value="SKU" />
          <el-option label="MSKU" value="MSKU" />
          <el-option label="FNSKU" value="FNSKU" />
          <el-option label="品名" value="品名" />
        </el-select>
        <TipInput
          v-model="queryParams.skuValue"
          inputClass="!w-150px"
          placeholder=""
          tooltipIcon="fa-solid:bars"
          @enter="handleTipInputEnter"
        />
      </el-form-item>
      <el-form-item label="负责人" prop="principalUids">
        <RcInputSelect
          v-model="queryParams.principalUids"
          style="min-width: 150px"
          :options="principalOptions"
        />
      </el-form-item>
      <el-form-item label="是否申请补货" prop="applyRestock">
        <el-select v-model="queryParams.applyRestock" class="!w-120px" clearable placeholder="全部">
          <el-option label="全部" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否建议补货" prop="resuggestRestock">
        <el-select
          v-model="queryParams.resuggestRestock"
          class="!w-120px"
          clearable
          placeholder="全部"
        >
          <el-option label="全部" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="globalTagIds">
        <RcInputSelect
          v-model="queryParams.globalTagIds"
          style="min-width: 150px"
          :options="tagOptions"
        />
      </el-form-item>
      <el-form-item label="权重" prop="customWeightIds">
        <RcInputSelect
          v-model="queryParams.customWeightIds"
          style="min-width: 150px"
          :options="weightOptions"
        />
      </el-form-item>
      <el-form-item label="分析时间" prop="createTime">
        <ShortcutDateRangePicker v-model="queryParams.analysisTime" />
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          @click="handleQuery"
          style="margin-left: 10px"
        >
          查询
        </el-button>
        <el-button @click="resetQuery"> 重置 </el-button>
        <QueryConditionTemplate
          :type="'replenishmentProposal'"
          :form-data="queryParams"
          @update:form-data="onUpdateFormData"
        />
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          type="primary"
          @click="handleGenerates"
        >
          智能补货分析
        </el-button>
        <el-tooltip
          class="box-item"
          effect="dark"
          content="默认规则设置用于为所有商品统一配置规则参数。设置完成后，系统在进行“智能补货分析”时，将优先使用您为个别商品单独设置的规则参数；若未单独设置，则按照默认规则进行分析。"
          placement="top-start"
        >
          <el-button
            v-hasPermi="['operation:restock-suggest:query']"
            plain
            type="primary"
            @click="openRuleSettingsDialog('默认规则设置')"
          >
            默认规则设置
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="box-item"
          effect="dark"
          content="自定义规则设置允许为个别商品单独配置特定的规则参数。设置完成后，系统在进行“智能补货分析”时，将优先使用这些自定义规则，而不再采用默认规则。"
          placement="top-start"
        >
          <el-button
            v-hasPermi="['operation:restock-suggest:query']"
            plain
            type="primary"
            @click="openRuleSettingsDialog('自定义规则设置')"
          >
            自定义规则设置
          </el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <!-- 使用封装的Table组件 -->
    <Table
      ref="copywritingTableRef"
      :loading="loading"
      :data="list"
      :columns="tableColumns"
      selection
      border
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @selection-change="handleSelectionChange"
      @update:page-size="
        (val) => {
          queryParams.pageSize = val
          getList()
        }
      "
      @update:current-page="
        (val) => {
          queryParams.pageNo = val
          getList()
        }
      "
      @sort-change="handleSortChange"
      isSort
    >
      <!-- 操作按钮区域 -->
      <template #top-btn>
        <el-button
          type=""
          plain
          @click="handleRequestRestock"
          v-hasPermi="['operation:restock-suggest:query']"
        >
          申请补货
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('select')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出选中
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('all')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出全部
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('applyRestock')"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          <Icon icon="ep:download" />导出已申请补货
        </el-button>
      </template>
      <!-- 右边操作按钮区域 -->
      <template #top-btn-right>
        <el-popover class="box-item" title="" placement="left-start" width="500px">
          <template #reference>
            <Icon icon="ep:warning" />
          </template>
          <div
            v-html="
              `<b>关于数据更新说明（库存/利润等核心指标）</b></br>
• 更新频次：每日3次定时更新（07:00/12:00/18:00）</br>
• 数据延迟：每次更新后30分钟内完成全量数据同步</br>
</br>
<b>关于库存预测与补货计算规则说明</b></br>
一、单日预测销量计算</br>
采用历史销量加权平均法计算，假如权重分配为：3天（35%）、7天（30%）、14天（20%）、30天（10%）、60天（4%）、90天（1%）。</br>
</br>
计算公式如下：</br>
单日预测 = 各周期平均销量 × 对应权重= 3天均销量×35% + 7天均销量×30% + 14天均销量×20% + 30天均销量×10% + 60天均销量×4% + 90天均销量×1%</br>
</br>
示例：</br>
当各周期均销量均为100件时：</br>
单日预测 = 100×(35%+30%+20%+10%+4%+1%) = 100件</br>
</br>
二、预测总库存计算</br>
基于设定的库存周期（如7/14/30天等），按以下公式计算：</br>
预测总库存 = 单日预测值 × 库存周期</br>
</br>
示例：</br>
库存周期30天时：</br>
预测总库存 = 100件/天 × 30天 = 3,000件</br>
</br>
三、建议补货量计算</br>
根据预测库存与实际库存差值确定，计算规则：</br>
建议补货量 = 预测总库存 - 当前总库存</br>
取整规则：按个位数进行四舍五入</br>
● 个位数<5 → 个位归零</br>
● 个位数≥5 → 向上取整至最近的10的倍数</br>
</br>
示例1</br>
预测总库存 = 3,000件，当前库存 = 2,876件</br>
补货量 = 3,000 - 2,876 = 124件 → 个位数4（<5） → 建议补货120件</br>
</br>
示例2</br>
预测总库存 = 3,000件，当前库存 = 2,875件</br>
补货量 = 3,000 - 2,875 = 125件 → 个位数5（≥5） → 建议补货130件</br>`
            "
          >
          </div>
        </el-popover>
      </template>

      <template #skuInfo="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.localName" class="w-100%" truncated>{{
            row.localName
          }}</el-text>
          <el-text v-clipboard="row.localSku" class="w-100%" truncated>{{ row.localSku }}</el-text>
        </div>
      </template>
      <template #mskuInfo="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.msku" class="w-100%" truncated>{{ row.msku }}</el-text>
          <el-text v-clipboard="row.fnsku" class="w-100%" truncated>{{ row.fnsku }}</el-text>
        </div>
      </template>
      <template #countryShop="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.country" class="w-100%" truncated>{{ row.country }}</el-text>
          <el-text v-clipboard="row.sellerItem" class="w-100%" truncated>{{
            row.sellerItem
          }}</el-text>
        </div>
      </template>
      <!-- 操作列模板 -->
      <template #action="scope">
        <!-- 如果是待生成状态就不展示 -->
        <el-button
          v-hasPermi="['operation:restock-suggest:query']"
          link
          type="primary"
          @click="openLogForm(scope.row.asin, scope.row.id)"
        >
          历史诊断
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleRequestRestock(scope.row)"
          v-hasPermi="['operation:restock-suggest:export']"
        >
          申请补货
        </el-button>
        <el-button
          type="primary"
          link
          @click="openDetailDialog(scope.row)"
          v-hasPermi="['operation:restock-suggest:query']"
        >
          详情
        </el-button>
      </template>

      <!-- 备注列模板 -->
      <template #remark="scope">
        <el-input v-model="scope.row.remarkEdit" @blur="handleInput(scope.row)" />
      </template>

      <!-- 标签 -->
      <template #tagsResp="scope">
        <div
          v-if="
            scope.row.restockSuggestGlobalTagRespVoList &&
            scope.row.restockSuggestGlobalTagRespVoList.length > 0
          "
          class="flex items-center"
        >
          <el-tag
            :style="{
              backgroundColor: scope.row.restockSuggestGlobalTagRespVoList[0].color || '#409EFF',
              color: '#fff'
            }"
            class="mr-5px"
          >
            {{ scope.row.restockSuggestGlobalTagRespVoList[0].tagName }}
          </el-tag>
          <el-popover
            v-if="scope.row.restockSuggestGlobalTagRespVoList.length > 1"
            placement="top-start"
            trigger="hover"
            width="max-content-box"
          >
            <template #reference>
              <el-link type="primary" :underline="false" class="ml-5px"
                >共{{ scope.row.restockSuggestGlobalTagRespVoList.length }}个</el-link
              >
            </template>
            <div style="display: flex">
              <el-tag
                v-for="item in scope.row.restockSuggestGlobalTagRespVoList"
                :key="item.globalTagId"
                :style="{
                  backgroundColor: item.color || '#409EFF',
                  color: '#fff',
                  marginRight: '5px',
                  marginBottom: '5px'
                }"
              >
                {{ item.tagName }}
              </el-tag>
            </div>
          </el-popover>
        </div>
        <span v-else>-</span>
      </template>

      <!-- Listing负责人 -->
      <template #restockSuggestPrincipalInfoRespVos="scope">
        <span>
          {{
            scope.row.restockSuggestPrincipalInfoRespVos
              ?.map((item) => item.principalName)
              .join(',') || '-'
          }}
        </span>
      </template>

      <!-- 单日预测列模板 -->
      <template #predictedDailySales="scope">
        <span style="color: #f56c6c">{{ scope.row.predictedDailySales || '-' }}</span>
      </template>

      <!-- 预测总库存列模板 -->
      <template #predictedInventoryTarget="scope">
        <span style="color: #f56c6c">{{ scope.row.predictedInventoryTarget || '-' }}</span>
      </template>

      <!-- 建议补货列模板 -->
      <template #suggestedRestockQuantity="scope">
        <span style="font-weight: bold; color: #67c23a">{{
          scope.row.suggestedRestockQuantity || '-'
        }}</span>
      </template>

      <!-- 日均 -->
      <template #salesAvg="{ row }">
        <div class="flex items-center gap-4px flex-wrap">
          <el-tooltip placement="top" effect="dark" content="3天日均">
            <div class="text-center">{{ row.salesAvg3 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="7天日均">
            <div class="text-center">{{ row.salesAvg7 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="14天日均">
            <div class="text-center">{{ row.salesAvg14 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="30天日均">
            <div class="text-center">{{ row.salesAvg30 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="60天日均">
            <div class="text-center">{{ row.salesAvg60 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="90天日均">
            <div class="text-center">{{ row.salesAvg90 }}</div>
          </el-tooltip>
        </div>
      </template>

      <!-- 毛利润趋势 -->
      <template #grossProfitTrendDataList="{ row }">
        <GrossProfitTrendChart
          :data="row.grossProfitTrendDataList"
          :row-data="row"
          style="cursor: pointer"
        />
      </template>

      <!-- 总库存 -->
      <template #totalInventory="scope">
        <div class="flex items-center gap-4px w-100%">
          <div>{{ scope.row.totalInventory }}</div>
          <el-popover
            placement="right"
            :width="240"
            trigger="hover"
            v-if="scope.row.totalInventory"
          >
            <template #reference>
              <Icon :size="12" icon="ep:arrow-down" class="cursor-pointer" />
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>采购计划</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购计划单待采购量<br />
                        统计数据：采购计划（待采购）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchasePlan || '-' }}
              </el-descriptions-item>
              <el-descriptions-item class="relative">
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA库存</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 待调仓 + 调仓中 + 可售 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                <div class="flex items-center gap-4px">
                  <span>{{ scope.row.inventorys[0]?.fbaInventory }}</span>
                  <el-popover
                    placement="right"
                    :width="240"
                    trigger="hover"
                    v-if="scope.row.inventorys[0]?.fba"
                  >
                    <template #reference>
                      <Icon :size="12" icon="ep:arrow-right" class="cursor-pointer" />
                    </template>
                    <el-descriptions :column="1" border>
                      <el-descriptions-item label="FBA可售-可售">
                        {{ scope.row.inventorys[0]?.fba[0]?.afnFulfillableQuantity }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-待调仓">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcTransfers }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-调仓中">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcProcessing }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-popover>
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        shipments申报量-签收量，仅统计6个月内创建且系统货件状态为"进行中"的货件
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>预计发货量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        统计以下单据：<br />
                        ①发货计划：未关联货件且未关联发货单，且状态为待审批、待处理状态的发货计划<br />
                        ②发货单：未关联货件，且状态为待审批、待配货、待发货状态的发货单
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShippingPlan }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        海外仓备货单待收货量+目的仓为海外仓的调拨单待收货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓可用/本地可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待检待上架量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的待检待上架量（汇总SKU无绑定FNSKU数量与SKU+FNSKU数量）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalQc }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待交付</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购单的待交付量，SKU为组合产品则额外包含期望待到货量<br />
                        统计数据：采购单(待提交) + 采购单(待审批) + 采购单(待下单) + 采购单(待到货)
                        + 采购单(已驳回) + 委外订单(待提交) + 委外订单(待审批) + 委外订单(待下单) +
                        委外订单(待到货) + 期望待到货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchaseShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 目的仓为本地仓的调拨单待收货量 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalShipping }}
              </el-descriptions-item>
            </el-descriptions>
          </el-popover>
        </div>
      </template>
    </Table>

    <div class="flex items-center justify-between mt-10px">
      <el-text type="primary">已申请补货数 {{ isMarked }}</el-text>
    </div>
  </ContentWrap>

  <!-- 表单弹窗：诊断历史 -->
  <logListForm ref="logFormRef" @success="getList" />

  <!-- 表单弹窗：权重列表 -->
  <weightListForm ref="weightFormRef" @apply="applyWeight" />

  <RuleSettingsDialog ref="ruleSettingsDialogRef" @success="handleRuleSettingsSuccess" />

  <!-- 申请补货弹窗 -->
  <ReplenishmentRequestDialog
    ref="replenishmentRequestDialogRef"
    v-model:visible="replenishmentRequestVisible"
    @success="handleReplenishmentRequestSuccess"
    @item-removed="handleItemRemovedFromDialog"
  />
  <DetailDialog ref="detailDialogRef" />
</template>

<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import QueryConditionTemplate from '@/components/QueryConditionTemplate/index.vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import type { TableColumn } from '@/types/table'
import download from '@/utils/download'
import { formatDate } from '@/utils/formatTime'
import { computed, onMounted, reactive, ref } from 'vue'
import DetailDialog from './DetailDialog.vue'
import GrossProfitTrendChart from './GrossProfitTrendChart.vue'
import logListForm from './logListForm.vue'
import ReplenishmentRequestDialog from './ReplenishmentRequestDialog.vue'
import RuleSettingsDialog from './RuleSettingsDialog.vue'
import weightListForm from './weightListForm.vue'

// 默认时间范围：今天 00:00:00 - 23:59:59
const defaultTimeRange = computed(() => {
  const now = new Date()
  const todayStart = new Date(now)
  todayStart.setHours(0, 0, 0, 0)

  const todayEnd = new Date(now)
  todayEnd.setHours(23, 59, 59, 999)
  return [formatDate(todayStart), formatDate(todayEnd)]
})

defineOptions({ name: 'ReplenishmentProposal' })

const onUpdateFormData = (newFormData) => {
  // Reset form fields to their initial state before applying the new filter data
  queryFormRef.value?.resetFields()

  // Manually clear array and other specific fields to ensure a clean state
  queryParams.asinList = []
  queryParams.localSkuList = []
  queryParams.mskuList = []
  queryParams.fnskuList = []
  queryParams.localNameList = []
  queryParams.uids = []
  queryParams.sids = []
  queryParams.countrys = []
  queryParams.countrysList = []
  queryParams.shopNames = []
  queryParams.unames = []
  queryParams.sortingFields = []
  queryParams.customWeightIds = []

  // Apply the new data from the filter over the cleaned state
  Object.assign(queryParams, newFormData)

  // Trigger a new search with the applied filter
  handleQuery()
}

const accountShopSelectorRef = ref(null)

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 表格数据
const markedCount = ref(0) // 已标记补货数量

// 主查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  countrys: [], // 国家
  countrysList: [], // 国家
  sids: [], // 店铺
  shopNames: [], // 店铺名称
  uids: [],
  unames: [],
  inventoryCycle: 30, // 周期库存
  skuType: 'SKU', // 默认选择SKU类型
  skuValue: '', // SKU值
  principalName: '', // 负责人
  globalTagIds: [], //标签
  principalUids: [], // 负责人
  asinList: [], // ASIN
  mskuList: [], // MSKU
  fnskuList: [], // FNSKU
  localSkuList: [], // 本地SKU
  localNameList: [], // 品名
  sortingFields: [] as { field: string; order: string }[], // 排序字段
  applyRestock: '', // 是否标记补货 0:否 1:是
  resuggestRestock: '', // 是否建议补货 0:否 1:是
  customWeightIds: [], // 权重选择
  analysisTime: defaultTimeRange.value //分析时间
})

const skuQueryList = ref([]) // 查询列表的，多个SKU查询列表

const totalWeight = ref(0)

const principalOptions = ref([])
const tagOptions = ref([])
const weightOptions = ref([])

// 获取负责人列表
const getPrincipalListFc = async () => {
  try {
    const res = await ReplenishmentProposalApi.getPrincipalList()
    if (res) {
      // 将接口返回的负责人数据转换为下拉框选项格式
      principalOptions.value = res.map((item) => ({
        label: item.principalName,
        value: item.principalUid
      }))
    }
  } catch (error) {
    console.error('获取负责人列表失败', error)
  }
}

// 获取标签列表
const getTagList = async () => {
  try {
    const res = await ReplenishmentProposalApi.getGlobalTagList()
    if (res) {
      tagOptions.value = res.map((item) => ({
        label: item.tagName,
        value: item.globalTagId
      }))
    }
  } catch (error) {
    console.error('获取标签列表失败', error)
  }
}

// 获取权重选项列表
const getWeightOptions = async () => {
  try {
    const res = await ReplenishmentProposalApi.getWeightOptions()
    if (res) {
      weightOptions.value = res.map((item) => ({
        label: item.name + `（库存周期:${item.inventoryCycle || 0}天）`,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取权重选项列表失败', error)
  }
}

const queryFormRef = ref() // 搜索的表单
interface TableRowData {
  id: number
  [key: string]: any // 允许其他属性
}
const multipleSelection = ref<TableRowData[]>([]) // 选中的列表项

// 列表接口
const getList = async () => {
  loading.value = true
  try {
    const skuTypeMap = {
      ASIN: 'asinList',
      MSKU: 'mskuList',
      FNSKU: 'fnskuList',
      SKU: 'localSkuList',
      品名: 'localNameList'
    }

    // 清空所有列表
    const resetAllLists = () => {
      queryParams.asinList = []
      queryParams.mskuList = []
      queryParams.fnskuList = []
      queryParams.localSkuList = []
      queryParams.localNameList = []
    }

    // 根据skuType设置对应的查询参数
    if (queryParams.skuType && skuTypeMap[queryParams.skuType]) {
      resetAllLists()
      if (queryParams.skuValue !== '') {
        queryParams[skuTypeMap[queryParams.skuType]] = skuQueryList.value
      }
    }
    // 如果是数组，则使用第一个值或者逗号分隔的字符串
    if (Array.isArray(skuQueryList.value) && skuQueryList.value.length > 0) {
      queryParams.skuValue = skuQueryList.value.join(',')
    }

    let qdata = JSON.parse(JSON.stringify(queryParams))

    // 如果qdata.uids有all，删除all
    if (qdata.uids.includes('all')) {
      qdata.uids = qdata.uids.filter((item) => item !== 'all')
    }
    // 如果qdata.sids有all，删除all
    if (qdata.sids.includes('all')) {
      qdata.sids = qdata.sids.filter((item) => item !== 'all')
    }
    const data = await ReplenishmentProposalApi.getTableList(qdata)
    list.value =
      (data.list &&
        data.list.map((item) => {
          item.remarkEdit = item.remark
          item.grossProfitTrendDataList = item.grossProfitTrendDataList || []
          return item
        })) ||
      []
    total.value = data.total
    // 计算已标记补货数量 - 这里可能需要根据实际业务逻辑调整
    markedCount.value = data.list && data.list.filter((item) => item.remark).length
    await getIsMarked()
  } finally {
    loading.value = false
  }
}

// 选择列表项
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 打开诊断历史
const logFormRef = ref()
const openLogForm = (asin: any, id: number) => {
  logFormRef.value.open(asin, id)
}

const isMarked = ref(0)
const getIsMarked = async () => {
  const data = await ReplenishmentProposalApi.getIsMarked(queryParams)
  if (data) {
    isMarked.value = data.applyRestockQty
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.asinList = []
  queryParams.skuValue = ''
  queryParams.localSkuList = []
  queryParams.mskuList = []
  queryParams.fnskuList = []
  queryParams.localNameList = []
  queryParams.uids = []
  queryParams.sids = []
  queryParams.countrys = []
  queryParams.countrysList = []
  queryParams.shopNames = []
  queryParams.unames = []
  queryParams.analysisTime = []
  queryParams.globalTagIds = []
  queryParams.sortingFields = []
  skuQueryList.value = []
  handleQuery()
}

/** 导出选中 */
const replenishmentRequestDialogRef = ref()
const detailDialogRef = ref()
const replenishmentRequestVisible = ref(false)
const copywritingTableRef = ref(null)
// 处理申请补货成功
const handleReplenishmentRequestSuccess = () => {
  getList() // 刷新列表
  multipleSelection.value = [] // 清空选择
  // 如果表格组件有清空选择的方法，也调用一下
  if (copywritingTableRef.value && copywritingTableRef.value.clearSelection) {
    copywritingTableRef.value.clearSelection()
  }
}

// 处理从弹窗中移除列表项
const handleItemRemovedFromDialog = (itemId: number) => {
  multipleSelection.value = multipleSelection.value.filter((item) => item.id !== itemId)
  // 如果表格组件有暴露 toggleRowSelection 方法，可以取消表格行的选中状态
  const rowInTable = list.value.find((item) => item.id === itemId)
  if (rowInTable && copywritingTableRef.value && copywritingTableRef.value.toggleRowSelection) {
    copywritingTableRef.value.toggleRowSelection(rowInTable, false)
  }
}

// 处理申请补货按钮点击
const openDetailDialog = (row: TableRowData) => {
  detailDialogRef.value?.open(row)
}

const handleRequestRestock = (row?: TableRowData) => {
  let selectedData: TableRowData[] = []
  if (row && row.id) {
    // 单行操作：直接传递当前行数据，不修改multipleSelection状态
    selectedData = [row]
  } else {
    // 批量操作
    if (multipleSelection.value.length === 0) {
      message.warning('请至少选择一项进行批量申请补货')
      return
    }
    selectedData = multipleSelection.value
  }

  if (selectedData.length === 0) {
    message.warning('没有有效的选中数据')
    return
  }
  replenishmentRequestDialogRef.value?.openDialog(JSON.parse(JSON.stringify(selectedData))) // 使用深拷贝传递数据
}

const handleExp = async (type) => {
  try {
    let ids = []
    if (type == 'select') {
      if (multipleSelection.value.length === 0) {
        message.warning('请至少选择一项')
        return
      }
      // 获取选中项的ID
      ids = multipleSelection.value.map((item) => item.id)
    } else if (type == 'applyRestock') {
      queryParams.applyRestock = '1'
    }

    const data = await ReplenishmentProposalApi.exportFc(queryParams, ids)
    download.excel(data, '补货分析.xls')
    message.success('导出成功')
  } catch (error) {
    console.error(error)
  }
}

/** 智能补货分析 */
const handleGenerates = async () => {
  try {
    // 检查必填参数
    if (queryParams.uids.length == 0) {
      message.error('请选择领星账号')
      return
    }

    await message.confirm('确定要进行智能补货分析吗？这可能需要一些时间。')
    loading.value = true
    let qdata = JSON.parse(JSON.stringify(queryParams))

    // 如果qdata.uids有all，删除all
    if (qdata.uids.includes('all')) {
      qdata.uids = qdata.uids.filter((item) => item !== 'all')
    }
    // 如果qdata.sids有all，删除all
    if (qdata.sids.includes('all')) {
      qdata.sids = qdata.sids.filter((item) => item !== 'all')
    }

    await ReplenishmentProposalApi.analyzeRestock(qdata)
    queryParams.uids = qdata.uids
    message.success('分析完成')
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 查询领星账号切换
const queryAccountChange = (values, names) => {
  queryParams.uids = values //选全部的时候传出所有账号的ids
}

// 查询店铺切换
const queryShopChange = (values, names) => {
  queryParams.sids = values
  handleQuery()
}

// 国家变更
const queryCountryChange = (value) => {
  queryParams.countrys = value
}

// 修改补货建议列表备注
const handleInput = async (row) => {
  await ElMessageBox.confirm('确定修改备注吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await ReplenishmentProposalApi.batchEditRemark({ ids: [row.id], remark: row.remarkEdit })
      try {
        message.success('修改成功')
      } catch (error) {
        row.remarkEdit = row.remark
        message.error('修改失败')
      }
    })
    .catch(() => {
      row.remarkEdit = row.remark
      console.info('操作取消')
    })
}

// 处理TipInput组件的回车事件
const handleTipInputEnter = (values) => {
  // 定义SKU类型与参数名的映射关系
  skuQueryList.value = values
}

// 打开权重列表弹窗
const weightFormRef = ref()
const ruleSettingsDialogRef = ref()
const openWeightList = () => {
  weightFormRef.value?.openDialog()
}

const openRuleSettingsDialog = (title: string) => {
  if (title === '自定义规则设置') {
    const selectedIds = multipleSelection.value.map((item) => item.id)
    if (selectedIds.length === 0) {
      message.warning('请至少选择一条数据进行自定义规则设置')
      return
    }
    ruleSettingsDialogRef.value?.openDialog(title, selectedIds)
  } else {
    ruleSettingsDialogRef.value?.openDialog(title)
  }
}

const handleRuleSettingsSuccess = (settings) => {
  // 处理保存成功后的逻辑，例如更新查询参数并发起请求
  // queryParams.inventoryCycle = settings.inventoryCycle
  // ... 更新其他权重参数
  handleQuery() // 重新查询列表
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  {
    label: '毛利润趋势',
    field: 'grossProfitTrendDataList',
    width: 200,
    icon: 'ep:warning',
    sortable: false,
    tips: '展示15天毛利润趋势图,点击可查看详情',
    showOverflowTooltip: false
  },
  {
    label: '单件毛利率',
    tips: `单件毛利率：</br>毛利率 = (毛利润 ÷ 销售额) × 100%</br>数据说明：当前展示的单件毛利率为昨日数据。</br>含义：</br>表示每销售一单位产品所带来的利润占销售额的比例。</br>业务意义：</br>反映产品的盈利能力。毛利率越高，产品越有补货价值（建议 ≥30% 为健康）。`,
    field: 'grossMarginPerUnitPercent',
    width: 120,
    icon: 'ep:warning',
    sortable: false //'custom',
  },
  {
    label: '单件毛利润（¥）',
    tips: `单件毛利润：</br>数据说明：当前展示的单件毛利润为昨日数据。</br>含义 ：</br>产品销售收入扣除直接成本后的利润金额。</br>业务意义 ：</br>判断产品是否赚钱。</br>若毛利润为负，即使销量高也不应补货。`,
    field: 'grossProfitPerUnit',
    width: 150,
    formatter: (row) => `¥${row.grossProfitPerUnit || 0}`,
    icon: 'ep:warning',
    sortable: false //'custom',
  },
  {
    label: '广告占比',
    field: 'acoasPercent',
    width: 120,
    icon: 'ep:warning',
    sortable: false, //'custom',
    tips: `广告占比：</br>广告占比 = 广告花费 ÷ 净销售额</br>数据说明：当前展示的广告占比为昨日数据。</br>含义 ：</br>表示整体销售额中，有多少是通过广告“买来的”。</br>业务意义 ：</br>广告占比越低越好，表示自然流量强。</br>广告占比 > 25%：可能过度依赖广告，补货风险高。`
  },
  {
    label: '利润投产比',
    field: 'profitToInvestmentRatio',
    width: 120,
    icon: 'ep:warning',
    sortable: false, //'custom',
    tips: `利润投产比：</br>利润投产比 = ROAS × 毛利率</br>数据说明：当前展示的利润投产比为昨日数据。</br>含义 ：</br>衡量每投入1元广告费用，能带来多少毛利润。</br>业务意义 ：</br>≥ 150% ：广告高效，产品盈利能力强，建议加大投入。</br>100% ~ 150% ：广告健康，保持当前策略。</br>80% ~ 100% ：接近盈亏平衡，需优化广告或调整定价。</br>< 80% ：广告亏损，建议暂停或优化关键词、出价等策略。`
  },
  {
    field: 'predictedDailySales',
    label: '单日预测',
    width: 90,
    sortable: 'custom',
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'predictedInventoryTarget',
    label: '预测总库存',
    width: 100,
    sortable: 'custom',
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'suggestedRestockQuantity',
    label: '建议补货',
    sortable: 'custom',
    width: 90,
    fixed: 'right',
    style: { color: '#f56c6c' }
  },
  {
    field: 'returnGoodsRate',
    label: '退货率',
    width: 100,
    align: 'center',
    sortable: false //'custom'
  },
  {
    field: 'returnGoodsCount',
    label: '退货量',
    width: 100,
    align: 'center',
    sortable: false //'custom'
  },
  {
    field: 'statusDesc',
    label: 'Listing状态',
    width: 100,
    align: 'center'
  },
  {
    field: 'skuInfo',
    label: '品名/SKU',
    width: 200,
    align: 'left',
    fixed: 'left',
    slotName: 'skuInfo',
    showOverflowTooltip: true
  },
  {
    field: 'mskuInfo',
    label: 'MSKU/FNSKU',
    width: 200,
    align: 'left',
    fixed: 'left',
    slotName: 'mskuInfo',
    showOverflowTooltip: true
  },
  {
    field: 'countryShop',
    label: '国家/店铺',
    align: 'left',
    fixed: 'left',
    slotName: 'countryShop',
    width: 150,
    showOverflowTooltip: true
  },
  {
    field: 'asin',
    label: 'ASIN',
    width: 130,
    align: 'left',
    fixed: 'left',
    isCopy: true
  },
  {
    field: 'analyzeTime',
    label: '分析时间',
    width: 160,
    align: 'left'
  },
  {
    field: 'tagsResp',
    label: '标签',
    width: 120,
    minWidth: 120,
    align: 'left',
    showOverflowTooltip: false
  },
  {
    field: 'restockSuggestPrincipalInfoRespVos',
    label: 'Listing负责人',
    width: 130,
    align: 'left'
  },

  {
    field: 'imgUrl',
    label: '图片',
    type: 'image',
    showOverflowTooltip: false
  },
  {
    field: 'price',
    label: '价格',
    width: 80,
    tips: '同步后台的Your Price',
    formatter: (row) => row.landedPrice + row.currencyCode
  },
  // {
  //   field: 'landedPrice',
  //   label: '总价',
  //   width: 80,
  //   tips: `总价: 售价=优惠价+FBM运费-积分\n</br>1、当后台未设置优惠价或优惠价已过期，取价格 (Your Price)\n</br>2、仅日本站有积分数据`,
  //   formatter: (row) => row.landedPrice + row.currencyCode
  // },

  {
    field: 'action',
    label: '操作',
    width: 220,
    fixed: 'right'
  },
  {
    label: '3 | 7 | 14 | 30 | 60 | 90天日均',
    field: 'salesAvg',
    width: 350,
    minWidth: 350,
    slotName: 'salesAvg',
    align: 'center'
  },
  {
    field: 'totalInventory',
    label: '总库存',
    width: 90,
    align: 'center',
    sortable: false, //'custom',
    icon: 'ep:warning',
    tips: '总库存 = 本地可用 + 待检待上架量 + 待交付 + 本地仓在途 + 采购计划 + 海外仓可用 + 海外仓在途 + FBA可售 + FBA在途 + 预计发货量'
  },
  {
    field: 'remark',
    label: '备注',
    width: 100,
    align: 'center',
    slots: {
      default: 'remark'
    }
  },
  {
    field: 'weightFactor',
    label: '权重',
    width: 100,
    align: 'center'
  },
  {
    field: 'inventoryCycle',
    label: '库存周期',
    width: 100,
    align: 'center'
  }
])

onMounted(() => {
  getPrincipalListFc()
  getTagList()
  getWeightOptions()
  setTimeout(() => {
    getList()
  }, 300)
})
</script>
