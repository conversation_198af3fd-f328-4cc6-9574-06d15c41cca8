<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    size="90vw"
    @close="closeDialog"
    class="copywriting_drawer"
  >
    <!-- 列表 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="分析时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-340px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          查询
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      border
      ref="copywritingTableRef"
      v-loading="loading"
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 固定字段 -->
      <el-table-column
        align="center"
        label="单日预测"
        width="90"
        prop="predictedDailySales"
        fixed="right"
        :style="{ right: '55px' }"
      >
        <template #header>
          <div style="color: #f56c6c">单日预测</div>
        </template>
        <template #default="scope">
          <span>{{ scope.row.predictedDailySales || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="预测总库存"
        width="100"
        prop="predictedInventoryTarget"
        fixed="right"
        :style="{ right: '175px' }"
      >
        <template #header>
          <div style="color: #f56c6c">预测总库存</div>
        </template>
        <template #default="scope">
          <span>{{ scope.row.predictedInventoryTarget || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="建议补货"
        width="90"
        prop="suggestedRestockQuantity"
        fixed="right"
        :style="{ right: '295px' }"
      >
        <template #header>
          <div style="color: #f56c6c">建议补货</div>
        </template>
        <template #default="scope">
          <span style="font-weight: bold; color: #67c23a">{{
            scope.row.suggestedRestockQuantity || '-'
          }}</span>
        </template>
      </el-table-column>
      <!-- 不固定字段 -->
      <el-table-column
        align="left"
        label="分析时间"
        width="160"
        :formatter="dateFormatter"
        prop="analyzeTime"
      />
      <el-table-column align="left" label="国家" width="80" prop="country" />
      <el-table-column
        align="left"
        label="Listing负责人"
        width="130"
        prop="restockSuggestPrincipalInfoRespVos"
      >
        <template #default="scope">
          <span>
            {{
              scope.row.restockSuggestPrincipalInfoRespVos
                ?.map((item) => item.principalName)
                .join(',') || '-'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="店铺" width="150" fixed="left" prop="sellerItem" />
      <el-table-column align="left" label="ASIN" width="130" prop="asin" fixed="left" />
      <el-table-column
        align="left"
        label="MSKU"
        width="240"
        prop="msku"
        fixed="left"
        show-overflow-tooltip
      />
      <el-table-column align="left" label="品名" width="200" prop="localName" />
      <el-table-column align="left" label="SKU" width="240" prop="localSku" show-overflow-tooltip />
      <el-table-column
        align="center"
        label="FNSKU"
        width="120"
        prop="fnsku"
        show-overflow-tooltip
      />
      <el-table-column align="center" label="3天日均" width="100" prop="salesAvg3" />
      <el-table-column align="center" label="7天日均" width="100" prop="salesAvg7" />
      <el-table-column align="center" label="14天日均" width="100" prop="salesAvg14" />
      <el-table-column align="center" label="30天日均" width="100" prop="salesAvg30" />
      <el-table-column align="center" label="60天日均" width="100" prop="salesAvg60" />
      <el-table-column align="center" label="90天日均" width="100" prop="salesAvg90" />
      <el-table-column
        align="center"
        label="总库存（含预计发货量）"
        width="90"
        prop="totalInventory"
      >
        <template #header>
          总库存
          <el-tooltip content="（含预计发货量）" raw-content>
            <Icon icon="ep:warning" :size="12" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" label="FBA库存" width="100" prop="fbaInventory" />
      <el-table-column align="center" label="FBA在途" width="100" prop="amazonQuantityShipping" />
      <el-table-column
        align="center"
        label="预计发货量"
        width="100"
        prop="amazonQuantityShippingPlan"
      />
      <el-table-column
        align="center"
        label="海外仓可用"
        width="100"
        prop="scQuantityOverseaValid"
      />
      <el-table-column
        align="center"
        label="海外仓在途"
        width="100"
        prop="scQuantityOverseaShipping"
      />
      <el-table-column align="center" label="本地可用" width="100" prop="scQuantityLocalValid" />
      <el-table-column align="center" label="待检待上架量" width="120" prop="scQuantityLocalQc" />
      <el-table-column
        align="center"
        label="待交付"
        width="100"
        prop="scQuantityPurchaseShipping"
      />
      <el-table-column
        align="center"
        label="本地仓在途"
        width="100"
        prop="scQuantityLocalShipping"
      />
      <el-table-column align="center" label="权重" width="100" prop="weightFactor" />
      <el-table-column align="center" label="库存周期" width="100" prop="inventoryCycle" />
      <el-table-column align="center" label="是否标记需补货" width="130" prop="markedRestock">
        <template #default="scope">
          {{ scope.row.markedRestock === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <template #footer>
      <div style="text-align: center">
        <el-button @click="closeDialog()">关 闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { dateFormatter } from '@/utils/formatTime'
import { useClipboard } from '@vueuse/core'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'ReplenishmentProposalLogForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('诊断历史：') // 弹窗的标题
const loading = ref(false) // 表格加载状态
const recordId = ref<number>() // 记录ID

const total = ref(0) // 列表的总页数
const list = ref([]) // 字典表格数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  id: undefined as number | undefined,
  createTime: [] as string[]
})

// 多选相关
const multipleSelection = ref([])
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('诊断历史：' + type)
  resetForm()
  // 设置ID并获取数据
  if (id) {
    recordId.value = id
    queryParams.id = id
    getList()
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 列表接口
const getList = async () => {
  if (!queryParams.id) {
    message.error('缺少必要参数ID')
    return
  }

  loading.value = true
  try {
    // 处理时间范围
    let params: any = { ...queryParams }
    if (queryParams.createTime && queryParams.createTime.length === 2) {
      params.beginTime = queryParams.createTime[0]
      params.endTime = queryParams.createTime[1]
    }
    delete params.createTime

    const res = await ReplenishmentProposalApi.getLogList(params)
    list.value = res.list || []
    total.value = res.total || 0
  } catch (error) {
    console.error('获取历史诊断数据失败', error)
    message.error('获取历史诊断数据失败')
  } finally {
    loading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
  queryParams.createTime = []
}

/** 关闭弹窗 */
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
  queryParams.id = undefined
  recordId.value = undefined
}
</script>

<style lang="scss" scoped>
.copywriting_drawer {
  :deep(.el-drawer__body) {
    padding: 20px;
  }
}
</style>
