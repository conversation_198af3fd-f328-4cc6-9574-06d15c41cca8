<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'我的权重'"
    :width="dialogWidth"
    @close="closeDialog"
  >
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="weightList"
      style="width: 100%"
    >
      <el-table-column
        type="index"
        label="#"
        width="50"
        align="center"
      />
      <el-table-column
        prop="name"
        label="权重名称"
        width="120"
        align="center"
      >
        <template #default="scope">
          <div
            class="weight-values"
            v-if="!scope.row.isEdit"
          >
            {{ scope.row.name }}
          </div>
          <el-input
            v-else
            v-model="scope.row.name"
            placeholder="请输入权重名称"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="权重值"
        align="center"
      >
        <template #default="scope">
          <div
            class="weight-values"
            v-if="!scope.row.isEdit"
          >
            3天: {{ scope.row.weight3d }}%
            7天: {{ scope.row.weight7d }}%
            14天: {{ scope.row.weight14d }}%
            30天: {{ scope.row.weight30d }}%
            60天: {{ scope.row.weight60d }}%
            90天: {{ scope.row.weight90d }}%
          </div>
          <div
            v-else
            class="flex"
          >
            <el-input-number
              placeholder="3天"
              v-model="scope.row.weight3d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
            <el-input-number
              placeholder="7天"
              v-model="scope.row.weight7d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
            <el-input-number
              placeholder="14天"
              v-model="scope.row.weight14d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
            <el-input-number
              placeholder="30天"
              v-model="scope.row.weight30d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
            <el-input-number
              placeholder="60天"
              v-model="scope.row.weight60d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
            <el-input-number
              placeholder="90天"
              v-model="scope.row.weight90d"
              :min="0"
              :max="100"
              :precision="1"
              :step="1"
              class="!w-full"
              @change="calculateEditTotalWeight(scope.row)"
            />
          </div>
          <span
            v-if="scope.row.isEdit"
            :class="{ 'text-red-500': scope.row.total.toFixed(1) != 100 }"
          >
            {{scope.row.total}}
            总计: {{ scope.row.total.toFixed(1) }}% {{ scope.row.total.toFixed(1) != 100 ? '(必须等于100%)' : '' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="150"
        align="center"
      />
      <el-table-column
        label="操作"
        width="180"
        align="center"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleApply(scope.row)"
          >
            快速应用
          </el-button>
          <el-button
            type="primary"
            link
            @click="scope.row.isEdit? submitEditForm(scope.row):handleEdit(scope.row)"
          >
            {{ scope.row.isEdit ? '保存' : '编辑' }}
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div style="text-align: right;">
        <el-button @click="dialogVisible = false">关 闭</el-button>
        <el-button
          type="primary"
          @click="handleAdd(null)"
        >新 增</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { formatDate } from '@/utils/formatTime'

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false)
const loading = ref(false)
const weightList = ref([])

// 编辑相关
const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = ref({
  id: undefined,
  name: '',
  weight3d: 0,
  weight7d: 0,
  weight14d: 0,
  weight30d: 0,
  weight60d: 0,
  weight90d: 0,
  isEdit: true,
  total: 0
})
const dialogWidth = ref('1200px')
const editTotalWeight = ref(0)

// 编辑表单验证规则
const editRules = {
  name: [
    { required: true, message: '请输入权重名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 计算编辑表单中的总权重
const calculateEditTotalWeight = (row) => {
  row.total =
    row.weight3d + row.weight7d + row.weight14d + row.weight30d + row.weight60d + row.weight90d
}

// 打开弹窗
const openDialog = async () => {
  dialogVisible.value = true
  await getWeightList()
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
}

// 获取权重列表
const getWeightList = async () => {
  loading.value = true
  try {
    const res = await ReplenishmentProposalApi.listWeight()
    if (res) {
      weightList.value = res.map((item) => ({
        ...item,
        createTime: formatDate(item.createTime)
      }))
    }
  } catch (error) {
    console.error('获取权重列表失败', error)
  } finally {
    loading.value = false
  }
}

// 处理编辑
const handleEdit = (row) => {
  row.isEdit = true
  dialogWidth.value = '1700px'
  calculateEditTotalWeight(row)
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该权重吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await ReplenishmentProposalApi.deleteWeight(row.id)
    message.success('删除成功')
    await getWeightList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权重失败', error)
    }
  }
}

// 处理新增
const handleAdd = (row) => {
  editForm.value = row || {
    name: '',
    weight3d: 0,
    weight7d: 0,
    weight14d: 0,
    weight30d: 0,
    weight60d: 0,
    weight90d: 0,
    isEdit: true,
    total: 0
  }
  weightList.value.unshift(editForm.value)
  handleEdit(editForm.value)
}

// 提交编辑表单
const submitEditForm = async (row) => {
  if (!row.name) {
    message.error('请输入权重名称')
    return
  }
  if (row.total.toFixed(1) != 100) {
    message.error('权重总和必须等于100%')
    return
  }
  try {
    if (row.id) {
      await ReplenishmentProposalApi.updateWeight(row)
    } else {
      await ReplenishmentProposalApi.createWeight(row)
    }
    message.success(row.id ? '更新成功' : '新增成功')
    row.isEdit = false
    await getWeightList()
  } catch (error) {
    console.error('提交权重失败', error)
  }
}

// 定义emit事件
const emit = defineEmits(['apply'])

// 处理应用权重
const handleApply = (row) => {
  emit('apply', {
    weight3d: row.weight3d,
    weight7d: row.weight7d,
    weight14d: row.weight14d,
    weight30d: row.weight30d,
    weight60d: row.weight60d,
    weight90d: row.weight90d
  })
  dialogVisible.value = false
}

// 暴露方法给父组件
defineExpose({
  calculateEditTotalWeight,
  openDialog,
  handleAdd
})
</script>

<style lang="scss" scoped>
.weight-values {
  display: flex;
  gap: 4px;

  div {
    display: flex;
  }
}
</style>
