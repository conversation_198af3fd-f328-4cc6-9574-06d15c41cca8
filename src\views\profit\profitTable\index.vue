<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="模板" prop="templateId">
        <el-select
          v-model="queryParams.templateId"
          placeholder="请选择模板"
          clearable
          @change="handleTemplateChange"
          class="!w-240px"
        >
          <el-option
            v-for="item in templateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="appId" prop="appId">
        <el-select
          v-model="queryParams.appId"
          placeholder="请选择"
          clearable
          class="!w-240px"
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="item in loginUrlOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="asin" prop="asin">
        <el-input
          v-model="queryParams.asin"
          placeholder="请输入模板名称"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="orderTime">
        <shortcut-date-range-picker v-model="queryParams.orderTime" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <Table
      :columns="columns"
      :data="list"
      :loading="loading"
      @selection-change="handleSelectionChange"
      v-model:page-size="queryParams.pageSize"
      v-model:current-page="queryParams.pageNo"
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @update:page-size="
        (val) => {
          queryParams.pageSize = val
          getList()
        }
      "
      @update:current-page="
        (val) => {
          queryParams.pageNo = val
          getList()
        }
      "
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ThirdPartyAccountApi } from '@/api/system/third'
import {
  getProfitPage,
  getProfitTableTemplatePage,
  getTableHeadByTemplateId
} from '@/api/tool/profitTableTemplate'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { Table } from '@/components/Table'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

defineOptions({ name: 'ProfitProfitTableIndex' })

const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  asin: '',
  appId: '',
  orderTime: [],
  templateId: null,
  tableHeadId: null
})
const queryFormRef = ref() // 搜索的表单
const templateList = ref<any[]>([]) // 模板列表
const columns = ref<any[]>([]) // 表格列
const selectedRows = ref<any[]>([]) // 表格选中行

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getProfitPage(queryParams)
    list.value = data.list.length > 0 ? data.list.map((item) => item.resultMap) : []
    total.value = data.total
    loading.value = false
  } finally {
    loading.value = false
  }
}

/** 从表头数据生成动态列 */
const generateColumnsFromTableHead = (tableHeadData) => {
  try {
    const newColumns = []

    // 如果tableHeadData是字符串，则解析JSON；如果是对象，则直接使用
    let parsedData = tableHeadData
    if (typeof tableHeadData === 'string') {
      parsedData = JSON.parse(tableHeadData)
    }

    if (parsedData && parsedData.fields && Array.isArray(parsedData.fields)) {
      parsedData.fields.forEach((field) => {
        if (field.fieldId && field.fieldName) {
          newColumns.push({
            ...field,
            label: field.fieldName,
            field: field.fieldId
          })
        }
      })
    }

    columns.value = newColumns
    console.log('生成的列配置:', newColumns)
  } catch (error) {
    console.error('解析表头数据失败:', error)
    ElMessage.error('解析表头数据失败')
  }
}

/** 获取模板列表 */
const getTemplateList = async () => {
  try {
    const data = await getProfitTableTemplatePage({ pageNo: 1, pageSize: 1000 }) // 获取所有模板
    templateList.value = data.list
  } catch (error) {
    ElMessage.error('获取模板列表失败')
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 模板选择变化 */
const handleTemplateChange = async (val) => {
  queryParams.pageNo = 1
  list.value = []
  columns.value = []
  total.value = 0
  queryParams.tableHeadId = null

  if (queryParams.templateId) {
    try {
      // 先获取表头ID和表头数据
      const tableHeadData = await getTableHeadByTemplateId(queryParams.templateId)
      console.log(tableHeadData)
      queryParams.tableHeadId = tableHeadData.id

      // 解析表头JSON字符串并生成列
      if (tableHeadData.tableHead) {
        generateColumnsFromTableHead(tableHeadData.tableHead)
      }

      // 再查询列表数据
      getList()
    } catch (error) {
      ElMessage.error('获取表头信息失败')
    }
  }
}
const loginUrlOptions = ref([])
// 获取历史登录网址
const getHistoryLoginUrls = async () => {
  try {
    // 这里可以替换为实际的API调用，获取历史登录网址
    const res = await ThirdPartyAccountApi.getHistoryLoginUrls()
    loginUrlOptions.value =
      res.map((item) => ({
        value: item.id,
        label: item.platformLoginUrl + `(${item.tenantName})`
      })) || []
  } catch (error) {
    console.error('获取历史登录网址失败', error)
  }
}
/** 表格选中行 */
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

onMounted(() => {
  getTemplateList()
  getHistoryLoginUrls()
})
</script>
