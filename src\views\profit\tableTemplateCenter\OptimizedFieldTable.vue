<template>
  <div class="optimized-field-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <el-button
        type="primary"
        @click="handleAddField"
        :loading="loading"
      >
        <Icon icon="ep:plus" /> 添加字段
      </el-button>
      <div class="table-info">
        <span>共 {{ totalCount }} 条记录</span>
      </div>
    </div>
    <!-- 虚拟滚动表格 -->
    <div
      class="virtual-table-container"
      ref="tableContainer"
      :id="tableId"
    >
      <el-auto-resizer :style="{ height: dynamicHeight }">
        <template #default="{ height, width }">
          <el-table
            v-if="visibleData.length > 0"
            ref="tableRef"
            :data="visibleData"
            :loading="loading"
            border
            stripe
            :height="height"
            class="optimized-table"
            :class="'optimized-table-'+props.category.fieldsKey"
            :row-key="(row, index) => row.id || row._tempId || `${props.category.fieldsKey}_${index}_${row.name}`"
          >
            <!-- 拖拽列 -->
            <el-table-column
              label="排序"
              width="60"
              align="center"
            >
              <template #default="{ $index }">
                <div
                  class="drag-handle cursor-move"
                  style=" display: flex;width: 30px; height: 30px; align-items: center; justify-content: center;"
                  :data-index="$index"
                >
                  <Icon
                    icon="ic:round-drag-indicator"
                    style="font-size: 16px; color: #8a909c;"
                  />
                </div>
              </template>
            </el-table-column>

            <!-- 字段名称列 -->
            <el-table-column label="字段名称">
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.name"
                  placeholder="请输入字段名称"
                  filterable
                  allow-create
                  default-first-option
                  :disabled="props.isRequiredField?.(row.name)"
                  @change="(value) => handleFieldChange($index, 'name', value)"
                  @blur="() => handleDuplicateCheck($index)"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="option in props.fieldOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 是否必填列 -->
            <el-table-column
              label="是否必填"
              width="120"
            >
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.required"
                  :disabled="props.isRequiredField?.(row.name)"
                  @change="(value) => handleFieldChange($index, 'required', value)"
                  style="width: 100%;"
                >
                  <el-option
                    label="是"
                    :value="true"
                  />
                  <el-option
                    label="否"
                    :value="false"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 是否可编辑列 -->
            <el-table-column
              label="是否可编辑"
              width="140"
            >
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.edit"
                  :disabled="props.isRequiredField?.(row.name)"
                  @change="(value) => handleFieldChange($index, 'edit', value)"
                  style="width: 100%;"
                >
                  <el-option
                    label="是"
                    :value="1"
                  />
                  <el-option
                    label="否"
                    :value="0"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 是否有单位列 -->
            <el-table-column
              label="是否有单位"
              width="140"
            >
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.hasUnit"
                  :disabled="props.isRequiredField?.(row.name)"
                  @change="(value) => handleUnitChange($index, value, row)"
                  style="width: 100%;"
                >
                  <el-option
                    label="是"
                    :value="true"
                  />
                  <el-option
                    label="否"
                    :value="false"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 单位列 -->
            <el-table-column
              label="单位"
              width="220"
            >
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.unit"
                  placeholder="单位"
                  filterable
                  :disabled="!row.hasUnit"
                  clearable
                  @change="(value) => handleFieldChange($index, 'unit', value)"
                  style="width: 100%;"
                >
                  <el-option-group
                    v-for="group in props.unitOptions"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.children"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
                </el-select>
              </template>
            </el-table-column>

            <!-- 参数类型列 -->
            <el-table-column
              label="参数类型"
              width="140"
            >
              <template #default="{ row, $index }">
                <el-select
                  v-model="row.paramTypeCode"
                  :disabled="props.isRequiredField?.(row.name)"
                  @change="(value) => handleFieldChange($index, 'paramTypeCode', value)"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="type in props.paramTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column
              label="默认值"
              width="140"
            >
              <template #default="{ row}">
                <el-input
                  v-model="row.defaultValue"
                  placeholder=""
                />
              </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
              label="操作"
              width="120"
              fixed="right"
            >
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  link
                  @click="() => handleDeleteField($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 空数据提示 -->
          <div
            v-else
            class="empty-data-container"
            :style="{ width: width + 'px', height: height + 'px' }"
          >
            <el-empty description="暂无数据" />
          </div>
        </template>
      </el-auto-resizer>
    </div>

  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, h } from 'vue'
import { debounce, throttle } from 'lodash-es'
import Sortable from 'sortablejs'
import { Icon } from '@/components/Icon'

interface FieldItem {
  id?: string | number
  name: string
  required: boolean
  edit: number
  hasUnit: boolean
  unit?: number | null
  paramTypeCode: string
  categoryCode?: string
  categoryName?: string
  [key: string]: any
}

interface Props {
  data: FieldItem[]
  category: {
    label: string
    value: string
    fieldsKey: string
    categoryCode?: string
    categoryName?: string
  }
  fieldOptions: string[]
  paramTypes: Array<{ label: string; value: string }>
  unitOptions: Array<{ label: string; children: Array<{ label: string; value: string }> }>
  loading?: boolean
  enableVirtualScroll?: boolean
  isRequiredField?: (name: string) => boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  enableVirtualScroll: true
})

const emit = defineEmits([
  'update:data',
  'add-field',
  'delete-field',
  'field-change',
  'duplicate-check',
  'unit-change'
])

// 响应式数据
const tableRef = ref()
const tableContainer = ref()
const sortableInstance = ref<Sortable | null>(null)

// 生成唯一的表格ID
const tableId = `field-table-${props.category.fieldsKey}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

// 计算属性
const totalCount = computed(() => props.data.length)

// 动态高度计算
const dynamicHeight = computed(() => {
  const rowHeight = 50 // 每行大约50px
  const headerHeight = 40 // 表头高度
  const padding = 20 // 内边距
  const minHeight = 200 // 最小高度
  const maxHeight = 800 // 最大高度
  
  const calculatedHeight = Math.min(
    Math.max(visibleData.value.length * rowHeight + headerHeight + padding, minHeight),
    maxHeight
  )
  
  return `${calculatedHeight}px`
})

// 显示数据 - 为没有id的数据项生成稳定的临时id
const visibleData = computed(() => {
  return props.data.map((item, index) => {
    if (!item.id) {
      // 使用稳定的字段组合生成临时id，避免使用index以防拖拽后变化
      const tempId = `temp_${props.category.fieldsKey}_${item.name}_${item.paramTypeCode || 'default'}`
      return { ...item, _tempId: tempId }
    }
    return item
  })
})

// 移除tableColumns，改用el-table的列定义

// 防抖处理字段变更
const debouncedFieldChange = debounce((index: number, field: string, value: any) => {
  const newData = [...props.data]
  newData[index] = { ...newData[index], [field]: value }
  emit('update:data', newData)
  emit('field-change', { index, field, value, item: newData[index] })
}, 300)

// 节流处理滚动
const throttledScroll = throttle((scrollInfo: any) => {
  // 处理滚动逻辑，如懒加载等
}, 100)

// 事件处理函数
const handleFieldChange = (index: number, field: string, value: any) => {
  debouncedFieldChange(index, field, value)
}

const handleUnitChange = (index: number, hasUnit: boolean, rowData: any) => {
  const newData = [...props.data]
  newData[index] = { ...newData[index], hasUnit }
  if (!hasUnit) {
    newData[index].unit = null
  }
  emit('update:data', newData)
  emit('unit-change', hasUnit, newData[index])
}

const handleDuplicateCheck = (index: number) => {
  emit('duplicate-check', props.category.fieldsKey, index)
}

const handleAddField = () => {
  emit('add-field', props.category.fieldsKey)
}

const handleDeleteField = (index: number) => {
  emit('delete-field', props.category.fieldsKey, index)
}

const handleScroll = (scrollInfo: any) => {
  throttledScroll(scrollInfo)
}

// 查找表格的tbody元素
const findTableBody = (tableElement) => {
  if (!tableElement) return null
  
  // 查找el-table的tbody元素
  const tbody = tableElement.querySelector('.el-table__body-wrapper tbody')
  if (tbody) {
    return tbody
  }
  
  return null
}

// 初始化拖拽排序 - 优化性能
const initDragSort = () => {
  // 如果数据量很大，延迟初始化拖拽功能
  if (props.data.length > 100) {
    console.log('数据量较大，延迟初始化拖拽功能')
    setTimeout(() => {
      initSortable()
    }, 500) // 延迟500ms初始化
  } else {
    nextTick(() => {
      initSortable()
    })
  }
}

// 抽取初始化Sortable的逻辑，减少代码重复
const initSortable = () => {
  // 等待DOM更新后再查找元素
  setTimeout(() => {
    if (!tableRef.value) {
      return
    }
    
    // 查找表格的tbody元素
    const tbody = findTableBody(tableRef.value.$el)
    
    if (tbody && !sortableInstance.value) {
      sortableInstance.value = new Sortable(tbody, {
        animation: 150, // 降低动画时间
        handle: '.drag-handle',
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        delay: 0,
        delayOnTouchOnly: false,
        touchStartThreshold: 3,
        forceFallback: false, // 使用原生拖拽以提高性能
        fallbackTolerance: 3,
        scroll: true,
        scrollSensitivity: 50,
        scrollSpeed: 20,
        bubbleScroll: true,
        // 指定可拖拽的元素选择器
        draggable: 'tr',
        group: {
          name: tableId,
          pull: false,
          put: false
        },
        onStart: (evt) => {
          document.body.classList.add('dragging-active')
          tbody.classList.add('drag-active')
        },
        onEnd: (evt) => {
          document.body.classList.remove('dragging-active')
          tbody.classList.remove('drag-active')
          const { oldIndex, newIndex } = evt
          
          if (oldIndex !== newIndex && oldIndex !== undefined && newIndex !== undefined) {
            // 确保索引在有效范围内
            if (oldIndex >= 0 && oldIndex < props.data.length && 
                newIndex >= 0 && newIndex < props.data.length) {
              const newData = [...props.data]
              
              // 使用 splice 方法进行数组元素移动
              const movedItem = newData.splice(oldIndex, 1)[0]
              newData.splice(newIndex, 0, movedItem)
              
              emit('update:data', newData)
            }
          }
        },
        onMove: (evt) => {
          // 确保拖拽只在当前表格内进行
          return evt.related.tagName === 'TR'
        }
      })
    } 
  }, 50) // 减少延迟时间
}

// 清理拖拽实例
const destroyDragSort = () => {
  if (sortableInstance.value) {
    sortableInstance.value.destroy()
    sortableInstance.value = null
  }
}

// 监听数据变化重新初始化拖拽 - 优化性能
watch(
  () => props.data.length,
  (newLength, oldLength) => {
    // 只有当数据长度发生显著变化时才重新初始化拖拽
    if (Math.abs(newLength - oldLength) > 5 || !sortableInstance.value) {
      destroyDragSort()
      initDragSort()
    }
  },
  { flush: 'post' }
)

// 监听表格引用变化
watch(
  () => tableRef.value,
  () => {
    if (tableRef.value) {
      destroyDragSort()
      initDragSort()
    }
  },
  { flush: 'post' }
)

// 生命周期
onMounted(() => {
  initDragSort()
})

onUnmounted(() => {
  destroyDragSort()
})

// 暴露方法
defineExpose({
  refresh: () => {
    destroyDragSort()
    initDragSort()
  }
})
</script>

<style lang="scss" scoped>
// 响应式设计
@media (width <= 768px) {
  .optimized-field-table {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .virtual-table-container {
      min-height: 300px;
    }
  }
}

.optimized-field-table {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  .table-toolbar {
    display: flex;
    padding: 0 4px;
    margin-bottom: 16px;
    justify-content: space-between;
    align-items: center;

    .table-info {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  .virtual-table-container {
    position: relative;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    flex: 1;

    .empty-data-container {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-bg-color);
    }

    .optimized-table {
      :deep(.el-table-v2__header-row) {
        background-color: var(--el-fill-color-light);
      }

      :deep(.el-table-v2__row) {
        &:hover {
          background-color: var(--el-fill-color-lighter);
        }
      }

      :deep(.el-table-v2__cell) {
        padding: 8px;
        border-right: 1px solid var(--el-border-color-lighter);
      }

      // 拖拽激活状态
      &.drag-active {
        :deep(.el-table-v2__body) {
          background-color: var(--el-color-primary-light-9);
          border: 2px dashed var(--el-color-primary);
        }
      }
    }
  }
}

// 拖拽相关样式
.drag-handle {
  cursor: move;
  transition: color 0.2s;
  user-select: none;

  &:hover {
    color: var(--el-color-primary) !important;
    transform: scale(1.1);
  }
}

.sortable-ghost {
  background-color: var(--el-color-primary-light-9) !important;
  border: 2px dashed var(--el-color-primary);
  opacity: 0.6;
}

.sortable-chosen {
  background-color: var(--el-color-primary-light-8) !important;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
}

.sortable-drag {
  z-index: 9999;
  background-color: var(--el-color-primary-light-7) !important;
  transform: rotate(2deg) scale(1.05);
  box-shadow: 0 8px 25px rgb(0 0 0 / 20%);
}

.sortable-fallback {
  background-color: var(--el-color-primary-light-6) !important;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

// 全局拖拽状态
:global(.dragging-active) {
  .optimized-field-table {
    :deep(.el-table-v2__table-body) {
      will-change: transform;
    }
  }

  // 禁用其他交互
  * {
    pointer-events: none;
  }

  .drag-handle,
  .sortable-chosen,
  .sortable-ghost {
    pointer-events: auto;
  }
}
</style>
