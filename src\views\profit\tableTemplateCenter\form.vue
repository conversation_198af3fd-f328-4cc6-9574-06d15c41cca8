<template>
  <ContentWrap>
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      class="template-form"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <h2 class="section-title">基本信息</h2>
      <div class="form-section basic-info" style="box-shadow: unset !important">
        <div class="basic-info-content">
          <el-form-item
            label="国家"
            prop="country"
            :rules="[{ required: true, message: '请选择国家', trigger: 'change' }]"
          >
            <el-select
              v-model="formData.country"
              placeholder="请选择国家"
              clearable
              filterable
              class="short-input"
            >
              <el-option
                v-for="item in countryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="模板名称"
            prop="templateName"
            :rules="[{ required: true, message: '请输入模板名称', trigger: 'blur' }]"
          >
            <el-input
              v-model="formData.templateName"
              placeholder="请输入模板名称"
              class="short-input"
            />
          </el-form-item>

          <!-- <el-form-item
            label="店铺类型"
            prop="type"
            :rules="[{ required: true, message: '请选择店铺类型', trigger: 'change' }]"
          >
            <el-select
              v-model="formData.type"
              placeholder="请选择店铺类型"
              clearable
              class="short-input"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_SHOP_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item> -->
        </div>
      </div>

      <h2 class="section-title" style="margin-bottom: 20px">字段配置</h2>
      <!-- 字段配置区块 -->
      <div class="form-section">
        <div class="category-header">
          <h2 class="category-title">利润字段</h2>
        </div>

        <div data-category="profitFields">
          <Table
            height="600px"
            ref="tableRef_profitFields"
            :columns="columns"
            :data="formData.profitFields"
            draggable
            :showColumnConfig="false"
            :dragField="(row, index) => (row.id ? row.id : `profitFields_${index}_${row.name}`)"
            @update:data="(newData) => handleFieldDataUpdate('profitFields', newData)"
          >
            <template #name="{ row, $index }">
              <el-select
                :ref="`nameSelect_profitFields_${$index}`"
                v-model="row.name"
                placeholder="请输入字段名称"
                filterable
                allow-create
                default-first-option
                :disabled="isRequiredField(row.name)"
                @change="checkDuplicate('profitFields', $index)"
              >
                <el-option
                  v-for="item in getFieldNameOptions('profit')"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </template>
            <template #code="{ row }">
              <el-input
                v-model="row.code"
                placeholder="请输入code"
                :disabled="isRequiredField(row.name)"
              />
            </template>
            <template #hasUnit="{ row }">
              <el-select
                v-model="row.hasUnit"
                :disabled="isRequiredField(row.name)"
                @change="(val) => handleUnitChange(val, row)"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </template>
            <template #unit="{ row }">
              <el-select
                v-model="row.unit"
                placeholder="单位"
                filterable
                :disabled="!row.hasUnit"
                clearable
              >
                <el-option-group
                  v-for="group in unitOptions"
                  :key="group.label"
                  :label="group.label"
                >
                  <el-option
                    v-for="item in group.children"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-option-group>
              </el-select>
            </template>
            <template #paramTypeCode="{ row }">
              <el-select v-model="row.paramTypeCode" :disabled="isRequiredField(row.name)">
                <el-option
                  v-for="type in paramTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </template>
            <template #defaultValue="{ row }">
              <el-input
                v-model="row.defaultValue"
                placeholder=""
                :disabled="isRequiredField(row.name)"
              />
            </template>

            <template #basic="{ row }">
              <el-select v-model="row.basic" :disabled="isRequiredField(row.name)">
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </template>
            <template #action="{ $index }">
              <el-button
                type="danger"
                link
                @click="handleDelete('profitFields', $index)"
                :disabled="isRequiredField(formData.profitFields[$index].name)"
              >
                删除
              </el-button>
            </template>
          </Table>
        </div>
        <el-button style="margin: 10px 0" type="primary" @click="handleAddField('profitFields')">
          <Icon icon="ep:plus" /> 添加字段
        </el-button>
      </div>

      <!-- 公式配置区块 -->
      <h2 class="section-title">公式配置</h2>
      <div class="form-section formula-config">
        <!-- 动态生成的公式配置项 -->
        <div v-for="(formula, index) in formData.formulas" :key="index" class="formula-item">
          <div class="formula-header">
            <span class="formula-title"
              >{{
                formula && formula.calculationField ? formula.calculationField + '  ' : ''
              }}公式配置</span
            >

            <div class="formula-actions">
              <el-button
                type="primary"
                link
                @click="openTestDialog(formula, index)"
                v-hasPermi="['infra:tools-table-template:test']"
              >
                测试
              </el-button>
              <el-button
                type="danger"
                link
                v-hasPermi="['infra:tools-table-template:delete']"
                @click="removeFormula(index)"
              >
                删除
              </el-button>
            </div>
          </div>

          <div class="formula-content">
            <div class="formula-row">
              <span class="formula-label">计算字段：</span>
              <el-select
                v-model="formula.calculationField"
                placeholder="请选择计算字段"
                filterable
                clearable
                class="formula-select"
                @change="checkFieldUnique(index)"
              >
                <!-- @change="handleCalculationFieldChange" -->
                <el-option-group
                  v-for="category in fieldCategories"
                  :key="category.value"
                  :label="category.label"
                >
                  <el-option
                    v-for="field in getFieldsFromCategory(category.fieldsKey)"
                    :key="field.name"
                    :label="field.name"
                    :value="field.name"
                    :disabled="isFieldUsedInFormula(field.name)"
                  />
                </el-option-group>
              </el-select>
            </div>

            <div class="formula-row">
              <span class="formula-label">公式操作：</span>
              <div class="formula-operation">
                <el-select
                  v-model="formula.selectedField"
                  placeholder="请选择字段"
                  filterable
                  clearable
                  class="formula-select"
                  @change="handleFieldSelect($event, index)"
                >
                  <el-option-group
                    v-for="category in fieldCategories"
                    :key="category.value"
                    :label="category.label"
                  >
                    <el-option
                      v-for="field in getFieldsFromCategory(category.fieldsKey)"
                      :key="field.name"
                      :label="field.name"
                      :value="field.name"
                    />
                  </el-option-group>
                </el-select>
                <div class="formula-operators">
                  <el-button @click="addOperator('+', index)">+</el-button>
                  <el-button @click="addOperator('-', index)">-</el-button>
                  <el-button @click="addOperator('*', index)">*</el-button>
                  <el-button @click="addOperator('/', index)">/</el-button>
                  <el-button @click="addOperator('(', index)">（</el-button>
                  <el-button @click="addOperator(')', index)">）</el-button>
                  <el-popover ref="popover" placement="right" width="auto" trigger="focus">
                    <div class="keyboard-container">
                      <el-button
                        v-for="symbol in symbols"
                        :key="symbol"
                        :class="{ 'symbol-active': formula.tempSymbol === symbol }"
                        @click="confirmSymbol(symbol, index)"
                      >
                        {{ symbol }}
                      </el-button>
                    </div>
                    <template #reference>
                      <!-- <el-button @click="openKeyboard"> -->
                      <el-button>
                        <Icon icon="ep:more" />
                      </el-button>
                    </template>
                  </el-popover>
                </div>
              </div>
            </div>

            <div class="formula-row">
              <span class="formula-label">公式预览：</span>
              <div class="formula-preview">
                {{ formula.calculationField }} =
                <el-input
                  style="flex: 1"
                  v-model="formula.formula"
                  placeholder="公式内容"
                  clearable
                  type="textarea"
                  @input="(val) => (formula.rule = val)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 新增配置按钮 -->
      <el-button type="primary" class="add-formula-btn" @click="addFormula">
        <Icon icon="ep:plus" /> 新增计算公式
      </el-button>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 保存 </el-button>
      </div>
    </el-form>
  </ContentWrap>

  <!-- 公式测试弹窗 -->
  <el-dialog
    v-model="testDialogVisible"
    :title="currentTestFormula.calculationField + ' 测试'"
    width="400px"
    :close-on-click-modal="false"
    class="test-formula-dialog"
  >
    <div class="test-dialog-content">
      <!-- 字段输入区域 -->
      <div class="test-fields" v-if="testFields.length > 0">
        <div class="field-input-item" v-for="(field, index) in testFields" :key="field + index">
          <label class="field-label">{{ field.name }}:</label>
          <el-input
            v-model="testFormData[field.name]"
            :placeholder="field.name"
            class="field-input"
          />
        </div>
      </div>

      <!-- 公式显示区域 -->
      <div class="formula-display">
        <div class="section-title">公式</div>
        <div class="formula-content">
          {{ currentTestFormula.formula || '暂无公式' }}
        </div>
      </div>

      <!-- 测试结果区域 -->
      <div class="test-result-display">
        <div class="section-title">测试结果</div>
        <div class="result-content">
          <div v-if="testResult !== null">
            <div v-if="!testResult.error" class="result-success">
              {{ testResult }}
            </div>
            <div v-else class="result-error">
              {{ testResult.error || '公式校验失败' }}
            </div>
          </div>
          <div v-else class="result-placeholder"> 点击测试按钮查看结果 </div>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="test-button-container">
        <el-button
          type="primary"
          @click="executeFormulaTest"
          :loading="testLoading"
          class="test-button"
        >
          测试
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="tsx" setup>
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { ProfitTableTemplateApi } from '@/api/tool/profitTableTemplate'
import { ToolsTableTemplateApi } from '@/api/tool/tableTemplateCenter'
import { useTagsView } from '@/hooks/web/useTagsView'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { handleTree } from '@/utils/tree'
import { ElMessage } from 'element-plus'
import { getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
// import ProfitTemplateServiceWorker from './sw-optimization.js'

const { closeCurrent } = useTagsView()
const route = useRoute()
const { proxy } = getCurrentInstance()

defineOptions({ name: 'ProfitTableTemplateCenterForm' })

const columns = ref([
  {
    label: '字段名称',
    field: 'name'
  },
  {
    label: 'code',
    field: 'code',
    width: 220
  },
  {
    label: '是否为固定字段',
    field: 'basic',
    width: 140
  },
  {
    label: '是否有单位',
    field: 'hasUnit',
    width: 140
  },
  {
    label: '单位',
    field: 'unit',
    width: 220
  },
  {
    label: '参数类型',
    field: 'paramTypeCode',
    width: 140
  },
  {
    label: '默认值',
    field: 'defaultValue',
    width: 140
  },
  {
    label: '操作',
    field: 'action',
    width: '150'
  }
])

// 字段分类配置
const fieldCategories = [
  {
    label: '利润字段',
    value: 'profit',
    fieldsKey: 'profitFields',
    categoryCode: 0,
    categoryName: '利润字段'
  }
]

// 参数类型选项
const paramTypes = JSON.parse(JSON.stringify(getIntDictOptions(DICT_TYPE.INFRA_PARAM_TYPE))) || []
const loading = ref(false)

// 测试弹窗相关数据
const testDialogVisible = ref(false)
const testLoading = ref(false)
const currentTestFormula = ref({})
const currentFormulaIndex = ref(-1)
const testFields = ref([])
const testFormData = reactive({})
const testResult = ref(null)

// 表单数据结构
const formData = reactive({
  country: '',
  templateName: '',
  type: '',
  profitFields: [],
  formulas: [
    {
      calculationField: '',
      selectedField: '',
      tempSymbol: '',
      formula: '',
      rule: ''
    }
  ] // 存储公式配置
})

// 公式配置数据
const formulaData = reactive({
  calculationField: '', // 计算字段
  selectedField: '', // 当前选择的字段
  formula: '', // 公式内容
  tempSymbol: '' // 临时存储选择的符号
})

const symbols = [
  '+',
  '-',
  '*',
  '/',
  '(',
  ')',
  ',',
  '.',
  '%',
  '=',
  '<',
  '>',
  '<=',
  '>=',
  '!=',
  '==',
  '&&',
  '||',
  '!'
]

// 利润表必填字段列表（预留功能）
const profitRequiredFields = [
  { code: 'currencyCode', name: '货币代码' },
  { code: 'currencyIcon', name: '货币符号' },
  { code: 'grossProfit', name: '毛利润' },
  { code: 'grossMargin', name: '毛利率' },
  { code: 'avgGrossProfit', name: '平均毛利润' },
  { code: 'volume', name: '销量' },
  { code: 'replacementQuantity', name: '补换货量' },
  { code: 'multiChannelVolume', name: '多渠道销量' },
  { code: 'adSalesAmount', name: '广告销售额' },
  { code: 'adVolume', name: '广告销量' },
  { code: 'amount', name: '销售额' },
  { code: 'afnAmount', name: 'FBA销售额' },
  { code: 'mfnAmount', name: 'FBM销售额' },
  { code: 'taxAmount', name: '含税销售额' },
  { code: 'refundAmount', name: '退款金额' },
  { code: 'refundAmountRate', name: '退款率' },
  { code: 'shippingCost', name: '买家运费' },
  { code: 'promotionDiscount', name: '促销折扣' },
  { code: 'pmDiscount', name: '价格折扣' },
  { code: 'spDiscount', name: '配送折扣' },
  { code: 'returnQuantity', name: '退货量' },
  { code: 'returnRate', name: '退货率' },
  { code: 'sellingFee', name: '平台费' },
  { code: 'fulfillmentFee', name: 'FBA发货费' },
  { code: 'otherOrderFee', name: '其他订单费用' },
  { code: 'spend', name: '花费' },
  { code: 'adsSbCost', name: 'SB广告花费' },
  { code: 'adsSbvCost', name: 'SBV广告花费' },
  { code: 'adsSdCost', name: 'SD广告花费' },
  { code: 'adsSpCost', name: 'SP广告花费' },
  { code: 'purchaseCosts', name: '采购成本' },
  { code: 'avgPurchaseCosts', name: '采购均价' },
  { code: 'logisticsCosts', name: '头程成本' },
  { code: 'avgLogisticsCosts', name: '头程均价' },
  { code: 'otherCosts', name: '其他成本' },
  { code: 'avgOtherCosts', name: '其他均价' },
  { code: 'totalCosts', name: '合计成本' },
  { code: 'isParent', name: '是否有子项' },
  { code: 'smallImageUrl', name: '图片链接' },
  { code: 'itemName', name: '品名' },
  { code: 'refundQuantity', name: '退款量' },
  { code: 'principalNames', name: 'Listing负责人' },
  { code: 'adSalesAmountSp', name: 'SP广告销售额' },
  { code: 'adSalesAmountSd', name: 'SD广告销售额' },
  { code: 'adSalesAmountSb', name: 'SB广告销售额' },
  { code: 'adSalesAmountSbv', name: 'SBV广告销售额' },
  { code: 'adVolumeSp', name: 'SP广告销量' },
  { code: 'adVolumeSd', name: 'SD广告销量' },
  { code: 'adVolumeSb', name: 'SB广告销量' },
  { code: 'adVolumeSbv', name: 'SBV广告销量' },
  { code: 'afnVolume', name: 'FBA销量' },
  { code: 'mfnVolume', name: 'FBM销量' },
  { code: 'localSku', name: '本地SKU' },
  { code: 'localName', name: '本地名称' },
  { code: 'parentAsin', name: '父ASIN' },
  { code: 'asin', name: 'ASIN' },
  { code: 'sid', name: '店铺ID' },
  { code: 'cateTitle', name: '分类标题' },
  { code: 'country', name: '国家' },
  { code: 'appId', name: 'APP表主键ID' },
  { code: 'orderDate', name: '订单时间' }
]
const profitRequiredFieldNames = profitRequiredFields.map((item) => item.name)

// 国家选项
const countryOptions = ref([])

// 表单类型
const formType = ref('create')

// 单位字典
const TreeUnit = getIntDictOptions(DICT_TYPE.INFRA_TREE_UNIT)

// 单位选项
const unitOptions = handleTree(TreeUnit, 'value', 'parentId')

// Service Worker 优化相关
const swOptimization = ref(null)
const swStatus = reactive({
  enabled: false,
  cacheStatus: null,
  isOnline: navigator.onLine,
  preloadProgress: 0
})

// 初始化Service Worker优化（已禁用）
const initServiceWorkerOptimization = async () => {
  // Service Worker优化已被禁用
  swStatus.enabled = false
  console.log('[页面] Service Worker优化已禁用')
  return
  
  // 以下代码已被禁用
  /*
  try {
    // 动态导入 Service Worker 模块
    const { default: ProfitTemplateServiceWorker } = await import('./sw-optimization.ts')
    swOptimization.value = new ProfitTemplateServiceWorker()
    swStatus.enabled = swOptimization.value.isEnabled

    if (swStatus.enabled) {
      // 获取缓存状态
      swStatus.cacheStatus = await swOptimization.value.getCacheStatus()

      // 预加载模板详情（如果是编辑模式）
      if (route.query.id && route.query.type === 'edit') {
        await swOptimization.value.preloader.preloadTemplateDetail(route.query.id)
      }

      console.log('[页面] Service Worker优化已启用')
    }
  } catch (error) {
    console.error('[页面] Service Worker优化初始化失败:', error)
  }
  */
}

// 监听网络状态变化
const setupNetworkMonitoring = () => {
  const updateOnlineStatus = () => {
    swStatus.isOnline = navigator.onLine
    if (!swStatus.isOnline) {
      ElMessage.warning('网络连接已断开，正在使用缓存数据')
    } else {
      ElMessage.success('网络连接已恢复')
    }
  }

  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
}

// 优化的API请求函数
const optimizedFetch = async (url, options = {}) => {
  try {
    const response = await fetch(url, options)

    // 如果Service Worker启用，响应会被自动缓存
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response
  } catch (error) {
    // 如果网络请求失败且Service Worker启用，会自动使用缓存
    if (!swStatus.isOnline && swStatus.enabled) {
      console.log('[页面] 网络不可用，Service Worker将提供缓存数据')
    }
    throw error
  }
}

// 手动刷新缓存
const refreshCache = async () => {
  if (!swOptimization.value) return

  try {
    await swOptimization.value.clearCache()
    await swOptimization.value.preloadData()
    swStatus.cacheStatus = await swOptimization.value.getCacheStatus()
    ElMessage.success('缓存已刷新')
  } catch (error) {
    console.error('[页面] 缓存刷新失败:', error)
    ElMessage.error('缓存刷新失败')
  }
}

// 新增空白公式配置
const addFormula = () => {
  formData.formulas.push({
    calculationField: '',
    formula: '',
    rule: ''
  })
}

// 检查字段唯一性
const checkFieldUnique = (currentIndex: number) => {
  const currentField = formData.formulas[currentIndex].calculationField
  if (!currentField) return

  const isDuplicate = formData.formulas.some(
    (formula, index) => formula.calculationField === currentField && index !== currentIndex
  )

  if (isDuplicate) {
    ElMessage.warning(`计算字段 ${currentField} 已存在`)
    formData.formulas[currentIndex].calculationField = ''
  }
}

// 从公式中提取字段名
function extractFieldsByString(inputStr) {
  if (!inputStr) {
    testFields.value = []
    return
  }
  // 使用正则表达式提取字段名，支持带括号的字段名
  // 匹配：字母、中文、下划线开头，后面可以跟字母、数字、中文、下划线，以及括号内的内容
  const regex = /[a-zA-Z_\u4e00-\u9fa5][\w\u4e00-\u9fa5]*(?:[（(][^）)]*[）)])?/g
  const keywords = new Set(['if', 'else', 'return', 'function', 'var', 'let', 'const'])

  const matches = inputStr.match(regex) || []

  const uniqueFieldNames = [
    ...new Set(matches.filter((match) => !keywords.has(match) && !/^\d+(\.\d+)?$/.test(match)))
  ]
  let result = []
  uniqueFieldNames.map((item) => {
    if (item != '未知') {
      result.push({ name: item, value: '' })
    }
  })
  // console.log(result)
  return result
}

// 打开测试弹窗
const openTestDialog = (formula: FormulaConfig, index: number) => {
  if (!formula.calculationField || !formula.formula) {
    return ElMessage.warning('请先填写完整配置')
  }

  // 设置当前测试的公式
  currentTestFormula.value = { ...formula }
  currentFormulaIndex.value = index

  // 解析公式中的字段
  const fields = extractFieldsByString(formula.rule)
  testFields.value = fields
  // 重置测试表单数据
  Object.keys(testFormData).forEach((key) => {
    delete testFormData[key]
  })
  fields.forEach((field) => {
    testFormData[field.name] = ''
  })

  // 重置测试结果
  testResult.value = null

  // 显示弹窗
  testDialogVisible.value = true
}

// 执行公式测试
const executeFormulaTest = async () => {
  try {
    testLoading.value = true
    const emptyFields = []
    // 检查是否所有字段都有值
    for (const key in testFormData) {
      if (!testFormData[key]) {
        emptyFields.push({ name: key })
      }
    }
    if (emptyFields.length > 0) {
      ElMessage.warning(`请填写 ${emptyFields[0].name} 的测试值`)
      return
    }
    // 构建测试参数
    const testParams = {
      rule: currentTestFormula.value.formula,
      variables: testFormData
    }

    // TODO: 调用后端测试接口
    const result = await ToolsTableTemplateApi.testFormulaWithValues(testParams)

    // 预留接口调用方法
    // const result = await testFormulaWithValues(testParams)

    testResult.value = result
  } catch (error) {
    console.error('公式测试失败:', error)
    testResult.value = {
      error: error.message || '公式校验失败'
    }
  } finally {
    testLoading.value = false
  }
}

// 删除配置
const removeFormula = (index: number) => {
  if (formData.formulas.length <= 1) {
    formData.formulas = [
      {
        calculationField: '',
        selectedField: '',
        tempSymbol: '',
        formula: '',
        rule: ''
      }
    ]
  } else {
    formData.formulas.splice(index, 1)
  }
}

// 自动聚焦到新增行的name字段
const focusFc = (fieldsKey: string) => {
  const newIndex = formData[fieldsKey].length - 1
  const selectRef =
    proxy.$refs[`nameSelect_${fieldsKey}_${newIndex}`].length > 0
      ? proxy.$refs[`nameSelect_${fieldsKey}_${newIndex}`][0]
      : ''
  if (selectRef && selectRef.focus) {
    selectRef.focus()
  }
}

// 字段操作逻辑
const handleAddField = async (fieldsKey: string) => {
  if (formData[fieldsKey].some((item) => item.name === '')) {
    return ElMessage.warning('请先填写或删除空白数据')
  }
  formData[fieldsKey].push({
    name: '',
    code: '',
    hasUnit: false,
    unit: '',
    paramTypeCode: null,
    defaultValue: '',
    basic: 0
  })
}

// 检查是否为必加字段
const isRequiredField = (fieldName: string) => {
  const requiredFields = profitRequiredFieldNames
  return requiredFields.includes(fieldName)
}

// 删除字段
const handleDelete = (fieldsKey: string, index: number) => {
  const fieldName = formData[fieldsKey][index].name

  // 检查是否为必加字段
  if (isRequiredField(fieldName)) {
    ElMessage.warning('必加字段不能删除')
    return
  }

  // 删除字段
  formData[fieldsKey].splice(index, 1)
}

const handleUnitChange = (val: boolean, row: any) => {
  if (!val) row.unit = null
}

// 重复检查
const checkDuplicate = (fieldsKey: string, index: number) => {
  const currentName = formData[fieldsKey][index].name
  if (!currentName) return
  // 检查当前分类下是否有重复
  const hasDuplicateInCategory = formData[fieldsKey].some(
    (field, i) => field.name === currentName && i !== index
  )

  // 检查所有分类下是否有重复
  let hasDuplicateInOtherCategory = false
  for (const category of fieldCategories) {
    if (category.fieldsKey !== fieldsKey) {
      const duplicateExists = formData[category.fieldsKey].some(
        (field) => field.name === currentName
      )
      if (duplicateExists) {
        hasDuplicateInOtherCategory = true
        break
      }
    }
  }

  if (hasDuplicateInCategory || hasDuplicateInOtherCategory) {
    ElMessage.warning(`'${currentName}' 已存在，重复字段已删除`)
    // 直接删除重复的字段
    formData[fieldsKey].splice(index, 1)
  }
}

// 公式配置相关方法
// 获取指定分类的字段列表
const getFieldsFromCategory = (fieldsKey: string) => {
  return formData[fieldsKey] || []
}

// 检查字段是否已被用作计算字段
const isFieldUsedInFormula = (fieldName: string) => {
  return formData.formulas.some((formula) => formula.calculationField === fieldName)
}

// 处理计算字段变更
const handleCalculationFieldChange = (value: string) => {
  if (!value) {
    formulaData.formula = ''
    return
  }

  // 检查是否已存在该计算字段的公式
  const existingFormula = formData.formulas.find((f) => f.calculationField === value)
  if (existingFormula) {
    formulaData.formula = existingFormula.formula
  } else {
    formulaData.formula = ''
  }
}

// 处理字段选择
const handleFieldSelect = (value: string, index: number) => {
  if (value) {
    // 将选择的字段添加到公式中
    formData.formulas[index].formula += value
    formData.formulas[index].rule += value
    formData.formulas[index].selectedField = ''
  }
}

// 添加操作符
const addOperator = (operator: string, index: number) => {
  formData.formulas[index].formula += operator
}

// 选择的符号
const confirmSymbol = (symbol: string, index: number) => {
  if (symbol) {
    formData.formulas[index].formula += symbol
  }
}
const formRef = ref()
// 保存提交
const handleSubmit = async () => {
  // 校验必填formData必填项
  const valid = await formRef.value.validate()
  if (!valid) return

  try {
    // 合并所有字段，过滤掉字段名称为空的数据
    const allFields = fieldCategories.reduce((acc, category) => {
      const fields = formData[category.fieldsKey]
        .filter((field) => field.name && field.name.trim() !== '') // 过滤掉字段名称为空的数据
        .map((field) => {
          const fieldData = {
            id: field.id,
            name: field.name,
            code: field.code,
            category: category.value,
            categoryCode: category.categoryCode,
            categoryName: category.categoryName,
            required: field.required ? 1 : 0,
            edit: field.edit,
            needUnit: field.hasUnit ? 1 : 0,
            unit: field.unit ? Number(field.unit) : null,
            paramTypeCode: field.paramTypeCode,
            defaultValue: field.defaultValue,
            basic: field.basic,
            paramTypeName: paramTypes.find((p) => p.value === field.paramTypeCode)?.label
          }

          // 查找该字段是否有公式配置
          // const formula = formData.formulas.find((f) => f.calculationField === field.name)
          const formula = formData.formulas.find((f) => f.calculationField === field.name)
          if (formula) {
            fieldData.rule = formula.rule || formula.formula
          }
          return fieldData
        })
      return acc.concat(fields)
    }, [])
    // 构造请求参数
    const params = {
      name: formData.templateName,
      nation: formData.country,
      type: formData.type,
      templateTypeCode: '1', // 固定为利润表
      templateTypeName: '利润表',
      fields: allFields
    }

    // 根据场景选择API
    if (route.query.id && formType.value == 'edit') {
      params.id = route.query.id
      await ProfitTableTemplateApi.update(params)
    } else {
      await ProfitTableTemplateApi.create(params)
    }
    ElMessage.success('操作成功')
    handleCancel()
  } catch (error) {
    console.error('保存失败', error)
  }
}

// 初始化必加字段
const initRequiredFields = () => {
  // 清空现有字段
  fieldCategories.forEach((category) => {
    formData[category.fieldsKey] = []
  })

  // 初始化利润字段
  formData.profitFields = profitRequiredFields.map((field) => ({
    name: field.name,
    code: field.code,
    hasUnit: false,
    unit: '',
    paramTypeCode: null,
    defaultValue: '',
    basic: 1
  }))

  loading.value = false
}

// 创建字段对象
const createField = (name: string, isRequired: boolean = false) => {
  // 检查是否为必加字段
  const isRequiredFieldName = isRequiredField(name)

  // 默认编辑权限设置
  let defaultEditValue = 1 // 默认可编辑

  // 根据字段名称设置默认参数类型
  let defaultParamType = 'input'
  if (name === '图片') {
    defaultParamType = 'file'
  } else if (name.includes('时间') || name.includes('日期')) {
    defaultParamType = 'time'
  }

  return {
    name,
    required: isRequired,
    edit: defaultEditValue,
    hasUnit: false,
    unit: null,
    paramTypeCode: defaultParamType
  }
}

route.meta.title = route.query.name

// 初始化加载
const initData = async () => {
  loading.value = true
  // 加载国家列表
  const countryRes = await ReplenishmentProposalApi.getCountryList()
  countryOptions.value = countryRes.map((item) => ({
    label: item.country,
    value: item.country
  }))

  formType.value = route.query.type || 'create'
  // 处理编辑/复制场景
  if (route.query.id) {
    let detail

    // 如果是固定模板的复制操作，使用本地数据
    // const fixedTemplate = fixedTemplateData.find((template) => template.id === route.query.id)
    // if (fixedTemplate && route.query.type === 'copy') {
    //   detail = fixedTemplate
    // } else {
    // 其他情况调用接口获取详情
    detail = await ProfitTableTemplateApi.get(route.query.id)
    // }

    Object.assign(formData, {
      templateName:
        (route.query.type && route.query.type) == 'edit' ? detail.name : detail.name + '--复制',
      country: detail.nation
    })

    // 分配字段到对应分类
    fieldCategories.forEach((category) => {
      formData[category.fieldsKey] = detail.profitFieldDtoList
        .filter((f) => f.categoryCode == category.categoryCode)
        .map((f) => {
          const fieldData = {
            ...f,
            required: f.required === 1,
            edit: f.edit,
            hasUnit: f.needUnit === 1,
            unit: f.unit ? Number(f.unit) : null,
            basic: f.basic,
            // 确保categoryCode和categoryName被正确映射
            categoryCode: f.categoryCode || category.categoryCode,
            categoryName: f.categoryName || category.categoryName
          }

          // 除了编辑模式外，删除字段的id
          if (route.query.type !== 'edit') {
            delete fieldData.id
          }

          return fieldData
        })
    })
    formData.formulas = detail.profitFieldDtoList
      .filter((f) => f.rule != null && f.rule != '') // 严格过滤rule非空
      .map((f) => ({
        // 直接map转换结构
        calculationField: f.name,
        selectedField: '',
        tempSymbol: '',
        formula: f.rule,
        rule: f.rule
      }))

    if (formData.formulas.length == 0) {
      formData.formulas = [
        {
          calculationField: '',
          selectedField: '',
          tempSymbol: '',
          formula: '',
          rule: ''
        }
      ]
    }
    loading.value = false
  } else {
    // 新建模板时，初始化必加字段
    initRequiredFields()
  }
}

// 取消操作,并关闭标签页
const handleCancel = () => {
  history.back()
  closeCurrent(route)
}

onMounted(() => {
  initData()
})

// 根据分类获取字段名称选项
const getFieldNameOptions = (category: string) => {
  // 利润字段选项
  const profitFieldOptions = {
    profit: []
  }

  // 过滤掉已经选择的字段
  const fieldsKey = fieldCategories.find((c) => c.value === category)?.fieldsKey
  if (fieldsKey) {
    const selectedFields = formData[fieldsKey].map((field) => field.name)
    return (profitFieldOptions[category] || []).filter((field) => !selectedFields.includes(field))
  }

  return profitFieldOptions[category] || []
}

// 优化字段数据更新，避免不必要的深拷贝
const handleFieldDataUpdate = (fieldsKey, newData) => {
  // 直接赋值，避免深拷贝，Vue 3的响应式系统会处理
  formData[fieldsKey] = newData

  // 开发环境下才输出调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log(`更新 ${fieldsKey} 数据，长度:`, newData.length)
  }
}
</script>

<style lang="scss" scoped>
// 符号键盘弹窗样式
.keyboard-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  padding: 10px;

  :deep(.el-button) {
    display: flex;
    height: 50px;
    min-width: 50px;
    margin-left: 0;
    font-size: 12px;
    align-items: center;
    justify-content: center;

    &.symbol-active {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
    }
  }
}

.template-form {
  padding: 20px;

  .section-title {
    position: relative;
    padding-left: 12px;
    margin-bottom: 10px;
    font-size: 12px;
    font-weight: 600;
    color: var(--el-text-color-primary);

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      background: var(--el-color-primary);
      border-radius: 2px;
      content: '';
      transform: translateY(-50%);
    }
  }

  .form-section {
    padding: 10px 20px;
    margin-bottom: 24px;
    background: var(--el-bg-color);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .category-title {
      position: relative;
      padding-left: 12px;
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .category-actions {
      display: flex;
      gap: 10px;
    }
  }

  .basic-info {
    .short-input {
      width: 320px;
    }
  }

  // 公式配置区块样式
  .formula-config {
    .formula-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .formula-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .formula-title {
        font-size: 12px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .formula-content {
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 4px;
      }

      .formula-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .formula-label {
        width: 100px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }

      .formula-select {
        width: 240px;
      }

      .formula-operation {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
      }

      .formula-operators {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .el-button {
          height: 40px;
          min-width: 40px;
          padding: 0;
          font-size: 12px;
        }
      }

      .formula-preview {
        display: flex;
        min-height: 40px;
        padding: 10px;
        font-size: 12px;
        word-break: break-all;
        background: #fff;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        flex: 1;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .form-actions {
    margin-top: 32px;
    text-align: center;

    .el-button {
      min-width: 120px;
    }
  }

  .el-table {
    margin-top: 16px;

    ::v-deep(.el-table__cell) {
      padding: 12px 0;
    }
  }
}

/* 测试弹窗样式 */
.test-formula-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.test-dialog-content {
  .formula-title {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--el-text-color-regular);
    text-align: left;
  }

  .test-fields {
    margin-bottom: 20px;

    .field-input-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .field-label {
        width: 60px;
        margin-right: 8px;
        font-size: 14px;
        color: var(--el-text-color-primary);
        flex-shrink: 0;
      }

      .field-input {
        flex: 1;
      }
    }

    .more-fields {
      margin: 10px 0;
      font-size: 14px;
      color: var(--el-text-color-placeholder);
      text-align: center;
    }
  }

  .formula-display,
  .test-result-display {
    margin-bottom: 20px;

    .section-title {
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .formula-content,
    .result-content {
      min-height: 60px;
      padding: 12px;
      font-size: 14px;
      line-height: 1.5;
      background-color: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color-light);
      border-radius: 4px;

      .result-success {
        font-weight: 500;
        color: var(--el-color-success);
      }

      .result-error {
        font-weight: 500;
        color: var(--el-color-danger);
      }

      .result-placeholder {
        font-style: italic;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .test-button-container {
    margin-top: 20px;
    text-align: center;

    .test-button {
      width: 80px;
      height: 36px;
    }
  }
}
</style>
