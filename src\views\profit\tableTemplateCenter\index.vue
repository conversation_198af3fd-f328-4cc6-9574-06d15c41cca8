<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :inline="true" :model="queryParams" class="-mb-15px" label-width="80px">
      <el-form-item>
        <el-input
          v-model="queryParams.name"
          placeholder="请输入模板名称"
          clearable
          style="width: 240px"
        />
        <el-select
          v-model="queryParams.templateTypeCode"
          placeholder="请选择类型"
          clearable
          style="width: 120px"
        >
          <template
            v-for="dict in getIntDictOptions(DICT_TYPE.INFRA_TEMPLATE_TYPE)"
            :key="dict.value"
          >
            <el-option v-if="dict.value != 3" :label="dict.label" :value="dict.value" />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"> <Icon icon="ep:search" />查询 </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleCreateTemplate"
          v-hasPermi="['infra:tools-table-template:create']"
        >
          <Icon icon="ep:plus" />创建模板
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 模板列表 -->
  <ContentWrap>
    <ul
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="disabled"
      class="template-list"
      v-loading="listLoading"
    >
      <el-card
        v-for="(template, index) in templateList"
        :key="index"
        shadow="never"
        class="template-card"
      >
        <div class="template-header">
          <el-tooltip :content="template.name" placement="top">
            <span class="template-name text-ellipsis">{{ template.name }}</span>
          </el-tooltip>
          <el-tag
            v-if="template.templateTypeName"
            :type="template.templateTypeCode == 1 ? 'primary' : 'warning'"
            size="small"
          >
            {{ template.templateTypeName }}
          </el-tag>
        </div>

        <div class="template-stats">
          <div class="template-info">
            <span>创建时间：{{ template.createTime }}</span>
            <span>字段数：{{ template.fieldQty }}</span>
            <div
              class="flex font-size-12px justify-between"
              v-if="template.shareTeam && template.sharer"
            >
              <span>分享团队：{{ template.shareTeam }}</span>
              <span>分享人：{{ template.sharer }}</span>
            </div>
          </div>
        </div>

        <div class="template-actions pos-relative">
          <el-button
            size="small"
            @click="handleCopy(template)"
            v-hasPermi="['infra:tools-table-template:create']"
          >
            复制
          </el-button>
          <el-button
            v-if="!template.isFixed"
            size="small"
            @click="handleEdit(template)"
            v-hasPermi="['infra:tools-table-template:update']"
          >
            编辑
          </el-button>
          <el-button
            v-if="!template.isFixed"
            size="small"
            type="danger"
            @click="handleDelete(template)"
            v-hasPermi="['infra:tools-table-template:delete']"
          >
            删除
          </el-button>
        </div>
      </el-card>
    </ul>

    <!-- 加载更多提示 -->
    <div v-if="loadingMore" class="load-more">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="hasMore" class="load-more-tip"> 滚动加载更多 </div>
    <div v-else class="no-more"> 没有更多数据了 </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { deleteProfitTableTemplate, getProfitTableTemplatePage } from '@/api/tool/profitTableTemplate'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import fixedTemplateData from './data.js'

defineOptions({ name: 'ProfitTableTemplateCenterIndex' })

const router = useRouter() // 路由

const listLoading = ref(false)

// 查询参数
const queryParams = reactive<PageParam>({
  pageNo: 1,
  pageSize: 24,
  name: '',
  templateTypeCode: ''
})

// 固定的基础模板数组
const fixedTemplates = fixedTemplateData.map((template) => ({
  ...template,
  isFixed: true, // 标记为固定模板
  createTime: new Date(template.createTime)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    .replace(/\//g, '-'),
  fieldQty: template.toolsFieldDtoList ? template.toolsFieldDtoList.length : 0
}))

// 模板列表数据 (模拟数据)
const templateList = ref<any[]>([])
const loadingMore = ref(false)
const hasMore = ref(true)
const disabled = ref(false)

// 国家选项
const countryOptions = ref([])

/** 查询按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  templateList.value = [...fixedTemplates] // 重置时保留固定模板
  hasMore.value = true
  getList()
}

/** 创建模板 */
const handleCreateTemplate = () => {
  // 跳转到ProfitTableTemplateCenterForm
  router.push({
    name: 'ProfitTableTemplateCenterForm',
    query: { id: '', name: '创建模板', type: 'create' }
  })
}

/** 复制模板 */
const handleCopy = (template: any) => {
  // 跳转到创建模板页面，并传递复制的模板数据
  // 这里假设路由名称为 'CreateTemplate'
  // 实际路由配置由用户自己处理
  router.push({
    name: 'ProfitTableTemplateCenterForm',
    query: { id: template.id, name: template.name + '-复制', type: 'copy' }
  })
}

/** 编辑模板 */
const handleEdit = (template: any) => {
  // 固定模板不允许编辑
  if (template.isFixed) {
    ElMessage.warning('基础模板不允许编辑')
    return
  }
  // 跳转到创建模板页面，并传递编辑的模板数据
  // 这里假设路由名称为 'CreateTemplate'
  // 实际路由配置由用户自己处理
  router.push({
    name: 'ProfitTableTemplateCenterForm',
    query: { id: template.id, name: template.name + '-编辑', type: 'edit' }
  })
}

/** 删除模板 */
const handleDelete = (template: any) => {
  // 固定模板不允许删除
  if (template.isFixed) {
    ElMessage.warning('基础模板不允许删除')
    return
  }
  ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const res = await deleteProfitTableTemplate(template.id)
      if (res) {
        // 模拟删除操作
        const index = templateList.value.findIndex((item) => item.id === template.id)
        templateList.value.splice(index, 1)
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

/** 加载更多数据 */
const loadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    getList()
  }
}

/** 获取列表数据 */
const getList = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  disabled.value = true
  listLoading.value = templateList.value.length <= 1 // 只有在首次加载时显示全屏loading（考虑固定模板）
  try {
    const res = await getProfitTableTemplatePage(queryParams)
    const newData = res.list || []
    if (newData.length > 0) {
      // 确保固定模板始终在最前面
      const hasFixedTemplate = templateList.value.some((item) => item.isFixed)
      if (hasFixedTemplate) {
        // 如果已有固定模板，追加新数据
        templateList.value = [...templateList.value, ...newData]
      } else {
        // 如果没有固定模板，先添加固定模板再添加新数据
        templateList.value = [...fixedTemplates, ...templateList.value, ...newData]
      }
      queryParams.pageNo++
      if (templateList.value.length - fixedTemplates.length >= res.total) {
        // 减去固定模板的数量
        hasMore.value = false
      }
    } else {
      // 即使没有新数据，也要确保固定模板存在
      const hasFixedTemplate = templateList.value.some((item) => item.isFixed)
      if (!hasFixedTemplate) {
        templateList.value = [...fixedTemplates, ...templateList.value]
      }
      hasMore.value = false
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    // 即使出错，也要确保固定模板存在
    const hasFixedTemplate = templateList.value.some((item) => item.isFixed)
    if (!hasFixedTemplate) {
      templateList.value = [...fixedTemplates, ...templateList.value]
    }
    hasMore.value = false // 出错时停止加载更多
  } finally {
    loadingMore.value = false
    disabled.value = false
    listLoading.value = false
  }
}

onMounted(() => {
  // 初始化时先添加固定模板
  templateList.value = [...fixedTemplates]
  getList()
})
</script>

<style lang="scss" scoped>
.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.template-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;

  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .template-name {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .template-stats {
    margin: 12px 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);

    .template-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 8px;
    }
  }

  .template-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

.text-ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.load-more,
.load-more-tip,
.no-more {
  padding: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  text-align: center;

  .el-icon {
    margin-right: 8px;
    animation: rotating 2s linear infinite;
  }
}

.no-more {
  color: var(--el-text-color-placeholder);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
