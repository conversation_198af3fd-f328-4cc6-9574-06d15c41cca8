/**
 * Service Worker 优化配置 - 利润表模板配置页面
 * 提供缓存策略、离线支持和数据预加载功能
 */

// 类型定义
interface CacheStatus {
  enabled: boolean
  caches: Record<string, { count: number; urls: string[] }>
  totalSize?: number
}

interface OfflineQueueItem {
  request: Request
  timestamp: number
}

declare global {
  interface Window {
    ProfitTemplateServiceWorker: typeof ProfitTemplateServiceWorker
  }
}

// 缓存配置
const CACHE_CONFIG = {
  // 静态资源缓存
  STATIC_CACHE: 'profit-template-static-v1',
  // API数据缓存
  API_CACHE: 'profit-template-api-v1',
  // 页面缓存
  PAGE_CACHE: 'profit-template-page-v1',
  // 缓存过期时间（毫秒）
  CACHE_EXPIRY: {
    STATIC: 24 * 60 * 60 * 1000, // 24小时
    API: 30 * 60 * 1000, // 30分钟
    PAGE: 60 * 60 * 1000 // 1小时
  }
}

// 需要缓存的API端点
const CACHEABLE_APIS = [
  '/api/operate/replenishment-proposal/country-list',
  '/api/tool/profit-table-template',
  '/api/tool/table-template-center',
  '/api/system/dict-data/type'
]

// 需要缓存的静态资源
const CACHEABLE_STATIC = [
  '/src/views/profit/tableTemplateCenter/form.vue',
  '/src/components/Table/index.vue',
  '/src/components/Icon/index.vue',
  '/src/utils/dict.js',
  '/src/utils/tree.js'
]

class ProfitTemplateServiceWorker {
  public isEnabled: boolean
  public cacheManager: CacheManager
  public preloader: DataPreloader
  public offlineHandler: OfflineHandler

  constructor() {
    // Service Worker已被禁用
    this.isEnabled = false
    this.cacheManager = new CacheManager()
    this.preloader = new DataPreloader()
    this.offlineHandler = new OfflineHandler()

    console.log('[SW优化] Service Worker已被禁用')
    // 不再初始化Service Worker
    // if (this.isEnabled) {
    //   this.init()
    // }
  }

  // 检查Service Worker支持
  checkServiceWorkerSupport(): boolean {
    return 'serviceWorker' in navigator && 'caches' in window
  }

  // 初始化Service Worker优化
  async init(): Promise<void> {
    try {
      // 注册Service Worker
      await this.registerServiceWorker()

      // 预加载关键数据
      await this.preloader.preloadCriticalData()

      // 设置离线处理
      this.offlineHandler.setupOfflineHandling()
    } catch (error) {
      console.error('[SW优化] 初始化失败:', error)
    }
  }

  // 注册Service Worker
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | undefined> {
    if (!this.isEnabled) return

    try {
      const registration = await navigator.serviceWorker.register('/sw-profit-template.js', {
        scope: '/profit/tableTemplateCenter/'
      })

      return registration
    } catch (error) {
      console.error('[SW优化] Service Worker注册失败:', error)
      throw error
    }
  }

  // 获取缓存状态
  async getCacheStatus(): Promise<CacheStatus> {
    if (!this.isEnabled) return { enabled: false, caches: {} }

    const cacheNames = await caches.keys()
    const profitCaches = cacheNames.filter((name) => name.includes('profit-template'))

    const status: CacheStatus = {
      enabled: true,
      caches: {},
      totalSize: 0
    }

    for (const cacheName of profitCaches) {
      const cache = await caches.open(cacheName)
      const keys = await cache.keys()
      status.caches[cacheName] = {
        count: keys.length,
        urls: keys.map((req) => req.url)
      }
    }

    return status
  }

  // 清除缓存
  async clearCache(): Promise<void> {
    if (!this.isEnabled) return

    const cacheNames = await caches.keys()
    const profitCaches = cacheNames.filter((name) => name.includes('profit-template'))

    await Promise.all(profitCaches.map((name) => caches.delete(name)))
  }

  // 手动预加载数据
  async preloadData(): Promise<void> {
    if (!this.isEnabled) return

    await this.preloader.preloadCriticalData()
  }
}

// 缓存管理器
class CacheManager {
  // 缓存API响应
  async cacheApiResponse(request: Request, response: Response): Promise<void> {
    if (!this.shouldCacheApi(request.url)) return

    const cache = await caches.open(CACHE_CONFIG.API_CACHE)
    const responseClone = response.clone()

    // 添加时间戳
    const responseWithTimestamp = new Response(responseClone.body, {
      status: responseClone.status,
      statusText: responseClone.statusText,
      headers: {
        ...Object.fromEntries(responseClone.headers.entries()),
        'sw-cached-at': Date.now().toString()
      }
    })

    await cache.put(request, responseWithTimestamp)
  }

  // 获取缓存的API响应
  async getCachedApiResponse(request: Request): Promise<Response | null> {
    const cache = await caches.open(CACHE_CONFIG.API_CACHE)
    const cachedResponse = await cache.match(request)

    if (!cachedResponse) return null

    // 检查缓存是否过期
    const cachedAt = cachedResponse.headers.get('sw-cached-at')
    if (cachedAt) {
      const age = Date.now() - parseInt(cachedAt)
      if (age > CACHE_CONFIG.CACHE_EXPIRY.API) {
        await cache.delete(request)
        return null
      }
    }

    return cachedResponse
  }

  // 判断是否应该缓存API
  shouldCacheApi(url: string): boolean {
    return CACHEABLE_APIS.some((api) => url.includes(api))
  }

  // 缓存静态资源
  async cacheStaticResource(request: Request, response: Response): Promise<void> {
    if (!this.shouldCacheStatic(request.url)) return

    const cache = await caches.open(CACHE_CONFIG.STATIC_CACHE)
    await cache.put(request, response.clone())
  }

  // 判断是否应该缓存静态资源
  shouldCacheStatic(url: string): boolean {
    return (
      CACHEABLE_STATIC.some((resource) => url.includes(resource)) ||
      url.includes('.js') ||
      url.includes('.css') ||
      url.includes('.vue')
    )
  }
}

// 数据预加载器
class DataPreloader {
  // 预加载关键数据
  async preloadCriticalData(): Promise<void> {
    const criticalApis = [
      '/api/operate/replenishment-proposal/country-list',
      '/api/system/dict-data/type/infra_param_type',
      '/api/system/dict-data/type/infra_tree_unit'
    ]

    const preloadPromises = criticalApis.map((api) => this.preloadApi(api))
    await Promise.allSettled(preloadPromises)
  }

  // 预加载单个API
  async preloadApi(apiPath: string): Promise<void> {
    try {
      const response = await fetch(apiPath, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const cacheManager = new CacheManager()
        await cacheManager.cacheApiResponse(new Request(apiPath), response)
      }
    } catch (error) {
      console.warn(`[SW优化] 预加载失败: ${apiPath}`, error)
    }
  }

  // 预加载模板详情
  async preloadTemplateDetail(templateId: string | number): Promise<void> {
    if (!templateId) return

    const apiPath = `/api/tool/profit-table-template/${templateId}`
    await this.preloadApi(apiPath)
  }
}

// 离线处理器
class OfflineHandler {
  public isOnline: boolean
  public offlineQueue: OfflineQueueItem[]

  constructor() {
    this.isOnline = navigator.onLine
    this.offlineQueue = []
  }

  // 设置离线处理
  setupOfflineHandling(): void {
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.isOnline = true
      this.processOfflineQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.showOfflineNotification()
    })
  }

  // 显示离线通知
  showOfflineNotification(): void {
    // 这里可以集成Element Plus的消息组件
    console.warn('[SW优化] 网络连接已断开，正在使用缓存数据')
  }

  // 添加到离线队列
  addToOfflineQueue(request: Request): void {
    this.offlineQueue.push({
      request: request.clone(),
      timestamp: Date.now()
    })
  }

  // 处理离线队列
  async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return

    const promises = this.offlineQueue.map(async (item) => {
      try {
        await fetch(item.request)
        return true
      } catch (error) {
        console.error('[SW优化] 离线请求重试失败:', error)
        return false
      }
    })

    await Promise.allSettled(promises)
    this.offlineQueue = []
  }

  // 获取离线数据
  async getOfflineData(request: Request): Promise<Response | null> {
    const cacheManager = new CacheManager()
    return await cacheManager.getCachedApiResponse(request)
  }
}

// 导出Service Worker优化实例
export default ProfitTemplateServiceWorker

// 全局实例
window.ProfitTemplateServiceWorker = ProfitTemplateServiceWorker
