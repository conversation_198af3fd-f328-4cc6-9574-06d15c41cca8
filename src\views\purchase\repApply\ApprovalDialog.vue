<template>
  <Dialog v-model="dialogVisible" title="审核" width="500px">
    <el-form ref="formRef" :model="formData" label-width="80px">
      <el-form-item label="审核状态" prop="approvalStatusCode">
        <el-radio-group v-model="formData.approvalStatusCode">
          <el-radio-button :label="1">通过</el-radio-button>
          <!-- 1: 已通过 -->
          <el-radio-button :label="2">驳回</el-radio-button>
          <!-- 2: 已拒绝 -->
        </el-radio-group>
      </el-form-item>
      <el-form-item label="计划采购量" prop="plannedPurchaseQty">
        <el-input-number
          v-model="formData.plannedPurchaseQty"
          :min="0"
          :precision="0"
          placeholder="请输入计划采购量"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="operationChannelCode">
        <el-select
          v-model="formData.operationChannelCode"
          placeholder="请选择渠道"
          clearable
          class="!w-full"
          @change="handleChannelChange"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OPERATION_CHANNEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="approvalRemark">
        <el-input
          v-model="formData.approvalRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入审核备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { approveRepApply, type RepApplyApprovalReqVO } from '@/api/purchase/repApply'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ElMessage, type FormInstance } from 'element-plus'
import { reactive, ref } from 'vue'

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const formData = reactive<RepApplyApprovalReqVO>({
  ids: [],
  approvalStatusCode: 1, // 默认通过，对应字典值 1
  approvalRemark: '',
  operationChannelCode: '',
  operationChannelName: '',
  plannedPurchaseQty: undefined // 计划采购量
})

const emit = defineEmits(['success'])

const open = (ids: number[], rowData?: any) => {
  dialogVisible.value = true
  formData.ids = ids
  formData.approvalStatusCode = 1 // 重置状态为通过，对应字典值 1
  formData.approvalRemark = '' // 重置备注
  formData.operationChannelCode = '' // 重置渠道编码
  formData.operationChannelName = '' // 重置渠道名称
  // 如果是单行审核且有行数据，设置默认的计划采购量
  if (rowData && ids.length === 1) {
    formData.approvalRemark = rowData.approvalRemark || '' // 重置备注
    formData.plannedPurchaseQty = rowData.plannedPurchaseQty || undefined
    formData.operationChannelCode = rowData.operationChannelCode
      ? Number(rowData.operationChannelCode)
      : '' // 重置渠道编码
    formData.operationChannelName = rowData.operationChannelName || '' // 重置渠道名称
  } else {
    formData.plannedPurchaseQty = undefined // 批量审核时重置计划采购量
  }
  formRef.value?.resetFields() // 如果需要表单校验重置
}

const handleChannelChange = (value: string) => {
  if (value) {
    const channelDict = getIntDictOptions(DICT_TYPE.OPERATION_CHANNEL).find(
      (item) => item.value === value
    )
    formData.operationChannelName = channelDict?.label || ''
  } else {
    formData.operationChannelName = ''
  }
}

const submitForm = async () => {
  if (!formData.approvalStatusCode) {
    ElMessage.warning('请选择审核状态')
    return
  }
  // if (formData.approvalStatusCode === 2 && !formData.approvalRemark) {
  //   // 2: 已拒绝
  //   ElMessage.warning('驳回时必须填写备注')
  //   return
  // }

  loading.value = true
  try {
    const payload: RepApplyApprovalReqVO = {
      ids: formData.ids,
      approvalStatusCode: formData.approvalStatusCode,
      approvalRemark: formData.approvalRemark
    }

    // 只有在通过时才传递渠道和计划采购量相关字段
    // if (formData.approvalStatusCode === 1) {
    payload.operationChannelCode = formData.operationChannelCode
    payload.operationChannelName = formData.operationChannelName
    payload.plannedPurchaseQty = formData.plannedPurchaseQty
    // }

    await approveRepApply(payload)
    ElMessage.success('审核操作成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('审核失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>
