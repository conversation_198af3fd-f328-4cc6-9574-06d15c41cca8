<template>
  <Dialog v-model="dialogVisible" title="创建采购计划" width="80%" @close="resetForm">
    <el-form ref="formRef" :model="formData">
      <el-form-item label="计划备注" prop="planRemark">
        <el-input
          v-model="formData.planRemark"
          type="textarea"
          :rows="2"
          placeholder="请输入计划备注"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" border>
        <el-table-column label="图片" align="center" width="80">
          <template #default="scope">
            <el-image
              v-if="scope.row.imgUrl"
              :src="scope.row.imgUrl"
              :preview-src-list="[scope.row.imgUrl]"
              preview-teleported
              class="h-30px w-30px"
            />
          </template>
        </el-table-column>
        <el-table-column label="品名" align="left" prop="localName" />
        <el-table-column label="SKU" align="left" prop="localSku" />
        <el-table-column label="店铺" align="center" width="140">
          <template #header>
            <div class="flex items-center justify-between">
              <span>店铺</span>
              <el-popover placement="top" :width="200" :visible="visibleShop">
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        visibleShop = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <el-select
                  v-model="batchEditValues.sid"
                  placeholder="请选择店铺"
                  style="width: 100%"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in shops"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div style="margin-top: 10px; text-align: right">
                  <el-button size="small" @click="visibleShop = false">取消</el-button>
                  <el-button type="primary" size="small" @click="handleBatchApply('sid')"
                    >确认</el-button
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-select
              v-model="scope.row.sid"
              placeholder="请选择店铺"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in shops"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="FNSKU" align="center" prop="fnsku">
          <template #default="scope">
            <el-input v-model="scope.row.fnsku" placeholder="请输入FNSKU" clearable />
          </template>
        </el-table-column>
        <el-table-column label="仓库" align="center" width="120">
          <template #header>
            <div class="flex items-center justify-between">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="请务必准确选择对应仓库，若无匹配项请留空。错误选择将导致管理员无法及时处理采购计划。"
                placement="top-start"
              >
                <div class="flex items-center gap-5px"
                  >仓库
                  <Icon :size="12" icon="ep:warning" class="cursor-pointer" />
                </div>
              </el-tooltip>
              <el-popover placement="top" :width="200" :visible="visibleWarehouse">
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        visibleWarehouse = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="请务必准确选择对应仓库，若无匹配项请留空。错误选择将导致管理员无法及时处理采购计划。"
                  placement="top-start"
                >
                  <el-select
                    v-model="batchEditValues.warehouseId"
                    placeholder="请选择仓库"
                    style="width: 100%"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in warehouseOptions"
                      :key="item.wid"
                      :label="item.name"
                      :value="item.wid"
                    />
                  </el-select>
                </el-tooltip>
                <div style="margin-top: 10px; text-align: right">
                  <el-button size="small" @click="visibleWarehouse = false">取消</el-button>
                  <el-button type="primary" size="small" @click="handleBatchApply('warehouseId')"
                    >确认</el-button
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="请务必准确选择对应仓库，若无匹配项请留空。错误选择将导致管理员无法及时处理采购计划。"
              placement="top-start"
            >
              <el-select
                v-model="scope.row.warehouseId"
                placeholder="请选择仓库"
                filterable
                clearable
                @change="(val) => handleWarehouseChange(scope.row, val)"
              >
                <el-option
                  v-for="item in warehouseOptions"
                  :key="item.wid"
                  :label="item.name"
                  :value="item.wid"
                />
              </el-select>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="计划采购量" align="center" width="130">
          <template #header>
            <div class="flex items-center justify-between">
              <span>计划采购量</span>
              <el-popover
                placement="top"
                :width="200"
                trigger="click"
                v-model:visible="batchEditPopover.plannedPurchaseQty"
              >
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    :disabled="list.some((item) => item.approvalStatusCode == 1)"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        batchEditPopover.plannedPurchaseQty = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <!-- :disabled="scope.row.approvalStatusCode == 1" -->
                <el-input-number
                  v-model="batchEditValues.plannedPurchaseQty"
                  :min="0"
                  placeholder="输入数量"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="list.some((item) => item.approvalStatusCode == 1)"
                />
                <div style="margin-top: 10px; text-align: right">
                  <el-button size="small" @click="batchEditPopover.plannedPurchaseQty = false"
                    >取消</el-button
                  >
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleBatchApply('plannedPurchaseQty')"
                    :disabled="list.some((item) => item.approvalStatusCode == 1)"
                    >确认</el-button
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-input-number
              :disabled="scope.row.approvalStatusCode == 1"
              v-model="scope.row.plannedPurchaseQty"
              :min="0"
              controls-position="right"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="建议采购量" align="center" prop="suggestPurchaseQty" width="85" />
        <el-table-column
          label="期望到货时间"
          align="center"
          width="150"
          :show-overflow-tooltip="false"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <span>期望到货时间</span>
              <!-- v-model:visible="batchEditPopover.expectedArrivalTime" -->
              <!-- trigger="click" -->
              <el-popover placement="top" :width="280" :visible="visibleTime">
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        visibleTime = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <div @click.stop>
                  <el-date-picker
                    v-model="batchEditValues.expectedArrivalTime"
                    type="date"
                    placeholder="选择日期时间"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                  <div style="margin-top: 10px; text-align: right">
                    <el-button
                      size="small"
                      @click="
                        ((batchEditPopover.expectedArrivalTime = false), (visibleTime = false))
                      "
                      >取消</el-button
                    >
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleBatchApply('expectedArrivalTime')"
                      >确认</el-button
                    >
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-date-picker
              v-model="scope.row.expectedArrivalTime"
              type="date"
              placeholder="选择日期时间"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="采购员" align="center" width="120">
          <template #header>
            <div class="flex items-center justify-between">
              <span><span style="color: red">*</span>采购员</span>
              <el-popover placement="top" :width="200" :visible="visiblePurchaser">
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        visiblePurchaser = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <el-select
                  v-model="batchEditValues.purchaseId"
                  placeholder="请选择采购员"
                  style="width: 100%"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in purchaserOptions"
                    :key="item.uid"
                    :label="item.username"
                    :value="item.uid"
                  />
                </el-select>
                <div style="margin-top: 10px; text-align: right">
                  <el-button size="small" @click="visiblePurchaser = false">取消</el-button>
                  <el-button type="primary" size="small" @click="handleBatchApply('purchaseId')"
                    >确认</el-button
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-select
              v-model="scope.row.purchaseId"
              placeholder="请选择采购员"
              filterable
              style="width: 100%"
              clearable
              @change="(val) => handlePurchaserChange(scope.row, val)"
            >
              <el-option
                v-for="item in purchaserOptions"
                :key="item.uid"
                :label="item.username"
                :value="item.uid"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" width="220">
          <template #header>
            <div class="flex items-center justify-between">
              <span>备注</span>
              <el-popover
                placement="top"
                :width="200"
                trigger="click"
                :visible="batchEditPopover.remark"
              >
                <template #reference>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    class="ml-5px"
                    @click="
                      () => {
                        closeAllBatchPopovers()
                        batchEditPopover.remark = true
                      }
                    "
                    >批量</el-button
                  >
                </template>
                <el-input
                  v-model="batchEditValues.remark"
                  type="textarea"
                  :rows="2"
                  maxlength="2000"
                  show-word-limit
                  placeholder="请输入备注"
                  style="width: 100%"
                />
                <div style="margin-top: 10px; text-align: right">
                  <el-button size="small" @click="batchEditPopover.remark = false">取消</el-button>
                  <el-button type="primary" size="small" @click="handleBatchApply('remark')"
                    >确认</el-button
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="scope">
            <el-input
              v-model="scope.row.remark"
              type="textarea"
              :rows="1"
              maxlength="2000"
              show-word-limit
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100" fixed="right">
          <template #default="scope">
            <el-button link type="danger" @click="handleDelete(scope.$index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-right mt-10px">
        <span>合计：</span>
        <span class="font-bold">计划采购数量：{{ totalPlannedQuantity }}</span>
      </div>
    </ContentWrap>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" :loading="formLoading" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import {
  createPurchasePlan, // Alias to avoid name conflict
  CreatePurchasePlanReqVO,
  getAllAccount,
  getPurchasePlanListByApplyId,
  getWarehouseList,
  PurchasePlanItemVO as RepApplyPurchasePlanItemVO
} from '@/api/purchase/repApply'
import { ThirdPartyAccountApi } from '@/api/system/third'
import { formatDate } from '@/utils/formatTime'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

const dialogVisible = ref(false)
const loading = ref(false)
const formLoading = ref(false)
const list = ref<
  (RepApplyPurchasePlanItemVO & { warehouseName?: string; purchaseName?: string })[]
>([]) // Use aliased type and add optional name fields
const warehouseOptions = ref([])
const purchaserOptions = ref([])
const shops = ref([])

const batchEditPopover = reactive<Record<string, boolean>>({
  sid: false,
  warehouseId: false,
  plannedPurchaseQty: false,
  expectedArrivalTime: false,
  purchaseId: false,
  remark: false
})

const batchEditValues = reactive<
  Partial<RepApplyPurchasePlanItemVO & { warehouseName?: string; purchaseName?: string }>
>({
  // Use aliased type and add optional name fields
  sid: undefined,
  warehouseId: undefined,
  plannedPurchaseQty: undefined,
  expectedArrivalTime: undefined,
  purchaseId: undefined,
  remark: undefined
})

const visibleTime = ref(false)
const visibleShop = ref(false)
const visibleWarehouse = ref(false)
const visiblePurchaser = ref(false)

const formData = reactive<CreatePurchasePlanReqVO>({
  planRemark: '',
  items: []
})

// 计算计划采购量总计
const totalPlannedQuantity = computed(() => {
  return list.value.reduce((sum, item) => sum + (Number(item.plannedPurchaseQty) || 0), 0)
})

const open = async (data: any[]) => {
  dialogVisible.value = true
  loading.value = true
  try {
    const ids = data.map((item) => item.id)
    const res = await getPurchasePlanListByApplyId({ ids })
    list.value = res.map((item: RepApplyPurchasePlanItemVO) => ({
      // Ensure item type is correct here
      ...item,
      expectedArrivalTime: item.expectedArrivalTime || formatDate(new Date(), 'YYYY-MM-DD')
    }))
  } finally {
    loading.value = false
  }
}

const fetchWarehouseOptions = async () => {
  try {
    warehouseOptions.value = await getWarehouseList()
  } catch (error) {
    console.error('Failed to fetch warehouse options:', error)
    ElMessage.error('获取仓库列表失败')
  }
}

const fetchPurchaserOptions = async () => {
  try {
    let data = await getAllAccount()
    purchaserOptions.value = data.map((item) => ({
      ...item,
      username: item.username + '(' + item.realname + ')'
    }))
  } catch (error) {
    console.error('Failed to fetch purchaser options:', error)
    ElMessage.error('获取采购员列表失败')
  }
}

const getShopsByAccount = async () => {
  shops.value = []
  try {
    const res = await ThirdPartyAccountApi.getShopList()
    shops.value =
      res.map((item) => ({
        value: item.sid,
        label: item.sellerItem
      })) || []
  } catch (error) {
    console.error('获取店铺列表失败:', error)
  }
}

const handleWarehouseChange = (
  row: RepApplyPurchasePlanItemVO & { warehouseName?: string },
  wid: number | string | undefined
) => {
  const selectedWarehouse = warehouseOptions.value.find((w) => w.wid === wid)
  row.warehouseName = selectedWarehouse ? selectedWarehouse.name : ''
}

const handlePurchaserChange = (
  row: RepApplyPurchasePlanItemVO & { purchaseName?: string },
  uid: number | string | undefined
) => {
  const selectedPurchaser = purchaserOptions.value.find((p) => p.uid === uid)
  row.purchaseName = selectedPurchaser ? selectedPurchaser.username : ''
}

onMounted(() => {
  fetchWarehouseOptions()
  fetchPurchaserOptions()
  getShopsByAccount()
})

defineExpose({ open })

const handleBatchApply = (
  field: keyof (RepApplyPurchasePlanItemVO & { warehouseName?: string; purchaseName?: string })
) => {
  // Use aliased type
  if (
    batchEditValues[field] === undefined ||
    batchEditValues[field] === null ||
    batchEditValues[field] === ''
  ) {
    ElMessage.warning('请输入有效的批量编辑内容')
    return
  }
  // // 过滤掉已审批的项目，只对未审批的项目进行批量编辑
  // const editableItems = list.value.filter((item) => item.approvalStatusCode != 1)

  // if (batchEditValues[field] == 'plannedPurchaseQty' && editableItems.length === 0) {
  //   ElMessage.warning('没有可编辑的项目（所有项目都已审批）')
  //   return
  // }

  list.value.forEach((item) => {
    item[field] = batchEditValues[field] as any
    if (field === 'warehouseId') {
      const selectedWarehouse = warehouseOptions.value.find(
        (w) => w.wid === batchEditValues.warehouseId
      )
      item.warehouseName = selectedWarehouse ? selectedWarehouse.name : ''
    }
    if (field === 'purchaseId') {
      const selectedPurchaser = purchaserOptions.value.find(
        (p) => p.uid === batchEditValues.purchaseId
      )
      item.purchaseName = selectedPurchaser ? selectedPurchaser.username : ''
    }
  })
  batchEditPopover[field] = false

  if (field === 'expectedArrivalTime') {
    visibleTime.value = false
  }
  if (field === 'sid') {
    visibleShop.value = false
  }
  if (field === 'warehouseId') {
    visibleWarehouse.value = false
  }
  if (field === 'purchaseId') {
    visiblePurchaser.value = false
  }
  // batchEditValues[field] = undefined // 可选：应用后清空批量输入框
}

const handleDelete = (index: number) => {
  list.value.splice(index, 1)
}

const emit = defineEmits(['success'])

const submitForm = async () => {
  // 验证采购员必填
  const missingPurchaser = list.value.some((item) => !item.purchaseId)
  if (missingPurchaser) {
    ElMessage.error('请为所有商品选择采购员')
    return
  }

  formLoading.value = true
  try {
    formData.items = list.value
    await createPurchasePlan(formData)
    ElMessage.success('创建采购计划成功')
    // 清空数据
    resetForm()
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 关闭所有批量弹出层的函数
const closeAllBatchPopovers = () => {
  Object.keys(batchEditPopover).forEach((key) => {
    batchEditPopover[key] = false
  })
  visibleTime.value = false
  visibleShop.value = false
  visibleWarehouse.value = false
  visiblePurchaser.value = false
}

const resetForm = () => {
  // 清空数据
  list.value = []
  formData.planRemark = ''
  formData.items = []

  // 关闭所有批量弹出层
  closeAllBatchPopovers()
}
</script>
