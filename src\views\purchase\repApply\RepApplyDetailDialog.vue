<template>
  <Dialog v-model="dialogVisible" title="详情" width="60%">
    <div v-if="detailData">
      <div class="flex flex-col md:flex-row gap-3 mb-4 bg-gray-100 p-3 rounded-lg">
        <!-- 左侧信息区域 - 保证完整显示 -->
        <div class="flex items-start flex-none">
          <el-image
            :src="
              detailData.imgUrl ||
              'https://fuss10.elemecdn.com/e/5d/04aca53a41bbb12550c3ef2f984a3jpeg.jpeg'
            "
            fit="contain"
            class="w-16 h-16 mr-3 flex-none"
          />
          <div class="flex-1 min-w-0 break-words">
            <div class="font-bold text-xl mb-1 break-all font-semibold"
              >ASIN: {{ detailData.asin }}</div
            >
            <div class="text-gray-500 break-all text-sm">
              店铺名称 (国家):
              {{ detailData.sellerItem + ' (' + (detailData.country || '-') + ')' }}
            </div>
          </div>
        </div>

        <!-- 右侧信息区域 -->
        <div
          class="flex flex-col gap-3 text-sm text-gray-600 md:border-l md:border-gray-200 md:pl-4 ml-12 bdl-1"
        >
          <!-- 第一行 -->
          <div class="grid grid-cols-1 sm:grid-cols-1 gap-3">
            <!-- 品名/SKU (主要宽度) -->
            <div class="flex gap-2 min-w-0 items-baseline sm:col-span-2">
              <div class="whitespace-nowrap font-bold">品名/SKU:</div>
              <div class="break-all flex-1"
                >{{ detailData.localName }} / {{ detailData.localSku }}</div
              >
            </div>
            <!-- MSKU/FNSKU (主要宽度) -->
            <div class="flex gap-2 min-w-0 items-baseline">
              <div class="whitespace-nowrap font-bold">MSKU/FNSKU:</div>
              <div class="break-all flex-1">{{ detailData.msku }} / {{ detailData.fnsku }}</div>
            </div>
          </div>

          <!-- 第二行 -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <!-- Listing负责人 -->
            <div class="flex gap-1 min-w-0 items-baseline">
              <div class="whitespace-nowrap font-bold">负责人:</div>
              <div class="break-all">
                {{
                  detailData.restockSuggestPrincipalInfoRespVos
                    ?.map((item) => item.principalName)
                    .join(',') || '-'
                }}
              </div>
            </div>

            <!-- 标签 -->
            <div class="flex gap-1 min-w-0 items-baseline">
              <div class="whitespace-nowrap font-bold">标签:</div>
              <div class="break-all">
                {{
                  detailData.restockSuggestGlobalTagRespVoList?.map((t) => t.tagName).join(', ') ||
                  '-'
                }}
              </div>
            </div>
            <!-- 分析时间 -->
            <div class="flex gap-1 min-w-0 items-baseline">
              <div class="whitespace-nowrap font-bold">分析时间:</div>
              <div class="break-all">{{ detailData.analysisTime }}</div>
            </div>
          </div>
        </div>
      </div>

      <el-descriptions title="销售与库存数据" :column="4" border class="mb-20px">
        <el-descriptions-item label="统计周期">{{
          detailData.statisticalCycleName || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="3天日均">{{ detailData.salesAvg3 ?? 0 }}</el-descriptions-item>
        <el-descriptions-item label="7天日均">{{ detailData.salesAvg7 ?? 0 }}</el-descriptions-item>
        <el-descriptions-item label="14天日均">{{
          detailData.salesAvg14 ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="30天日均">{{
          detailData.salesAvg30 ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="60天日均">{{
          detailData.salesAvg60 ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="90天日均">{{
          detailData.salesAvg90 ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="库存类型">总库存</el-descriptions-item>
        <!-- 这个字段可能需要根据实际业务逻辑调整 -->
        <el-descriptions-item label="FBA库存">{{
          detailData.fbaInventory ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="预计发货量">{{
          detailData.amazonQuantityShippingPlan ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="海外仓可用">{{
          detailData.scQuantityOverseaValid ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="本地可用">{{
          detailData.scQuantityLocalValid ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="待检待上架">{{
          detailData.scQuantityLocalQc ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="待交付">{{
          detailData.scQuantityPurchaseShipping ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="本地仓在途">{{
          detailData.scQuantityLocalShipping ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="采购计划">{{
          detailData.purchasePlanRespVO?.plannedPurchaseQty ?? 0
        }}</el-descriptions-item>
        <!-- 采购计划数量 -->
        <el-descriptions-item label="海外仓在途">{{
          detailData.scQuantityOverseaShipping ?? 0
        }}</el-descriptions-item>
        <el-descriptions-item label="FBA在途">{{
          detailData.amazonQuantityShipping ?? 0
        }}</el-descriptions-item>
      </el-descriptions>

      <template v-if="profitData && Object.keys(profitData).length > 0">
        <el-descriptions title="成本与利润分析" :column="5" border class="mb-20px">
          <el-descriptions-item v-for="(value, key) in profitData" :key="key">
            <template #label>
              <div>{{ profitNameMap[key] }}</div>
            </template>
            {{ value || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </template>

      <el-descriptions title="广告利润分析" :column="5" border class="mb-20px mt-20px">
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>广告占比 = 广告花费 ÷ 净销售额</br>含义 ：</br>表示整体销售额中，有多少是通过广告“买来的”。</br>业务意义 ：</br>广告占比越低越好，表示自然流量强。</br>广告占比 > 25%：可能过度依赖广告，补货风险高。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />广告占比
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.acoas }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>利润投产比 = ROAS × 毛利率</br>含义 ：</br>衡量每投入1元广告费用，能带来多少毛利润。</br>业务意义 ：</br>≥ 150% ：广告高效，产品盈利能力强，建议加大投入。</br>100% ~ 150% ：广告健康，保持当前策略。</br>80% ~ 100% ：接近盈亏平衡，需优化广告或调整定价。</br>< 80% ：广告亏损，建议暂停或优化关键词、出价等策略。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />利润投产比
              </div>
            </el-tooltip>
          </template>

          {{ detailData?.profitToInvestmentRatio }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>ROAS = 广告销售额 ÷ 广告花费</br>含义 ：</br>衡量广告投入带来的销售额回报。</br>业务意义 ：</br>ROAS ≥ 4：广告效率高，可加大投入。</br>ROAS < 2：广告低效，需优化或暂停。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />ROAS
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.roas }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>ACoS = 广告花费 ÷ 广告销售额</br>含义 ：</br>表示为了获得 ¥100 的广告销售额，需要花多少钱在广告上。</br>业务意义 ：</br>ACoS 越低越好。</br>ACoS ≤ 20%：优秀；ACoS > 35%：需优化广告策略。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />ACoS
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.acos }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>广告订单量占比 = (广告销量 ÷ 总销量) × 100%</br>含义 ：</br>表示总销量中，由广告带来的订单占比。</br>业务意义 ：</br>广告订单量占比 > 50%：说明自然流量不足，补货需谨慎。</br>理想值：<30%，说明自然流量支撑力强。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />广告订单量占比
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.advRate }}</el-descriptions-item
        >
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>退货率 = (退货量 ÷ 总销量) × 100%</br>含义 ：</br>表示每卖出 100 个产品，有多少被退回。</br>业务意义 ：</br>高退货率直接影响利润与库存周转。</br>退货率 > 8%：需警惕产品质量或描述问题，影响补货决策。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />退货率
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.returnGoodsRate }}</el-descriptions-item
        >
        <el-descriptions-item>
          <template #label>
            <el-tooltip
              content="注释：</br>CPO = 广告花费 ÷ 广告订单量</br>含义 ：</br>获取一个广告订单的成本。</br>业务意义 ：</br>若 CPO > 毛利润：说明广告亏损，不建议补货。</br>建议结合毛利率一起使用，判断广告是否值得继续投放。"
              placement="top"
              raw-content
            >
              <div class="flex items-center gap-5px">
                <Icon class="ml-5px" icon="ep:warning" />CPO
              </div>
            </el-tooltip>
          </template>
          {{ detailData?.cpo }}
        </el-descriptions-item>
      </el-descriptions>

      <h3 class="text-lg font-semibold mb-10px mx-title">销量明细</h3>
      <el-table
        :data="salesDetailData"
        border
        class="mb-20px"
        :header-cell-style="{
          'background-color': '#f5f7fa !important',
          color: '#60626',
          'box-sizing': 'border-box',
          'font-size': '14px !important',
          'font-weight': 'normal',
          'line-height': '23px'
        }"
      >
        <el-table-column
          v-for="(value, key) in salesDetailData[0]"
          :key="key"
          :label="key"
          :prop="key"
          width="150px"
        />
      </el-table>

      <el-descriptions title="补货建议" :column="3" border class="mb-20px">
        <el-descriptions-item label="单日预测">{{
          detailData.predictedDailySales ?? '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="预测总库存">{{
          detailData.predictedInventoryTarget ?? '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="建议补货">{{
          detailData.suggestedRestockQuantity ?? '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ detailData.remark ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="计算权重">{{
          detailData.weightFactor ?? '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="库存周期">{{
          detailData.inventoryCycle ? detailData.inventoryCycle + '天' : '-'
        }}</el-descriptions-item>
      </el-descriptions>

      <div v-if="detailData.purchasePlanRespVO && detailData.purchasePlanRespVO.id">
        <!-- 确保采购计划存在且有ID -->
        <h3 class="text-lg font-semibold mb-10px">采购计划</h3>
        <el-descriptions :column="2" border class="mb-20px">
          <el-descriptions-item width="120px" label="计划备注">{{
            detailData.purchasePlanRespVO.planRemark || detailData.purchasePlanRespVO.remark || '-'
          }}</el-descriptions-item>
        </el-descriptions>
        <el-table
          :data="
            detailData.purchasePlanRespVO.purchasePlanItemRespVoList || [
              detailData.purchasePlanRespVO
            ]
          "
          border
        >
          <el-table-column label="图片" width="80">
            <template #default="{ row }">
              <el-image
                :src="
                  row.imgUrl ||
                  detailData.imgUrl ||
                  'https://fuss10.elemecdn.com/e/5d/04aca53a41bbb12550c3ef2f984a3jpeg.jpeg'
                "
                fit="contain"
                style="width: 60px; height: 60px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="localName" label="品名">
            <template #default="{ row }">{{ row.localName || detailData.localName }}</template>
          </el-table-column>
          <el-table-column prop="localSku" label="SKU">
            <template #default="{ row }">{{ row.localSku || detailData.localSku }}</template>
          </el-table-column>
          <el-table-column prop="sellerItem" label="店铺">
            <template #default="{ row }">{{ row.sellerItem || detailData.sellerItem }}</template>
          </el-table-column>
          <el-table-column prop="fnsku" label="FNSKU">
            <template #default="{ row }">{{ row.fnsku || detailData.fnsku }}</template>
          </el-table-column>
          <el-table-column prop="warehouseName" label="仓库" />
          <el-table-column prop="plannedPurchaseQty" label="计划采购量" />
          <el-table-column prop="suggestPurchaseQty" label="建议采购量" />
          <el-table-column prop="expectedArrivalTime" label="期望到货时间" />
          <el-table-column prop="purchaseName" label="采购员" />
          <el-table-column prop="remark" label="备注" />
        </el-table>
        <div class="mt-10px text-right">
          <span class="font-bold">合计</span>
          <span class="ml-10px"
            >计划采购量：{{
              detailData.purchasePlanRespVO.purchasePlanItemRespVoList
                ? detailData.purchasePlanRespVO.purchasePlanItemRespVoList.reduce(
                    (sum, item) => sum + (item.plannedPurchaseQty || 0),
                    0
                  )
                : detailData.purchasePlanRespVO.plannedPurchaseQty || 0
            }}</span
          >
        </div>
      </div>
    </div>
    <div v-else-if="loading" class="text-center p-20px">
      <el-icon class="is-loading" :size="26">
        <Loading />
      </el-icon>
      <p>加载中...</p>
    </div>
    <div v-else class="text-center p-20px">
      <el-empty description="暂无数据" />
    </div>
    <!-- <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template> -->
  </Dialog>
</template>

<script setup lang="ts">
import { ReplenishmentProposalApi } from '@/api/operate/replenishmentProposal'
import { getRepApplyDetail, type RestockApplyRespVO } from '@/api/purchase/repApply'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { computed, ref } from 'vue'
const dialogVisible = ref(false)
const detailData = ref<RestockApplyRespVO | null>(null)
const loading = ref(false)
const message = useMessage()

const salesDetailData = ref<any[]>([])
const profitData = ref<any>({})
const profitNameMap = ref<any>({})

// 审批状态字典，虽然详情接口直接返回了 approvalStatusName，但保留这个以备将来可能需要根据编码做判断
const approvalStatusDict = computed(() =>
  getIntDictOptions(DICT_TYPE.OPERATION_RESTORE_APPLY_APPROVAL_STATUS)
)

const open = async (row) => {
  dialogVisible.value = true
  profitData.value = {}
  profitNameMap.value = {}
  salesDetailData.value = []
  detailData.value = null // 重置数据
  if (row && row.id) {
    loading.value = true
    try {
      const res = await getRepApplyDetail(row.id)
      // 将接口返回的数据直接赋值，如果需要转换，可以在这里处理
      detailData.value = res
      // 如果接口返回的图片字段是 imageUrl，而模板中使用的是 imgUrl，则进行转换
      if (detailData.value && detailData.value.imageUrl && !detailData.value.imgUrl) {
        detailData.value.imgUrl = detailData.value.imageUrl
      }
      const [profitRes, salesRes] = await Promise.all([
        ReplenishmentProposalApi.getProfitData(res.restockId),
        ReplenishmentProposalApi.getSaleData(res.restockId)
      ])

      if (profitRes) {
        profitData.value = profitRes.resultMap
        profitNameMap.value = profitRes.resultNameMap
      }

      if (salesRes && salesRes.salesMap) {
        salesDetailData.value = [salesRes.salesMap]
      }
    } catch (error) {
      message.error('获取详情失败')
      console.error('获取详情失败:', error)
      detailData.value = null // 获取失败时清空数据
    } finally {
      loading.value = false
    }
  } else {
    console.warn('未提供ID，无法加载详情')
    detailData.value = null // 或者设置为一个表示空状态的对象
  }
}

defineExpose({ open })
</script>
<style lang="scss" scoped>
.mx-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.bdl-1 {
  padding-left: 20px;
  border-left: 2px solid #dcdcdc;
}

// 去掉 el-descriptions 标题和标签的加粗效果
:deep(.el-descriptions__title) {
  font-weight: normal !important;
}

:deep(.el-descriptions__label) {
  font-weight: normal !important;
}
</style>
