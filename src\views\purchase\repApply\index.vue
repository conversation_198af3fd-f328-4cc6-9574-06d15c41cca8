<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="68px">
      <el-form-item label="团队" prop="team">
        <el-select v-model="queryParams.team" placeholder="请选择团队" clearable class="!w-240px">
          <el-option
            v-for="item in teamOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <AccountShopSelector
          ref="createAccountRef"
          :showAccount="false"
          v-model:shop-values="queryParams.sids"
          v-model:country-values="queryParams.countrysList"
          v-model:country-names="queryParams.countrys"
          @country-change="queryCountryChange"
          style="padding-bottom: 0"
        />
      </el-form-item>
      <el-form-item label="" prop="skuType">
        <RcSelectInput
          v-model:select-value="queryParams.skuType"
          v-model:input-value="queryParams.skuValue"
          @on-btn="handleSkuValueQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicantId">
        <el-select
          v-model="queryParams.applicantId"
          placeholder="请选择申请人"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in applicantOptions"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核人" prop="approverId">
        <el-select
          v-model="queryParams.approverId"
          placeholder="请选择审核人"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in approverOptions"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatusList">
        <el-select
          v-model="queryParams.approvalStatusList"
          placeholder="请选择审批状态"
          clearable
          multiple
          class="!w-240px"
        >
          <el-option
            v-for="item in getIntDictOptions(DICT_TYPE.OPERATION_RESTORE_APPLY_APPROVAL_STATUS)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建采购计划" prop="createPurchasePlan" label-width="90px">
        <el-select
          v-model="queryParams.createPurchasePlan"
          placeholder="请选择是否创建采购计划"
          clearable
          class="!w-240px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <shortcut-date-range-picker isToday v-model="queryParams.applyTime" />
      </el-form-item>
      <el-form-item label="审批时间" prop="approvalTime">
        <shortcut-date-range-picker v-model="queryParams.approvalTime" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 查询 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <QueryConditionTemplate
          :type="'repApply'"
          :form-data="queryParams"
          @update:form-data="onUpdateFormData"
        />
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap class="mt-10px">
    <Table
      border
      height="auto"
      ref="tableRef"
      :loading="loading"
      :columns="columns"
      :data="list"
      selection
      :pagination="{
        total: totalCount,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo
      }"
      @selection-change="handleSelectChange"
      @update:page-size="handlePageSizeChange"
      @update:current-page="handlePageChange"
      @sort-change="handleSortChange"
      isSort
    >
      <template #top-btn>
        <el-button @click="handleBatchApprove" v-hasPermi="['operation:restock-apply:approval']"
          >批量审批</el-button
        >
        <el-button
          @click="handleCreatePurchasePlan"
          v-hasPermi="['operation:restock-apply:create-purchase-plan']"
          >创建采购计划</el-button
        >
        <el-button
          type=""
          plain
          @click="handleExp('select')"
          v-hasPermi="['operation:restock-apply:export']"
        >
          <Icon icon="ep:download" />导出选中
        </el-button>
        <el-button
          type=""
          plain
          @click="handleExp('all')"
          v-hasPermi="['operation:restock-apply:export']"
        >
          <Icon icon="ep:download" />导出全部
        </el-button>
      </template>
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleApprove(row)"
          v-hasPermi="['operation:restock-apply:approval']"
          :disabled="row.createPurchasePlan == 1"
          >审批</el-button
        >
        <el-button
          type="primary"
          link
          @click="handleCreatePurchasePlan(row)"
          v-hasPermi="['operation:restock-apply:create-purchase-plan']"
          :disabled="row.approvalStatusCode != 1 || row.createPurchasePlan == 1"
          >{{ row.createPurchasePlan == 1 ? '已创建计划' : '创建采购计划' }}</el-button
        >
        <el-button
          type="primary"
          link
          v-hasPermi="['operation:restock-apply:query']"
          @click="handleViewDetails(row)"
          >详情</el-button
        >
      </template>
      <!-- 标签列模板 -->
      <template #tagsResp="{ row }">
        <div
          v-if="
            row.restockSuggestGlobalTagRespVoList &&
            row.restockSuggestGlobalTagRespVoList.length > 0
          "
          class="flex items-center"
        >
          <el-tag
            :style="{
              backgroundColor: row.restockSuggestGlobalTagRespVoList[0].color || '#409EFF',
              color: '#fff'
            }"
            class="mr-5px"
          >
            {{ row.restockSuggestGlobalTagRespVoList[0].tagName }}
          </el-tag>
          <el-popover
            v-if="row.restockSuggestGlobalTagRespVoList.length > 1"
            placement="top-start"
            trigger="hover"
            width="max-content-box"
          >
            <template #reference>
              <el-link type="primary" :underline="false" class="ml-5px"
                >共{{ row.restockSuggestGlobalTagRespVoList.length }}个</el-link
              >
            </template>
            <div style="display: flex; flex-wrap: wrap">
              <el-tag
                v-for="item in row.restockSuggestGlobalTagRespVoList"
                :key="item.globalTagId"
                :style="{
                  backgroundColor: item.color || '#409EFF',
                  color: '#fff',
                  marginRight: '5px',
                  marginBottom: '5px'
                }"
              >
                {{ item.tagName }}
              </el-tag>
            </div>
          </el-popover>
        </div>
        <span v-else>-</span>
      </template>
      <template #plannedPurchaseQty="{ row }">
        <div class="flex items-center">
          <el-popover
            :visible="editingRow === row.id"
            placement="top"
            :width="200"
            trigger="manual"
          >
            <p>修改计划采购量</p>
            <el-input-number v-model="newPlannedPurchaseQty" :min="0" />
            <div style="margin-top: 10px; text-align: right">
              <el-button size="small" text @click="handleCancelEdit">取消</el-button>
              <el-button size="small" type="primary" @click="handleConfirmEdit(row)">
                确定
              </el-button>
            </div>
            <template #reference>
              <div>
                <span>{{ row.plannedPurchaseQty }}</span>
                <el-button
                  v-hasPermi="['operation:restock-apply:update-purchase-plan']"
                  type="primary"
                  link
                  @click="handleEditPlannedPurchaseQty(row)"
                  :disabled="row.createPurchasePlan == 1"
                  style="margin-left: 8px"
                >
                  <Icon icon="ep:edit" />
                </el-button>
              </div>
            </template>
          </el-popover>
        </div>
      </template>

      <!-- 毛利润趋势 -->
      <template #grossProfitTrendDataList="{ row }">
        <GrossProfitTrendChart
          :data="row.grossProfitTrendDataList"
          :row-data="row"
          style="cursor: pointer"
        />
      </template>

      <!-- 3 | 7 | 14 | 30 | 60 | 90天日均 -->
      <template #salesAvg="{ row }">
        <div class="flex items-center gap-4px flex-wrap">
          <el-tooltip placement="top" effect="dark" content="3天日均">
            <div class="text-center">{{ row.salesAvg3 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="7天日均">
            <div class="text-center">{{ row.salesAvg7 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="14天日均">
            <div class="text-center">{{ row.salesAvg14 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="30天日均">
            <div class="text-center">{{ row.salesAvg30 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="60天日均">
            <div class="text-center">{{ row.salesAvg60 }}</div>
          </el-tooltip>
          <el-divider direction="vertical" />
          <el-tooltip placement="top" effect="dark" content="90天日均">
            <div class="text-center">{{ row.salesAvg90 }}</div>
          </el-tooltip>
        </div>
      </template>
      <template #skuInfo="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.localName" class="w-100%" truncated>{{
            row.localName
          }}</el-text>
          <el-text v-clipboard="row.localSku" class="w-100%" truncated>{{ row.localSku }}</el-text>
        </div>
      </template>
      <template #mskuInfo="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.msku" class="w-100%" truncated>{{ row.msku }}</el-text>
          <el-text v-clipboard="row.fnsku" class="w-100%" truncated>{{ row.fnsku }}</el-text>
        </div>
      </template>
      <template #countryShop="{ row }">
        <div class="lh-20px flex flex-col">
          <el-text v-clipboard="row.country" class="w-100%" truncated>{{ row.country }}</el-text>
          <el-text v-clipboard="row.sellerItem" class="w-100%" truncated>{{
            row.sellerItem
          }}</el-text>
        </div>
      </template>
      <!-- 总库存 -->
      <template #totalInventory="scope">
        <div class="flex items-center gap-4px w-100%">
          <div>{{ scope.row.totalInventory }}</div>
          <el-popover
            placement="right"
            :width="240"
            trigger="hover"
            v-if="scope.row.totalInventory"
          >
            <template #reference>
              <Icon :size="12" icon="ep:arrow-down" class="cursor-pointer" />
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>采购计划</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购计划单待采购量<br />
                        统计数据：采购计划（待采购）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchasePlan }}
              </el-descriptions-item>
              <el-descriptions-item class="relative">
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA库存</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 待调仓 + 调仓中 + 可售 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                <div class="flex items-center gap-4px">
                  <span>{{ scope.row.inventorys[0]?.fbaInventory }}</span>
                  <el-popover
                    placement="right"
                    :width="240"
                    trigger="hover"
                    v-if="scope.row.inventorys[0]?.fba"
                  >
                    <template #reference>
                      <Icon :size="12" icon="ep:arrow-right" class="cursor-pointer" />
                    </template>
                    <el-descriptions :column="1" border>
                      <el-descriptions-item label="FBA可售-可售">
                        {{ scope.row.inventorys[0]?.fba[0]?.afnFulfillableQuantity }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-待调仓">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcTransfers }}
                      </el-descriptions-item>
                      <el-descriptions-item label="FBA可售-调仓中">
                        {{ scope.row.inventorys[0]?.fba[0]?.reservedFcProcessing }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-popover>
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>FBA在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        shipments申报量-签收量，仅统计6个月内创建且系统货件状态为"进行中"的货件
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>预计发货量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        统计以下单据：<br />
                        ①发货计划：未关联货件且未关联发货单，且状态为待审批、待处理状态的发货计划<br />
                        ②发货单：未关联货件，且状态为待审批、待配货、待发货状态的发货单
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.amazonQuantityShippingPlan }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>海外仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        海外仓备货单待收货量+目的仓为海外仓的调拨单待收货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityOverseaShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓可用/本地可用</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的可用量 + 可用锁定量 + 期望可用量，仅组合产品包含期望可用量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalValid }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待检待上架量</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU的待检待上架量（汇总SKU无绑定FNSKU数量与SKU+FNSKU数量）
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalQc }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>待交付</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content>
                        配对SKU相关采购单的待交付量，SKU为组合产品则额外包含期望待到货量<br />
                        统计数据：采购单(待提交) + 采购单(待审批) + 采购单(待下单) + 采购单(待到货)
                        + 采购单(已驳回) + 委外订单(待提交) + 委外订单(待审批) + 委外订单(待下单) +
                        委外订单(待到货) + 期望待到货量
                      </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityPurchaseShipping }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <div class="flex items-center gap-4px">
                    <span>本地仓在途</span>
                    <el-tooltip placement="right" raw-content trigger="hover">
                      <template #content> 目的仓为本地仓的调拨单待收货量 </template>
                      <Icon :size="12" icon="ep:info-filled" class="cursor-pointer" />
                    </el-tooltip>
                  </div>
                </template>
                {{ scope.row.inventorys[0]?.scQuantityLocalShipping }}
              </el-descriptions-item>
            </el-descriptions>
          </el-popover>
        </div>
      </template>
    </Table>
  </ContentWrap>

  <!-- 弹窗组件 -->
  <CreatePurchasePlanDialog ref="createPurchasePlanDialogRef" @success="getList" />
  <RepApplyDetailDialog ref="repApplyDetailDialogRef" />
  <ApprovalDialog ref="approvalDialogRef" @success="handleApprovalSuccess" />
</template>

<script setup lang="tsx">
import {
  exportFc as exportRepApplyExcel,
  getRepApplyList,
  updatePlannedPurchaseQty,
  type RepApplyListRequest,
  type RestockApplyRespVO
} from '@/api/purchase/repApply'
import { getCurrentTenant, getSimpleUserList, UserVO } from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import GrossProfitTrendChart from '@/views/operate/replenishmentProposal/GrossProfitTrendChart.vue'
import { computed, onMounted, reactive, ref } from 'vue'
import ApprovalDialog from './ApprovalDialog.vue' // 导入审核弹窗组件
import CreatePurchasePlanDialog from './CreatePurchasePlanDialog.vue' // 导入创建采购计划弹窗
import RepApplyDetailDialog from './RepApplyDetailDialog.vue'

const onUpdateFormData = (newVal) => {
  Object.assign(queryParams, newVal)
  handleQuery()
}
const handleSkuValueQuery = (values: string[]) => {
  queryParams.skuValue = values.join(',')
  handleQuery()
}
defineOptions({ name: 'RepApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false)
const list = ref<RestockApplyRespVO[]>([])
const totalCount = ref(0)
const queryParams = reactive<RepApplyListRequest>({
  pageNo: 1,
  pageSize: 10,
  team: undefined,
  country: undefined,
  shop: undefined,
  sku: undefined,
  applicantId: undefined,
  approverId: undefined,
  approvalStatusList: undefined,
  applyTime: [],
  approvalTime: [],
  sids: undefined,
  countrysList: undefined,
  countrys: undefined,
  skuType: 'MSKU',
  skuValue: undefined,
  createPurchasePlan: undefined
})
const applicantOptions = ref<UserVO[]>([]) // 申请人选项
const approverOptions = ref<UserVO[]>([]) // 审批人选项
const teamOptions = ref<{ id: number; name: string }[]>([]) // 团队选项
const queryFormRef = ref()
const tableRef = ref()
const selectedRows = ref([])
const createPurchasePlanDialogRef = ref() // 创建采购计划弹窗引用
const repApplyDetailDialogRef = ref()
const approvalDialogRef = ref() // 审核弹窗引用
const editingRow = ref(null)
const newPlannedPurchaseQty = ref(0)
// 审批状态字典
const approvalStatusDict = computed(() =>
  getIntDictOptions(DICT_TYPE.OPERATION_RESTORE_APPLY_APPROVAL_STATUS)
)

const columns = ref([
  {
    label: '毛利润趋势',
    field: 'grossProfitTrendDataList',
    width: 200,
    icon: 'ep:warning',
    sortable: false,
    tips: '展示15天毛利润趋势图,点击可查看详情',
    showOverflowTooltip: false
  },
  {
    label: '单件毛利率',
    tips: `单件毛利率：</br>毛利率 = (毛利润 ÷ 销售额) × 100%</br>数据说明：当前展示的单件毛利率为昨日数据。</br>含义：</br>表示每销售一单位产品所带来的利润占销售额的比例。</br>业务意义：</br>反映产品的盈利能力。毛利率越高，产品越有补货价值（建议 ≥30% 为健康）。`,
    field: 'grossMarginPerUnitPercent',
    width: 120,
    icon: 'ep:warning',
    sortable: false // 'custom',
  },
  {
    label: '单件毛利润（¥）',
    tips: `单件毛利润：</br>数据说明：当前展示的单件毛利润为昨日数据。</br>含义 ：</br>产品销售收入扣除直接成本后的利润金额。</br>业务意义 ：</br>判断产品是否赚钱。</br>若毛利润为负，即使销量高也不应补货。`,
    field: 'grossProfitPerUnit',
    width: 150,
    formatter: (row) => `¥${row.grossProfitPerUnit || 0}`,
    icon: 'ep:warning',
    sortable: false // 'custom',
  },
  {
    label: '广告占比',
    field: 'acoasPercent',
    width: 120,
    icon: 'ep:warning',
    sortable: false, // 'custom',
    tips: `广告占比：</br>广告占比 = 广告花费 ÷ 净销售额</br>数据说明：当前展示的广告占比为昨日数据。</br>含义 ：</br>表示整体销售额中，有多少是通过广告“买来的”。</br>业务意义 ：</br>广告占比越低越好，表示自然流量强。</br>广告占比 > 25%：可能过度依赖广告，补货风险高。`
  },
  {
    label: '利润投产比',
    field: 'profitToInvestmentRatio',
    width: 120,
    icon: 'ep:warning',
    sortable: false, // 'custom',
    tips: `利润投产比：</br>利润投产比 = ROAS × 毛利率</br>数据说明：当前展示的利润投产比为昨日数据。</br>含义 ：</br>衡量每投入1元广告费用，能带来多少毛利润。</br>业务意义 ：</br>≥ 150% ：广告高效，产品盈利能力强，建议加大投入。</br>100% ~ 150% ：广告健康，保持当前策略。</br>80% ~ 100% ：接近盈亏平衡，需优化广告或调整定价。</br>< 80% ：广告亏损，建议暂停或优化关键词、出价等策略。`
  },
  {
    field: 'skuInfo',
    label: '品名/SKU',
    width: 260,
    fixed: 'left',
    align: 'left',
    showOverflowTooltip: true
  },
  {
    field: 'mskuInfo',
    label: 'MSKU/FNSKU',
    width: 200,
    align: 'left',
    fixed: 'left',
    showOverflowTooltip: true
  },
  {
    field: 'countryShop',
    label: '国家/店铺',
    align: 'left',
    fixed: 'left',
    slotName: 'countryShop',
    width: 150,
    showOverflowTooltip: true
  },
  {
    field: 'imgUrl',
    label: '图片',
    type: 'image',
    align: 'left'
  },
  { field: 'asin', label: 'ASIN', width: 180, align: 'left' },
  {
    field: 'price',
    label: '价格',
    width: 80,
    tips: '同步后台的Your Price',
    sortable: false, // 'custom',
    formatter: (row) => row.currencyCode + row.price
  },
  // {
  //   field: 'landedPrice',
  //   label: '总价',
  //   width: 80,
  //   tips: `总价: 售价=优惠价+FBM运费-积分\n</br>1、当后台未设置优惠价或优惠价已过期，取价格 (Your Price)\n</br>2、仅日本站有积分数据`,
  //   formatter: (row) => row.currencyCode + row.landedPrice
  // },
  {
    field: 'totalInventory',
    label: '总库存',
    width: 90,
    align: 'center',
    sortable: false, // 'custom',
    icon: 'ep:warning',
    tips: '总库存 = 本地可用 + 待检待上架量 + 待交付 + 本地仓在途 + 采购计划 + 海外仓可用 + 海外仓在途 + FBA可售 + FBA在途 + 预计发货量'
  },
  {
    label: '3 | 7 | 14 | 30 | 60 | 90天日均',
    field: 'salesAvg',
    width: 350,
    minWidth: 350,
    slotName: 'salesAvg',
    sortable: false, // 'custom',
    align: 'center'
  },
  { field: 'analysisTime', label: '分析时间', width: 160 },
  {
    label: '标签',
    field: 'tagsResp',
    width: 120,
    minWidth: 120
  },
  {
    field: 'principalNames',
    label: 'Listing负责人',
    width: 130,
    align: 'left'
  },
  { field: 'applicantName', label: '申请人', width: 100 },
  { field: 'applyTime', label: '申请时间', width: 160 },
  { field: 'approvalName', label: '审核人', width: 100 },
  { field: 'createPlanName', label: '创建计划人', width: 100 },
  { field: 'createTime', label: '创建计划时间', width: 100 },
  { field: 'approvalTime', label: '审核时间', width: 160 },
  { field: 'approvalStatusName', label: '审核状态', width: 100 },
  { field: 'operationChannelName', label: '渠道', width: 100 },
  { field: 'approvalRemark', label: '审核备注', width: 120 },
  {
    field: 'predictedDailySales',
    label: '单日预测',
    sortable: false, // 'custom',
    width: 100
  },
  {
    field: 'predictedInventoryTarget',
    label: '预测总库存',
    sortable: false, // 'custom',
    width: 100
  },
  {
    field: 'suggestedRestockQuantity',
    label: '建议补货',
    sortable: false, // 'custom',
    width: 100
  },
  {
    field: 'returnGoodsRate',
    label: '退货率',
    width: 100,
    align: 'center',
    sortable: false // 'custom'
  },
  {
    field: 'returnGoodsCount',
    label: '退货量',
    width: 100,
    align: 'center',
    sortable: false // 'custom'
  },
  {
    field: 'statusDesc',
    label: 'Listing状态',
    width: 100,
    align: 'center'
  },
  {
    field: 'plannedPurchaseQty',
    label: '计划采购量',
    width: 100,
    sortable: false, // 'custom',
    slotName: 'plannedPurchaseQty'
  },
  { field: 'action', label: '操作', width: 180, fixed: 'right', slotName: 'action' }
])

const getList = async () => {
  loading.value = true
  try {
    const params: RepApplyListRequest = {
      ...queryParams,
      // 根据 queryParams.skuType 处理 skuValue
      asinList: queryParams.skuType === 'ASIN' ? queryParams.skuValue : undefined,
      localSkuList: queryParams.skuType === 'SKU' ? queryParams.skuValue : undefined,
      mskuList: queryParams.skuType === 'MSKU' ? queryParams.skuValue : undefined,
      fnskuList: queryParams.skuType === 'FNSKU' ? queryParams.skuValue : undefined,
      localNameList: queryParams.skuType === '品名' ? queryParams.skuValue : undefined,
      sids: queryParams.sids?.join(','),
      countrys: queryParams.countrys,
      applicationIds: queryParams.applicantId ? [queryParams.applicantId] : undefined, // Changed to applicationIds and ensure it's an array
      approvalIdList: queryParams.approverId ? [queryParams.approverId] : undefined, // Changed to approvalIdList and ensure it's an array
      approvalStatusList: queryParams.approvalStatusList // 假设 approvalStatusList 是状态码
    }
    // 清理未定义的参数
    Object.keys(params).forEach((key) => {
      if (
        params[key] === undefined ||
        params[key] === '' ||
        (Array.isArray(params[key]) && params[key].length === 0)
      ) {
        delete params[key]
      }
    })

    const res = await getRepApplyList(params)
    list.value = res.list || []
    totalCount.value = res.total || 0
  } catch (error) {
    console.error('获取列表失败:', error)
    list.value = [] // 出错时清空列表
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 国家变更
const queryCountryChange = (value) => {
  queryParams.countrys = value
}

const resetQuery = () => {
  queryFormRef.value?.resetFields()
  // queryParams.team = undefined
  queryParams.country = undefined
  queryParams.shop = undefined
  queryParams.sku = undefined
  queryParams.applicantId = undefined
  queryParams.approverId = undefined
  queryParams.approvalStatusList = undefined
  queryParams.createPurchasePlan = undefined
  queryParams.applyTime = []
  queryParams.approvalTime = []
  queryParams.sids = undefined
  queryParams.countrysList = undefined
  queryParams.countrys = undefined
  queryParams.skuType = 'MSKU'
  queryParams.skuValue = undefined
  handleQuery()
}

const handleCreatePurchasePlan = (row?: RestockApplyRespVO) => {
  const itemsToPlan = row && row.id ? [row] : selectedRows.value
  if (itemsToPlan.length === 0) {
    message.warning('请至少选择一条数据来创建采购计划')
    return
  }
  // 筛选出可用于创建采购计划的数据，例如：状态为“已通过”
  const validItems = itemsToPlan.filter(
    (item) => item.approvalStatusCode === 1 // 1 表示已通过
  )
  if (validItems.length === 0) {
    message.warning('选中的数据不符合创建采购计划的条件（非已通过状态）')
    return
  }
  if (validItems.length !== itemsToPlan.length && !(row && row.id)) {
    message.warning('部分选中的数据不符合创建采购计划的条件（非已通过状态），已自动过滤。')
  }
  createPurchasePlanDialogRef.value?.open(validItems)
}

const handleBatchApprove = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据进行审批')
    return
  }
  const idsToApprove = selectedRows.value
    .map((row) => row.id)
    .filter((id) => id !== undefined) as number[]
  if (idsToApprove.length === 0) {
    message.warning('选中的数据没有有效的ID进行审批')
    return
  }
  approvalDialogRef.value?.open(idsToApprove)
}

const handleViewDetails = (row) => {
  repApplyDetailDialogRef.value?.open(row)
}

const handleEditPlannedPurchaseQty = (row) => {
  editingRow.value = row.id
  newPlannedPurchaseQty.value = row.plannedPurchaseQty
}

const handleCancelEdit = () => {
  editingRow.value = null
}

const handleConfirmEdit = async (row) => {
  try {
    await updatePlannedPurchaseQty({
      id: row.id,
      plannedPurchaseQty: newPlannedPurchaseQty.value
    })
    message.success('修改成功')
    editingRow.value = null
    getList()
  } catch (error) {
    console.error('修改失败:', error)
  }
}

const handleApprove = (row: RestockApplyRespVO) => {
  if (!row.id) {
    message.warning('该条数据没有有效的ID进行审批')
    return
  }
  approvalDialogRef.value?.open([row.id], row)
}

const handleSelectChange = (selection: RestockApplyRespVO[]) => {
  selectedRows.value = selection
}

const handlePageSizeChange = (pageSize) => {
  queryParams.pageSize = pageSize
  getList()
}

const handlePageChange = (page) => {
  queryParams.pageNo = page
  getList()
}

// 处理排序
const handleSortChange = ({ column, prop, order }) => {
  queryParams.sortingFields = []
  if (prop && order) {
    queryParams.sortingFields.push({
      field: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    })
  }
  getList()
}

const handleApprovalSuccess = () => {
  getList() // 刷新列表
  selectedRows.value = [] // 清空选择
  // tableRef.value?.clearSelection() // 清空表格勾选
}

const handleExp = async (type: 'select' | 'all') => {
  try {
    let ids: number[] = []
    if (type === 'select') {
      if (selectedRows.value.length === 0) {
        message.warning('请至少选择一项进行导出')
        return
      }
      ids = selectedRows.value.map((item) => item.id).filter((id) => id !== undefined) as number[]
      if (ids.length === 0) {
        message.warning('选中的数据没有有效的ID进行导出')
        return
      }
    }

    // 调用导出接口，queryParams 需要传递，ids 根据类型传递
    const data = await exportRepApplyExcel(queryParams, ids)
    download.excel(data, '补货申请列表.xls')
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

onMounted(() => {
  getList()
})

onMounted(async () => {
  getList()
  try {
    const users = await getSimpleUserList()
    applicantOptions.value = users
    approverOptions.value = users

    getCurrentTenant().then((res) => {
      teamOptions.value = res
      queryParams.team = (res && res.length > 0 && res[0].id) || undefined
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  }
})
</script>

<style lang="scss" scoped>
// 可以在这里添加组件的特定样式
</style>
