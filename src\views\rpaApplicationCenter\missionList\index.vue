<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="任务名称">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称（模糊查询）"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
        >
          <Icon icon="ep:search" />查询
        </el-button>
        <el-button
          @click="syncTask"
          v-hasPermi="['thirdparty:rpa-my-task:sync']"
        >
          <Icon icon="ep:refresh" />同步最新任务
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 任务列表 -->
  <ContentWrap>
    <ul
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="disabled"
      class="task-list"
      v-loading="tableLoading"
    >
      <el-card
        v-for="(task, index) in taskList"
        :key="index"
        shadow="never"
        class="task-card"
      >
        <div class="task-header">
          <el-tooltip
            :content="task.scheduleName"
            placement="top"
          >
            <span class="task-name text-ellipsis">{{ task.scheduleName }}</span>
          </el-tooltip>
          <!-- <el-tag
            v-if="task.scheduleType"
            type="info"
            size="small"
          >
            {{ task.scheduleType }}
          </el-tag> -->
        </div>

        <div class="task-stats">
          <div class="task-info">
            <span>创建时间：{{ formatDate(task.createTime) }}</span>
            <!-- <span>下次执行：{{ formatDate(task.nextTime) }}</span> -->
          </div>
          <div
            class="task-schedule"
            v-if="task.time"
          >
            <span>执行时间：{{ task.time }}</span>
          </div>
        </div>

        <div class="task-actions">
          <el-button
            type="primary"
            style="width: 100%"
            size="large"
            @click="executeTask(task)"
            v-hasPermi="['thirdparty:rpa-my-task:executeImmediately']"
          >
            立即执行
          </el-button>
        </div>
      </el-card>
    </ul>

    <!-- 加载更多提示 -->
    <div
      v-if="loadingMore"
      class="load-more"
    >
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
    <div
      v-else-if="hasMore"
      class="load-more-tip"
    >
      滚动加载更多
    </div>
    <div
      v-else
      class="no-more"
    >
      没有更多数据了
    </div>
  </ContentWrap>
  <!-- 立即执行 -->
  <TaskExecuteDialog
    ref="executeDialogRef"
    @submit="handleTaskSubmit"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import TaskExecuteDialog from './startForm.vue'
import { getRpaTaskList, executeImmediately, syncTaskApi } from '@/api/rpa/rpaRobotClient'
import type { RpaTaskListRespVO } from '@/api/rpa/task'
import { formatDate } from '@/utils/formatTime'

const executeDialogRef = ref()
const message = useMessage()
const tableLoading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 24,
  taskName: ''
})

// 任务列表数据
const taskList = ref<RpaTaskListRespVO[]>([])
const loadingMore = ref(false)
const hasMore = ref(true)
const disabled = ref(false)

/** 查询按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  taskList.value = []
  hasMore.value = true
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.taskName = ''
  queryParams.pageNo = 1
  taskList.value = []
  hasMore.value = true
  getList()
}
/** 执行任务 */
const executeTask = (task: RpaTaskListRespVO) => {
  // 注释掉原有逻辑，直接掉接口

  // message.success(`开始执行任务: ${task.scheduleName}`)
  // // 这里添加实际执行任务的逻辑
  // // 模拟从后端获取的参数配置
  // const params = [
  //   {
  //     name: '字符参数',
  //     type: 'string',
  //     required: true,
  //     remark: '这是备注'
  //   },
  //   {
  //     name: '整数参数',
  //     type: 'integer',
  //     min: 0,
  //     max: 100,
  //     required: true,
  //     remark: '这是备注'
  //   },
  //   {
  //     name: '布尔值参数',
  //     type: 'boolean',
  //     required: true,
  //     remark: '这是备注'
  //   },
  //   {
  //     name: '小数参数',
  //     type: 'float',
  //     min: 0,
  //     max: 1,
  //     precision: 2,
  //     remark: '这是备注'
  //   },
  //   {
  //     name: '文件参数',
  //     type: 'file',
  //     required: true,
  //     remark: '这是备注',
  //     tip: '请上传不超过10MB的文件'
  //   }
  // ]

  // executeDialogRef.value.open(task.scheduleName, params)

  ElMessageBox.confirm('确定要执行任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      tableLoading.value = true
      let res = await executeImmediately({ id: task.id })
      if (res) {
        ElMessage.success('执行成功')
        getList()
      } else {
        ElMessage.error(res.msg)
      }
      tableLoading.value = false
    })
    .catch(() => {
      // 取消执行任务
    })
}

// 立即执行表单提交
const handleTaskSubmit = (submitData) => {
  console.log('提交数据:', submitData)
  // 调用API执行任务
  // taskApi.executeTask(submitData)
}

/** 加载更多数据 */
const loadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    getList()
  }
}

/** 获取列表数据 */
const getList = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  disabled.value = true
  try {
    const res = await getRpaTaskList(queryParams)

    if (res?.list?.length) {
      taskList.value = [...taskList.value, ...res.list]
      queryParams.pageNo++
      hasMore.value = taskList.value.length < (res.total || 0)
    } else {
      hasMore.value = false
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    hasMore.value = false
  } finally {
    loadingMore.value = false
    disabled.value = false
  }
}

// 同步最新任务列表
const syncTask = async () => {
  ElMessageBox.confirm('是否同步最新任务列表？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await syncTaskApi()
      if (!res) return
      ElMessage.success('同步任务列表成功')
      getList()
    } catch (error) {
      console.error('同步任务列表失败:', error)
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.task-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.task-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;

  // &:hover {
  //   transform: translateY(-3px);
  //   box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  // }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .task-name {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .task-stats {
    margin: 12px 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);

    .task-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 8px;
    }

    .task-schedule {
      font-weight: 500;
      color: var(--el-color-primary);
    }
  }

  .task-actions {
    display: flex;
    justify-content: center;
  }
}

.text-ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.load-more,
.load-more-tip,
.no-more {
  padding: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  text-align: center;

  .el-icon {
    margin-right: 8px;
    animation: rotating 2s linear infinite;
  }
}

.no-more {
  color: var(--el-text-color-placeholder);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
