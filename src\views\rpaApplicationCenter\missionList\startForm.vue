<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="50%"
    @close="closeDialog"
    class="task-execute-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      label-position="right"
    >
      <!-- 动态渲染参数表单项 -->
      <template
        v-for="(param, index) in taskParams"
        :key="index"
      >
        <!-- 字符参数 -->
        <el-form-item
          v-if="param.type === 'string'"
          :label="param.name"
          :prop="`params.${index}.value`"
          :rules="[{ required: param.required, message: `${param.name}不能为空`, trigger: 'blur' }]"
        >
          <el-input
            v-model="param.value"
            :placeholder="param.placeholder || `请输入${param.name}`"
            clearable
          />
          <div
            class="param-remark"
            v-if="param.remark"
          >{{ param.remark }}</div>
        </el-form-item>

        <!-- 整数参数 -->
        <el-form-item
          v-else-if="param.type === 'integer'"
          :label="param.name"
          :prop="`params.${index}.value`"
          :rules="[
            { required: param.required, message: `${param.name}不能为空`, trigger: 'blur' },
            { type: 'number', message: '必须为整数', trigger: 'blur' }
          ]"
        >
          <el-input-number
            v-model.number="param.value"
            :placeholder="param.placeholder || `请输入${param.name}`"
            :min="param.min"
            :max="param.max"
            controls-position="right"
          />
          <div
            class="param-remark"
            v-if="param.remark"
          >{{ param.remark }}</div>
        </el-form-item>

        <!-- 布尔值参数 -->
        <el-form-item
          v-else-if="param.type === 'boolean'"
          :label="param.name"
          :prop="`params.${index}.value`"
          :rules="[{ required: param.required, message: `请选择${param.name}`, trigger: 'change' }]"
        >
          <el-radio-group v-model="param.value">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <div
            class="param-remark"
            v-if="param.remark"
          >{{ param.remark }}</div>
        </el-form-item>

        <!-- 小数参数 -->
        <el-form-item
          v-else-if="param.type === 'float'"
          :label="param.name"
          :prop="`params.${index}.value`"
          :rules="[
            { required: param.required, message: `${param.name}不能为空`, trigger: 'blur' },
            { type: 'number', message: '必须为数字', trigger: 'blur' }
          ]"
        >
          <el-input-number
            v-model.number="param.value"
            :placeholder="param.placeholder || `请输入${param.name}`"
            :min="param.min"
            :max="param.max"
            :precision="param.precision || 2"
            controls-position="right"
          />
          <div
            class="param-remark"
            v-if="param.remark"
          >{{ param.remark }}</div>
        </el-form-item>

        <!-- 文件参数 -->
        <el-form-item
          v-else-if="param.type === 'file'"
          :label="param.name"
          :prop="`params.${index}.value`"
          :rules="[{ required: param.required, message: `请上传${param.name}`, trigger: 'change' }]"
        >
          <el-upload
            v-model:file-list="param.fileList"
            :action="uploadUrl"
            :limit="1"
            :on-success="handleUploadSuccess"
            :before-upload="(file) => beforeUpload(file, index)"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div
                class="el-upload__tip"
                v-if="param.tip"
              >{{ param.tip }}</div>
              <div
                class="param-remark"
                v-if="param.remark"
              >{{ param.remark }}</div>
            </template>
          </el-upload>
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <div style="text-align: center;">
        <el-button
          :disabled="formLoading"
          type="primary"
          @click="submitForm"
        >执 行</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { UploadProps, UploadUserFile } from 'element-plus'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗是否展示
const dialogTitle = ref('') // 弹窗标题
const formLoading = ref(false) // 表单加载状态
const formRef = ref() // 表单Ref

// 上传地址 - 根据实际API修改
const uploadUrl = ref(import.meta.env.VITE_UPLOAD_URL || '/api/upload')

// 任务参数数据结构
interface TaskParam {
  name: string // 参数名称
  type: 'string' | 'integer' | 'boolean' | 'float' | 'file' // 参数类型
  value: any // 参数值
  required?: boolean // 是否必填
  remark?: string // 备注说明
  placeholder?: string // 输入提示
  // 数字类型特有
  min?: number
  max?: number
  precision?: number // 小数位数
  // 文件类型特有
  fileList?: UploadUserFile[]
  tip?: string // 上传提示
}

// 表单数据
const taskParams = ref<TaskParam[]>([])
const formData = reactive({
  params: [] as TaskParam[]
})

/** 打开弹窗 */
const open = async (taskName: string, params: TaskParam[] = []) => {
  dialogVisible.value = true
  dialogTitle.value = taskName
  formLoading.value = false

  // 初始化参数
  taskParams.value = params.map((param) => ({
    ...param,
    value: param.type === 'boolean' ? false : undefined,
    fileList: param.type === 'file' ? [] : undefined
  }))

  formData.params = taskParams.value
}

/** 关闭弹窗 */
const closeDialog = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

/** 文件上传成功处理 */
const handleUploadSuccess: UploadProps['onSuccess'] = (response, uploadFile, uploadFiles) => {
  // 根据实际API返回结构调整
  if (response.code === 200) {
    message.success('文件上传成功')
  } else {
    message.error(response.msg || '文件上传失败')
  }
}

/** 文件上传前校验 */
const beforeUpload = (file: File, index: number) => {
  // 可以在这里添加文件类型、大小校验
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }

  // 更新文件列表
  taskParams.value[index].fileList = [
    {
      name: file.name,
      size: file.size,
      raw: file
    }
  ]

  return true
}

/** 提交表单 */
const emit = defineEmits(['submit']) // 定义submit事件
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  // 准备提交数据
  const submitData = {
    taskName: dialogTitle.value,
    params: taskParams.value.map((param) => {
      if (param.type === 'file') {
        return {
          name: param.name,
          type: param.type,
          value: param.fileList?.[0]?.raw || null
        }
      }
      return {
        name: param.name,
        type: param.type,
        value: param.value
      }
    })
  }

  formLoading.value = true
  try {
    // 触发submit事件，由父组件处理实际提交逻辑
    emit('submit', submitData)
    closeDialog()
  } finally {
    formLoading.value = false
  }
}

defineExpose({ open }) // 提供open方法
</script>

<style lang="scss" scoped>
.task-execute-dialog {
  .param-remark {
    margin-top: 4px;
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-secondary);
  }

  .el-form-item {
    margin-bottom: 22px;
  }

  :deep(.el-dialog__body) {
    max-height: 70vh;
    padding: 20px 20px 0;
    overflow-y: auto;
  }
}
</style>
