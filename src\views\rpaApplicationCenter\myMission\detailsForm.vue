<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    @close="closeDialog"
    class="task-detail-dialog"
  >
    <el-form
      v-loading="formLoading"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <el-card
        shadow="never"
        class="info-card"
      >
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称">
              <el-input
                v-model="formData.taskName"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务ID">
              <el-input
                v-model="formData.taskId"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交时间">
              <el-input
                v-model="formData.submitTime"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成时间">
              <el-input
                v-model="formData.finishTime"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态">
              <el-tag :type="getStatusTagType(formData.status)">
                {{ formData.statusName }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 执行参数 -->
      <el-card
        shadow="never"
        class="params-card"
      >
        <template #header>
          <div class="card-header">
            <span>执行参数</span>
          </div>
        </template>

        <div class="json-viewer">
          <pre>{{ formatJson(formData.executionParams) }}</pre>
        </div>
      </el-card>

      <!-- 执行日志 -->
      <el-card
        shadow="never"
        class="log-card"
      >
        <template #header>
          <div class="card-header">
            <span>执行日志</span>
            <el-button
              type="primary"
              size="small"
              @click="copyLogs"
              :icon="CopyDocument"
            >
              复制日志
            </el-button>
          </div>
        </template>

        <div class="log-content">
          <div
            v-for="(log, index) in formData.executionLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">[{{ log.time }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="formData.allowAction"
          type="primary"
          @click="handleTaskAction"
        >
          {{ formData.actionText }}
        </el-button>
        <el-button @click="closeDialog">关 闭</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CopyDocument } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
import { getRpaMyTaskDetail } from '@/api/rpa/rpaRobotClient'
import { formatDate } from '@/utils/formatTime'

const { t } = useI18n()
const message = useMessage()
const { copy } = useClipboard()

const dialogVisible = ref(false)
const dialogTitle = ref('任务详情')
const formLoading = ref(false)

// 表单数据
const formData = ref({
  taskName: '',
  taskId: '',
  submitTime: '',
  finishTime: '',
  status: '',
  executionParams: {},
  executionLogs: [],
  allowAction: false,
  actionText: '执行操作'
})

// 格式化JSON显示
const formatJson = (json: object) => {
  return JSON.stringify(json, null, 2)
}

// 根据状态获取标签类型
const getStatusTagType = (status: string) => {
  const map = {
    waiting: 'warning',
    running: 'primary',
    finish: 'success',
    stopping: 'warning',
    stopped: 'info',
    error: 'danger'
  }
  return map[status] || ''
}

// 复制日志
const copyLogs = () => {
  const logText = formData.value.executionLogs
    .map((log) => `[${log.time}] ${log.message}`)
    .join('\n')
  copy(logText)
  message.success('日志已复制到剪贴板')
}

// 任务操作
const handleTaskAction = () => {
  // 根据任务状态执行不同操作
  const taskId = formData.value.taskId
  const actionType = formData.value.actionText

  // 这里应该调用相应的API执行操作
  // 目前仅显示操作提示，后续可以集成实际API
  message.success(`已${actionType}任务: ${taskId}`)

  // 操作后关闭弹窗
  closeDialog()
}

// 打开弹窗
const open = async (row: any) => {
  dialogVisible.value = true
  formLoading.value = true

  try {
    // 调用API获取任务详情
    const data = await getRpaMyTaskDetail(row.id)
    data.addTime = formatDate(data.addTime)
    data.nextTime = formatDate(data.nextTime)
    // 构建执行参数对象，排除一些基本信息字段
    const executionParams = { ...data }
    const excludeFields = [
      'id',
      'scheduleName',
      'submitTime',
      'complateTime',
      'statusName',
      'createTime',
      'editTime'
    ]
    excludeFields.forEach((field) => delete executionParams[field])

    // 构建日志信息
    const executionLogs = [{ time: formatDate(data.createTime) || '', message: '任务已创建' }]

    if (data.submitTime) {
      executionLogs.push({ time: formatDate(data.submitTime), message: '任务已提交' })
    }

    if (data.editTime && data.editTime !== data.createTime) {
      executionLogs.push({ time: formatDate(data.editTime), message: '任务状态已更新' })
    }

    if (data.complateTime) {
      executionLogs.push({ time: formatDate(data.complateTime), message: '任务已完成' })
    }

    // 填充表单数据
    formData.value = {
      ...data,
      taskName: data.scheduleName || '',
      taskId: data.id?.toString() || '',
      submitTime: formatDate(data.submitTime) || '',
      finishTime: formatDate(data.complateTime) || '',
      status: data.status || '',
      executionParams,
      executionLogs,
      allowAction: ['waiting', 'running', 'exception'].includes(data.status || ''),
      actionText: data.status === 'exception' ? '暂停' : '停止'
    }
  } catch (error) {
    console.error('获取任务详情失败', error)
    message.error('获取任务详情失败')

    // 错误情况下设置默认值
    formData.value = {
      taskName: row.scheduleName || '',
      taskId: row.id?.toString() || '',
      submitTime: row.submitTime || '',
      finishTime: row.complateTime || '',
      status: row.status || '',
      executionParams: {},
      executionLogs: [{ time: new Date().toLocaleString(), message: '获取任务详情失败' }],
      allowAction: false,
      actionText: ''
    }
  } finally {
    formLoading.value = false
  }
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.task-detail-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .info-card,
  .params-card,
  .log-card {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      color: #303133;
    }
  }

  .json-viewer {
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;

    pre {
      margin: 0;
      font-family: 'Courier New', Courier, monospace;
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }

  .log-content {
    max-height: 300px;
    padding: 5px;
    overflow-y: auto;

    .log-item {
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.5;

      .log-time {
        margin-right: 8px;
        color: #909399;
      }

      .log-message {
        color: #303133;
      }
    }
  }

  .dialog-footer {
    padding-top: 16px;
    text-align: center;
    border-top: 1px solid #ebeef5;
  }
}

@media (width <=768px) {
  .task-detail-dialog {
    width: 95% !important;

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-col {
      margin-bottom: 0;
    }
  }
}
</style>
