<template>
  <!-- 状态统计卡片 -->
  <ContentWrap class="mb-20px">
    <el-row :gutter="20">
      <el-col
        :span="6"
        v-for="stat in statusStats"
        :key="stat.status"
      >
        <el-card
          class="stat-card"
          :class="`stat-${stat.status}`"
        >
          <div class="stat-content">
            <div class="stat-value">{{ stat.count }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>

  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="任务名称">
        <el-input
          v-model="queryParams.scheduleName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <!-- getIntDictOptions(DICT_TYPE.THIRDPARTY_RPA_TASK_STATUS) -->
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
        >
          <Icon icon="ep:search" />查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" />重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      border
      v-loading="loading"
      :data="list"
      style="width: 100%; margin-top: 20px"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column
        prop="scheduleName"
        label="任务名称"
      />
      <!-- <el-table-column prop="rules" label="排队">
        <template #default="{ row }">
          <div v-if="!row.children">
            <div>总任务数：{{ row.totalTasks }}</div>
            <div
              >当前队列： <el-text class="mx-1" type="primary">{{ row.currentQueue }}</el-text></div
            >
            <div
              >预设等待： <el-text class="mx-1" type="warning">{{ row.presetWait }}</el-text></div
            >
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        label="状态"
        width="150"
      >
        <template #default="{ row }">
          <el-tag
            v-if="row.status"
            :type="getStatusTagType(row.status)"
            :icon="getStatusIcon(row.status)"
            effect="light"
          >
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="submitTime"
        label="提交时间"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column
        label="操作"
        fixed="right"
        width="200"
      >
        <template #default="{ row }">
          <el-button
            v-if="!row.children"
            link
            type="primary"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
            v-if="!row.children && ['running', 'waiting', 'stopping', 'error'].includes(row.status)"
            link
            type="danger"
            @click="handleStop(row)"
          >
            {{ row.status === 'exception' ? '暂停' : '停止' }}
          </el-button>
          <!-- <el-button
            v-if="!row.children && ['finish', 'stopped'].includes(row.status)"
            link
            type="success"
            @click="handleDownload(row)"
          >
            下载结果
          </el-button> -->
          <!-- <el-button
            v-if="!row.children && row.status === 'waiting'"
            link
            type="warning"
            @click="handleCancel(row)"
          >
            取消
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <el-backtop
    :right="100"
    :bottom="100"
  />
  <!-- 详情弹窗 -->
  <detailsForm ref="detailsFormRef" />
</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import { ref, reactive, onMounted, computed } from 'vue'
import {
  getRpaMyTaskListPage,
  getRpaMyTaskStatusCount,
  taskStop,
  type RpaMyTaskRespVO,
  type RpaMyTaskListReqVO
} from '@/api/rpa/rpaRobotClient'
import detailsForm from './detailsForm.vue'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'

const message = useMessage()
const { t } = useI18n()

const loading = ref(true)
const total = ref(0)
const list = ref<RpaMyTaskRespVO[]>([])
const statusCounts = ref<RpaMyTaskStatusCountResp>({})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  scheduleName: '',
  status: ''
})

const detailsFormRef = ref(null) // 详情弹窗

// 状态选项
const statusOptions = [
  { value: 'waiting', label: '等待调度' },
  { value: 'running', label: '任务运行中' },
  { value: 'finish', label: '任务运行结束' },
  { value: 'stopping', label: '任务正在停止' },
  { value: 'stopped', label: '已结束' },
  { value: 'error', label: '异常' }
]

// 计算状态统计
const statusStats = computed(() => {
  const statusMap = {
    运行中: 'running',
    已完成: 'completed',
    等待中: 'waiting',
    异常: 'exception'
  }

  return Object.entries(statusCounts.value)
    .map(([label, count]) => ({
      label,
      count: count || 0,
      status: statusMap[label] || ''
    }))
    .filter((item) => item.status)
})

// 获取状态标签
const getStatusLabel = (status) => {
  const map = {
    waiting: '等待调度',
    running: '任务运行中',
    finish: '任务运行结束',
    stopping: '任务正在停止',
    stopped: '已结束',
    error: '异常'
  }
  return map[status] || status
}

// 获取图片
const getStatusIcon = (status) => {
  const map = {
    running: 'ep:caret-right',
    completed: 'ep:select',
    waiting: 'ep:loading',
    exception: 'ep:warn-triangle-filled',
    terminated: 'ep:success-filled'
  }
  return map[status] || ''
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const map = {
    running: 'primary',
    finish: 'success',
    waiting: 'info',
    error: 'danger',
    stopping: 'warning',
    stopped: 'info'
  }
  return map[status] || ''
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const params: RpaMyTaskListReqVO = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      scheduleName: queryParams.scheduleName, // queryParams.scheduleName 应该对应 RpaMyTaskListReqVO.scheduleName
      status: queryParams.status
    }
    const data = await getRpaMyTaskListPage(params)
    list.value = data.list || []
    total.value = data.total || 0
    const res = await getRpaMyTaskStatusCount()
    statusCounts.value = res
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.scheduleName = '' // 清空 scheduleName
  queryParams.status = ''
  handleQuery()
}

// 查看详情
const handleDetail = (row) => {
  detailsFormRef.value.open(row)
}

// 停止任务
const handleStop = async (row) => {
  ElMessageBox.confirm(
    `确定要${row.status === 'exception' ? '暂停' : '停止'}任务 ${row.scheduleName} 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    let res = await taskStop(row.id)
    if (res) {
      message.success(`已${row.status === 'exception' ? '暂停' : '停止'}任务 ${row.scheduleName}`)
      getList()
    }
  })
}

// 下载结果
const handleDownload = (row) => {
  message.success(`下载任务 ${row.id} 的结果`)
}

// 取消任务
const handleCancel = (row) => {
  message.success(`已取消任务 ${row.id}`)
}

onMounted(async () => {
  await getList()
})
</script>

<style scoped>
::v-deep(.el-tag__content) {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-card {
  display: flex;
  height: 100px;
  transition: all 0.3s;
  align-items: center;
  justify-content: center;
}

.stat-card:hover {
  //transform: translateY(-5px);
}

.stat-content {
  text-align: center;
}

.stat-value {
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: bold;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-running {
  border-top: 4px solid var(--el-color-success);
}

.stat-completed {
  border-top: 4px solid var(--el-color-info);
}

.stat-waiting {
  border-top: 4px solid var(--el-color-warning);
}

.stat-exception {
  border-top: 4px solid var(--el-color-danger);
}
</style>
