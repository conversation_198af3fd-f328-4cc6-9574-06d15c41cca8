<template>
  <!-- 搜索工作栏 -->
  <!-- <ContentWrap>
    <el-form
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="100px"
      ref="queryFormRef"
    >
      <el-form-item
        label="机器人UUID"
        prop="uuid"
      >
        <el-input
          v-model="queryParams.uuid"
          placeholder="请输入机器人UUID"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="机器人名称"
        prop="name"
      >
        <el-input
          v-model="queryParams.name"
          placeholder="请输入机器人名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="机器人状态"
        prop="status"
      >
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
        >
          <Icon
            icon="ep:search"
            class="mr-5px"
          />查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon
            icon="ep:refresh"
            class="mr-5px"
          />重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap> -->

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      border
      v-loading="loading"
      :data="list"
      style="width: 100%; margin-top: 20px;"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      highlight-current-row
    >
      <el-table-column
        prop="robotClientUuid"
        label="机器人UUID"
        show-overflow-tooltip
      />
      <el-table-column
        prop="robotClientName"
        label="机器人名称"
      />
      <el-table-column
        prop="statusName"
        label="机器人状态"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getDictLabel(DICT_TYPE.THIRDPARTY_RPA_ROBOT_CLIENT_STATUS, row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="machineName"
        label="客户端系统host名称"
      />
      <el-table-column
        prop="clientIp"
        label="客户端系统IP"
      />
      <!-- <el-table-column
        label="操作"
        fixed="right"
        width="180px"
      >
        <template #default="{ row }">
          <el-button
            link
            type="primary"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
            v-if="row.status === 'RUNNING' || row.status === 'running'"
            link
            type="danger"
            @click="handleStop(row)"
          >
            停止
          </el-button>
          <el-button
            v-if="row.status === 'WAITING' || row.status === 'waiting'"
            link
            type="success"
            @click="handleStart(row)"
          >
            启动
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { getRpaRobotClientListPage, type RpaRobotClientListRespVO } from '@/api/rpa/rpaRobotClient'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'

const message = useMessage()
const { t } = useI18n()

const loading = ref(true)
const total = ref(0)
const list = ref<RpaRobotClientListRespVO[]>([]) // 指定列表类型
const queryFormRef = ref() // 查询表单的引用
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  uuid: '',
  name: '',
  status: ''
})

// 状态选项 (根据实际API返回的状态值调整，假设API返回大写)
const statusOptions = [
  { value: 'WAITING', label: '等待调度' },
  { value: 'RUNNING', label: '任务运行中' },
  { value: 'OFFLINE', label: '离线' },
  { value: 'ERROR', label: '异常' }
  // 可以根据API实际返回的状态值添加更多选项，例如：
  // { value: 'IDLE', label: '空闲' },
  // { value: 'UNKNOWN', label: '未知' },
]

// 获取状态标签
const getStatusLabel = (status: string) => {
  // 优先从statusOptions查找，如果API返回的状态值与statusOptions不完全一致，则直接显示
  const option = statusOptions.find((item) => item.value.toLowerCase() === status?.toLowerCase())
  return option ? option.label : status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const lowerStatus = status?.toLowerCase()
  const map = {
    waiting: 'warning', // 等待调度
    running: 'success', // 任务运行中
    offline: 'info', // 离线
    error: 'danger', // 异常
    idle: 'primary', // 空闲 (假设)
    unknown: 'default' // 未知 (假设)
  }
  return map[lowerStatus] || 'default'
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const data = await getRpaRobotClientListPage(queryParams)
    list.value = data.list || []
    total.value = data.total || 0
  } catch (error) {
    console.error('获取机器人列表失败:', error)
    message.error('获取机器人列表失败') // 可选：给用户一个错误提示
    list.value = [] // 出错时清空列表
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields() // 使用el-form的resetFields方法
  handleQuery()
}

// 查看详情
const handleDetail = (row) => {
  message.success(`查看机器人 ${row.name} 的详情`)
  // 这里可以打开详情对话框或跳转到详情页
}

// 停止机器人
const handleStop = (row) => {
  message.success(`已停止机器人 ${row.name}`)
  // 这里可以调用停止API
}

// 启动机器人
const handleStart = (row) => {
  message.success(`已启动机器人 ${row.name}`)
  // 这里可以调用启动API
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
/* 自定义表格样式 */
.el-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

.el-table :deep(.el-table__cell) {
  padding: 12px 0;
}

/* 操作按钮间距 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 状态标签样式 */
.el-tag {
  height: 24px;
  min-width: 80px;
  padding: 0 10px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}
</style>
