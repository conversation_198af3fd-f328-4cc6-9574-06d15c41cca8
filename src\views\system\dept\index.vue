<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item
        label="部门名称"
        prop="name"
      >
        <el-input
          v-model="queryParams.name"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item
        label="部门状态"
        prop="status"
      >
        <el-select
          v-model="queryParams.status"
          placeholder="请选择部门状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon
            icon="ep:search"
            class="mr-5px"
          /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon
            icon="ep:refresh"
            class="mr-5px"
          /> 重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:dept:create']"
        >
          <Icon
            icon="ep:plus"
            class="mr-5px"
          /> 新增
        </el-button>
        <el-button
          type="primary"
          plain
          @click="toggleExpandAll"
        >
          <Icon
            :icon="isExpandAll ? 'ep:fold' : 'ep:expand'"
            class="mr-5px"
          />
          {{ isExpandAll ? '折叠' : '展开' }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      row-key="id"
      :default-expand-all="isExpandAll"
      v-if="refreshTable"
    >

      <!-- 添加树形结构指示图标 -->
      <el-table-column
        prop="name"
        label="部门名称"
      >
        <template #default="scope">
          <span
            v-if="scope.row.children && scope.row.children.length > 0"
            class="mr-5px"
          >
            <Icon
              icon="ep:office-building"
              class="text-primary"
            />
          </span>
          <span
            v-else
            class="mr-5px"
          >
            <Icon icon="ep:user-filled" />
          </span>
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column
        prop="id"
        label="部门编号"
        align="left"
      >
        <template #default="scope">
          <!-- <el-tooltip
            content="点击复制部门编号"
            placement="top"
          > -->
          <!-- type="primary"
          style="cursor: pointer"
          @click="copyText(scope.row.id)" -->
          <el-text>{{ scope.row.id }}</el-text>
          <!-- </el-tooltip> -->
        </template>
      </el-table-column>
      <el-table-column
        prop="leader"
        label="负责人"
      >
        <template #default="scope">
          {{ userList.find((user) => user.id === scope.row.leaderUserId)?.nickname }}
        </template>
      </el-table-column>
      <el-table-column
        prop="sort"
        label="排序"
      >
        <template #default="scope">
          <span
            v-if="scope.row.editingSort"
            class="flex items-center"
          >
            <el-input-number
              v-model="scope.row.sort"
              :min="0"
              :max="9999"
              size="small"
              controls-position="right"
              @blur="handleSortBlur(scope.row)"
              @keyup.enter="handleSortBlur(scope.row)"
            />
          </span>
          <span
            v-else
            @click="handleSortClick(scope.row)"
            class="cursor-pointer hover:text-primary"
          >
            {{ scope.row.sort }}
            <Icon
              icon="ep:edit-pen"
              class="ml-5px text-xs text-gray-400"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="100"
      >
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.COMMON_STATUS"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="操作"
        align="center"
        width="250"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:dept:update']"
          >
            <Icon
              icon="ep:edit"
              class="mr-1px"
            />修改
          </el-button>
          <el-button
            link
            type="success"
            @click="openForm('create', undefined, scope.row.id)"
            v-hasPermi="['system:dept:create']"
          >
            <Icon
              icon="ep:plus"
              class="mr-1px"
            />添加子部门
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:dept:delete']"
          >
            <Icon
              icon="ep:delete"
              class="mr-1px"
            />删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeptForm
    ref="formRef"
    @success="getList"
  />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import DeptForm from './DeptForm.vue'
import * as UserApi from '@/api/system/user'
import { useClipboard } from '@vueuse/core'

defineOptions({ name: 'SystemDept' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const copyText = async (text: string) => {
  const { copy, copied, isSupported } = useClipboard({ source: text })
  if (!isSupported) {
    message.error(t('common.copyError'))
    return
  }
  await copy()
  if (unref(copied)) {
    message.success(t('common.copySuccess'))
  }
}
const loading = ref(true) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  name: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const isExpandAll = ref(true) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    list.value = handleTree(data)
  } finally {
    loading.value = false
  }
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.pageNo = 1
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, parentId?: number) => {
  formRef.value.open(type, id, parentId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.confirm('是否确认删除该部门？删除后将无法恢复！')
    // 发起删除
    await DeptApi.deleteDept(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 获取用户列表
  userList.value = await UserApi.getSimpleUserList()
})

/** 点击排序值进入编辑状态 */
const handleSortClick = (row) => {
  // 保存原始值，以便取消时恢复
  row.originalSort = row.sort
  row.editingSort = true
}

/** 排序编辑完成 */
const handleSortBlur = async (row) => {
  // 如果值没有变化，直接退出编辑模式
  if (row.originalSort === row.sort) {
    row.editingSort = false
    return
  }

  try {
    // 调用API更新排序值，确保包含所有必填字段
    await DeptApi.updateDept({
      id: row.id,
      parentId: row.parentId,
      name: row.name,
      sort: row.sort,
      status: row.status
    })
    message.success('排序修改成功')
    row.editingSort = false
    // 刷新列表以保持数据一致性
    await getList()
  } catch (error) {
    // 发生错误时恢复原值
    row.sort = row.originalSort
    row.editingSort = false
    message.error('排序修改失败')
  }
}
</script>
