<template>
  <el-dialog
    v-model="dialogVisible"
    title="字典开发工具"
    width="800px"
    :before-close="handleClose"
  >
    <div class="dict-tools-container">
      <!-- 工具介绍 -->
      <el-card class="intro-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:tools" class="mr-2" />
            <span>字典开发工具</span>
          </div>
        </template>
        
        <div class="intro-content">
          <p class="description">
            这是一个强大的字典类型管理工具，可以自动发现、验证和同步字典类型，大大简化开发流程。
          </p>
          
          <div class="features">
            <div class="feature-item">
              <Icon icon="ep:search" class="feature-icon" />
              <span>自动发现新字典类型</span>
            </div>
            <div class="feature-item">
              <Icon icon="ep:check" class="feature-icon" />
              <span>智能验证字典有效性</span>
            </div>
            <div class="feature-item">
              <Icon icon="ep:document" class="feature-icon" />
              <span>自动生成代码建议</span>
            </div>
            <div class="feature-item">
              <Icon icon="ep:magic-stick" class="feature-icon" />
              <span>智能搜索和类型建议</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 快速操作 -->
      <el-card class="action-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:lightning" class="mr-2" />
            <span>快速操作</span>
          </div>
        </template>
        
        <div class="action-content">
          <div class="action-grid">
            <el-button 
              type="primary" 
              size="large"
              @click="executeCommand('sync')"
              :loading="loading.sync"
            >
              <Icon icon="ep:refresh" class="mr-2" />
              一键同步
            </el-button>
            
            <el-button 
              type="success" 
              size="large"
              @click="executeCommand('discover')"
              :loading="loading.discover"
            >
              <Icon icon="ep:search" class="mr-2" />
              发现类型
            </el-button>
            
            <el-button 
              type="warning" 
              size="large"
              @click="showValidateDialog = true"
            >
              <Icon icon="ep:check" class="mr-2" />
              验证类型
            </el-button>
            
            <el-button 
              type="info" 
              size="large"
              @click="showSearchDialog = true"
            >
              <Icon icon="ep:search" class="mr-2" />
              搜索类型
            </el-button>
          </div>
          
          <div class="console-tip">
            <Icon icon="ep:info" class="tip-icon" />
            <span>请打开浏览器控制台（F12）查看详细输出结果</span>
          </div>
        </div>
      </el-card>
      
      <!-- 使用指南 -->
      <el-card class="guide-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:document" class="mr-2" />
            <span>控制台命令</span>
          </div>
        </template>
        
        <div class="guide-content">
          <div class="commands-list">
            <div class="command-item" v-for="cmd in commands" :key="cmd.name">
              <div class="command-header">
                <code class="command-code">{{ cmd.command }}</code>
                <el-tag :type="cmd.type" size="small">{{ cmd.label }}</el-tag>
              </div>
              <p class="command-desc">{{ cmd.description }}</p>
            </div>
          </div>
          
          <div class="help-tip">
            <el-button type="text" @click="executeCommand('help')">
              <Icon icon="ep:question" class="mr-1" />
              在控制台查看完整帮助
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 验证对话框 -->
    <el-dialog v-model="showValidateDialog" title="验证字典类型" width="500px">
      <el-form @submit.prevent="validateTypes">
        <el-form-item label="字典类型">
          <el-input
            v-model="validateInput"
            type="textarea"
            :rows="4"
            placeholder="请输入要验证的字典类型，每行一个\n例如：\nuser_status\norder_type\npayment_method"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showValidateDialog = false">取消</el-button>
        <el-button type="primary" @click="validateTypes" :loading="loading.validate">
          验证
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 搜索对话框 -->
    <el-dialog v-model="showSearchDialog" title="搜索字典类型" width="400px">
      <el-form @submit.prevent="searchTypes">
        <el-form-item label="关键词">
          <el-input
            v-model="searchInput"
            placeholder="输入搜索关键词，如：status、type 等"
            @keyup.enter="searchTypes"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSearchDialog = false">取消</el-button>
        <el-button type="primary" @click="searchTypes" :loading="loading.search">
          搜索
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'DictToolsDialog' })

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = reactive({
  sync: false,
  discover: false,
  validate: false,
  search: false
})

const showValidateDialog = ref(false)
const showSearchDialog = ref(false)
const validateInput = ref('')
const searchInput = ref('')

// 命令列表
const commands = [
  {
    name: 'sync',
    command: 'dictDevTools.sync()',
    description: '扫描并同步新的字典类型，推荐定期使用',
    type: 'primary',
    label: '推荐'
  },
  {
    name: 'discover',
    command: 'dictDevTools.discover()',
    description: '仅发现新类型，不进行同步操作',
    type: 'success',
    label: '发现'
  },
  {
    name: 'validate',
    command: 'dictDevTools.validate([...])',
    description: '验证指定字典类型的有效性',
    type: 'warning',
    label: '验证'
  },
  {
    name: 'search',
    command: 'dictDevTools.search("关键词")',
    description: '根据关键词搜索匹配的字典类型',
    type: 'info',
    label: '搜索'
  },
  {
    name: 'help',
    command: 'dictDevTools.help()',
    description: '显示完整的使用帮助和指南',
    type: '',
    label: '帮助'
  }
]

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 执行命令
const executeCommand = async (command: string) => {
  if (typeof window === 'undefined' || !(window as any).dictDevTools) {
    ElMessage.error('字典开发工具未加载，请确保在开发环境下运行')
    return
  }
  
  const tools = (window as any).dictDevTools
  loading[command] = true
  
  try {
    switch (command) {
      case 'sync':
        await tools.sync()
        ElMessage.success('同步完成，请查看控制台输出')
        break
      case 'discover':
        await tools.discover()
        ElMessage.success('发现完成，请查看控制台输出')
        break
      case 'help':
        tools.help()
        ElMessage.info('帮助信息已显示在控制台')
        break
      default:
        ElMessage.warning('未知命令')
    }
  } catch (error) {
    console.error('执行命令失败:', error)
    ElMessage.error('执行失败，请查看控制台错误信息')
  } finally {
    loading[command] = false
  }
}

// 验证类型
const validateTypes = async () => {
  if (!validateInput.value.trim()) {
    ElMessage.warning('请输入要验证的字典类型')
    return
  }
  
  if (typeof window === 'undefined' || !(window as any).dictDevTools) {
    ElMessage.error('字典开发工具未加载')
    return
  }
  
  loading.validate = true
  
  try {
    const types = validateInput.value
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
    
    const tools = (window as any).dictDevTools
    tools.validate(types)
    
    ElMessage.success('验证完成，请查看控制台输出')
    showValidateDialog.value = false
    validateInput.value = ''
  } catch (error) {
    console.error('验证失败:', error)
    ElMessage.error('验证失败，请查看控制台错误信息')
  } finally {
    loading.validate = false
  }
}

// 搜索类型
const searchTypes = () => {
  if (!searchInput.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  if (typeof window === 'undefined' || !(window as any).dictDevTools) {
    ElMessage.error('字典开发工具未加载')
    return
  }
  
  loading.search = true
  
  try {
    const tools = (window as any).dictDevTools
    tools.search(searchInput.value.trim())
    
    ElMessage.success('搜索完成，请查看控制台输出')
    showSearchDialog.value = false
    searchInput.value = ''
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请查看控制台错误信息')
  } finally {
    loading.search = false
  }
}
</script>

<style scoped>
.dict-tools-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.intro-content {
  padding: 0;
}

.description {
  margin-bottom: 16px;
  color: #606266;
  line-height: 1.6;
}

.features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.feature-icon {
  color: #409eff;
  font-size: 16px;
}

.action-content {
  padding: 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.console-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  color: #d46b08;
  font-size: 14px;
}

.tip-icon {
  color: #fa8c16;
}

.guide-content {
  padding: 0;
}

.commands-list {
  margin-bottom: 16px;
}

.command-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.command-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.command-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background: #f1f3f4;
  padding: 4px 8px;
  border-radius: 4px;
  color: #e6a23c;
}

.command-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.help-tip {
  text-align: center;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
}
</style>