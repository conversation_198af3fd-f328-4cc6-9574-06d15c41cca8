<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="公告标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入公告标题" />
      </el-form-item>
      <el-form-item label="轮播图配置" prop="imgUrlList">
        <div
          v-for="(item, index) in formData.imgUrlList"
          :key="index"
          class="mb-10px flex w-100% gap-5px items-center"
        >
          <UploadImg height="60px" width="60px" v-model="item.imgUrl" :limit="1" />
          <el-input
            style="height: 30px"
            v-model="item.linkUrl"
            placeholder="请输入跳转链接"
            class="mt-5px"
          />
          <el-button link type="danger" @click="removeCarouselItem(index)" class="ml-10px"
            >删除</el-button
          >
        </div>
        <el-button v-if="formData.imgUrlList.length < 6" @click="addCarouselItem"
          >添加轮播图</el-button
        >
      </el-form-item>
      <el-form-item label="公告类型" prop="type">
        <el-select v-model="formData.type" clearable placeholder="请选择公告类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NOTICE_TYPE)"
            :key="parseInt(dict.value as any)"
            :label="dict.label"
            :value="parseInt(dict.value as any)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="通知类型" prop="noticeType" v-if="formData.type == 1">
        <el-radio-group v-model="formData.noticeType">
          <el-radio :label="1">全部租户</el-radio>
          <el-radio :label="2">指定租户</el-radio>
          <el-radio :label="3">当前团队全部用户</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.noticeType === 2" label="通知租户" prop="noticeTenant">
        <el-select v-model="formData.noticeTenant" multiple filterable placeholder="请选择通知租户">
          <el-option
            v-for="item in tenantList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="重新登录" prop="loginAgain">
        <el-radio-group v-model="formData.loginAgain">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="公告内容" prop="content">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="parseInt(dict.value as any)"
            :label="dict.label"
            :value="parseInt(dict.value as any)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输备注" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as NoticeApi from '@/api/system/notice'
import * as TenantApi from '@/api/system/tenant'
import { UploadImg } from '@/components/UploadFile'
import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

defineOptions({ name: 'SystemNoticeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: '',
  type: undefined,
  content: '',
  status: CommonStatusEnum.ENABLE,
  remark: '',
  noticeTenant: [], // 通知租户列表
  noticeType: 1, // 通知类型：1-全部租户，2-指定租户，3-当前团队全部用户
  loginAgain: false, // 是否重新登录：true-是，false-否
  imgUrlList: [] as { imgUrl: string; linkUrl: string }[] // 轮播图配置
})

const tenantList = ref<TenantApi.TenantVO[]>([]) // 租户列表

// 获取租户列表
const getTenantList = async () => {
  try {
    tenantList.value = await TenantApi.getSimpleTenantList()
  } catch (error) {
    console.error('获取租户列表失败', error)
  }
}
const formRules = reactive({
  title: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  content: [{ required: true, message: '公告内容不能为空', trigger: 'blur' }],
  noticeType: [{ required: true, message: '通知类型不能为空', trigger: 'change' }],
  noticeTenant: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.noticeType === 2 && (!value || value.length === 0)) {
          callback(new Error('指定租户时，通知租户不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  loginAgain: [{ required: true, message: '是否重新登录不能为空', trigger: 'change' }]
})

// 添加轮播图项
const addCarouselItem = () => {
  if (formData.value.imgUrlList.length < 6) {
    formData.value.imgUrlList.push({ imgUrl: '', linkUrl: '' })
  }
}

// 删除轮播图项
const removeCarouselItem = (index: number) => {
  formData.value.imgUrlList.splice(index, 1)
}
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // 加载租户列表
      await getTenantList()
      const data = await NoticeApi.getNotice(id)
      formData.value = data
      formData.value.imgUrlList = data.imgUrl ? JSON.parse(data.imgUrl) : []
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 当公告类型为3时，校验当前用户必须是admin
  if (formData.value.type === 3) {
    const loginForm = localStorage.getItem('loginForm')
    if (loginForm) {
      try {
        const loginData = JSON.parse(loginForm).v
          ? JSON.parse(JSON.parse(loginForm).v)
          : JSON.parse(loginForm)
        console.log('🚀 ~ submitForm ~ loginData:', loginData.v)
        if (loginData.username !== 'admin') {
          message.error('只有admin用户才能创建此类型的公告')
          return
        }
      } catch (error) {
        message.error('获取用户信息失败')
        return
      }
    } else {
      message.error('获取用户信息失败')
      return
    }
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as NoticeApi.NoticeVO
    data.imgUrl = JSON.stringify(data.imgUrlList)
    if (formType.value === 'create') {
      await NoticeApi.createNotice(data)
      message.success(t('common.createSuccess'))
    } else {
      await NoticeApi.updateNotice(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    type: undefined,
    content: '',
    status: CommonStatusEnum.ENABLE,
    remark: '',
    noticeTenant: [],
    noticeType: 1,
    loginAgain: false,
    imgUrlList: []
  }
  // 新增时，加载租户列表
  if (formType.value === 'create') {
    getTenantList()
  }
  formRef.value?.resetFields()
}
</script>
