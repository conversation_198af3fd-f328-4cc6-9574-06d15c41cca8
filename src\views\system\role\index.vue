<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item
        label="角色名称"
        prop="name"
      >
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入角色名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="角色标识"
        prop="code"
      >
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入角色标识"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="状态"
        prop="status"
      >
        <el-select
          v-model="queryParams.status"
          class="!w-240px"
          clearable
          placeholder="请选择状态"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="创建时间"
        prop="createTime"
      >
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon
            class="mr-5px"
            icon="ep:search"
          />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon
            class="mr-5px"
            icon="ep:refresh"
          />
          重置
        </el-button>
        <el-button
          v-hasPermi="['system:role:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon
            class="mr-5px"
            icon="ep:plus"
          />
          新增
        </el-button>
        <el-button
          v-hasPermi="['system:role:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <Icon
            class="mr-5px"
            icon="ep:download"
          />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
    >
      <el-table-column
        align="center"
        label="角色编号"
        prop="id"
      />
      <el-table-column
        align="center"
        label="角色名称"
        prop="name"
      />
      <el-table-column
        label="角色类型"
        align="center"
        prop="type"
      >
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_ROLE_TYPE"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="角色标识"
        prop="code"
      />
      <el-table-column
        align="center"
        label="显示顺序"
        prop="sort"
      >
        <template #default="scope">
          <span
            v-if="scope.row.editingSort"
            class="flex items-center justify-center"
          >
            <el-input-number
              v-model="scope.row.sort"
              :min="0"
              :max="9999"
              size="small"
              controls-position="right"
              @blur="handleSortBlur(scope.row)"
              @keyup.enter="handleSortBlur(scope.row)"
            />
          </span>
          <span
            v-else
            @click="handleSortClick(scope.row)"
            class="cursor-pointer hover:text-primary"
          >
            {{ scope.row.sort }}
            <Icon
              icon="ep:edit-pen"
              class="ml-5px text-xs text-gray-400"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="备注"
        prop="remark"
      />
      <el-table-column
        align="center"
        label="状态"
        prop="status"
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="停用"
            inline-prompt
            @change="(val) => handleStatusChange(scope.row, val)"
            v-hasPermi="['system:role:update']"
          />
        </template>
      </el-table-column>
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="创建时间"
        prop="createTime"
        width="180"
      />
      <el-table-column
        :width="350"
        align="center"
        label="操作"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:role:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            <Icon
              icon="ep:edit"
              class="mr-1px"
            />编辑
          </el-button>
          <el-button
            v-hasPermi="['system:permission:assign-role-menu']"
            link
            type="primary"
            @click="openAssignMenuForm(scope.row)"
          >
            <Icon
              icon="ep:menu"
              class="mr-1px"
            />菜单权限
          </el-button>
          <el-button
            v-hasPermi="['system:permission:assign-role-data-scope']"
            link
            type="primary"
            @click="openDataPermissionForm(scope.row)"
          >
            <Icon
              icon="ep:data-line"
              class="mr-1px"
            />数据权限
          </el-button>
          <el-button
            v-hasPermi="['system:role:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            <Icon
              icon="ep:delete"
              class="mr-1px"
            />删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RoleForm
    ref="formRef"
    @success="getList"
  />
  <!-- 表单弹窗：菜单权限 -->
  <RoleAssignMenuForm
    ref="assignMenuFormRef"
    @success="()=>{getList(),refreshMenu()}"
  />
  <!-- 表单弹窗：数据权限 -->
  <RoleDataPermissionForm
    ref="dataPermissionFormRef"
    @success="()=>{getList(),refreshMenu()}"
  />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as RoleApi from '@/api/system/role'
import RoleForm from './RoleForm.vue'
import RoleAssignMenuForm from './RoleAssignMenuForm.vue'
import RoleDataPermissionForm from './RoleDataPermissionForm.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

defineOptions({ name: 'SystemRole' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RoleApi.getRolePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 刷新菜单缓存按钮操作 */
const refreshMenu = async () => {
  try {
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.USER)
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
    // 刷新浏览器
    location.reload()
  } catch {}
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 数据权限操作 */
const dataPermissionFormRef = ref()
const openDataPermissionForm = async (row: RoleApi.RoleVO) => {
  dataPermissionFormRef.value.open(row)
}

/** 菜单权限操作 */
const assignMenuFormRef = ref()
const openAssignMenuForm = async (row: RoleApi.RoleVO) => {
  assignMenuFormRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.confirm('是否确认删除该角色？删除后将无法恢复，且可能影响使用该角色的用户！')
    // 发起删除
    await RoleApi.deleteRole(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RoleApi.exportRole(queryParams)
    download.excel(data, '角色列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

/** 点击排序值进入编辑状态 */
const handleSortClick = (row) => {
  // 保存原始值，以便取消时恢复
  row.originalSort = row.sort
  row.editingSort = true
}

/** 排序编辑完成 */
const handleSortBlur = async (row) => {
  // 如果值没有变化，直接退出编辑模式
  if (row.originalSort === row.sort) {
    row.editingSort = false
    return
  }

  try {
    // 调用API更新排序值，确保包含所有必填字段
    await RoleApi.updateRole({
      id: row.id,
      name: row.name,
      code: row.code,
      sort: row.sort,
      status: row.status
    })
    message.success('排序修改成功')
    row.editingSort = false
    // 刷新列表以保持数据一致性
    await getList()
  } catch (error) {
    // 发生错误时恢复原值
    row.sort = row.originalSort
    row.editingSort = false
    message.error('排序修改失败')
  }
}

/** 修改角色状态 */
const handleStatusChange = async (row, newStatus) => {
  const oldStatus = newStatus === 0 ? 1 : 0 // 保存原始状态值
  try {
    const text = newStatus === 0 ? '启用' : '停用'
    await message.confirm(
      `确认要${text}"${row.name}"角色吗？${
        newStatus === 1 ? '停用后使用该角色的用户将无法登录' : ''
      }`
    )
    await RoleApi.updateRole({
      id: row.id,
      name: row.name,
      code: row.code,
      sort: row.sort,
      status: newStatus
    })
    message.success(`${text}成功`)
    await getList()
  } catch {
    // 用户取消操作，恢复UI状态
    row.status = oldStatus
  }
}
</script>
