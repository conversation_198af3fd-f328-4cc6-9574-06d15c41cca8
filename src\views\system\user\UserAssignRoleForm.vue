<template>
  <Dialog
    v-model="dialogVisible"
    title="分配角色"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="80px"
    >
      <template v-if="formType != 'ids'">
        <el-form-item label="账户名称">
          <el-input
            v-model="formData.username"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input
            v-model="formData.nickname"
            :disabled="true"
          />
        </el-form-item>
      </template>
      <template v-if="formType == 'ids'">
        <el-form-item label="账户名称">
          {{formData.usernames}}
        </el-form-item>
        <el-form-item label="用户姓名">
          {{formData.nicknames}}
        </el-form-item>
      </template>
      <el-form-item label="角色">
        <el-select
          v-model="formData.roleIds"
          multiple
          placeholder="请选择角色"
        >
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        :disabled="formLoading"
        type="primary"
        @click="submitForm"
      >确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as PermissionApi from '@/api/system/permission'
import * as UserApi from '@/api/system/user'
import * as RoleApi from '@/api/system/role'

defineOptions({ name: 'SystemUserAssignRoleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: -1,
  nickname: '',
  nicknames: '',
  username: '',
  usernames: '',
  roleIds: [],
  userIds: []
})
const formType = ref('') // 表单的类型
const formRef = ref() // 表单 Ref
const roleList = ref([] as RoleApi.RoleVO[]) // 角色的列表

/** 打开弹窗 */
const open = async (row: UserApi.UserVO, type: string) => {
  dialogVisible.value = true
  resetForm()
  formType.value = type
  // 获得角色拥有的菜单集合
  formLoading.value = true
  try {
    if (!type) {
      // 设置数据
      formData.value.id = row.id
      formData.value.username = row.username
      formData.value.nickname = row.nickname
      formData.value.roleIds = await PermissionApi.getUserRoleList(row.id)
    } else {
      formData.value.userIds = row.userIds
      formData.value.usernames = row.usernames
      formData.value.nicknames = row.nicknames
    }
  } finally {
    formLoading.value = false
  }
  // 获得角色列表
  roleList.value = await RoleApi.getSimpleRoleList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    if (!formType.value) {
      await PermissionApi.assignUserRole({
        userId: formData.value.id,
        roleIds: formData.value.roleIds
      })
    } else {
      await PermissionApi.batchassignUserRole({
        userIds: formData.value.userIds,
        roleIds: formData.value.roleIds
      })
    }
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: -1,
    nickname: '',
    username: '',
    roleIds: []
  }
  formRef.value?.resetFields()
}
</script>
