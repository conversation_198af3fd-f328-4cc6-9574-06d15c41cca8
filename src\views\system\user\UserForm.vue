<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="nickname">
            <el-input v-model="formData.nickname" placeholder="请输入用户姓名，例如：张三" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归属部门" prop="deptId">
            <el-tree-select
              v-model="formData.deptId"
              :data="deptList"
              :props="defaultProps"
              check-strictly
              node-key="id"
              placeholder="请选择归属部门"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="formData.mobile" maxlength="11" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" maxlength="50" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="formData.id === undefined" label="账户名称" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入账户名称，例如：zhangsan123"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="formData.id === undefined" label="账户密码" prop="password">
            <el-input
              v-model="formData.password"
              placeholder="请输入账户密码"
              show-password
              type="password"
              autocomplete="new-password"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户性别">
            <el-select v-model="formData.sex" placeholder="请选择">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位">
            <el-select v-model="formData.postIds" multiple placeholder="请选择">
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id!"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="角色">
            <el-select v-model="formData.roleIds" multiple placeholder="请选择角色">
              <el-option
                v-for="item in roleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="formData.remark" placeholder="请输入内容" type="textarea" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 状态字段优化 -->
      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="0"
          :inactive-value="1"
          active-text="启用"
          inactive-text="停用"
          inline-prompt
        />
        <div class="text-gray-400 text-xs mt-1"> 用户状态，停用后该用户将无法登录系统 </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 创建成功后显示账户信息弹窗 -->
  <el-dialog
    v-model="successDialogVisible"
    title=""
    width="500px"
    :before-close="handleSuccessDialogClose"
    center
  >
    <div class="success-dialog-content">
      <div class="success-icon">
        <el-icon class="success-icon-inner">
          <Check />
        </el-icon>
      </div>
      <div class="success-title">用户创建成功</div>
      <div class="welcome-text">
        已将您添加至「{{ tenantName }}」团队，请使用以下信息进行登录。
      </div>

      <div class="user-info-container">
        <div class="user-info-item">
          <span class="label">团队名：</span>
          <span class="value">{{ tenantName }}</span>
        </div>
        <div class="user-info-item">
          <span class="label">用户名：</span>
          <span class="value">{{ createdUserInfo.username }}</span>
        </div>
        <div class="user-info-item">
          <span class="label">登录密码：</span>
          <span class="value">{{ createdUserInfo.password }}</span>
        </div>
        <div class="user-info-item">
          <span class="label">系统链接：</span>
          <span class="value link-text">https://www.40zhiyuan.com</span>
        </div>
      </div>

      <div class="copy-button-container">
        <el-button type="primary" @click="copyAllInfo" class="copy-all-btn">
          <el-icon class="mr-5px">
            <DocumentCopy />
          </el-icon>
          复制全部信息
        </el-button>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleSuccessDialogClose">知道了</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import * as DeptApi from '@/api/system/dept'
import * as PermissionApi from '@/api/system/permission'
import * as PostApi from '@/api/system/post'
import * as RoleApi from '@/api/system/role'
import * as UserApi from '@/api/system/user'
import { copyText } from '@/utils/clipboard'
import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import { Check, DocumentCopy } from '@element-plus/icons-vue'
import { FormRules } from 'element-plus'

defineOptions({ name: 'SystemUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  nickname: '',
  deptId: '',
  mobile: '',
  email: '',
  id: undefined,
  username: '',
  password: '',
  sex: undefined,
  postIds: [],
  remark: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: []
})
const formRules = reactive<FormRules>({
  username: [
    { required: true, message: '账户名称不能为空由 数字、字母 组成且至少4位', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '用户姓名不能为空用且长度不能超过30个字符', trigger: 'blur' }
  ],
  password: [{ required: true, message: '账户密码不能为空且至少4位', trigger: 'blur' }],
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    }
  ],
  mobile: [
    {
      pattern: /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
})
const formRef = ref() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([] as PostApi.PostVO[]) // 岗位列表
const roleList = ref([] as RoleApi.RoleVO[]) // 角色列表

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
      formData.value.roleIds = await PermissionApi.getUserRoleList(id)
    } finally {
      formLoading.value = false
    }
  }
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  // 加载岗位列表
  postList.value = await PostApi.getSimplePostList()
  roleList.value = await RoleApi.getSimpleRoleList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 创建成功后显示的用户信息
const successDialogVisible = ref(false)
const createdUserInfo = ref({
  username: '',
  password: '',
  teamName: ''
})
// 获取当前账号的租户信息
const loginForm = JSON.parse(localStorage.getItem('loginForm') || '{}') || ''
const tenantName = loginForm ? JSON.parse(loginForm.v).tenantName || '' : ''
// 复制全部信息
const copyAllInfo = () => {
  const copyContent = `已将您添加至「${createdUserInfo.value.teamName}」团队，请使用以下信息进行登录。

团队名：${createdUserInfo.value.teamName}
用户名：${createdUserInfo.value.username}
登录密码：${createdUserInfo.value.password}
系统链接：https://www.40zhiyuan.com`

  copyText(
    copyContent,
    () => message.success('用户信息已复制到剪贴板'),
    (err) => message.error('复制失败: ' + err.message)
  )
}

// 关闭成功提示弹窗
const handleSuccessDialogClose = () => {
  successDialogVisible.value = false
  createdUserInfo.value = {
    username: '',
    password: '',
    teamName: ''
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserApi.UserVO
    if (formType.value === 'create') {
      await UserApi.createUser(data)
      message.success(t('common.createSuccess'))

      // 获取部门名称作为团队名
      const findDeptInTree = (depts, targetId) => {
        for (const dept of depts) {
          if (dept.id == targetId) {
            return dept
          }
          if (dept.children && dept.children.length > 0) {
            const found = findDeptInTree(dept.children, targetId)
            if (found) {
              return found
            }
          }
        }
        return null
      }

      const selectedDept = findDeptInTree(deptList.value, data.deptId)
      const teamName = selectedDept ? selectedDept.name : '暂未分配部门'

      // 保存创建的用户信息并显示成功弹窗
      createdUserInfo.value = {
        username: data.username,
        password: data.password,
        teamName: teamName
      }
      dialogVisible.value = false
      successDialogVisible.value = true
    } else {
      await UserApi.updateUser(data)
      message.success(t('common.updateSuccess'))
      dialogVisible.value = false
    }

    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    nickname: '',
    deptId: '',
    mobile: '',
    email: '',
    id: undefined,
    username: '',
    password: '',
    sex: undefined,
    postIds: [],
    remark: '',
    status: CommonStatusEnum.ENABLE,
    roleIds: []
  }
}
</script>

<style scoped>
.success-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.success-icon {
  display: flex;
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
  background-color: #67c23a;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
}

.success-icon-inner {
  font-size: 30px;
  color: white;
}

.success-title {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.welcome-text {
  padding: 0 20px;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  text-align: center;
}

.user-info-container {
  width: 100%;
  padding: 0 20px;
  margin-bottom: 20px;
}

.user-info-item {
  display: flex;
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.label {
  width: 80px;
  margin-right: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
  flex: 1;
}

.link-text {
  color: #409eff;
  text-decoration: underline;
}

.copy-button-container {
  display: flex;
  width: 100%;
  padding: 0 20px;
  justify-content: center;
}

.copy-all-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
}

.mr-5px {
  margin-right: 5px;
}
</style>
