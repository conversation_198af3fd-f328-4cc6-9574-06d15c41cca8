<template>
  <el-dialog
    title="第三方账号管理"
    v-model="dialogVisible"
    width="70%"
    class="copywriting_drawer"
  >
    <ContentWrap>
      <el-row
        :gutter="20"
        class="mb-20px"
      >
        <el-col :span="5">
          <el-select
            v-model="selectedPlatform"
            placeholder="请选择平台"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <!-- 登录网址选择框（原来是第三个位置的输入框，现在改为第二个位置的选择框） -->
        <el-col :span="5">
          <el-select
            v-model="bindForm.appId"
            placeholder="请选择平台登录网址"
            clearable
            filterable
            style="width: 100%"
            @change="handleLoginUrlChange"
          >
            <el-option
              v-for="item in loginUrlOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select
            v-model="selectedAccount"
            placeholder="请选择平台账号"
            clearable
            filterable
            :disabled="!selectedPlatform"
            style="width: 100%"
          >
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col
          :span="9"
          class="flex items-center"
          style="display: flex !important;"
        >
          <el-button
            v-hasPermi="['third:party-account:bind']"
            type="primary"
            @click="handleBind"
          >
            <Icon icon="ep:link" />绑定
          </el-button>
          <el-button
            type="danger"
            v-hasPermi="['third:party-account:unbind']"
            :disabled="!multipleSelection.length"
            @click="handleBatchUnbind"
            class="ml-10px"
          >
            <Icon icon="ep:delete" />批量解绑
          </el-button>
          <el-button
            :disabled="!selectedPlatform"
            @click="handleSync"
            v-hasPermi="['third:party-account:sync']"
            class="ml-10px pos-absolute right-24px"
            size="small"
          >
            <Icon icon="ep:refresh" />同步第三方数据
          </el-button>
        </el-col>
      </el-row>
    </ContentWrap>

    <!-- 账号列表 -->
    <ContentWrap>
      <el-table
        border
        v-loading="loading"
        :data="list"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          prop="platformName"
          label="平台"
          width="150"
        />
        <el-table-column
          prop="username"
          label="用户名"
        />
        <el-table-column
          prop="thirdpartyAccountName"
          label="账号"
        />
        <el-table-column
          prop="loginUrl"
          label="登录网址"
        >
          <template #default="{ row }">
            <el-input
              v-if="row.editing"
              v-model="row.tempLoginUrl"
              @change="handleUrlChange(row)"
            />
            <el-input
              v-if="!row.editing"
              v-model="row.loginUrl"
              :readonly="true"
              @click="row.editing = true"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-hasPermi="['third:party-account:bind-password']"
              link
              type="primary"
              @click="handleBindPsw(row)"
            >
              绑定平台密码
            </el-button>
            <el-button
              v-hasPermi="['third:party-account:unbind']"
              link
              type="danger"
              @click="handleUnbind(row)"
            >
              解绑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 密码绑定对话框 -->
    <el-dialog
      title="密码绑定"
      v-model="dialogVisiblePsw"
      width="500px"
    >
      <el-form
        ref="pswFormRef"
        :model="bindPswForm"
        label-width="80px"
        :rules="pswRules"
      >
        <el-form-item
          label="密码"
          prop="password"
        >
          <el-input
            v-model="bindPswForm.password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisiblePsw = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleBindPassword"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div style="text-align: center;">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
import { ThirdPartyAccountApi } from '@/api/system/third'

const { copy } = useClipboard() // 初始化 copy 到粘贴板

defineOptions({ name: 'SystemDictTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 主弹窗展示
const dialogVisiblePsw = ref(false) // 密码弹窗展示
const bindPswForm = ref({
  id: 0,
  password: ''
})
const pswRules = {
  password: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}
const bindForm = ref({
  id: 0,
  userId: 0,
  appId: ''
})
const formRef = ref() // 表单 Ref
const loading = ref(true)
const total = ref(0)
const list = ref<any[]>([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  platform: '',
  account: '',
  userId: undefined
})

// 平台和账号选择
const selectedPlatform = ref('LX')
const selectedAccount = ref('')
const platformOptions = [{ value: 'LX', label: '领星' }]
const accountOptions = ref([])

// 登录网址选择相关
const loginUrlOptions = ref([])

// 获取历史登录网址
const getHistoryLoginUrls = async () => {
  try {
    // 这里可以替换为实际的API调用，获取历史登录网址
    const res = await ThirdPartyAccountApi.getHistoryLoginUrls()
    loginUrlOptions.value =
      res.map((item) => ({
        value: item.id,
        label: item.platformLoginUrl + `(${item.tenantName})`
      })) || []
  } catch (error) {
    console.error('获取历史登录网址失败', error)
  }
}

// 监听平台选择变化
watch(selectedPlatform, (val) => {
  selectedAccount.value = ''
  getHistoryLoginUrls() // 获取该平台的历史登录网址
})

// 多选
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  bindForm.value.userId = row.id
  bindForm.value.appId = ''
  queryParams.userId = row.id
  selectedAccount.value = ''
  getList()
  getHistoryLoginUrls() // 获取历史登录网址
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    // 更新查询参数
    queryParams.platform = selectedPlatform.value
    queryParams.account = selectedAccount.value

    const res = await ThirdPartyAccountApi.getAccountList(queryParams)
    list.value = res.list.map((item: any) => ({
      ...item,
      editing: false,
      tempLoginUrl: item.loginUrl
    }))
    total.value = res.total || 0
  } catch (error) {
    console.error('获取第三方账号列表失败', error)
  } finally {
    loading.value = false
  }
}

// 获取平台下的账号选项
const getAccountOptions = async (data?: any) => {
  try {
    const res = await ThirdPartyAccountApi.getAccountOptions(data)
    accountOptions.value = res.map((item) => ({
      value: item.id,
      label: item.username + `(${item.realname})`
    }))
  } catch (error) {
    console.error('获取账号选项失败', error)
    accountOptions.value = []
  }
}

// 绑定账号
const handleBind = async () => {
  if (!selectedPlatform.value) {
    message.warning('请先选择平台')
    return
  }
  if (!selectedAccount.value) {
    message.warning('请先选择账号')
    return
  }
  if (!bindForm.value.appId) {
    message.warning('请输入登录网址')
    return
  }
  await ThirdPartyAccountApi.bindAccount(bindForm.value)
  message.success('账号绑定成功')
  getList()
}

// 同步第三方数据
const handleSync = async () => {
  if (!selectedPlatform.value) {
    message.warning('请选择平台')
    return
  }

  try {
    loading.value = true
    await ThirdPartyAccountApi.syncPlatformData(selectedPlatform.value)
    message.success('数据同步成功')
    getList()
  } catch (error) {
    console.error('同步失败', error)
  } finally {
    loading.value = false
  }
}

// 打开密码绑定弹窗
const handleBindPsw = (row) => {
  bindPswForm.value.id = row.id
  dialogVisiblePsw.value = true
}
const pswFormRef = ref(null)
// 提交密码绑定
const handleBindPassword = async () => {
  try {
    await pswFormRef.value.validate()
    await ThirdPartyAccountApi.bindPassword(bindPswForm.value)
    message.success('密码绑定成功')
    dialogVisiblePsw.value = false
    getList()
  } catch (error) {
    console.error('密码绑定失败', error)
  }
}

// 单个解绑
const handleUnbind = async (row) => {
  let ids = [row.id]
  try {
    await message.confirm(`确定要解绑账号 ${row.username} 吗？`, '提示')
    await ThirdPartyAccountApi.unbindAccount(ids)
    message.success('解绑成功')
    getList()
  } catch (error) {
    console.error(error)
    // 用户取消不处理
  }
}

// 批量解绑
const handleBatchUnbind = async () => {
  if (!multipleSelection.value.length) {
    message.warning('请至少选择一条记录')
    return
  }
  try {
    await message.confirm(`确定要解绑选中的 ${multipleSelection.value.length} 个账号吗？`, '提示')
    const ids = multipleSelection.value.map((item) => item.id)
    await ThirdPartyAccountApi.batchUnbindAccount(ids)
    message.success(`成功解绑 ${multipleSelection.value.length} 个账号`)
    getList()
  } catch (error) {
    console.error(error)
    // 用户取消不处理
  }
}

// url变化
const handleLoginUrlChange = (val) => {
  getAccountOptions({ platformCode: selectedPlatform.value, appId: val })
}

// 监听平台选择变化
watch(selectedPlatform, (val) => {
  selectedAccount.value = ''
  getAccountOptions({ platformCode: selectedPlatform.value, appId: '' })
})

// 监听账号选择变化
watch(selectedAccount, (val) => {
  bindForm.value.id = val
})

// 新增编辑状态管理
const editingStates = ref<Record<number, { original: string; temp: string }>>({})

// 处理网址修改
const handleUrlChange = (row: any) => {
  editingStates.value[row.id] = {
    original: row.loginUrl,
    temp: row.tempLoginUrl
  }
  message
    .confirm('确定要修改登录网址吗？', '提示')
    .then(async () => {
      try {
        await ThirdPartyAccountApi.editLoginUrl({
          id: row.id,
          loginUrl: row.tempLoginUrl
        })
        row.loginUrl = row.tempLoginUrl
        row.editing = false
        message.success('修改成功')
      } catch {
        row.tempLoginUrl = editingStates.value[row.id].original
      } finally {
        delete editingStates.value[row.id]
      }
    })
    .catch(() => {
      row.tempLoginUrl = editingStates.value[row.id].original
      row.editing = false
    })
}

// 新增接口方法
const editLoginUrl = async (data: any) => {
  return await ThirdPartyAccountApi.editLoginUrl(data)
}
</script>

<style lang="scss" scoped>
</style>
