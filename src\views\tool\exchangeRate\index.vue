<template>
  <ContentWrap>
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>汇率设置</h1>
      <p class="page-desc">管理各币种汇率设置，支持自定义汇率和历史汇率查询</p>
    </div>
  </ContentWrap>

  <!-- 搜索区域 -->
  <ContentWrap>
    <el-form :inline="true" :model="queryParams" class="-mb-15px" label-width="80px">
      <el-form-item>
        <div class="date-search-container">
          <el-radio-group v-model="queryParams.dateType" size="small" class="date-type-selector">
            <el-radio-button value="single">单月</el-radio-button>
            <el-radio-button value="range">区间</el-radio-button>
          </el-radio-group>
          <el-date-picker
            v-if="queryParams.dateType === 'single'"
            v-model="queryParams.singleMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY年MM月"
            value-format="YYYY-MM"
            style="width: 200px; margin-left: 12px"
          />
          <!-- @change="handleDateChange" -->
          <el-date-picker
            v-else
            v-model="queryParams.dateRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="YYYY年MM月"
            value-format="YYYY-MM"
            style="width: 280px; margin-left: 12px"
            :disabled-date="disabledDate"
          />
          <!-- @change="handleDateChange" -->
        </div>
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="queryParams.currencies"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          filterable
          placeholder="请选择币种"
          style="width: 300px"
        >
          <!-- @change="handleCurrencyChange" -->
          <el-option
            v-for="currency in currencyOptions"
            :key="currency.code"
            :label="`${currency.name} (${currency.code})`"
            :value="currency.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch" v-hasPermi="['infra:rate:query']">
          <Icon icon="ep:search" class="mr-5px" />
          查询
        </el-button>
        <el-button @click="handleReset">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 汇率表格 -->
  <ContentWrap title="汇率管理">
    <template #header>
      <div class="section-header" v-hasPermi="['infra:rate:update']">
        <div class="header-actions" v-loading="refreshing">
          <el-tooltip content="只刷新当前月份官方汇率数据" placement="top">
            <el-button type="primary" circle @click="refreshRates">
              <Icon icon="ep:refresh" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <Table
      v-loading="loading"
      :columns="columns"
      :data="exchangeRateData"
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @update:page-size="handlePageSizeChange"
      @update:current-page="handlePageChange"
      border
      stripe
      class="exchange-rate-table"
      row-key="id"
      :showColumnConfig="false"
    >
      <template #currencyInfo="{ row }">
        <div class="currency-cell">
          <div class="currency-name">{{ row.name }}</div>
          <div class="currency-code">{{ row.code }} | {{ row.icon }}</div>
        </div>
      </template>

      <template #rateOrg="{ row }">
        <span class="rate-value">{{ formatRate(row.rateOrg) }}</span>
      </template>

      <template #rateSelf="{ row }">
        <div
          class="my-rate-cell"
          @mouseenter="showEditButton(row.id)"
          @mouseleave="hideEditButton(row.id)"
        >
          <span class="rate-value">{{ formatRate(row.rateSelf) }}</span>
          <el-popover
            :visible="editingRow === row.id"
            placement="top"
            :width="280"
            :show-after="100"
            :hide-after="50"
          >
            <div class="rate-edit-popup" v-hasPermi="['infra:rate:update']">
              <div class="popup-title">编辑汇率 - {{ row.name }}</div>
              <div class="popup-content">
                <el-input-number
                  v-model="editingRate"
                  :min="0"
                  :step="0.0001"
                  controls-position="right"
                  size="small"
                  style="width: 100%"
                />
              </div>
              <div class="popup-actions">
                <el-button size="small" @click="cancelEdit(row)">取消</el-button>
                <el-button type="primary" size="small" @click="saveEdit(row)" :loading="saving"
                  >保存</el-button
                >
              </div>
            </div>
            <template #reference>
              <el-button
                v-hasPermi="['infra:rate:update']"
                v-show="hoveredRow === row.id"
                type="primary"
                text
                size="small"
                @click="startEdit(row)"
                class="edit-btn"
              >
                <Icon icon="ep:edit" />
              </el-button>
            </template>
          </el-popover>
        </div>
      </template>

      <template #date="{ row }">
        <span class="effective-month">{{ row.date }}</span>
      </template>

      <template #createTime="{ row }">
        <span class="update-time">{{ formatDateTime(row.createTime) }}</span>
      </template>
    </Table>
  </ContentWrap>
</template>

<script lang="ts" setup>
import {
  getRateCode,
  getRatePage,
  refreshRate,
  updateRate,
  type RateCodeVO,
  type RatePageReqVO,
  type RateRespVO,
  type RateSaveReqVO
} from '@/api/infra/rate'
import type { TableColumn } from '@/types/table'

defineOptions({ name: 'ExchangeRate' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const saving = ref(false)
const hoveredRow = ref(null)
const editingRow = ref(null)
const editingRate = ref(0)

// 分页数据
const total = ref(0)

// 搜索表单
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dateType: 'single',
  singleMonth: new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0'),
  dateRange: null,
  currencies: []
})

// 币种选项
const currencyOptions = ref<RateCodeVO[]>([])

// 汇率数据
const exchangeRateData = ref<RateRespVO[]>([])

// 表格列配置
const columns: TableColumn[] = [
  {
    label: '原币种',
    field: 'currencyInfo',
    width: 240,
    align: 'center'
  },
  {
    label: '官方汇率',
    field: 'rateOrg',
    width: 180,
    align: 'center',
    tips: '1.汇率取自于领星的【汇率管理】【官方汇率】，每个月拉取一次。每次拉取会将【官方汇率】覆盖到【我的汇率】</br>2.如需查看最新的官方汇率，请点击右上角刷新按钮',
    icon: 'ep:question-filled'
  },
  {
    label: '我的汇率',
    field: 'rateSelf',
    width: 240,
    align: 'center',
    tips: '系统按照【我的汇率】进行币种换算，非月报数据取最新月份的汇率。注：月初将拉取最新的【官方汇率】覆盖到【我的汇率】',
    icon: 'ep:question-filled'
  },
  {
    label: '生效月份',
    field: 'date',
    width: 220,
    align: 'center',
    sortable: true
  },
  {
    label: '最后更新',
    field: 'createTime',
    minWidth: 250,
    align: 'center'
  }
]

// 日期选择限制（最长12个月）
const disabledDate = (time) => {
  if (!queryParams.dateRange || !queryParams.dateRange[0]) return false

  const startDate = new Date(queryParams.dateRange[0])
  const currentDate = new Date(time)

  // 计算月份差
  const monthDiff =
    (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
    (currentDate.getMonth() - startDate.getMonth())

  return Math.abs(monthDiff) > 12
}

// 构建查询参数
const buildQueryParams = (): RatePageReqVO => {
  const params: RatePageReqVO = {
    pageNo: queryParams.pageNo,
    pageSize: queryParams.pageSize
  }

  // 处理日期参数
  if (queryParams.dateType === 'single' && queryParams.singleMonth) {
    params.date = [queryParams.singleMonth]
  } else if (queryParams.dateType === 'range' && queryParams.dateRange) {
    params.date = queryParams.dateRange
  }

  // 处理币种参数
  if (queryParams.currencies.length > 0) {
    params.codes = queryParams.currencies
  }

  return params
}

// 获取当前选择的月份
const getCurrentMonths = () => {
  if (queryParams.dateType === 'single') {
    return queryParams.singleMonth ? [queryParams.singleMonth] : []
  } else {
    return queryParams.dateRange || []
  }
}

// 格式化汇率
const formatRate = (rate) => {
  return rate ? Number(rate).toFixed(4) : '0.0000'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 分页显示数量变化
const handlePageSizeChange = (pageSize: number) => {
  queryParams.pageSize = pageSize
  loadExchangeRates()
}

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.pageNo = page
  loadExchangeRates()
}

// 显示编辑按钮
const showEditButton = (id) => {
  hoveredRow.value = id
}

// 隐藏编辑按钮
const hideEditButton = (id) => {
  if (!editingRow.value) hoveredRow.value = null
}

// 处理日期变化
const handleDateChange = (value) => {
  queryParams.pageNo = 1
  loadExchangeRates()
}

// 处理币种变化
const handleCurrencyChange = (value) => {
  queryParams.pageNo = 1
  loadExchangeRates()
}

// 搜索
const handleSearch = () => {
  queryParams.pageNo = 1
  loadExchangeRates()
}

// 重置搜索
const handleReset = () => {
  queryParams.pageNo = 1
  // queryParams.dateType = 'single'
  queryParams.singleMonth =
    new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0')
  queryParams.dateRange = null
  queryParams.currencies = []
  loadExchangeRates()
}

// 刷新汇率
const refreshRates = async () => {
  refreshing.value = true
  try {
    await refreshRate()
    await loadExchangeRates()
    message.success('汇率数据已刷新')
  } catch (error) {
    console.error('刷新汇率失败:', error)
    message.error('刷新失败：' + (error.message || '未知错误'))
  } finally {
    refreshing.value = false
  }
}

// 加载汇率数据
const loadExchangeRates = async () => {
  loading.value = true
  try {
    const params = buildQueryParams()
    const response = await getRatePage(params)

    exchangeRateData.value = response.list || []
    total.value = response.total || 0
  } catch (error) {
    console.error('加载汇率数据失败:', error)
    message.error('加载数据失败：' + (error.message || '未知错误'))
    exchangeRateData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 开始编辑汇率
const startEdit = (row: RateRespVO) => {
  editingRow.value = row.id
  editingRate.value = parseFloat(row.rateSelf || '0')
}

// 取消编辑
const cancelEdit = (row?: any) => {
  editingRow.value = null
  editingRate.value = 0
  setTimeout(() => {
    hideEditButton(row.id)
  }, 300)
}

// 保存编辑
const saveEdit = async (row: RateRespVO) => {
  if (editingRate.value <= 0) {
    message.error('汇率必须大于0')
    return
  }

  saving.value = true

  try {
    const updateData: RateSaveReqVO = {
      id: row.id,
      date: row.date,
      code: row.code,
      icon: row.icon,
      name: row.name,
      rateSelf: editingRate.value.toString(),
      rateOrg: row.rateOrg
    }

    await updateRate(updateData)
    message.success('汇率保存成功')
    cancelEdit()
    setTimeout(() => {
      hideEditButton(row.id)
    }, 300)
    // 重新加载数据
    await loadExchangeRates()
  } catch (error) {
    console.error('保存汇率失败:', error)
    message.error('保存失败：' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

// 加载币种选项
const loadCurrencyOptions = async () => {
  try {
    const response = await getRateCode()
    currencyOptions.value = response || []
  } catch (error) {
    console.error('加载币种选项失败:', error)
    message.error('加载币种选项失败：' + (error.message || '未知错误'))
  }
}

// 初始化
onMounted(async () => {
  await loadCurrencyOptions()
  await loadExchangeRates()
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;

  h1 {
    margin: 0 0 8px;
    font-size: 24px;
    color: #333;
  }

  .page-desc {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

.date-search-container {
  display: flex;
  align-items: center;

  .date-type-selector {
    flex-shrink: 0;
  }
}

.section-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.exchange-rate-table {
  .currency-cell {
    text-align: center;

    .currency-name {
      margin-bottom: 4px;
      font-weight: 500;
      color: #333;
    }

    .currency-code {
      font-size: 12px;
      color: #666;
    }
  }

  .rate-value {
    font-family: monospace;
    font-weight: 500;
  }

  .my-rate-cell {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .edit-btn {
      margin-left: 8px;
    }
  }

  .effective-month {
    font-family: monospace;
  }

  .update-time {
    font-size: 12px;
    color: #666;
  }
}

.rate-edit-popup {
  .popup-title {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-align: center;
  }

  .popup-content {
    margin-bottom: 12px;
  }

  .popup-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
