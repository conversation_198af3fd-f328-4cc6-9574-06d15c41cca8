<template>
  <div class="overhead-allocation-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>管理费用分摊中心</h1>
      <p class="page-desc"
        >将线下管理费用数据化，建立透明的分摊规则，实现从业务利润到财务净利润的精准核算</p
      >
    </div>

    <!-- 第一部分: 每月费用池管理 -->
    <ContentWrap class="cost-pool-section" title="每月费用池管理">
      <template #header>
        <div class="section-header">
          <div class="header-actions">
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="选择月份"
              format="YYYY年MM月"
              value-format="YYYY-MM"
              @change="handleMonthChange"
              style="width: 150px; margin-right: 12px"
            />
            <!-- <el-button type="success" @click="showCostTypeModal = true">
              <Icon icon="ep:setting" class="mr-5px" />
              自定义费用类型
            </el-button> -->
            <el-button type="primary" @click="showAddCostModal = true">
              <Icon icon="ep:plus" class="mr-5px" />
              添加费用项
            </el-button>
            <!-- <el-button @click="showImportModal = true">
              <Icon icon="ep:download" class="mr-5px" />
              从Excel导入
            </el-button>
            <el-button type="info" @click="showCopyModal = true">
              <Icon icon="ep:copy-document" class="mr-5px" />
              将该月费用复制到
            </el-button> -->
          </div>
        </div>
      </template>

      <!-- 费用项列表表格 -->
      <Table
        v-loading="loading"
        :columns="columns"
        :data="costItems"
        :pagination="false"
        :showColumnConfig="false"
        border
        stripe
        class="cost-table"
      >
        <template #costType="{ row }">
          <el-tag :type="getCostTypeColor(getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, row.type))">
            {{ getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, row.type) || '未知类型' }}
          </el-tag>
        </template>

        <template #amount="{ row }">
          <span class="amount-text">{{ formatNumber(row.amount) }}</span>
        </template>

        <template #rule="{ row }">
          <span>{{ getAllocationRuleText(row.rule) }}</span>
        </template>

        <template #isMonthly="{ row }">
          <el-tag :type="row.isMonthly ? 'success' : 'info'" size="small">
            {{ row.isMonthly ? '是' : '否' }}
          </el-tag>
        </template>

        <template #action="{ row, $index }">
          <el-button link type="primary" @click="editCostItem(row, $index)">编辑</el-button>
          <el-button link type="danger" @click="deleteCostItem(row, $index)">删除</el-button>
        </template>
      </Table>

      <!-- 费用总计 -->
      <div class="cost-summary">
        <div class="summary-item">
          <span class="label">本月管理费用总计:</span>
          <span class="total-amount">{{ formatNumber(totalCost) }}</span>
        </div>
      </div>
    </ContentWrap>

    <!-- 添加/编辑费用项弹窗 -->
    <el-dialog
      v-model="showAddCostModal"
      :title="editingIndex !== -1 ? '编辑费用项' : '添加费用项'"
      width="600px"
      @close="resetCostForm"
    >
      <el-form ref="costFormRef" :model="costForm" :rules="costFormRules" label-width="120px">
        <el-form-item label="费用名称" prop="name">
          <el-input v-model="costForm.name" placeholder="如：办公室租金、5月美工设计费" />
        </el-form-item>

        <el-form-item label="费用类型" prop="type">
          <el-select
            placeholder="请选择费用类型"
            v-model="costForm.type"
            @change="handleCostTypeChange"
            style="width: 100%"
          >
            <el-option
              v-for="option in managementFeeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="parseInt(option.value)"
            />
          </el-select>
          <div
            v-if="costTypeTips[getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, costForm.type)]"
            class="form-tip"
          >
            {{ costTypeTips[getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, costForm.type)] }}
          </div>
        </el-form-item>

        <el-form-item label="金额" prop="amount">
          <el-input-number
            @focus="costForm.amount == 0 ? (costForm.amount = '') : costForm.amount"
            v-model="costForm.amount"
            :min="0"
            :precision="2"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="分摊方式" prop="rule">
          <el-select
            placeholder="请选择分摊方式"
            v-model="costForm.rule"
            @change="handleRuleChange"
            style="width: 100%"
          >
            <el-option
              v-for="method in availableAllocationMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
          <div class="form-tip">
            {{ allocationMethodTip }}
          </div>
        </el-form-item>

        <!-- 当月配置自动复制开关 -->
        <!-- <el-form-item v-if="isCurrentMonthCost" label="应用到下月份">
          <div class="auto-copy-switch">
            <el-switch
              v-model="costForm.isMonthly"
              active-text="自动将该费用项应用到下月份"
              inactive-text="仅应用到当月"
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            />
            <div class="copy-hint">
              <Icon icon="ep:info-filled" class="mr-5px" />
              <span>开启后，保存时会自动将当前费用项复制到 {{ getNextMonthText() }}</span>
            </div>
          </div>
        </el-form-item> -->
      </el-form>

      <template #footer>
        <el-button @click="showAddCostModal = false">取消</el-button>
        <el-button type="primary" @click="saveCostItem">确定</el-button>
      </template>
    </el-dialog>

    <!-- Excel导入弹窗 -->
    <el-dialog v-model="showImportModal" title="从Excel导入费用数据" width="500px">
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          description="请先下载模板，按照模板格式填写费用数据后再上传"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        />

        <div class="import-actions">
          <el-button @click="downloadTemplate">
            <Icon icon="ep:download" class="mr-5px" />
            下载Excel模板
          </el-button>

          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept=".xlsx,.xls"
            style="display: inline-block; margin-left: 12px"
          >
            <el-button type="primary">
              <Icon icon="ep:upload" class="mr-5px" />
              选择文件
            </el-button>
          </el-upload>
        </div>

        <div v-if="uploadFile" class="file-info">
          <p>已选择文件: {{ uploadFile.name }}</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="showImportModal = false">取消</el-button>
        <el-button type="primary" @click="importData" :disabled="!uploadFile">确认导入</el-button>
      </template>
    </el-dialog>

    <!-- 复制费用弹窗 -->
    <el-dialog
      v-model="showCopyModal"
      title="将该月费用复制到"
      width="500px"
      @close="resetCopyForm"
    >
      <el-form ref="copyFormRef" :model="copyForm" :rules="copyFormRules" label-width="120px">
        <el-form-item label="当前月份">
          <el-input :value="formatMonth(selectedMonth)" readonly />
        </el-form-item>

        <el-form-item label="目标月份" prop="targetMonth">
          <el-date-picker
            v-model="copyForm.targetMonth"
            type="month"
            placeholder="选择目标月份"
            format="YYYY年MM月"
            value-format="YYYY-MM"
            style="width: 100%"
            @change="checkTargetMonth"
          />
        </el-form-item>

        <el-form-item label="复制内容" prop="copyType">
          <el-checkbox-group v-model="copyForm.copyTypes">
            <el-checkbox
              v-for="option in managementFeeTypeOptions"
              :key="option.value"
              :value="option.label"
            >
              {{ option.label }}
            </el-checkbox>
            <el-checkbox v-for="type in customCostTypes" :key="type" :value="type">{{
              type
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-alert
          v-if="isTargetMonthClosed"
          title="目标月份已封账，无法进行复制操作"
          type="error"
          :closable="false"
          style="margin-top: 12px"
        />

        <el-alert
          v-if="copyForm.targetMonth"
          :title="`将复制 ${getCopyDescription()} 到 ${formatMonth(copyForm.targetMonth)}`"
          type="info"
          :closable="false"
          style="margin-top: 12px"
        />
      </el-form>

      <template #footer>
        <el-button @click="showCopyModal = false">取消</el-button>
        <el-button
          type="primary"
          @click="copyCosts"
          :disabled="
            isTargetMonthClosed || !copyForm.targetMonth || copyForm.copyTypes.length === 0
          "
          >确认复制</el-button
        >
      </template>
    </el-dialog>

    <!-- 费用类型管理弹窗 -->
    <el-dialog
      v-model="showCostTypeModal"
      title="自定义费用类型管理"
      width="600px"
      @close="resetCostTypeForm"
    >
      <div class="cost-type-section">
        <h4>系统费用类型（不可删除）</h4>
        <el-tag
          v-for="option in managementFeeTypeOptions"
          :key="option.value"
          type="success"
          style="margin-right: 8px; margin-bottom: 8px"
        >
          {{ option.label }}
        </el-tag>
      </div>

      <div class="cost-type-section">
        <h4>自定义费用类型</h4>
        <div class="add-type-form">
          <el-input
            v-model="newCostType"
            placeholder="请输入新的费用类型名称"
            style="width: 300px; margin-right: 12px"
            @keyup.enter="addCostType"
          />
          <el-button type="primary" @click="addCostType" :disabled="!newCostType.trim()">
            添加
          </el-button>
        </div>

        <div class="custom-types-list" style="margin-top: 16px">
          <el-tag
            v-for="type in customCostTypes"
            :key="type"
            closable
            @close="removeCostType(type)"
            style="margin-right: 8px; margin-bottom: 8px"
          >
            {{ type }}
          </el-tag>
          <div v-if="customCostTypes.length === 0" class="empty-tip"> 暂无自定义费用类型 </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showCostTypeModal = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import type { ManagementFeePageReqVO, ManagementFeeSaveReqVO } from '@/api/infra/managementFee'
import * as ManagementFeeApi from '@/api/infra/managementFee'
import type { TableColumn } from '@/types/table'
import { DICT_TYPE, getDictLabel, getDictOptions } from '@/utils/dict'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({ name: 'OverheadAllocation' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 响应式数据
const loading = ref(false)
const selectedMonth = ref(
  new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0')
)
const showAddCostModal = ref(false)
const showImportModal = ref(false)
const showCopyModal = ref(false)
const showCostTypeModal = ref(false)
const editingIndex = ref(-1)
const uploadFile = ref(null)
const newCostType = ref('')
const isTargetMonthClosed = ref(false)

// 查询参数
const queryParams = reactive<ManagementFeePageReqVO>({
  pageNo: 1,
  pageSize: 1000,
  name: undefined,
  amount: undefined,
  type: undefined,
  rule: undefined,
  isMonthly: undefined,
  createTime: undefined,
  date: [selectedMonth.value] + '-01'
})

// 分页数据
const total = ref(0)

// 费用类型管理 - 使用字典数据
const managementFeeTypeOptions = ref([])
const customCostTypes = ref([])

// 费用项数据
const costItems = ref<ManagementFeeApi.ManagementFeeRespVO[]>([])

// 表单数据
const costForm = reactive<ManagementFeeSaveReqVO>({
  id: undefined,
  name: '',
  amount: 0,
  type: undefined,
  rule: undefined,
  isMonthly: false,
  date: selectedMonth.value + '-01'
})

const copyForm = reactive({
  targetMonth: '',
  copyTypes: []
})

// 表单验证规则
const costFormRules = {
  name: [{ required: true, message: '请输入费用名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
  amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
  rule: [{ required: true, message: '请选择分摊方式', trigger: 'change' }]
}

const copyFormRules = {
  targetMonth: [{ required: true, message: '请选择目标月份', trigger: 'change' }]
}

// 计算属性
const allCostTypes = computed(() => {
  // 获取字典数据中的费用类型
  const dictOptions = getDictOptions(DICT_TYPE.MANAGEMENT_FEE_TYPE)
  // 将字典数据转换为页面需要的格式
  const systemTypes = dictOptions.map((item) => item.label)
  return [...systemTypes, ...customCostTypes.value]
})
const totalCost = computed(() => costItems.value.reduce((sum, item) => sum + item.amount, 0))
const isCurrentMonthCost = computed(() => {
  const currentMonth =
    new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0')
  return selectedMonth.value === currentMonth
})

// 费用类型提示
const costTypeTips = {
  人工费: '按负责人分摊到各产品',
  设计费: '按产品数量平均分摊',
  场地费: '按店铺销售额比例分摊',
  其它均摊费: '按店铺销售额比例分摊',
  VAT: '按店铺销售额比例分摊（固定分摊方式）'
}

// 分摊方式选项 - 使用字典数据
const managementFeeRuleOptions = ref([])

const availableAllocationMethods = computed(() => {
  // 获取字典数据中的分摊方式
  const dictOptions = getDictOptions(DICT_TYPE.MANAGEMENT_FEE_RULE)
  const allMethods = dictOptions.map((item) => ({ label: item.label, value: parseInt(item.value) }))

  // 检查当前选择的费用类型是否为VAT
  const selectedTypeLabel = getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, costForm.type)
  if (selectedTypeLabel === 'VAT') {
    // VAT类型只能使用销售额占比分摊
    return allMethods.filter((method) => method.label === '销售额占比分摊')
  }
  return allMethods
})

const allocationMethodTip = computed(() => {
  const tips = {
    0: '根据各店铺的销售额占比进行分摊',
    1: '按产品数量进行平均分摊',
    2: '按销售量占比进行分摊'
  }
  return tips[costForm.rule] || ''
})

// 表格列配置
const columns: TableColumn[] = [
  {
    label: '费用名称',
    field: 'name',
    width: 250,
    align: 'left'
  },
  {
    label: '费用类型',
    field: 'type',
    width: 120,
    slot: 'costType',
    align: 'left',
    tips: '1. 默认费用类型为"场地费","人工费","VAT","其它均摊费","设计费"。</br>2. 新增费用项时，选择以上默认费用类型时会默认匹配分摊规则，具体如下：</br>场地费：按销售额占比分摊</br>人工费：按销售额占比分摊</br>VAT：按销售额占比分摊</br>其它均摊费：按销售额占比分摊</br>设计费：按销量占比分摊',
    icon: 'ep:question-filled'
  },
  {
    label: '金额(￥)',
    field: 'amount',
    width: 180,
    slot: 'amount',
    align: 'left'
  },
  {
    label: '分摊规则',
    field: 'rule',
    minWidth: 250,
    align: 'left',
    headerAlign: 'left',
    tips: `
无论选择哪种分摊方式，系统首先会计算出 “某项费用的平均每日管理费”。</br>
- 公式:</br>
- 某项费用平均每日管理费 = 当前月份某项费用的总额 / 当前月份的总天数</br>
- 示例:</br>
  - 假设当前是 6月 (共30天)。</br>
  - 从上面的费用池表格中得知，6月的 某项费用的管理费 为 ￥68,000。</br>
  - 则，平均每日某项管理费 = 68,000 / 30 = ￥2,266.67。</br>
按销售额占比分摊: 卖得越多的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 “当日总销售额”。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销售额 / 当日总销售额)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的“当日总销售额”为 ￥150,000。</br>
  - 计算ASIN-A:</br>
    - ASIN-A 当日销售额 = ￥15,000。</br>
    - ASIN-A 应分摊的某项管理费 = 2,266.67 * (15,000 / 150,000) = 2,266.67 * 10% = ￥226.67。</br>
  - 计算ASIN-B:</br>
    - ASIN-B 当日销售额 = ￥7,500。</br>
    - ASIN-B 应分摊的某项管理费 = 2,266.67 * (7,500 / 150,000) = 2,266.67 * 5% = ￥113.33。</br>
按销量占比分摊: 销量越高的ASIN，承担越多的管理费。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）所有出单ASIN的 “当日总销量”。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 * (该ASIN当日销量 / 当日总销量)</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日的“当日总销量”为 500件。</br>
  - 计算ASIN-C:</br>
    - ASIN-C 当日销量 = 25件。</br>
    - ASIN-C 应分摊的某项管理费 = 2,266.67 * (25 / 500) = 2,266.67 * 5% = ￥113.33。</br>
均摊: 只要当天出单了，每个ASIN都平等地分摊管理费，无论其销售额或销量大小。</br>
- 计算步骤 :</br>
  1. 系统获取指定日期（例如6月12日）出单的ASIN总数量。</br>
  2. 对每一个出单的ASIN，应用以下公式：</br>
1. ASIN当日分摊费用 = 平均每日某项管理费 / 当日出单的ASIN总数</br>
- 计算示例:</br>
  - 已知:</br>
    - 平均每日某项管理费 = ￥2,266.67</br>
    - 假设6月12日共有 100个 不同的ASIN产生了订单。</br>
  - 计算任意一个出单的ASIN（如ASIN-D）:</br>
    - ASIN-D 应分摊的某项管理费 = 2,266.67 / 100 = ￥22.67。</br>
    - （所有其他99个出单ASIN分摊到的费用也都是￥22.67）。</br>
    `,
    icon: 'ep:question-filled'
  },
  // {
  //   label: '是否应用到下月',
  //   field: 'isMonthly',
  //   width: 120,
  //   slot: 'isMonthly',
  //   align: 'center'
  // },
  {
    label: '操作',
    field: 'action',
    width: 120,
    fixed: 'right',
    slot: 'action'
  }
]

// 价格符号方法
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(num || 0)
}

const formatMonth = (month) => {
  if (!month) return ''
  const [year, monthNum] = month.split('-')
  return `${year}年${monthNum}月`
}

const getCostTypeColor = (type) => {
  const colors = {
    人工费: 'primary',
    设计费: 'success',
    场地费: 'warning',
    其它均摊费: 'danger',
    VAT: 'info'
  }
  return colors[type] || 'info'
}

const getAllocationRuleText = (rule) => {
  return getDictLabel(DICT_TYPE.MANAGEMENT_FEE_RULE, rule) || '未知规则'
}

const getNextMonthText = () => {
  const [year, month] = selectedMonth.value.split('-')
  const nextMonth = new Date(parseInt(year), parseInt(month), 1)
  return `${nextMonth.getFullYear()}年${String(nextMonth.getMonth() + 1).padStart(2, '0')}月`
}

const getCopyDescription = () => {
  return copyForm.copyTypes.join('、')
}

const handleMonthChange = () => {
  queryParams.date = [selectedMonth.value] + '-01'
  costForm.date = selectedMonth.value + '-01'
  getList()
}

const handleCostTypeChange = () => {
  // 重置分摊方式
  costForm.rule = null
  // VAT类型默认使用销售额占比分摊
  const selectedTypeLabel = getDictLabel(DICT_TYPE.MANAGEMENT_FEE_TYPE, costForm.type)
  if (selectedTypeLabel === 'VAT') {
    // 查找销售额占比分摊的value值
    const vatRuleOption = getDictOptions(DICT_TYPE.MANAGEMENT_FEE_RULE).find(
      (item) => item.label === '销售额占比分摊'
    )
    if (vatRuleOption) {
      costForm.rule = parseInt(vatRuleOption.value)
    }
  }
}

const handleRuleChange = () => {
  // 分摊方式改变时的处理逻辑
  // 可以在这里添加相关的业务逻辑
}

const resetCostForm = () => {
  costForm.id = undefined
  costForm.name = ''
  costForm.amount = 0
  costForm.type = undefined
  costForm.rule = undefined
  costForm.isMonthly = false
  costForm.date = selectedMonth.value + '-01'
  editingIndex.value = -1
}

// 编辑费用项
const editCostItem = (row: ManagementFeeApi.ManagementFeeRespVO, index: number) => {
  costForm.id = row.id
  costForm.name = row.name
  costForm.amount = row.amount
  costForm.type = row.type
  costForm.rule = row.rule
  costForm.isMonthly = row.isMonthly
  costForm.date = row.date
  editingIndex.value = index
  showAddCostModal.value = true
}

// 删除费用项
const deleteCostItem = async (row: ManagementFeeApi.ManagementFeeRespVO, index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个费用项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await ManagementFeeApi.deleteManagementFee(row.id)
    ElMessage.success('删除成功')
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除费用项失败', error)
      ElMessage.error('删除失败')
    }
  }
}

const resetCopyForm = () => {
  Object.assign(copyForm, {
    targetMonth: '',
    copyTypes: []
  })
}

const resetCostTypeForm = () => {
  newCostType.value = ''
}

const saveCostItem = async () => {
  try {
    loading.value = true

    if (editingIndex.value !== -1) {
      // 编辑模式
      await ManagementFeeApi.updateManagementFee(costForm)
      ElMessage.success('费用项更新成功')
    } else {
      // 新增模式
      await ManagementFeeApi.createManagementFee(costForm)
      ElMessage.success('费用项添加成功')
    }

    showAddCostModal.value = false
    resetCostForm()
    await getList()

    // 如果选择应用到下月份
    if (costForm.applyToNextMonth) {
      const nextMonthData = { ...costData }
      // 这里可以添加应用到下月份的逻辑
      ElMessage.success('已同时应用到下月份')
    }

    showAddCostModal.value = false
    resetCostForm()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 下载模板
import download from '@/utils/download'
const downloadTemplate = async () => {
  try {
    const data = await ManagementFeeApi.exportManagementFee(queryParams)
    download.excel(data, '费用数据模板.xls')
    ElMessage.success('Excel模板下载成功')
  } catch (error) {
    console.error('下载模板失败', error)
    ElMessage.error('下载模板失败')
  }
}

const handleFileChange = (file) => {
  uploadFile.value = file
  return false // 阻止自动上传
}

const importData = async () => {
  if (!uploadFile.value) {
    message.warning('请先选择文件')
    return
  }

  try {
    // 模拟导入
    message.success('导入成功')
    showImportModal.value = false
    uploadFile.value = null
    loadCostItems()
  } catch (error) {
    message.error('导入失败')
  }
}

const checkTargetMonth = () => {
  // 检查目标月份是否已封账
  isTargetMonthClosed.value = false
}

const copyCosts = async () => {
  try {
    // 模拟复制
    message.success('复制成功')
    showCopyModal.value = false
    resetCopyForm()
  } catch (error) {
    message.error('复制失败')
  }
}

const addCostType = () => {
  if (!newCostType.value.trim()) {
    ElMessage.warning('请输入费用类型名称')
    return
  }
  if (allCostTypes.value.includes(newCostType.value.trim())) {
    ElMessage.warning('该费用类型已存在')
    return
  }
  customCostTypes.value.push(newCostType.value.trim())
  newCostType.value = ''
  ElMessage.success('费用类型添加成功')
}

const removeCostType = (type) => {
  const index = customCostTypes.value.indexOf(type)
  if (index > -1) {
    customCostTypes.value.splice(index, 1)
    ElMessage.success('费用类型删除成功')
  }
}

// 获取费用项列表
const getList = async () => {
  loading.value = true
  try {
    const data = await ManagementFeeApi.getManagementFeePage(queryParams)
    costItems.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取费用项列表失败', error)
    ElMessage.error('获取费用项列表失败')
  } finally {
    loading.value = false
  }
}

const loadCostItems = async () => {
  await getList()
}

// 初始化字典数据
const initDictData = () => {
  // 加载管理费类型字典
  managementFeeTypeOptions.value = getDictOptions(DICT_TYPE.MANAGEMENT_FEE_TYPE)
  // 加载管理费分摊方式字典
  managementFeeRuleOptions.value = getDictOptions(DICT_TYPE.MANAGEMENT_FEE_RULE)
}

// 初始化
onMounted(() => {
  initDictData()
  loadCostItems()
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;

  h1 {
    margin: 0 0 8px;
    font-size: 24px;
    color: #333;
  }

  .page-desc {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

.section-header {
  display: flex;
  justify-content: end;
  align-items: center;
  flex: 1;

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.cost-table {
  .amount-text {
    font-family: monospace;
    font-weight: 500;
  }

  .related-info {
    margin-top: 4px;
  }
}

.cost-summary {
  margin-top: 16px;
  border-radius: 4px;

  .summary-item {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .label {
      margin-right: 12px;
      font-size: 16px !important;
      font-weight: 500;
      color: #666;
    }

    .total-amount {
      font-family: monospace;
      font-size: 20px !important;
      font-weight: bold;
      color: #e6a23c;
    }
  }
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.auto-copy-switch {
  .copy-hint {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }
}

.import-content {
  .import-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }

  .file-info {
    padding: 8px;
    font-size: 14px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

.cost-type-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px;
    font-size: 14px;
    color: #333;
  }

  .add-type-form {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .empty-tip {
    padding: 20px;
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
}

.search-section {
  padding: 16px;
  margin-bottom: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .el-form {
    margin: 0;
  }
}

.store-select-content,
.product-select-content {
  .selection-summary {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
  }
}

.selection-container {
  .el-radio-group {
    margin-bottom: 8px;
  }
}
</style>
