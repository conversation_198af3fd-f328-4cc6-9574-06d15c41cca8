<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="店铺名" prop="sellerItem">
        <el-input
          v-model="queryParams.sellerItem"
          placeholder="请输入店铺名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="利润表模板" prop="tableHeadId" label-width="80px">
        <el-select
          v-model="queryParams.tableHeadId"
          placeholder="请选择利润表模板"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in profitTableList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="店铺状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择店铺状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_SHOP_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="店铺类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择店铺类型"
          clearable
          class="!w-240px"
        >
          <el-option label="本土" :value="0" />
          <el-option label="中企" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <Table
      v-loading="loading"
      :columns="columns"
      :data="list"
      :pagination="{
        total: total,
        pageSize: queryParams.pageSize,
        currentPage: queryParams.pageNo,
        layout: 'total, sizes, prev, pager, next, jumper'
      }"
      @update:page-size="
        (val) => {
          queryParams.pageSize = val
          getList()
        }
      "
      @update:current-page="
        (val) => {
          queryParams.pageNo = val
          getList()
        }
      "
      :showColumnConfig="false"
      border
    >
      <!-- 说明文字 -->
      <template #top-btn-right>
        <el-popover class="box-item" title="" placement="left-start" width="500px">
          <template #reference>
            <Icon icon="ep:warning" />
          </template>
          <div
            v-html="
              `<b>1.利润表模板</b></br>
  • 补货建议将基于所选模板计算利润</br>
  • 请确保选择正确的模板</br>
<b>2.数据同步</b></br>
  • 如店铺数据不全：【系统管理】→【用户管理】→【第三方账号绑定】→点击【同步第三方数据】</br>
  • 同步后约10分钟生效</br>
<b>注意：使用错误模板会导致计算结果偏差</b></br>
`
            "
          >
          </div>
        </el-popover>
      </template>

      <!-- 店铺状态列 -->
      <template #status="{ row }">
        <DictTag :type="DICT_TYPE.INFRA_SHOP_STATUS" :value="row.status" />
      </template>

      <!-- 店铺类型列 -->
      <template #type="{ row }">
        <el-radio-group
          v-model="row.type"
          @change="handleShopTypeRadioChange(row)"
          :disabled="row.saving"
        >
          <el-radio :label="0">本土</el-radio>
          <el-radio :label="1">中企</el-radio>
        </el-radio-group>
      </template>

      <!-- 利润表模板列 -->
      <template #shopType="{ row }">
        <el-select
          v-model="row.tableHeadId"
          placeholder="请选择利润表模板"
          @change="handleShopTypeChange(row)"
          :loading="row.saving"
          clearable
        >
          <el-option
            v-for="item in profitTableList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </template>
    </Table>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as ShopSettingApi from '@/api/infra/shopsetting'
import type { TableColumn } from '@/types/table'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'

defineOptions({ name: 'ToolShop' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const profitTableList = ref([]) // 利润表模板列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  sellerItem: undefined,
  tableHeadId: undefined,
  status: undefined,
  type: undefined
})
const queryFormRef = ref() // 搜索的表单

// 表格列配置
const columns: TableColumn[] = [
  {
    label: '店铺名',
    field: 'sellerItem',
    align: 'center'
  },
  {
    label: '站点简称',
    field: 'region',
    align: 'center'
  },
  {
    label: '国家',
    field: 'country',
    align: 'center'
  },
  {
    label: '店铺类型',
    field: 'type',
    align: 'center',
    slot: 'type',
    icon: 'ep:warning',
    tips: '请正确选择店铺类型。'
  },
  {
    label: '店铺状态',
    field: 'status',
    align: 'center',
    slot: 'status'
  },
  {
    label: '利润表模板',
    field: 'shopType',
    align: 'center',
    slot: 'shopType'
  }
]

/** 查询列表 */
const getList = async () => {
  loading.value = true
  list.value = []
  try {
    const data = await ShopSettingApi.getShopSettingPage(queryParams)
    list.value = data.list.map((item) => ({
      ...item,
      saving: false // 添加保存状态
    }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 店铺类型变更处理 */
const handleShopTypeRadioChange = async (row: any) => {
  try {
    row.saving = true

    // 提醒用户修改了店铺类型
    // message.warning('你修改了店铺类型，请在利润表模版列选择对应正确的模版')

    const params: any = {
      type: row.type
    }

    // 如果有id和sid就传，没有就不传
    if (row.id) {
      params.id = row.id
    }
    if (row.sid) {
      params.sid = row.sid
    }
    if (row.tableHeadId) {
      params.tableHeadId = row.tableHeadId
    }
    if (row.appid) {
      params.appid = row.appid
    }

    let data = await ShopSettingApi.saveShopSetting(params)
    if (data && !row.id) {
      row.id = data
    }
    message.success('店铺类型更新成功')
  } catch (error) {
    message.error('店铺类型更新失败')
    // 恢复原值
    await getList()
  } finally {
    row.saving = false
  }
}

/** 利润表模板变更处理 */
const handleShopTypeChange = async (row: any) => {
  try {
    row.saving = true
    const params: any = {
      tableHeadId: row.tableHeadId || null
    }

    // 如果有id和sid就传，没有就不传
    if (row.id) {
      params.id = row.id
    }
    if (row.sid) {
      params.sid = row.sid
    }
    if (row.appid) {
      params.appid = row.appid
    }
    if (row.type) {
      params.type = row.type
    }

    let data = await ShopSettingApi.saveShopSetting(params)
    if (data && !row.id) {
      row.id = data
    }
    message.success('利润表模板更新成功')
  } catch (error) {
    message.error('利润表模板更新失败')
    // 恢复原值
    await getList()
  } finally {
    row.saving = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  const data = await ShopSettingApi.getShopSimpleListProfit()
  profitTableList.value = data
})
</script>

<style scoped>
.text-red-500 {
  color: #ef4444;
}
</style>
