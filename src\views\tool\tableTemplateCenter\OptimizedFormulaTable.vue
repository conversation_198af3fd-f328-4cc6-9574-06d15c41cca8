<template>
  <div class="optimized-formula-table">
    <!-- 公式配置区块 -->
    <div class="formula-config">
      <!-- 动态生成的公式配置项 -->
      <div
        v-for="(formula, index) in formulas"
        :key="index"
        class="formula-item"
      >
        <div class="formula-header">
          <span class="formula-title">{{ (formula&&formula.calculationField)?formula.calculationField+'  ':''}}公式配置</span>

          <div class="formula-actions">
            <el-button
              type="primary"
              link
              @click="handleTestFormula(formula, index)"
              v-hasPermi="['infra:tools-table-template:test']"
            >
              测试
            </el-button>
            <el-button
              type="danger"
              link
              v-hasPermi="['infra:tools-table-template:delete']"
              @click="handleRemoveFormula(index)"
            >
              删除
            </el-button>
          </div>
        </div>

        <div class="formula-content">
          <div class="formula-row">
            <span class="formula-label">计算字段：</span>
            <el-select
              v-model="formula.calculationField"
              placeholder="请选择计算字段"
              filterable
              clearable
              class="formula-select"
              @change="() => handleCheckFieldUnique(index)"
            >
              <el-option-group
                v-for="category in fieldCategories"
                :key="category.value"
                :label="category.label"
              >
                <el-option
                  v-for="field in getFieldsFromCategory(category.fieldsKey)"
                  :key="field.name"
                  :label="field.name"
                  :value="field.name"
                  :disabled="isFieldUsedInFormula(field.name, index)"
                />
              </el-option-group>
            </el-select>
          </div>

          <div class="formula-row">
            <span class="formula-label">公式操作：</span>
            <div class="formula-operation">
              <el-select
                v-model="formula.selectedField"
                placeholder="请选择字段"
                filterable
                clearable
                class="formula-select"
                @change="(value) => handleFieldSelect(value, index)"
              >
                <el-option-group
                  v-for="category in fieldCategories"
                  :key="category.value"
                  :label="category.label"
                >
                  <el-option
                    v-for="field in getFieldsFromCategory(category.fieldsKey)"
                    :key="field.name"
                    :label="field.name"
                    :value="field.name"
                  />
                </el-option-group>
              </el-select>
              <div class="formula-operators">
                <el-button @click="handleAddOperator('+', index)">+</el-button>
                <el-button @click="handleAddOperator('-', index)">-</el-button>
                <el-button @click="handleAddOperator('*', index)">*</el-button>
                <el-button @click="handleAddOperator('/', index)">/</el-button>
                <el-button @click="handleAddOperator('(', index)">（</el-button>
                <el-button @click="handleAddOperator(')', index)">）</el-button>
                <el-popover
                  placement="bottom"
                  :width="300"
                  trigger="click"
                >
                  <template #reference>
                    <el-button>更多</el-button>
                  </template>
                  <div class="keyboard-container">
                    <el-button
                      v-for="symbol in symbols"
                      :key="symbol"
                      @click="handleAddOperator(symbol, index)"
                      :class="{ 'symbol-active': formula.tempSymbol == symbol }"
                    >
                      {{ symbol }}
                    </el-button>
                  </div>
                </el-popover>
              </div>
            </div>
          </div>

          <div class="formula-row">
            <span class="formula-label">公式预览：</span>
            <div class="formula-preview">
              <el-input
                v-model="formula.rule"
                type="textarea"
                :rows="2"
                placeholder="公式预览"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 添加公式按钮 -->
      <el-button
        type="primary"
        @click="handleAddFormula"
        :loading="loading"
      >
        <Icon icon="ep:plus" /> 添加公式
      </el-button>
    </div>

    <!-- 公式测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="公式测试"
      width="600px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="test-dialog-body">
        <div class="test-dialog-content">
          <div class="formula-title">{{ currentFormula?.calculationField }} 公式：</div>
          <div class="formula-display">{{ currentFormula?.rule }}</div>

          <div class="test-fields">
            <div
              v-for="(field, index) in testFields"
              :key="index"
              class="test-field-item"
            >
              <span class="field-name">{{ field.name }}：</span>
              <el-input
                v-model="field.value"
                placeholder="请输入测试值"
                @input="handleTestInputChange"
              />
            </div>
          </div>

          <div class="test-result">
            <div class="result-label">计算结果：</div>
            <div
              class="result-value"
              :class="{ 'error': testError }"
            >
              {{ testResult }}
            </div>
          </div>
        </div>

        <div class="test-button-container">
          <el-button
            type="primary"
            @click="handleExecuteTest"
            :loading="testLoading"
          >测试</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { debounce } from 'lodash-es'
import { Icon } from '@/components/Icon'
import { ToolsTableTemplateApi } from '@/api/tool/tableTemplateCenter'

interface FieldItem {
  id?: string | number
  name: string
  required: boolean
  edit: number
  hasUnit: boolean
  unit?: number | null
  paramTypeCode: string
  categoryCode?: string
  categoryName?: string
  [key: string]: any
}

interface FormulaItem {
  calculationField: string
  selectedField: string
  tempSymbol: string
  rule: string
}

interface FieldCategory {
  label: string
  value: string
  fieldsKey: string
  categoryCode?: string
}

interface Props {
  formulas: FormulaItem[]
  fieldCategories: FieldCategory[]
  getFieldsFromCategory: (fieldsKey: string) => FieldItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits([
  'update:formulas',
  'add-formula',
  'remove-formula',
  'check-field-unique',
  'field-select',
  'add-operator'
])

// 符号列表
const symbols = [
  '+',
  '-',
  '*',
  '/',
  '(',
  ')',
  ',',
  '.',
  '%',
  '=',
  '<',
  '>',
  '<=',
  '>=',
  '!=',
  '==',
  '&&',
  '||',
  '!'
]

// 响应式数据
const testDialogVisible = ref(false)
const currentFormula = ref<FormulaItem | null>(null)
const currentFormulaIndex = ref<number | null>(null)
const testFields = ref<Array<{ name: string; value: string }>>([])
const testResult = ref<string | null>(null)
const testError = ref(false)
const testLoading = ref(false)

// 本地计算属性
const formulas = computed({
  get: () => props.formulas,
  set: (value) => emit('update:formulas', value)
})

// 防抖处理测试输入变化 - 增加延迟时间以减少不必要的状态重置
const handleTestInputChange = debounce(() => {
  testResult.value = null
}, 500) // 从300ms增加到500ms

// 事件处理函数
const handleAddFormula = () => {
  emit('add-formula')
}

const handleRemoveFormula = (index: number) => {
  emit('remove-formula', index)
}

const handleCheckFieldUnique = (index: number) => {
  emit('check-field-unique', index)
}

const handleFieldSelect = (value: string, index: number) => {
  emit('field-select', value, index)
}

const handleAddOperator = (operator: string, index: number) => {
  emit('add-operator', operator, index)
}

// 检查字段是否已在公式中使用 - 使用Set优化查找性能
const isFieldUsedInFormula = (fieldName: string, currentIndex: number) => {
  // 创建一个Set来存储所有已使用的计算字段名称
  const usedFields = new Set()
  props.formulas.forEach((formula, index) => {
    if (index !== currentIndex && formula.calculationField) {
      usedFields.add(formula.calculationField)
    }
  })
  return usedFields.has(fieldName)
}

// 公式测试相关 - 优化字段提取逻辑
const handleTestFormula = (formula: FormulaItem, index: number) => {
  if (!formula.rule) {
    ElMessage.warning('请先设置公式')
    return
  }

  currentFormula.value = formula
  currentFormulaIndex.value = index
  testDialogVisible.value = true
  testResult.value = null
  testError.value = false

  // 优化字段提取逻辑
  extractTestFields(formula.rule, formula.calculationField)
}

// 提取测试字段的优化函数
const extractTestFields = (rule: string, calculationField: string) => {
  console.log(rule)
  if (!rule) {
    testFields.value = []
    return
  }
  // 使用正则表达式提取字段名
  const regex = /[a-zA-Z_\u4e00-\u9fa5][\w\u4e00-\u9fa5]*/g
  const keywords = new Set(['if', 'else', 'return', 'function', 'var', 'let', 'const'])

  const matches = rule.match(regex) || []

  const uniqueFieldNames = [
    ...new Set(matches.filter((match) => !keywords.has(match) && !/^\d+(\.\d+)?$/.test(match)))
  ]
  console.log(uniqueFieldNames)
  testFields.value = uniqueFieldNames.map((name) => ({ name, value: '' }))
}

// 执行公式测试
const handleExecuteTest = async () => {
  if (!currentFormula.value) return

  // 检查是否所有字段都有值
  const emptyFields = testFields.value.filter((field) => !field.value.trim())
  if (emptyFields.length > 0) {
    ElMessage.warning(`请填写 ${emptyFields[0].name} 的测试值`)
    return
  }

  testLoading.value = true
  testResult.value = null
  testError.value = false

  try {
    // 构建测试参数
    const testValues = {}
    testFields.value.forEach((field) => {
      testValues[field.name] = field.value
    })

    // 调用API进行测试
    const result = await ToolsTableTemplateApi.testFormulaWithValues({
      rule: currentFormula.value.rule,
      variables: testValues
    })

    testResult.value = result
  } catch (error) {
    console.error('公式测试失败:', error)
    testResult.value = '计算错误，请检查公式和输入值'
    testError.value = true
  } finally {
    testLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
// 响应式设计
@media (width <= 768px) {
  .optimized-formula-table {
    .formula-row {
      flex-direction: column;

      .formula-label {
        width: 100%;
        padding-top: 0;
        text-align: left;
      }

      .formula-select {
        width: 100%;
      }
    }
  }

  .test-dialog-body {
    .test-field-item {
      flex-direction: column;
      align-items: flex-start;

      .field-name {
        width: 100%;
        text-align: left;
      }
    }
  }
}

.keyboard-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  padding: 10px;

  :deep(.el-button) {
    display: flex;
    height: 50px;
    min-width: 50px;
    margin-left: 0;
    font-size: 12px;
    align-items: center;
    justify-content: center;

    &.symbol-active {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
    }
  }
}

.optimized-formula-table {
  width: 100%;

  .formula-config {
    padding: 10px 20px;
    margin-bottom: 24px;
    background: var(--el-bg-color);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .formula-item {
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    }
  }

  .formula-header {
    display: flex;
    padding-bottom: 8px;
    margin-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    justify-content: space-between;
    align-items: center;
  }

  .formula-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .formula-actions {
    display: flex;
    gap: 8px;
  }

  .formula-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .formula-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;

    .formula-label {
      width: 80px;
      padding-top: 8px;
      font-size: 14px;
      color: var(--el-text-color-regular);
      text-align: right;
      flex-shrink: 0;
    }

    .formula-select {
      width: 200px;
    }

    .formula-operation {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
    }

    .formula-operators {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;
    }

    .formula-preview {
      flex: 1;
    }
  }
}

// 测试对话框样式
.test-dialog-body {
  padding: 0 20px;

  .test-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .formula-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .formula-display {
    padding: 12px;
    font-family: monospace;
    word-break: break-all;
    white-space: pre-wrap;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
  }

  .test-fields {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
  }

  .test-field-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .field-name {
      width: 120px;
      color: var(--el-text-color-regular);
      text-align: right;
    }
  }

  .test-result {
    display: flex;
    padding: 12px;
    margin-top: 16px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    align-items: center;

    .result-label {
      margin-right: 8px;
      font-weight: 500;
    }

    .result-value {
      font-weight: 500;
      color: var(--el-color-success);

      &.error {
        color: var(--el-color-danger);
      }
    }
  }

  .test-button-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
} // 符号键盘弹窗样式
</style>
