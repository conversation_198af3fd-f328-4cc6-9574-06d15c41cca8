export default [
  {
    id: '1928076736727347200',
    name: '基础模板(通用)',
    nation: '德国',
    templateTypeCode: '1',
    templateTypeName: '开发表',
    refId: null,
    draft: 0,
    remark: null,
    createTime: 1748524291058,
    fieldQty: null,
    sharer: '系统',
    shareTeam: '系统',
    toolsFieldDtoList: [
      {
        code: '',
        name: '尾程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '定价 <= 10.99 ?\r\n                                  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 1.61 :\r\n                                  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 1.64 :\r\n                                  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 1.66 :\r\n                                  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 1.8 :\r\n                                  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 1.83 :\r\n                                  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 1.86 :\r\n                                  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 2.02 :\r\n                                  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.39 :\r\n                                  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 2.78 :\r\n                                  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 2.78 :\r\n                                  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 2.99 : "未知"))))))))))) :\r\n                                  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 2.07 :\r\n                                  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 2.11 :\r\n                                  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 2.13 :\r\n                                  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 2.26 :\r\n                                  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 2.28 :\r\n                                  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 2.31 :\r\n                                  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 2.42 :\r\n                                  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.78 :\r\n                                  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 3.16 :\r\n                                  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 3.12 :\r\n                                  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 3.14 :\r\n                                  (高 < 12 && 抛重 < 0.9 && 实重 < 0.9 ? 3.41 :\r\n                                  (高 < 12 && 抛重 < 1.4 && 实重 < 1.4 ? 4.03 : "未知")))))))))))))\r\n'
      },
      {
        code: '',
        name: '产品卖点',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '日期',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '502',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购价',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '总成本',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '采购成本+头程运费+国内运费加辅料'
      },
      {
        code: '',
        name: '创建人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '物流渠道',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '毛利率',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '701',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '毛利润/(定价*7.8)'
      },
      {
        code: '',
        name: '投产比',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '毛利润/总成本'
      },
      {
        code: '',
        name: '产品名称',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '国内运费加辅料',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '抛重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '(长*宽*高)/6000'
      },
      {
        code: '',
        name: '物流运费单价',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '头程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: 'max(抛重, 实重) * 物流运费单价'
      },
      {
        code: '',
        name: '毛利润',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '(定价*0.8-尾程运费-税费)*7.8-总成本'
      },
      {
        code: '',
        name: '竞品链接',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '长',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核状态',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核备注',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '是否投产',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '宽',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购成本',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '尾程占比',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '701',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: '尾程运费/定价'
      },
      {
        code: '',
        name: '税费',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: '定价*0.17'
      },
      {
        code: '',
        name: '定价',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '高',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '体积重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '实重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1928076736727347200',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '汇率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1928076736727347200',
        remark: null,
        rule: ''
      }
    ],
    pendingReviewQty: null
  },
  {
    id: '1929445105083559936',
    name: '基础模板(中企德国)',
    nation: '德国',
    templateTypeCode: '1',
    templateTypeName: '开发表',
    refId: null,
    draft: 0,
    remark: null,
    createTime: 1748850535246,
    fieldQty: null,
    sharer: '袁智玮',
    shareTeam: '今昇团队',
    toolsFieldDtoList: [
      {
        code: '',
        name: '审核状态',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '审核时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '审核备注',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '是否投产',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '创建人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '创建时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '审核人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购链接1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购规格1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '实重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '宽',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '尾程占比',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '701',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '尾程运费/定价'
      },
      {
        code: '',
        name: '空派总成本',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '采购成本+空派头程运费+国内运费辅料'
      },
      {
        code: '',
        name: '卡航总成本',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '采购成本+卡航头程运费+国内运费辅料'
      },
      {
        code: '',
        name: '空派头程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: 'max(抛重, 实重) * 空派运费'
      },
      {
        code: '',
        name: '卡航头程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: 'max(抛重, 实重) * 卡航运费'
      },
      {
        code: '',
        name: '空派运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '41'
      },
      {
        code: '',
        name: '卡航运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '25'
      },
      {
        code: '',
        name: '长',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '国内运费辅料',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '2.5'
      },
      {
        code: '',
        name: '抛重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '(长*宽*高)/6000'
      },
      {
        code: '',
        name: '高',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '尾程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '定价 <= 10.99 ?\r\n                                  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 1.61 :\r\n                                  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 1.64 :\r\n                                  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 1.66 :\r\n                                  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 1.8 :\r\n                                  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 1.83 :\r\n                                  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 1.86 :\r\n                                  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 2.02 :\r\n                                  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.39 :\r\n                                  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 2.78 :\r\n                                  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 2.78 :\r\n                                  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 2.99 : "未知"))))))))))) :\r\n                                  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 2.07 :\r\n                                  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 2.11 :\r\n                                  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 2.13 :\r\n                                  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 2.26 :\r\n                                  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 2.28 :\r\n                                  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 2.31 :\r\n                                  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 2.42 :\r\n                                  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.78 :\r\n                                  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 3.16 :\r\n                                  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 3.12 :\r\n                                  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 3.14 :\r\n                                  (高 < 12 && 抛重 < 0.9 && 实重 < 0.9 ? 3.41 :\r\n                                  (高 < 12 && 抛重 < 1.4 && 实重 < 1.4 ? 4.03 : "未知")))))))))))))\r\n'
      },
      {
        code: '',
        name: '定价',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '卡航毛利率',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '701',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '卡航利润/(定价*7.8)'
      },
      {
        code: '',
        name: '空派投产比',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '空派利润/空派总成本'
      },
      {
        code: '',
        name: '卡航投产比',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '卡航利润/卡航总成本'
      },
      {
        code: '',
        name: '空派利润',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '(定价*0.82-尾程运费-税费)*7.8-空派总成本'
      },
      {
        code: '',
        name: '卡航利润',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '(定价*0.82-尾程运费-税费)*7.8-卡航总成本'
      },
      {
        code: '',
        name: '空派毛利率',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '701',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: '空派利润/(定价*7.8)'
      },
      {
        code: '',
        name: '税费',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 0,
        needUnit: 1,
        unit: '103',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: '定价*0.17'
      },
      {
        code: '',
        name: '竞品链接2',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'href',
        paramTypeName: '链接',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '竞品链接3',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'href',
        paramTypeName: '链接',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '竞品链接',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'href',
        paramTypeName: '链接',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '产品卖点',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '产品名称',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '日期',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '502',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '开发人',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 0,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '核心关键词',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '市场趋势',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购链接3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购规格3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购链接2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购规格2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '采购成本',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'inputNumber',
        paramTypeName: '数值 ',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '汇率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929445105083559936',
        remark: null,
        rule: ''
      }
    ],
    pendingReviewQty: null
  },
  {
    id: '1929425726891569152',
    name: '基础模板(印度)',
    nation: '印度',
    templateTypeCode: '1',
    templateTypeName: '开发表',
    refId: null,
    draft: 0,
    remark: null,
    createTime: 1748845915097,
    fieldQty: null,
    sharer: '钱龙',
    shareTeam: '磐石团队',
    toolsFieldDtoList: [
      {
        code: '',
        name: '配送费含税500克以下',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '63+63*0.09*2'
      },
      {
        code: '',
        name: '海运利润',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '(售价-佣金含税-tds税tcs税-分拣费含税-配送费含税500克以下-固定成交费含税-(采购价*汇率)-(售价*0.03))*0.905/汇率-采购价-空运运费'
      },
      {
        code: '',
        name: '海运投产比',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '海运利润/(采购价+船运费卢比/汇率)'
      },
      {
        code: '',
        name: '采购备注',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '发货备注',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核状态',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核备注',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '是否投产',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '固定成交费含税',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: 'if(售价 > 1000) { 50 * 1.18 } else { 25 * 1.18 }'
      },
      {
        code: '',
        name: '体积重',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '长*宽*高/5000'
      },
      {
        code: '',
        name: '货价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '采购价*11.5'
      },
      {
        code: '',
        name: '空运运费单价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '船运运费',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '(长/100)*(宽/100)*(高/100)'
      },
      {
        code: '',
        name: '空运回本售卖率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '(采购价+空运运费)/(采购价+空运运费+空运利润)'
      },
      {
        code: '',
        name: '空运投产比',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '空运利润/(采购价+空运运费)'
      },
      {
        code: '',
        name: '空运运费',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '自动取重*空运运费单价'
      },
      {
        code: '',
        name: '自动取重',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '实际重量'
      },
      {
        code: '',
        name: '采购链接3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '宽',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '毛利率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '空运利润/(售价/11.5)'
      },
      {
        code: '',
        name: '售价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '实际重量',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '高',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '船运运费单价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '海运运费+4300'
      },
      {
        code: '',
        name: '佣金含税',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '售价*佣金比例*1.18'
      },
      {
        code: '',
        name: '产品详情',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '长',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '海运回本售卖率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '(采购价+船运费卢比/汇率)/(采购价+船运费卢比/汇率+海运利润)'
      },
      {
        code: '',
        name: '图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '搜索量',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '产品名称',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '海关编码',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '汇率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '类目链接',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '类目链接图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购特殊备注（下单改颜色/改价/改运费）',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '趋势图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接备注1',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接2',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接1',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接备注2',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接3',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '空运利润',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '(售价-佣金含税-tds税tcs税-分拣费含税-配送费含税500克以下-固定成交费含税-(售价*0.03))*0.905/汇率-采购价-空运运费'
      },
      {
        code: '',
        name: '竞品链接备注3',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '开发日期',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '关键词+长尾词',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '分拣费含税',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '14+14*0.09*2'
      },
      {
        code: '',
        name: 'cpc',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '佣金比例',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: 'SKU',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: 'tds税tcs税',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: '售价/1.18*0.006'
      },
      {
        code: '',
        name: '核心词',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929425726891569152',
        remark: null,
        rule: null
      }
    ],
    pendingReviewQty: null
  },
  {
    id: '1929433765916913664',
    name: '基础模板(本土英国)',
    nation: '英国',
    templateTypeCode: '1',
    templateTypeName: '开发表',
    refId: null,
    draft: 0,
    remark: null,
    createTime: 1748847831724,
    fieldQty: null,
    sharer: '李紫馨',
    shareTeam: '四十致远',
    toolsFieldDtoList: [
      {
        code: '',
        name: '核心关键词',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '搜索量',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '产品名称',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品数',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: 'SKU',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '备注           (趋势，节日，季节)',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核状态',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '自动取重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: 'max(抛重, 实重) '
      },
      {
        code: '',
        name: '是否投产',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '总成本',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '采购价+限时提头程'
      },
      {
        code: '',
        name: '发货备注',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '文案/A+/图片/广告状态',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '本本利润',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '((售价*0.85) - (售价*0.15*0.2) - (尾程*0.2) - (售价*0.15*0.02 + 尾程*0.02) - ((售价*0.15*0.02 + 尾程*0.02)*0.2) - 尾程) * 9 - 总成本 - 2'
      },
      {
        code: '',
        name: '高',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '宽',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '长',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品规格',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '供应商1',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '限时提运费',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '45'
      },
      {
        code: '',
        name: '售价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '102',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '限时提头程',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: 'max(抛重, 实重) * 限时提运费'
      },
      {
        code: '',
        name: '采购规格',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '本本   清货回款',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '(售价*0.85)-(售价*0.15*0.2)-(尾程*0.2)-(售价*0.15*0.02+尾程*0.02)-(((售价*0.15*0.02+尾程*0.02)*0.2)-尾程)*9'
      },
      {
        code: '',
        name: '利润占比',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '总成本/本本利润'
      },
      {
        code: '',
        name: '竞品链接1',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '本本投产比',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '本本利润/总成本'
      },
      {
        code: '',
        name: '实重',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '卡航头程',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: 'max(抛重, 实重) * 卡航运费'
      },
      {
        code: '',
        name: '竞品链接2',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品定价',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '开发日期',
        categoryCode: '5',
        categoryName: '开发进度管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: ''
      },
      {
        code: '',
        name: '供应商2',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '尾程',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '102',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '卡航运费',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '25'
      },
      {
        code: '',
        name: '抛重',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: '长*宽*高/6000'
      },
      {
        code: '',
        name: '采购价',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格2',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '供应商3',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格3',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核备注',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '汇率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929433765916913664',
        remark: null,
        rule: ''
      }
    ],
    pendingReviewQty: null
  },
  {
    id: '1929532740399198208',
    name: '基础模板(中企英国)',
    nation: '英国',
    templateTypeCode: '1',
    templateTypeName: '开发表',
    refId: null,
    draft: 0,
    remark: null,
    createTime: 1748871429063,
    fieldQty: null,
    sharer: '刘硕',
    shareTeam: '峰值团队',
    toolsFieldDtoList: [
      {
        code: '',
        name: '审核备注',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: 'SKU',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '日期',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '502',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '抛重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '长*宽*高/6000'
      },
      {
        code: '',
        name: '尾程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '102',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '售价 < 9.99 ?\n  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 1.46 :\n  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 1.50 :\n  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 1.52 :\n  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 1.67 :\n  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 1.70 :\n  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 1.73 :\n  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 1.87 :\n  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.42 :\n  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 2.65 :\n  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 2.67 :\n  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 2.70 : "未知")))))))))))\n:\n(售价 > 9.99 ?\n  (高 < 2.5 && 抛重 < 0.02 && 实重 < 0.02 ? 1.83 :\n  (高 < 2.5 && 抛重 < 0.04 && 实重 < 0.04 ? 1.87 :\n  (高 < 2.5 && 抛重 < 0.06 && 实重 < 0.06 ? 1.89 :\n  (高 < 2.5 && 抛重 < 0.08 && 实重 < 0.08 ? 2.07 :\n  (高 < 2.5 && 抛重 < 0.1 && 实重 < 0.1 ? 2.08 :\n  (高 < 2.5 && 抛重 < 0.21 && 实重 < 0.21 ? 2.10 :\n  (高 < 2.5 && 抛重 < 0.46 && 实重 < 0.46 ? 2.16 :\n  (高 < 4 && 抛重 < 0.96 && 实重 < 0.96 ? 2.72 :\n  (高 < 6 && 抛重 < 0.96 && 实重 < 0.96 ? 2.94 :\n  (高 < 12 && 抛重 < 0.15 && 实重 < 0.15 ? 2.91 :\n  (高 < 12 && 抛重 < 0.4 && 实重 < 0.4 ? 3.0 :\n  (高 < 12 && 抛重 < 0.9 && 实重 < 0.9 ? 3.04 :\n  (高 < 12 && 抛重 < 1.4 && 实重 < 1.4 ? 3.05 : "未知")))))))))))))\n: "未知")'
      },
      {
        code: '',
        name: '头程运费',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '自动取重*物流运费单价'
      },
      {
        code: '',
        name: '竞品链接2',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购价',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '宽',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '高',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '实重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '201',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '投产比',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '毛利润/(采购价+头程运费)'
      },
      {
        code: '',
        name: '审核时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '是否投产',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '创建时间',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'time',
        paramTypeName: '时间 ',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '自动取重',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: 'max(抛重, 实重)'
      },
      {
        code: '',
        name: '物流运费单价',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '售价',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '102',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '毛利润',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '116',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '((售价*0.67-尾程运费)-(售价*0.15*0.02)-(尾程运费*0.02))*9-头程运费-采购价-2'
      },
      {
        code: '',
        name: '竞品链接1',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '竞品链接3',
        categoryCode: '4',
        categoryName: '竞品分析',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '审核人',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '产品名称',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格2',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格3',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接4',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格4',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购链接1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '采购规格1',
        categoryCode: '1',
        categoryName: '采购管理',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '核心关键词',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '图片',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'file',
        paramTypeName: '文件 ',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '长',
        categoryCode: '2',
        categoryName: '物流与成本',
        required: 1,
        edit: 1,
        needUnit: 1,
        unit: '301',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '平台回款',
        categoryCode: '3',
        categoryName: '运营与销售指标',
        required: 1,
        edit: 1,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: '((售价*0.67-尾程运费)-(售价*0.15*0.02)-(尾程运费*0.02))*9'
      },
      {
        code: '',
        name: '审核状态',
        categoryCode: '6',
        categoryName: '其它',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: null
      },
      {
        code: '',
        name: '汇率',
        categoryCode: '0',
        categoryName: '基本信息',
        required: 1,
        edit: 0,
        needUnit: 0,
        unit: '',
        paramTypeCode: 'input',
        paramTypeName: '文本',
        templateId: '1929532740399198208',
        remark: null,
        rule: ''
      }
    ],
    pendingReviewQty: null
  }
]
