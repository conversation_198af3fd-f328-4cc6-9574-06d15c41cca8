# 跨境电商OA系统操作文档

## 系统概述

本OA系统是基于领星ERP的跨境电商提效工具集合，主要服务于运营人员、老板、财务等角色。系统采用Vue3 + Element Plus + TypeScript技术栈构建，提供智能化的运营管理功能。

### 技术架构
- **前端框架**: Vue 3.5.12
- **UI组件库**: Element Plus 2.9.1
- **开发语言**: TypeScript 5.3.3
- **构建工具**: Vite 5.1.4
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.4.5

## 系统主要功能模块

### 1. 补货建议模块 (operate/replenishmentProposal)
智能分析商品销售数据，提供科学的补货建议，帮助优化库存管理。

### 2. 智能文案模块 (dev/copywriting)
基于AI技术生成产品文案，支持多语言翻译和竞品分析。

### 3. 广告策略管理模块 (operate/advertisingStrategyManage)
管理广告投放策略，提供智能分析和优化建议。

### 4. 创建广告模块 (operate/advertisingManage)
创建和管理广告活动，支持模板化操作。

### 5. 禁用词管理模块 (operate/forbiddenWords)
管理产品文案中的禁用词汇，确保合规性。

## 核心功能详解

### 一、补货建议模块

#### 功能概述
补货建议模块通过分析历史销售数据、库存周期、市场趋势等因素，为商品提供智能化的补货建议。

#### 主要功能
1. **智能补货分析**: 基于多维度数据分析生成补货建议
2. **规则设置**: 支持默认规则和自定义规则配置
3. **申请补货**: 批量或单个商品补货申请
4. **历史诊断**: 查看补货历史记录和分析结果
5. **数据导出**: 支持多种格式的数据导出

#### 操作界面说明
- **查询条件区**: 支持按SKU、ASIN、店铺、时间等条件筛选
- **操作按钮区**: 智能分析、规则设置、申请补货、导出等功能
- **数据列表区**: 显示商品补货建议详情
- **详情弹窗**: 查看单个商品的详细分析数据

#### 核心业务流程
1. **数据收集**: 系统自动收集商品销售、库存、竞品等数据
2. **智能分析**: 基于配置的权重规则进行多维度分析
3. **生成建议**: 计算出建议补货量和补货时机
4. **人工审核**: 运营人员可查看详情并调整建议
5. **执行补货**: 提交补货申请到采购系统

### 二、智能文案模块

#### 功能概述
智能文案模块利用AI技术为产品生成高质量的营销文案，支持多语言翻译和竞品分析功能。

#### 主要功能
1. **文案生成**: AI自动生成产品标题、五点描述、详情页文案
2. **竞品分析**: 分析竞品文案，提取优势元素
3. **多语言翻译**: 支持文案的多语言翻译
4. **文案编辑**: 可视化编辑器支持富文本编辑
5. **禁用词检测**: 自动检测并标记禁用词汇
6. **批量操作**: 支持批量生成和管理文案

#### 操作界面说明
- **产品信息区**: 输入产品基本信息和竞品数据
- **文案生成区**: 显示生成的标题、五点、详情等文案
- **编辑工具区**: 提供文案编辑、翻译、复制等工具
- **预览模式**: 支持预览和编辑模式切换

### 三、广告策略管理模块

#### 功能概述
广告策略管理模块帮助用户制定和优化广告投放策略，提供数据驱动的决策支持。

#### 主要功能
1. **策略创建**: 基于模板创建广告策略
2. **智能分析**: 分析广告效果和优化建议
3. **规则配置**: 设置广告投放的各种规则参数
4. **效果监控**: 实时监控广告投放效果
5. **预算管理**: 智能调整广告预算和竞价

### 四、创建广告模块

#### 功能概述
创建广告模块提供便捷的广告创建和管理功能，支持模板化操作提高效率。

#### 主要功能
1. **广告创建**: 支持自动和手动广告创建
2. **模板管理**: 创建和管理广告模板
3. **批量操作**: 支持批量创建广告
4. **状态监控**: 实时监控广告创建状态
5. **失败重试**: 支持失败广告的重新创建

## 系统特色功能

### 1. 智能化分析
- 基于机器学习算法的销售预测
- 多维度数据分析和建议生成
- 实时市场趋势分析

### 2. 自动化操作
- 批量数据处理和操作
- 定时任务和自动化流程
- 智能化的规则引擎

### 3. 可视化展示
- 丰富的图表和数据可视化
- 直观的操作界面和交互体验
- 响应式设计适配多种设备

### 4. 集成化管理
- 与领星ERP深度集成
- 统一的用户权限管理
- 完整的操作日志记录

## 用户角色与权限

### 运营人员
- 查看和使用所有运营相关功能
- 执行补货申请和广告创建
- 管理产品文案和广告策略

### 财务人员
- 查看成本和利润相关数据
- 审核补货申请和预算
- 导出财务报表数据

### 管理人员
- 查看所有模块数据
- 设置系统规则和参数
- 管理用户权限和部门设置

## 系统使用注意事项

1. **数据安全**: 系统涉及商业敏感数据，请妥善保管账号信息
2. **操作规范**: 按照既定流程操作，避免误操作影响业务
3. **及时更新**: 关注系统更新通知，及时升级到最新版本
4. **权限管理**: 严格按照权限使用系统功能，不得越权操作
5. **数据备份**: 重要数据请及时导出备份，防止数据丢失

## 详细操作指南

### 补货建议模块操作详解

#### 1. 模块入口
- 导航路径：运营管理 → 补货建议
- 页面路由：`/operate/replenishmentProposal`
- 权限要求：`operation:restock-suggest:query`

#### 2. 页面布局说明

**查询条件区域**
- **账号选择**: 支持多选领星账号，可选择"全部"
- **店铺筛选**: 根据选择的账号动态加载店铺列表
- **商品筛选**: 支持按SKU、ASIN、商品名称搜索
- **时间范围**: 可选择分析的时间范围
- **状态筛选**: 筛选不同状态的补货建议

**操作按钮区域**
- **智能补货分析**: 触发AI分析生成补货建议
- **默认规则设置**: 配置全局默认的分析规则
- **自定义规则设置**: 为特定商品设置个性化规则

**数据列表区域**
- 显示商品的补货建议详情
- 支持排序、分页、多选操作
- 实时显示分析状态和结果

#### 3. 核心功能操作

##### 3.1 智能补货分析

**操作步骤：**
1. 设置查询条件（账号、店铺、时间范围等）
2. 点击"智能补货分析"按钮
3. 系统开始分析，显示进度提示
4. 分析完成后自动刷新列表数据

**分析逻辑：**
- 基于历史销售数据计算日均销量
- 考虑库存周期和安全库存
- 应用权重规则（3天、7天、14天、30天、60天、90天销量权重）
- 生成建议补货量和补货时机

**权重规则说明：**
- 默认权重模板：65%-35%-0%-0%-0%-0%（3天65%，7天35%）
- 支持自定义权重配置
- 权重总和必须等于100%

##### 3.2 规则设置功能

**默认规则设置：**
1. 点击"默认规则设置"按钮
2. 配置库存周期（默认30天）
3. 设置权重比例模板或自定义权重
4. 保存后应用到所有未单独设置的商品

**自定义规则设置：**
1. 选择需要设置的商品（支持多选）
2. 点击"自定义规则设置"按钮
3. 配置特定的权重规则
4. 保存后优先使用自定义规则

**权重模板选项：**
- 保守型：65-35-0-0-0-0（重视近期数据）
- 均衡型：40-30-20-10-0-0（平衡各时间段）
- 激进型：20-20-20-20-10-10（考虑长期趋势）
- 自定义：手动设置各时间段权重

##### 3.3 申请补货功能

**单个商品申请：**
1. 在列表中找到目标商品
2. 点击操作列的"申请补货"按钮
3. 在弹窗中确认或调整补货量
4. 提交申请到采购系统

**批量申请补货：**
1. 勾选需要补货的商品
2. 点击"申请补货"按钮
3. 在批量申请弹窗中：
   - 查看建议补货量
   - 可点击"应用"将建议量应用到计划量
   - 支持批量设置计划补货量
   - 可单独调整每个商品的补货量
4. 确认后提交批量申请

**补货申请弹窗功能：**
- 显示商品基本信息（SKU、ASIN、当前库存等）
- 显示建议补货量和计划补货量
- 支持"应用建议"一键填充
- 支持批量设置功能
- 可移除不需要补货的商品

##### 3.4 数据查看功能

**详情查看：**
1. 点击商品操作列的"详情"按钮
2. 查看详细的分析数据：
   - 商品基本信息
   - 库存和销售数据
   - 补货建议详情
   - 毛利趋势图表
   - 销售数据分析

**历史诊断：**
1. 点击"历史诊断"按钮
2. 查看该商品的历史补货记录
3. 分析历史决策的准确性
4. 为未来决策提供参考

##### 3.5 数据导出功能

**导出选项：**
- **导出选中**: 导出当前选中的商品数据
- **导出全部**: 导出当前查询条件下的所有数据
- **导出已申请补货**: 导出已标记为需要补货的商品

**导出内容包括：**
- 商品基本信息
- 库存和销售数据
- 补货建议详情
- 分析参数和结果

#### 4. 操作注意事项

1. **数据时效性**: 补货建议基于最新的销售数据，建议定期重新分析
2. **规则配置**: 不同类型商品可能需要不同的权重规则
3. **库存周期**: 根据实际供应链情况合理设置库存周期
4. **人工审核**: AI建议仅供参考，需结合实际情况人工审核
5. **批量操作**: 批量申请前请仔细检查每个商品的补货量

#### 5. 常见问题处理

**Q: 为什么某些商品没有补货建议？**
A: 可能原因：销售数据不足、商品状态异常、未达到分析条件等

**Q: 如何调整权重规则？**
A: 通过"默认规则设置"或"自定义规则设置"功能调整权重比例

**Q: 补货申请提交后如何跟踪？**
A: 可通过采购系统查看申请状态，或查看操作日志

### 智能文案模块操作详解

#### 1. 模块入口
- 导航路径：开发工具 → 智能文案
- 页面路由：`/dev/copywriting`
- 权限要求：`dev:automation-product-info:query`

#### 2. 页面布局说明

**列表页面**
- **查询条件**: 支持按SKU、产品名称、状态、语言、创建时间筛选
- **操作按钮**: 新增文案、批量生成、批量删除、导入等
- **数据列表**: 显示产品文案列表，包含状态、生成进度等信息

**文案编辑页面**
- **左侧信息区**: 产品基本信息、竞品信息、受众场景分析
- **右侧文案区**: 标题、五点描述、详情页文案的生成和编辑

#### 3. 核心功能操作

##### 3.1 创建新文案

**操作步骤：**
1. 点击"新增"按钮进入文案创建页面
2. 填写产品基本信息：
   - SKU（必填）
   - 产品名称
   - 目标语言
   - 产品类别
   - 目标受众
   - 使用场景
3. 添加竞品信息（可选但推荐）
4. 点击"保存并生成"开始AI文案生成

**产品信息填写要点：**
- **SKU**: 确保唯一性，用于系统识别
- **产品名称**: 简洁明了，突出核心功能
- **目标受众**: 详细描述目标用户群体
- **使用场景**: 具体说明产品使用情况

##### 3.2 竞品分析功能

**添加竞品信息：**
1. 在产品信息页面找到"竞品信息"区域
2. 点击"添加竞品"按钮
3. 输入竞品ASIN或手动填写：
   - 竞品标题
   - 竞品五点描述
4. 可添加多个竞品进行对比分析

**解析受众场景：**
1. 确保已添加至少一个竞品
2. 点击"解析受众场景"按钮
3. AI自动分析竞品数据，生成：
   - 目标受众分析
   - 使用场景建议
4. 系统自动填充到对应字段

**竞品分析价值：**
- 了解市场竞争情况
- 提取优秀文案元素
- 优化产品定位
- 提高文案质量

##### 3.3 AI文案生成

**生成流程：**
1. 完善产品信息和竞品数据
2. 点击"保存并生成"按钮
3. 系统开始AI文案生成：
   - 产品标题生成
   - 五点描述生成
   - 详情页文案生成
4. 生成过程中显示进度提示
5. 完成后在右侧文案区域显示结果

**生成内容类型：**
- **产品标题**: 吸引眼球的产品标题
- **五点描述**: 突出产品核心卖点
- **详情页文案**: 详细的产品描述内容

**生成状态说明：**
- 待生成：尚未开始生成
- 生成中：AI正在处理
- 生成完成：文案已生成完毕
- 生成失败：需要检查信息后重试

##### 3.4 文案编辑功能

**编辑模式切换：**
- **预览模式**: 查看生成的文案效果
- **编辑模式**: 修改和优化文案内容

**编辑工具：**
- **富文本编辑器**: 支持格式化文本编辑
- **字数统计**: 实时显示文案字数
- **禁用词检测**: 自动标记违规词汇
- **翻译功能**: 支持多语言翻译

**编辑操作：**
1. 点击文案区域的"编辑"按钮
2. 在编辑器中修改文案内容
3. 使用工具栏进行格式化
4. 点击"保存"确认修改

##### 3.5 翻译功能

**翻译操作：**
1. 在文案编辑区域点击"翻译"按钮
2. 系统自动调用翻译API
3. 翻译结果显示在对应区域
4. 可进一步编辑翻译内容

**翻译特点：**
- 支持多种语言互译
- 保持文案营销性质
- 考虑文化差异
- 可手动调整优化

##### 3.6 复制和导出

**复制功能：**
- **单项复制**: 复制单个文案内容
- **全部复制**: 一键复制所有文案
- **格式复制**: 保持富文本格式

**导出功能：**
- 支持多种格式导出
- 可选择导出内容范围
- 保持文案格式和结构

##### 3.7 批量操作

**批量生成：**
1. 在列表页面选择多个产品
2. 点击"批量生成"按钮
3. 系统依次处理每个产品
4. 可查看批量处理进度

**批量删除：**
1. 选择需要删除的文案
2. 点击"批量删除"按钮
3. 确认后执行删除操作

**导入功能：**
1. 点击"导入"按钮
2. 下载导入模板
3. 按模板格式填写产品信息
4. 上传文件完成批量导入

#### 4. 文案质量优化建议

**标题优化要点：**
- 突出核心卖点
- 包含关键词
- 控制字符长度
- 吸引目标用户

**五点描述优化：**
- 每点突出一个核心功能
- 使用具体数据和参数
- 考虑用户关注点
- 避免重复表达

**详情页文案优化：**
- 结构清晰层次分明
- 图文并茂增强说服力
- 解决用户痛点
- 引导购买行为

#### 5. 常见问题处理

**Q: AI生成的文案质量不满意怎么办？**
A: 可以完善产品信息和竞品数据后重新生成，或手动编辑优化

**Q: 如何提高文案生成质量？**
A: 提供详细的产品信息、添加优质竞品、明确目标受众和使用场景

**Q: 翻译功能支持哪些语言？**
A: 系统集成百度翻译API，支持多种主流语言互译

**Q: 如何处理禁用词问题？**
A: 系统会自动标记禁用词，需要手动替换为合规词汇

### 广告策略管理模块操作详解

#### 1. 模块入口
- 导航路径：运营管理 → 广告策略管理
- 页面路由：`/operate/advertisingStrategyManage`
- 权限要求：`operation:automation-advertising-strategy:query`

#### 2. 模块结构说明

**策略管理页面** (`/strategy`)
- 策略列表展示和管理
- 新增策略和模板选择
- 策略复制和删除操作

**策略设置页面** (`/strategy/settingForm`)
- 区间划分规则设置
- 广告组合规则配置
- 批量编辑和保存功能

**广告分析页面** (`/advertAnalysis`)
- 智能广告效果分析
- 实时数据监控和调整
- 预算竞价优化建议

#### 3. 核心功能操作

##### 3.1 策略创建与管理

**创建新策略：**
1. 在策略列表页面点击"新增策略"
2. 选择策略模板：
   - 浏览现有策略模板
   - 选择最适合的基础模板
3. 输入新策略名称
4. 点击"确定"创建策略
5. 系统自动跳转到策略设置页面

**策略模板选择要点：**
- 根据产品类型选择合适模板
- 考虑投放目标和预算规模
- 参考历史成功案例
- 可后续调整优化参数

**策略管理操作：**
- **查看详情**: 点击策略名称查看详细配置
- **编辑策略**: 进入设置页面修改规则
- **复制策略**: 基于现有策略创建新策略
- **删除策略**: 删除不再使用的策略

##### 3.2 区间划分规则设置

**规则配置流程：**
1. 进入策略设置页面
2. 在"区间划分规则"标签页配置：
   - **ACOS区间**: 设置不同ACOS范围
   - **展现量区间**: 配置展现量分段
   - **点击率区间**: 设置CTR分段标准
   - **转化率区间**: 配置CVR分段规则
3. 为每个区间设置对应的操作策略
4. 系统实时保存配置

**区间设置原则：**
- 根据历史数据设置合理区间
- 考虑行业平均水平
- 预留调整空间
- 定期优化区间划分

**操作策略配置：**
- **预算调整**: 增加/减少/暂停
- **竞价调整**: 提高/降低竞价
- **状态控制**: 启用/暂停广告
- **调整幅度**: 设置具体调整比例

##### 3.3 广告组合规则配置

**组合规则设置：**
1. 切换到"广告组合"标签页
2. 查看系统生成的组合规则列表
3. 编辑具体的操作参数：
   - **预算操作**: 设置预算调整策略
   - **竞价操作**: 配置CPC调整规则
4. 支持批量编辑多个组合规则

**预算操作配置：**
- **操作类型**: 增加/减少/设置固定值
- **调整数值**: 具体的调整金额或比例
- **调整单位**: 固定值或百分比

**竞价操作配置：**
- **操作类型**: 增加/减少/设置固定值
- **调整数值**: 具体的调整金额或比例
- **调整单位**: 固定值或百分比

**批量编辑功能：**
1. 选择需要批量编辑的规则
2. 点击"批量编辑"按钮
3. 设置统一的调整参数
4. 应用到所选规则

##### 3.4 智能广告分析

**分析功能入口：**
- 从策略管理页面进入分析页面
- 或直接访问广告分析模块

**开始智能分析：**
1. 点击"智能分析"按钮
2. 配置分析参数：
   - **领星账号**: 选择要分析的账号
   - **广告策略**: 选择应用的策略
   - **广告状态**: 筛选广告状态
   - **分析时间范围**: 设置数据时间段
3. 点击"开始分析"执行智能分析
4. 系统显示分析进度
5. 分析完成后查看结果和建议

**分析结果查看：**
- **广告效果概览**: 整体表现数据
- **优化建议**: AI生成的调整建议
- **详细数据**: 具体的广告数据分析
- **趋势图表**: 可视化的数据趋势

##### 3.5 实时监控与调整

**数据监控：**
- 实时查看广告投放数据
- 监控关键指标变化
- 接收异常预警通知

**手动调整：**
1. 在分析结果页面找到需要调整的广告
2. 点击对应的调整按钮：
   - **预算调整**: 修改日预算
   - **竞价调整**: 修改关键词竞价
   - **状态调整**: 启用/暂停广告
3. 输入新的参数值
4. 确认提交调整

**批量调整：**
1. 选择多个需要调整的项目
2. 选择调整类型和参数
3. 批量应用调整策略

##### 3.6 预算竞价优化

**预算优化建议：**
- 基于ROI表现调整预算分配
- 识别高效广告增加投入
- 暂停低效广告节省成本

**竞价优化策略：**
- 根据关键词表现调整竞价
- 优化广告位置和展现
- 提高转化率和降低成本

**自动化执行：**
- 系统可根据策略自动执行调整
- 支持设置执行频率和条件
- 提供执行日志和结果反馈

#### 4. 策略优化建议

**新手策略配置：**
1. 从保守的区间设置开始
2. 小幅度调整观察效果
3. 逐步优化参数配置
4. 积累经验后扩大调整幅度

**高级策略技巧：**
- 根据产品生命周期调整策略
- 结合季节性因素优化参数
- 分析竞品策略制定对策
- 定期回顾和优化策略效果

**数据驱动决策：**
- 基于历史数据制定策略
- 持续监控关键指标
- 及时调整优化方向
- 建立策略效果评估体系

#### 5. 常见问题处理

**Q: 策略设置后多久生效？**
A: 策略配置实时保存，分析执行后立即生效

**Q: 如何判断策略效果好坏？**
A: 关注ACOS、转化率、ROI等关键指标的变化趋势

**Q: 可以同时运行多个策略吗？**
A: 可以，但需要避免策略冲突，建议分不同产品或账号使用

**Q: 策略调整频率建议？**
A: 建议每周评估一次，根据数据表现决定是否调整

### 创建广告模块操作详解

#### 1. 模块入口
- 导航路径：运营管理 → 广告管理 → 创建广告
- 页面路由：`/operate/advertisingManage/advertisingManage`
- 权限要求：`operation:advertising-creation:create`

#### 2. 模块结构说明

**广告列表页面**
- 显示已创建的广告任务
- 监控广告创建状态和进度
- 提供重试和详情查看功能

**广告模板管理页面**
- 创建和管理广告模板
- 配置模板参数和规则
- 支持自动和手动广告模板

**广告创建页面**
- 选择模板和输入SKU
- 配置关键词和竞价
- 提交创建任务

#### 3. 核心功能操作

##### 3.1 广告模板管理

**创建广告模板：**
1. 点击"创建广告模板"按钮
2. 选择广告类型：
   - **自动广告**: 系统自动匹配关键词
   - **手动广告**: 手动设置关键词和竞价
3. 配置模板基本信息：
   - 模板名称
   - 站点选择
   - 广告组合
4. 设置投放参数：
   - 投放预算
   - 竞价策略
   - 开始/结束时间
5. 配置命名规则
6. 保存模板

**自动广告模板配置：**
- **默认竞价**: 设置广告组默认竞价
- **竞价策略**: 选择动态竞价策略
- **投放位置调整**: 配置不同位置的竞价调整
  - 商品页面顶部
  - 商品页面其他位置
  - 搜索结果页面

**手动广告模板配置：**
- **匹配类型设置**:
  - 精确匹配及竞价
  - 词组匹配及竞价
  - 广泛匹配及竞价
- **自动投放设置**:
  - 替代品投放及竞价
  - 互补品投放及竞价
- **竞价调整**: 各种匹配类型的竞价调整

**命名规则配置：**
- **广告系列命名**: 前缀-中缀-后缀-自定义
- **广告组命名**: 前缀-中缀-后缀-自定义
- **动态元素**: SKU、创建日期等
- **命名预览**: 实时显示命名效果

##### 3.2 创建广告操作

**广告创建流程：**
1. 点击"创建广告"按钮
2. **第一步：选择模板和输入SKU**
   - 从下拉列表选择广告模板
   - 输入要推广的SKU列表
   - 系统自动获取SKU对应的ASIN
3. **第二步：配置关键词（手动广告）**
   - 为每个SKU添加关键词
   - 设置关键词匹配类型
   - 配置关键词竞价
4. **第三步：确认并提交**
   - 预览广告配置信息
   - 确认无误后提交创建

**SKU输入方式：**
- **手动输入**: 在文本框中输入SKU列表
- **批量导入**: 支持Excel文件批量导入
- **格式要求**: 每行一个SKU，支持批量粘贴

**关键词配置（手动广告）：**
- **关键词来源**:
  - 手动输入关键词
  - 从关键词库选择
  - 竞品关键词分析
- **匹配类型**: 精确、词组、广泛匹配
- **竞价设置**: 可统一设置或单独配置
- **否定关键词**: 设置否定关键词列表

##### 3.3 广告状态监控

**状态类型说明：**
- **创建中**: 广告正在创建过程中
- **创建成功**: 广告创建完成
- **创建失败**: 广告创建失败，需要处理
- **部分成功**: 部分广告创建成功

**进度监控：**
- 实时显示创建进度
- 显示成功/失败/总数统计
- 提供详细的创建日志

**失败处理：**
1. 查看失败原因
2. 修正问题（如SKU无效、预算不足等）
3. 点击"重试"按钮重新创建
4. 或删除失败任务重新创建

##### 3.4 广告详情查看

**详情信息包括：**
- **基本信息**: 模板名称、创建时间、状态等
- **SKU列表**: 推广的商品列表及状态
- **关键词详情**: 关键词列表及竞价信息
- **创建日志**: 详细的创建过程记录

**SKU状态监控：**
- 每个SKU的创建状态
- 失败SKU的具体错误信息
- 成功创建的广告ID

**操作记录：**
- 创建时间和操作人
- 重试记录和结果
- 参数修改历史

##### 3.5 批量操作功能

**批量创建广告：**
1. 准备包含多个SKU的Excel文件
2. 选择适合的广告模板
3. 上传文件进行批量创建
4. 监控批量创建进度

**批量重试：**
1. 选择失败的广告任务
2. 点击批量重试按钮
3. 系统自动重新尝试创建

**批量删除：**
1. 选择不需要的广告任务
2. 执行批量删除操作
3. 清理无效的创建记录

#### 4. 模板优化建议

**自动广告模板优化：**
- 根据产品类型调整默认竞价
- 优化投放位置竞价调整比例
- 定期分析自动广告表现
- 提取高效关键词用于手动广告

**手动广告模板优化：**
- 基于关键词研究设置匹配类型
- 合理配置不同匹配类型的竞价
- 定期优化关键词列表
- 添加否定关键词提高精准度

**命名规则优化：**
- 建立统一的命名标准
- 便于后续管理和分析
- 包含关键信息便于识别
- 考虑字符长度限制

#### 5. 常见问题处理

**Q: 为什么某些SKU创建失败？**
A: 常见原因包括：SKU无效、ASIN不存在、预算不足、权限问题等

**Q: 如何提高广告创建成功率？**
A: 确保SKU有效性、检查账户余额、验证权限设置、使用经过验证的模板

**Q: 创建的广告多久生效？**
A: 通常在创建成功后几分钟到几小时内生效，具体时间取决于亚马逊审核

**Q: 可以修改已创建的广告吗？**
A: 创建后的广告需要在亚马逊广告后台进行修改，本系统主要负责创建

**Q: 如何选择合适的广告模板？**
A: 根据产品类型、推广目标、预算规模选择，新品建议先用自动广告

## 完整操作案例

### 案例一：新品上架完整流程

**背景**: 新上架一款蓝牙耳机产品，需要完成文案创建、补货分析、广告投放的完整流程。

**第一步：创建产品文案**
1. 进入智能文案模块 (`/dev/copywriting`)
2. 点击"新增"创建新文案项目
3. 填写产品基本信息：
   - SKU: `BT-EARPHONE-001`
   - 产品名称: `无线蓝牙耳机5.0`
   - 目标语言: `英语`
   - 目标受众: `年轻上班族、运动爱好者`
   - 使用场景: `通勤、运动、办公`
4. 添加竞品信息：
   - 搜索竞品ASIN: `B08XYZ123`
   - 系统自动获取竞品标题和五点描述
   - 添加2-3个主要竞品
5. 点击"解析受众场景"进行AI分析
6. 点击"保存并生成"开始文案生成
7. 等待AI生成完成，查看生成的：
   - 产品标题
   - 五点描述
   - 详情页文案
8. 手动优化文案内容，确保符合品牌调性
9. 使用翻译功能生成其他语言版本

**第二步：设置补货规则**
1. 进入补货建议模块 (`/operate/replenishmentProposal`)
2. 点击"默认规则设置"配置基础规则：
   - 库存周期: `30天`
   - 权重模板: `保守型 (65-35-0-0-0-0)`
3. 为新品设置自定义规则：
   - 选择新品SKU
   - 点击"自定义规则设置"
   - 调整为新品适用的权重: `40-30-20-10-0-0`
   - 库存周期设为: `45天`（新品需要更长安全期）

**第三步：创建广告策略**
1. 进入广告策略管理 (`/operate/advertisingStrategyManage/strategy`)
2. 点击"新增策略"
3. 选择"新品推广"模板
4. 输入策略名称: `蓝牙耳机新品推广策略`
5. 进入策略设置页面配置：
   - ACOS区间: `<20%`, `20-30%`, `>30%`
   - 展现量区间: `<1000`, `1000-5000`, `>5000`
   - 对应操作: 低ACOS增加预算，高ACOS降低竞价

**第四步：创建广告模板**
1. 进入广告管理 (`/operate/advertisingManage/advertisingTemlMg`)
2. 点击"创建广告模板"
3. 配置自动广告模板：
   - 模板名称: `新品自动广告模板`
   - 广告类型: `自动广告`
   - 站点: `美国站`
   - 投放预算: `$50`
   - 默认竞价: `$0.5`
   - 竞价策略: `动态竞价-仅降低`
4. 配置命名规则：
   - 广告系列: `SKU-AUTO-创建日期`
   - 广告组: `SKU-AUTO-创建日期`

**第五步：执行广告创建**
1. 进入创建广告页面 (`/operate/advertisingManage/advertisingManage`)
2. 点击"创建广告"
3. 选择刚创建的自动广告模板
4. 输入SKU: `BT-EARPHONE-001`
5. 确认信息无误后提交创建
6. 监控创建状态，确保成功

**第六步：监控和优化**
1. 等待3-7天收集初始数据
2. 进入补货建议模块执行"智能补货分析"
3. 根据销售数据调整库存策略
4. 在广告策略管理中查看广告表现
5. 根据数据表现优化广告策略

### 案例二：老品优化流程

**背景**: 已销售3个月的产品，需要优化补货策略和广告投放效果。

**第一步：分析历史数据**
1. 进入补货建议模块查看历史补货记录
2. 点击"历史诊断"分析过往决策准确性
3. 查看毛利趋势图，识别优化机会

**第二步：优化补货规则**
1. 基于历史数据调整权重规则
2. 将权重调整为: `30-25-20-15-10-0`（更平衡的权重分配）
3. 根据供应链稳定性调整库存周期

**第三步：广告策略优化**
1. 进入广告分析页面查看当前表现
2. 识别高ACOS的关键词和广告组
3. 调整竞价策略和预算分配
4. 暂停表现不佳的广告

**第四步：文案优化**
1. 基于客户反馈和竞品分析优化文案
2. 更新产品五点描述
3. 优化详情页内容

## 系统维护与最佳实践

### 日常维护建议

**每日检查项目：**
- 查看广告创建状态，处理失败任务
- 监控广告投放数据异常
- 检查补货建议更新情况
- 处理系统通知和预警

**每周分析项目：**
- 执行智能补货分析
- 评估广告策略效果
- 优化关键词和竞价
- 更新产品文案内容

**每月优化项目：**
- 全面评估策略效果
- 调整规则参数配置
- 清理无效数据和模板
- 培训团队使用技巧

### 数据安全与备份

**权限管理：**
- 定期检查用户权限设置
- 及时删除离职员工账号
- 按角色分配最小必要权限
- 记录重要操作日志

**数据备份：**
- 定期导出重要业务数据
- 备份自定义规则和模板
- 保存历史分析结果
- 建立数据恢复流程

### 团队协作建议

**角色分工：**
- **运营人员**: 负责日常数据分析和策略执行
- **文案人员**: 负责产品文案创建和优化
- **广告专员**: 负责广告策略制定和优化
- **数据分析师**: 负责深度数据分析和规则优化

**协作流程：**
1. 建立标准操作流程(SOP)
2. 定期团队培训和经验分享
3. 建立问题反馈和解决机制
4. 制定绩效评估标准

## 系统更新与技术支持

### 版本更新说明
- 系统会定期发布功能更新
- 重要更新会提前通知用户
- 建议及时升级到最新版本
- 关注新功能使用指南

### 技术支持渠道
- **在线帮助**: 系统内置帮助文档
- **技术支持**: 联系技术支持团队
- **用户社区**: 参与用户交流群
- **培训服务**: 定期组织使用培训

### 常见技术问题

**Q: 系统响应慢怎么办？**
A: 检查网络连接，清理浏览器缓存，或联系技术支持

**Q: 数据不同步怎么处理？**
A: 刷新页面，检查权限设置，或等待系统自动同步

**Q: 忘记密码如何重置？**
A: 使用密码重置功能，或联系管理员重置

---

*本文档基于系统前端代码深度分析生成，涵盖了系统的核心功能和操作流程。如有疑问或需要进一步支持，请联系技术支持团队。*

**文档版本**: v1.0
**更新日期**: 2024年12月
**适用系统版本**: v2.4.1-snapshot
